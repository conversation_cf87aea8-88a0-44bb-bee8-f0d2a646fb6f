// Copyright (c) Pixel Crushers. All rights reserved.

using UnityEngine;

namespace PixelCrushers.Wrappers
{

    /// <summary>
    /// This wrapper for PixelCrushers.JsonDataSerializer keeps references intact if you switch 
    /// between the compiled assembly and source code versions of the original class.
    /// </summary>
    [AddComponentMenu("Pixel Crushers/Save System/Data Serializers/Json Data Serializer")]
    public class JsonDataSerializer : PixelCrushers.JsonDataSerializer
    {
    }

}
