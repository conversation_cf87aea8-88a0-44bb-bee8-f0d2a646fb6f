// Copyright (c) Pixel Crushers. All rights reserved.

using UnityEngine;

namespace PixelCrushers.Wrappers
{

    /// <summary>
    /// This wrapper for PixelCrushers.AutoSaveLoad keeps references intact if you switch 
    /// between the compiled assembly and source code versions of the original class.
    /// </summary>
    [AddComponentMenu("Pixel Crushers/Save System/Misc/Auto Save Load")]
    public class AutoSaveLoad : PixelCrushers.AutoSaveLoad
    {
    }

}
