// Copyright (c) Pixel Crushers. All rights reserved.

using UnityEngine;

namespace PixelCrushers.Wrappers
{

    /// <summary>
    /// This wrapper for PixelCrushers.CheckPhysics2D keeps references intact if you
    /// switch between the compiled assembly and source code versions of the original
    /// class.
    /// </summary>
    [AddComponentMenu("Pixel Crushers/Common/Misc/Check Physics 2D")]
    public class CheckPhysics2D : PixelCrushers.CheckPhysics2D
    {
    }

}
