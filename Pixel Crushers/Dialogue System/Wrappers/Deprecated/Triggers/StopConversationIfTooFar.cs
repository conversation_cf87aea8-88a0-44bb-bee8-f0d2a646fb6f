// Copyright (c) Pixel Crushers. All rights reserved.

using UnityEngine;

namespace PixelCrushers.DialogueSystem.Wrappers
{

    /// <summary>
    /// This wrapper class keeps references intact if you switch between the 
    /// compiled assembly and source code versions of the original class.
    /// </summary>
    [AddComponentMenu("")] // Deprecated
    public class StopConversationIfTooFar : PixelCrushers.DialogueSystem.StopConversationIfTooFar
    {
    }

}
