// Copyright (c) Pixel Crushers. All rights reserved.

using UnityEngine;

namespace PixelCrushers.DialogueSystem.Wrappers
{

    /// <summary>
    /// This wrapper class keeps references intact if you switch between the 
    /// compiled assembly and source code versions of the original class.
    /// </summary>
    [HelpURL("http://www.pixelcrushers.com/dialogue_system/manual2x/html/standard_u_i_quest_log_window.html")]
    [AddComponentMenu("Pixel Crushers/Dialogue System/UI/Standard UI/Selection/Standard UI Quest Log Window")]
    public class StandardUIQuestLogWindow : PixelCrushers.DialogueSystem.StandardUIQuestLogWindow
    {
    }

}
