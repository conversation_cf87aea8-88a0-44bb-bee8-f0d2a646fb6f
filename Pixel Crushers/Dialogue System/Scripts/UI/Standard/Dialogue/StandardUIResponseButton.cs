// Copyright (c) Pixel Crushers. All rights reserved.

using UnityEngine;
using TMPro;
using UnityEngine.UI;
using System.Collections;

namespace PixelCrushers.DialogueSystem
{

    /// <summary>
    /// Response button for use with Standard Dialogue UI. Add this component to every
    /// response button in the dialogue UI.
    /// </summary>
    [AddComponentMenu("")] // Use wrapper.
    public class StandardUIResponseButton : MonoBehaviour
    {

        public UnityEngine.UI.Button button;

        [Tooltip("Text element to display response text.")]
        public UITextField label;

        [Tooltip("Apply emphasis tag colors to button text.")]
        public bool setLabelColor = true;

        [Tooltip("Set button's text to this color by default.")]
        public Color defaultColor = Color.white;


        //<PERSON>'s dirty additions
        public RectTransform lg = default;
        public ScrollRect scrollRect = default;
        [SerializeField] private RectTransform textRect = default;
        [SerializeField] private float minHeight = default;
        [SerializeField] private RectTransform buttonRect = default;

        /// <summary>
        /// Gets or sets the response text element.
        /// </summary>
        public string text
        {
            get
            {
                return label.text;
            }
            set
            {
                label.text = UITools.StripRPGMakerCodes(value);
                //UITextField label1 = new UITextField(label);
                

                UITools.SendTextChangeMessage(label);
            }
        }

        /// <summary>
        /// Indicates whether the button is an allowable response.
        /// </summary>
        public bool isClickable
        {
            get { return (button != null) && button.interactable; }
            set { if (button != null) button.interactable = value; }
        }

        /// <summary>
        /// Indicates whether the button is shown or not.
        /// </summary>
        public bool isVisible { get; set; }

        /// <summary>
        /// Gets or sets the response associated with this button. If the player clicks this 
        /// button, this response is sent back to the dialogue system.
        /// </summary>
        public Response response { get; set; }

        /// <summary>
        /// Gets or sets the target that will receive click notifications.
        /// </summary>
        public Transform target { get; set; }


        /// <summary>
        /// Clears the button.
        /// </summary>
        public void Reset()
        {
            isClickable = false;
            isVisible = false;
            response = null;
            if (label != null)
            {
                label.text = string.Empty;
                SetColor(defaultColor);
            }
            Invoke(nameof(ScaleSizeAppropriately), 0.1f);//We have to wait one frame to make sure the gameobject is setactive
        }

        public virtual void Awake()
        {
            if (button == null) button = GetComponent<UnityEngine.UI.Button>();
            if (button == null) Debug.LogWarning("Dialogue System: Response button '" + name + "' is missing a Unity UI Button component!", this);
        }

        public virtual void Start()
        {
            if (button != null) button.onClick.AddListener(OnClick);
        }

        /// <summary>
        /// Sets the button's text using the specified formatted text.
        /// </summary>
        public virtual void SetFormattedText(FormattedText formattedText)
        {
            if (formattedText == null) return;
            text = UITools.GetUIFormattedText(formattedText);
            SetColor((formattedText.emphases.Length > 0) ? formattedText.emphases[0].color : defaultColor);
            Invoke(nameof(ScaleSizeAppropriately), 0.1f);//We have to wait one frame to make sure the gameobject is setactive
            
            //StartCoroutine(ScaleSizeAppropriately());

        }

        //IMPORTANT:::::: Stephens addition
        private void ScaleSizeAppropriately()
        {
            
            RectTransform parentRect = this.GetComponent(typeof(RectTransform)) as RectTransform;
            Vector2 parentRectSize = parentRect.sizeDelta;
            Vector2 textRectSize = textRect.sizeDelta;
            if (textRectSize.y < minHeight)
                parentRectSize.y = minHeight;
            else
                parentRectSize.y = textRectSize.y;

            parentRect.sizeDelta = parentRectSize;
            buttonRect.sizeDelta = parentRectSize;
            lg.ForceUpdateRectTransforms();
            LayoutRebuilder.ForceRebuildLayoutImmediate(lg);
            scrollRect.normalizedPosition = new Vector2(0, 0);

        }

        /// <summary>
        /// Sets the button's text using plain text.
        /// </summary>
        public virtual void SetUnformattedText(string unformattedText)
        {
            text = unformattedText;
            SetColor(defaultColor);
            Invoke(nameof(ScaleSizeAppropriately), 0.1f);//We have to wait one frame to make sure the gameobject is setactive
        }

        protected virtual void SetColor(Color currentColor)
        {
            if (setLabelColor) label.color = currentColor;
        }

        /// <summary>
        /// Handles a button click by calling the response handler.
        /// </summary>
        public virtual void OnClick()
        {
            if (target != null) target.SendMessage("OnClick", response, SendMessageOptions.RequireReceiver);
        }

    }

}
