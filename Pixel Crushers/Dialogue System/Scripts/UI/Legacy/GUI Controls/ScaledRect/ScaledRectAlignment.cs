using UnityEngine;

namespace PixelCrushers.DialogueSystem.UnityGUI
{

    /// <summary>
    /// Specifies an alignment for the origin or alignment of a ScaledRect.
    /// </summary>
    public enum ScaledRectAlignment
    {

        /// <summary>
        /// Top left
        /// </summary>
        TopLeft,

        /// <summary>
        /// Top center
        /// </summary>
        TopCenter,

        /// <summary>
        /// Top right
        /// </summary>
        TopRight,

        /// <summary>
        /// Middle left
        /// </summary>
        MiddleLeft,

        /// <summary>
        /// Middle center
        /// </summary>
        MiddleCenter,

        /// <summary>
        /// Middle right
        /// </summary>
        MiddleRight,

        /// <summary>
        /// Bottom left
        /// </summary>
        BottomLeft,

        /// <summary>
        /// Bottom center
        /// </summary>
        BottomCenter,

        /// <summary>
        /// Bottom right
        /// </summary>
        BottomRight
    }

}
