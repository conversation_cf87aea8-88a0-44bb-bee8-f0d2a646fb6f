// Copyright (c) Pixel Crushers. All rights reserved.

using UnityEngine;

namespace PixelCrushers.DialogueSystem
{

    /// <summary>
    /// Add [DialogueTriggerEvent] to a string variable show a popup of 
    /// dialogue trigger events in the inspector.
    /// </summary>
    public class DialogueTriggerEventAttribute : PropertyAttribute
    {

        public DialogueTriggerEventAttribute()
        {
        }
    }
}
