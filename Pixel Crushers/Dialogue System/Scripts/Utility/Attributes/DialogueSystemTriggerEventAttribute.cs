// Copyright (c) Pixel Crushers. All rights reserved.

using UnityEngine;

namespace PixelCrushers.DialogueSystem
{

    /// <summary>
    /// Add [DialogueSystemTriggerEvent] to a string variable show a popup of 
    /// dialogue trigger events in the inspector.
    /// </summary>
    public class DialogueSystemTriggerEventAttribute : PropertyAttribute
    {

        public DialogueSystemTriggerEventAttribute()
        {
        }
    }
}
