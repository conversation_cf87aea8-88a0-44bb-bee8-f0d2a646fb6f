using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Language.Lua.Library;

namespace Language.Lua
{
    public class LuaInterpreter
    {
		public static LuaValue RunFile(string luaFile)
		{
			//[PixelCrushers]
			//return Interpreter(File.ReadAllText(luaFile));
			UnityEngine.Debug.LogWarning("LuaInterpreter.RunFile() is disabled in this version of LuaInterpreter.");
			return LuaNil.Nil;
		}
		
		public static LuaValue RunFile(string luaFile, LuaTable enviroment)
		{
			//[PixelCrushers]
			//return Interpreter(File.ReadAllText(luaFile), enviroment);
			UnityEngine.Debug.LogWarning("LuaInterpreter.RunFile() is disabled in this version of LuaInterpreter.");
			return LuaNil.Nil;
		}
		
        public static LuaValue Interpreter(string luaCode)
        {
            return Interpreter(luaCode, CreateGlobalEnviroment());
        }

        public static LuaValue Interpreter(string luaCode, LuaTable enviroment)
        {
            Chunk chunk = Parse(luaCode);
            chunk.Enviroment = enviroment;
            return chunk.Execute();
        }

        static Parser parser = new Parser();

        public static Chunk Parse(string luaCode)
        {
            bool success;
            Chunk chunk = parser.ParseChunk(new TextInput(luaCode), out success);
            if (success)
            {
                return chunk;
            }
            else
            {
                throw new ArgumentException("Code has syntax errors:\r\n" + parser.GetEorrorMessages());
            }
        }

        public static LuaTable CreateGlobalEnviroment()
        {
            LuaTable global = new LuaTable();

            BaseLib.RegisterFunctions(global);
            StringLib.RegisterModule(global);
            TableLib.RegisterModule(global);
            IOLib.RegisterModule(global);
            FileLib.RegisterModule(global);
            MathLib.RegisterModule(global);
            OSLib.RegisterModule(global);

            global.SetNameValue("_G", global);

            return global;
        }
    }
}
