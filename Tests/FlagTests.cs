// Copyright Isto Inc.
using Isto.Core.Enums;
using NUnit.Framework;
using System;
using UnityEngine;

namespace Isto.Core.Tests
{
    [Category("Enums")]
    public class ExpandableFlagsTests
    {
        private class FingerFlags : Int32Flags<FingerFlags>
        {
            public static readonly FingerFlags NONE = new FingerFlags(BIT_0, nameof(NONE));
            public static readonly FingerFlags THUMB = new FingerFlags(BIT_1, nameof(THUMB));
            public static readonly FingerFlags INDEX = new FingerFlags(BIT_2, nameof(INDEX));
            public static readonly FingerFlags MAJOR = new FingerFlags(BIT_3, nameof(MAJOR));
            public static readonly FingerFlags ANNULAR = new FingerFlags(BIT_4, nameof(ANNULAR));
            public static readonly FingerFlags AURICULAR = new FingerFlags(BIT_5, nameof(AURICULAR));
            public static readonly FingerFlags ALL = new FingerFlags(BIT_ALL, nameof(ALL));

            // Constructor for building flags at runtime (e.g. when combining flags)
            public FingerFlags() : base()
            {
            }

            // Constructor for defining flags only at initialization, "constant style"
            public FingerFlags(UInt32 value, string name) : base(value, name)
            {
            }
        }

        // Hopefully project extension class only needs to define desired extra values, this reflects that scheme
        private class MutantFingerFlags : FingerFlags
        {
            public static readonly MutantFingerFlags SECOND_PINKY = new MutantFingerFlags(BIT_6, nameof(SECOND_PINKY));
            public static readonly MutantFingerFlags ELBOW_FINGER = new MutantFingerFlags(BIT_32, nameof(ELBOW_FINGER));

            public MutantFingerFlags(UInt32 value, string name) : base(value, name)
            {
            }
        }

        [Test]
        public void DefinitionTest()
        {
            Debug.Log($"Finger Flags GetValues() enumeration:");
            foreach (FingerFlags flag in FingerFlags.GetValues())
            {
                string log = string.Format("Value:{0} Name:{1}", flag.Value, flag.Name);
                Debug.Log(log);

                if (flag == FingerFlags.NONE)
                {
                    Debug.Assert(flag.Value == 0, "Flag Fingers was expected to define NONE with value 0");
                    Debug.Assert(flag.Name == "NONE", "Flag Fingers was expected to define NONE with value 0");
                }
                else if (flag == FingerFlags.THUMB)
                {
                    Debug.Assert(flag.Value == 1, "Flag Fingers was expected to define THUMB with value 1");
                    Debug.Assert(flag.Name == "THUMB", "Flag Fingers was expected to define THUMB with value 1");
                }
                else if (flag == FingerFlags.INDEX)
                {
                    Debug.Assert(flag.Value == 2, "Flag Fingers was expected to define INDEX with value 2");
                    Debug.Assert(flag.Name == "INDEX", "Flag Fingers was expected to define INDEX with value 2");
                }
                else if (flag == FingerFlags.MAJOR)
                {
                    Debug.Assert(flag.Value == 4, "Flag Fingers was expected to define MAJOR with value 4");
                    Debug.Assert(flag.Name == "MAJOR", "Flag Fingers was expected to define MAJOR with value 4");
                }
                else if (flag == FingerFlags.ANNULAR)
                {
                    Debug.Assert(flag.Value == 8, "Flag Fingers was expected to define ANNULAR with value 8");
                    Debug.Assert(flag.Name == "ANNULAR", "Flag Fingers was expected to define ANNULAR with value 8");
                }
                else if (flag == FingerFlags.AURICULAR)
                {
                    Debug.Assert(flag.Value == 16, "Flag Fingers was expected to define AURICULAR with value 16");
                    Debug.Assert(flag.Name == "AURICULAR", "Flag Fingers was expected to define AURICULAR with value 16");
                }
                else if (flag == FingerFlags.ALL)
                {
                    Debug.Assert(flag.Value == 4294967295, "Flag FingerFlags was expected to define ALL with value 4294967295");
                    Debug.Assert(flag.Name == "ALL", "Flag FingerFlags was expected to define ALL with value 4294967295");
                }
                else if (flag == MutantFingerFlags.SECOND_PINKY)
                {
                    Debug.Assert(flag.Value == 32, "Flag MutantFingerFlags was expected to define SECOND_PINKY with value 32");
                    Debug.Assert(flag.Name == "SECOND_PINKY", "Flag MutantFingerFlags was expected to define SECOND_PINKY with value 32");
                }
                else if (flag == MutantFingerFlags.ELBOW_FINGER)
                {
                    Debug.Assert(flag.Value == 2147483648, "Flag MutantFingerFlags was expected to define ELBOW_FINGER with value 2147483648");
                    Debug.Assert(flag.Name == "ELBOW_FINGER", "Flag MutantFingerFlags was expected to define ELBOW_FINGER with value 2147483648");
                }
            }
        }

        [Test]
        public void CastingTest()
        {
            Debug.Log("Getting FingerFlags with value = 4");
            uint fingerValue = 4;
            FingerFlags fingers = FingerFlags.GetFromValue(fingerValue);
            Debug.Assert(fingers != null, "FingerFlags with value 4 was not found in FingerFlags declarations");
            Debug.Assert(fingers.Value == 4, "Flag Fingers #4 was expected to have value 4");
            Debug.Assert(fingers.Name == "MAJOR", "Flag Fingers #4 was expected to match flag named MAJOR");
            Debug.Assert(fingers == FingerFlags.MAJOR, "Flag Fingers #4 was expected to match flag symbol for MAJOR");

            Debug.Log("Getting FingerFlags with name = SECOND_PINKY");
            string fingerName = "SECOND_PINKY";
            fingers = FingerFlags.GetFromName(fingerName);
            Debug.Assert(fingers != null, "FingerFlags with name SECOND_PINKY was not found in FingerFlags declarations");
            Debug.Assert(fingers.Value == 32, "Flag Fingers 'SECOND_PINKY' was expected to match flag with value 32");
            Debug.Assert(fingers.Name == "SECOND_PINKY", "Flag Fingers 'SECOND_PINKY' was expected to have name SECOND_PINKY");
            Debug.Assert(fingers == MutantFingerFlags.SECOND_PINKY, "Flag Fingers 'SECOND_PINKY' was expected to match flag symbol for SECOND_PINKY");
        }

        // This test logs by doing comparisons instead of just printing the enum's value and name
        [Test]
        public void LogAllFingerFlagsTest()
        {
            LogSingleFlagTestValue(MutantFingerFlags.NONE);
            LogSingleFlagTestValue(MutantFingerFlags.THUMB);
            LogSingleFlagTestValue(MutantFingerFlags.INDEX);
            LogSingleFlagTestValue(MutantFingerFlags.MAJOR);
            LogSingleFlagTestValue(MutantFingerFlags.ANNULAR);
            LogSingleFlagTestValue(MutantFingerFlags.AURICULAR);
            LogSingleFlagTestValue(MutantFingerFlags.SECOND_PINKY);
            LogSingleFlagTestValue(MutantFingerFlags.ELBOW_FINGER);
            LogSingleFlagTestValue(MutantFingerFlags.ALL);
        }

        [Test]
        public void CombineTest()
        {
            FingerFlags current = FingerFlags.NONE;
            LogSingleFlagTestValue(current); // None

            FingerFlags combined;

            //combined = current.CombineWith(FingerFlags.Thumb); // needs extensions but can be written once for all Int32Flags
            combined = current | FingerFlags.THUMB; // cleaner but needs to be written once per type
            Debug.Assert(combined.Value == FingerFlags.THUMB.Value, "Expected (None+Thumb) to be == to Thumb");

            // naive check - should work since we combined a flag with a 0
            LogSingleFlagTestValue(combined); // Thumb

            combined = current | MutantFingerFlags.ELBOW_FINGER;
            Debug.Assert(combined.Value == MutantFingerFlags.ELBOW_FINGER.Value, "Expected (None+ElbowFinger) to be == to ElbowFinger");
            LogSingleFlagTestValue(combined); // ElbowFinger

            combined = combined | MutantFingerFlags.ANNULAR;
            Debug.Assert(combined.Value == 2147483656, "Finger Flags (Auricular + ElbowFinger) was expected to have value 2147483656");
            LogSingleFlagTestValue(combined); // nothing logged (Auricular + ElbowFinger)

            Debug.Log($"Combined Finger Flags (Auricular+ElbowFinger) enumeration:");
            int flagCount = FingerFlags.LogAndCountFlags(combined); // Auricular + ElbowFinger
            Debug.Assert(flagCount == 2, $"Expected combination (None+Auricular+ElbowFinger) to contain 2 flags");
        }

        // naive value check - should work for any flag set explicitely defined
        private void LogSingleFlagTestValue(FingerFlags flag)
        {
            string testLog = "nothing logged!";
            if (flag == MutantFingerFlags.NONE)
            {
                testLog = "value is NONE";
            }
            else if (flag == MutantFingerFlags.THUMB)
            {
                testLog = "value is Thumb";
            }
            else if (flag == MutantFingerFlags.INDEX)
            {
                testLog = "value is Index";
            }
            else if (flag == MutantFingerFlags.MAJOR)
            {
                testLog = "value is Major";
            }
            else if (flag == MutantFingerFlags.ANNULAR)
            {
                testLog = "value is Auricular";
            }
            else if (flag == MutantFingerFlags.AURICULAR)
            {
                testLog = "value is Pinky";
            }
            else if (flag == MutantFingerFlags.ALL)
            {
                testLog = "value is All";
            }
            else if (flag == MutantFingerFlags.SECOND_PINKY)
            {
                testLog = "value is SecondPinky";
            }
            else if (flag == MutantFingerFlags.ELBOW_FINGER)
            {
                testLog = "value is ElbowFinger";
            }
            Debug.Log(testLog);
        }

        [Test]
        public void CombineAllTest()
        {
            FingerFlags combined = FingerFlags.Union(FingerFlags.INDEX, FingerFlags.MAJOR, FingerFlags.AURICULAR);
            Debug.Assert(combined.Value == 0b10110, "Finger Flags (Index+Major+Pinky) was expected to have value 22 (0b10110)");

            Debug.Log($"Combined Finger Flags (Index+Major+Pinky) enumeration:");
            int individualValueCount = FingerFlags.LogAndCountFlags(combined);
            Debug.Assert(individualValueCount == 3, "Finger Flags (Index+Major+Pinky) was expected to have 3 distinct flags matched");

            combined = FingerFlags.Union(FingerFlags.INDEX, FingerFlags.INDEX, FingerFlags.INDEX);
            Debug.Assert(combined.Value == 0b10, "Finger Flags (Index+Index+Index) was expected to have value 2 (0b10)");

            Debug.Log($"Combined Finger Flags (Index+Index+Index) enumeration:");
            individualValueCount = FingerFlags.LogAndCountFlags(combined);
            Debug.Assert(individualValueCount == 1, "Finger Flags (Index+Index+Index) was expected to have 1 distinct flag matched");
            Debug.Assert(combined.Value == FingerFlags.INDEX.Value, "Expected (Index+Index+Index) to be == to Index");
        }

        [Test]
        public void ExcludeTest()
        {
            FingerFlags current = FingerFlags.ALL;
            Debug.Log($"Trying to Single Log the All flag:");
            LogSingleFlagTestValue(current); // All

            FingerFlags excluded;
            int flagCount;

            Debug.Assert(current.HasFlag(FingerFlags.THUMB), "FingerFlags.All was expected to HasFlag for value FingerFlags.Thumb");
            Debug.Assert(current.HasFlag(FingerFlags.AURICULAR), "FingerFlags.All was expected to HasFlag for value FingerFlags.Pinky");
            Debug.Assert(current.HasFlag(MutantFingerFlags.ELBOW_FINGER), "FingerFlags.All was expected to HasFlag for value MutantFingerFlags.ElbowFinger");

            excluded = current.Exclude(FingerFlags.AURICULAR);

            Debug.Assert(excluded.HasFlag(FingerFlags.THUMB), "FingerFlags.Thumb was expected to still be part of flags after Exclude Pinky");
            Debug.Assert(!excluded.HasFlag(FingerFlags.AURICULAR), "FingerFlags.Pinky was expected to not be part of flags after Exclude Pinky");
            Debug.Assert(excluded.HasFlag(MutantFingerFlags.ELBOW_FINGER), "MutantFingerFlags.ElbowFinger was expected to still be part of flags after Exclude Pinky");

            excluded = excluded.Exclude(FingerFlags.THUMB);

            Debug.Assert(!excluded.HasFlag(FingerFlags.THUMB), "ingerFlags.Thumb was expected to not be part of flags after Exclude Thumb");
            Debug.Assert(!excluded.HasFlag(FingerFlags.AURICULAR), "FingerFlags.Pinky was expected to not be part of flags after Exclude Thumb");
            Debug.Assert(excluded.HasFlag(MutantFingerFlags.ELBOW_FINGER), "MutantFingerFlags.ElbowFinger was expected to still be part of flags after Exclude Thumb");

            // thirty flags remain because of other undefined bits in Int32 so numerical value is large
            Debug.Assert(excluded.Value == 4294967278, "Finger Flags All excluding Pinky and Thumb was expected to have value 4294967278");

            Debug.Log($"All Finger Flags excluding Thumb and Pinky enumeration:");
            flagCount = FingerFlags.LogAndCountFlags(excluded); // thirty flags remain in data but we only iterate on declared values when using GetValues
            Debug.Assert(flagCount == 5, $"Expected All Finger Flags excluding Pinky and Thumb to have 5 flags remaining");

            current = FingerFlags.Union(FingerFlags.INDEX, FingerFlags.MAJOR, FingerFlags.AURICULAR);
            Debug.Assert(current.Value == 0b10110, "Finger Flags (Index+Major+Pinky) was expected to have value 22 (0b10110)");
            Debug.Log($"Trying to Single Log the (Index+Major+Pinky) flag:");
            LogSingleFlagTestValue(current); // nothing
            Debug.Log($"Combined Finger Flags (Index+Major+Pinky) enumeration:");
            flagCount = FingerFlags.LogAndCountFlags(current); // Index + Major + Pinky
            Debug.Assert(flagCount == 3, $"Expected (Index+Major+Pinky) Flags to have 3 flags");

            excluded = current.Exclude(FingerFlags.AURICULAR);
            Debug.Assert(excluded.Value == 0b00110, "Finger Flags (Index+Major+Pinky) excluding Pinky was expected to have value 6 (0b00110)");
            Debug.Log($"Combined Finger Flags (Index+Major+Pinky) excluding Pinky enumeration:");
            flagCount = FingerFlags.LogAndCountFlags(excluded); // Index + Major
            Debug.Assert(flagCount == 2, $"Expected (Index+Major+Pinky) excluding Pinky to have 2 flags remaining");

            excluded = current.Exclude(FingerFlags.AURICULAR);
            Debug.Assert(excluded.Value == 0b00110, "Finger Flags (Index+Major+Pinky) excluding Pinky was expected to have value 6 (0b00110)");
            Debug.Log($"Combined Finger Flags (Index+Major+Pinky) excluding Pinky and Pinky enumeration:");
            flagCount = FingerFlags.LogAndCountFlags(excluded); // Index + Major
            Debug.Assert(flagCount == 2, $"Expected (Index+Major+Pinky) excluding Pinky and Pinky to have 2 flags remaining");

            excluded = excluded.Exclude(FingerFlags.MAJOR);
            Debug.Assert(excluded.Value == 0b00010, "Finger Flags (Index+Major+Pinky) excluding Pinky and Major was expected to have value 2 (0b00010)");
            Debug.Log($"Combined Finger Flags (Index+Major+Pinky) excluding Pinky and Major enumeration:");
            flagCount = FingerFlags.LogAndCountFlags(excluded); // Index
            Debug.Assert(flagCount == 1, $"Expected (Index+Major+Pinky) excluding Pinky and Major to have 1 flag remaining");
            Debug.Assert(excluded.Value == FingerFlags.INDEX.Value, "Expected (Index+Major+Pinky) excluding Pinky and Major to be == to Index");

            Debug.Log($"Trying to Single Log the Index flag:");
            LogSingleFlagTestValue(excluded); // Index
        }

        [Test]
        public void InvertTest()
        {
            Debug.Assert(FingerFlags.NONE == ~FingerFlags.ALL, "FingerFlags.All was expected to be the Inverse of FingerFlags.None");
            Debug.Assert(FingerFlags.ALL == ~FingerFlags.NONE, "FingerFlags.All was expected to be the Inverse of FingerFlags.None");
        }
    }
}