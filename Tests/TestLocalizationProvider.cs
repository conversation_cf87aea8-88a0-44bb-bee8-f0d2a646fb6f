// Copyright Isto Inc.

using Isto.Core.Localization;
using System.Collections.Generic;

namespace Isto.Core.Tests
{
    public class TestLocalizationProvider : ILocalizationProvider
    {
        private LanguageChanger.LanguageEnum _currentLanguage = LanguageChanger.LanguageEnum.ENGLISH;

        private Dictionary<string, Dictionary<LanguageChanger.LanguageEnum, string>> translations =
            new Dictionary<string, Dictionary<LanguageChanger.LanguageEnum, string>>
            {
                {
                    LocTestConstants.LANGUAGE_KEY,
                    LocTestConstants.LANGUAGE_TRANSLATION_TABLE
                },
                {
                    LocTestConstants.DISCARD_KEY,
                    LocTestConstants.DISCARD_TRANSLATION_TABLE
                },
                {
                    LocTestConstants.NEVER_KEY,
                    LocTestConstants.NEVER_TRANSLATION_TABLE
                }
            };

        public string GetLocalizedText(string locKey)
        {
            string translatedWord = LocTestConstants.LOCALIZATION_KEY_NOT_FOUND;

            if (translations.TryGetValue(locKey, out var languageDictionary))
            {
                if (languageDictionary.TryGetValue(_currentLanguage, out string translation))
                {
                    translatedWord = translation;
                }
            }

            return translatedWord;
        }

        public void SetLanguage(LanguageChanger.LanguageEnum language)
        {
            _currentLanguage = language;
        }

        public LanguageChanger.LanguageEnum GetCurrentLanguage()
        {
            throw new System.NotImplementedException();
        }

        public string GetCurrentLanguageName()
        {
            throw new System.NotImplementedException();
        }

        public string GetCurrentDeviceLanguage()
        {
            throw new System.NotImplementedException();
        }
    }
}