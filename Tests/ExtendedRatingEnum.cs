// Copyright Isto Inc.

namespace Isto.Core.Tests
{
    // This is a demo of an extension to the Rating Enum, used for unit testing.
    // Some test enums are directly in the test script and this one is outside so as to validate both use cases.
    public class ExtendedRatingEnum : RatingEnum
    {
        // Extending Rating allows you to define new RatingEnum values, but make them
        // ExtendedRatingEnum instances and not RatingEnum instances or some enumeration tools might not work
        public static readonly ExtendedRatingEnum FAIL_RANK = new ExtendedRatingEnum(nameof(FAIL_RANK));
        public static readonly ExtendedRatingEnum SEXY_RANK = new ExtendedRatingEnum(nameof(SEXY_RANK));

        public static readonly ExtendedRatingEnum SPECIAL_RANK = new ExtendedRatingEnum(15, nameof(SPECIAL_RANK));

        // Needs to be defined, but not intended to be used - keep private
        private ExtendedRatingEnum(string name) : base(name)
        {
        }

        // Needs to be defined, but not intended to be used - keep private
        private ExtendedRatingEnum(int value, string name) : base(value, name)
        {
        }
    }
}