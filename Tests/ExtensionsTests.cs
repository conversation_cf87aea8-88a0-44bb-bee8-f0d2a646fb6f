using NUnit.Framework;
using UnityEngine;

namespace Isto.Core.Tests
{
    [Category("Extensions")]
    public class ExtensionsTests
    {
        [Test]
        public void GetSnappedPosition()
        {
            Vector3 before, after;
            before = new Vector3(1.0f, 0f, 0f);
            after = before.GetSnappedPosition(1f);
            Assert.AreEqual(new Vector3(1.0f, 0f, 0f), after);

            before = new Vector3(1.1f, 0f, 0f);
            after = before.GetSnappedPosition(1f);
            Assert.AreEqual(new Vector3(1.0f, 0f, 0f), after);

            before = new Vector3(1.5f, 0f, 0f);
            after = before.GetSnappedPosition(1f);
            Assert.AreEqual(new Vector3(2.0f, 0f, 0f), after);

            before = new Vector3(1.9f, 0f, 0f);
            after = before.GetSnappedPosition(1f);
            Assert.AreEqual(new Vector3(2.0f, 0f, 0f), after);
        }


        [Test]
        public void FlipAlongX()
        {
            Vector3 before = new Vector3(1f, 0f, 0f);
            Vector3 after = before.FlipAlongX();

            Assert.AreEqual(new Vector3(-1f, 0f, 0f), after);
        }
    }
}