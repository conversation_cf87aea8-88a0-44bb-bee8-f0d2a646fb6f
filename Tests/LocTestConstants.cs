// Copyright Isto Inc.

using Isto.Core.Localization;
using System.Collections.Generic;

namespace Isto.Core.Tests
{
    public static class LocTestConstants
    {
        // Keys
        // These keys match what is currently set up in I2. This allows us to have some separation from I2
        // but allows us to easily test the I2LocalizationProvider if need be.
        public static readonly string LANGUAGE_KEY = "Settings/SingleSetting/Language";
        public static readonly string DISCARD_KEY = "UI/Settings/Discard";
        public static readonly string NEVER_KEY = "UI/Settings/Never";
        
        // English Terms
        public static readonly string ENGLISH_TERM_LANGUAGE = "Language";
        public static readonly string ENGLISH_TERM_DISCARD = "Discard";
        public static readonly string ENGLISH_TERM_NEVER = "Never";
        
        // Errors
        public static readonly string LOCALIZATION_KEY_NOT_FOUND = "Error! Localization not found!";

        // Language dictionaries 
        public static readonly Dictionary<LanguageChanger.LanguageEnum, string> LANGUAGE_TRANSLATION_TABLE =
            new Dictionary<LanguageChanger.LanguageEnum, string>
            {
                { LanguageChanger.LanguageEnum.ENGLISH, ENGLISH_TERM_LANGUAGE },
                { LanguageChanger.LanguageEnum.FRENCH, "Langue" },
                { LanguageChanger.LanguageEnum.RUSSIAN, "Язык" },
                { LanguageChanger.LanguageEnum.TRADITIONAL_CHINESE, "語言" },
                { LanguageChanger.LanguageEnum.SIMPLIFIED_CHINESE, "语言" },
                { LanguageChanger.LanguageEnum.KOREAN, "언어" },
                { LanguageChanger.LanguageEnum.JAPANESE, "言語" },
                { LanguageChanger.LanguageEnum.UKRANIAN, "Мова" },
                { LanguageChanger.LanguageEnum.GERMAN, "Sprache" },
                { LanguageChanger.LanguageEnum.HUNGARIAN, "Nyelv" },
                { LanguageChanger.LanguageEnum.TURKISH, "Dil" }
            };

        public static readonly Dictionary<LanguageChanger.LanguageEnum, string> DISCARD_TRANSLATION_TABLE =
            new Dictionary<LanguageChanger.LanguageEnum, string>
            {
                { LanguageChanger.LanguageEnum.ENGLISH, ENGLISH_TERM_DISCARD },
                { LanguageChanger.LanguageEnum.FRENCH, "Annuler" },
                { LanguageChanger.LanguageEnum.RUSSIAN, "Сбросить" },
                { LanguageChanger.LanguageEnum.TRADITIONAL_CHINESE, "丟棄" },
                { LanguageChanger.LanguageEnum.SIMPLIFIED_CHINESE, "丢弃" },
                { LanguageChanger.LanguageEnum.KOREAN, "버리기" },
                { LanguageChanger.LanguageEnum.JAPANESE, "破棄" },
                { LanguageChanger.LanguageEnum.UKRANIAN, "Скинути" },
                { LanguageChanger.LanguageEnum.GERMAN, "Verwerfen" },
                { LanguageChanger.LanguageEnum.HUNGARIAN, "Elvet" },
                { LanguageChanger.LanguageEnum.TURKISH, "Vazgeç" }
            };

        public static readonly Dictionary<LanguageChanger.LanguageEnum, string> NEVER_TRANSLATION_TABLE =
            new Dictionary<LanguageChanger.LanguageEnum, string>
            {
                { LanguageChanger.LanguageEnum.ENGLISH, ENGLISH_TERM_NEVER },
                { LanguageChanger.LanguageEnum.FRENCH, "Jamais" },
                { LanguageChanger.LanguageEnum.RUSSIAN, "никогда" },
                { LanguageChanger.LanguageEnum.TRADITIONAL_CHINESE, "絕不" },
                { LanguageChanger.LanguageEnum.SIMPLIFIED_CHINESE, "从不" },
                { LanguageChanger.LanguageEnum.KOREAN, "절대" },
                { LanguageChanger.LanguageEnum.JAPANESE, "一度もない" },
                { LanguageChanger.LanguageEnum.UKRANIAN, "Ніколи" },
                { LanguageChanger.LanguageEnum.GERMAN, "Nie" },
                { LanguageChanger.LanguageEnum.HUNGARIAN, "Soha" },
                { LanguageChanger.LanguageEnum.TURKISH, "Asla" }
            };
    }
}