// Copyright Isto Inc.
using Isto.Core.Enums;

namespace Isto.Core.Tests
{
    // This is a demo of an extendable Enum definition, used for unit testing.
    // Some test enums are directly in the test script and this one is outside so as to validate both use cases.
    public class RatingEnum : Int32Enum<RatingEnum>
    {
        public static readonly RatingEnum AWFUL = new RatingEnum(nameof(AWFUL));
        public static readonly RatingEnum BAD = new RatingEnum(nameof(BAD));
        public static readonly RatingEnum MEDIUM = new RatingEnum(nameof(MEDIUM));
        public static readonly RatingEnum GOOD = new RatingEnum(nameof(GOOD));
        public static readonly RatingEnum AWESOME = new RatingEnum(nameof(AWESOME));

        public static readonly RatingEnum UNCATEGORIZED = new RatingEnum(99, nameof(UNCATEGORIZED));

        // Auto-value constructor - optional
        internal protected RatingEnum(string name) : base(name)
        {
        }

        // Specific value constructor - mandatory
        internal protected RatingEnum(int value, string name) : base(value, name)
        {
        }
    }
}