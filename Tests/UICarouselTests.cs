// Copyright Isto Inc.

using Isto.Core.Localization;
using Isto.Core.UI;
using NUnit.Framework;
using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace Isto.Core.Tests
{
    [Category("UI Carousel Tests")]
    public class UICarouselTests
    {
#if UNITY_EDITOR
        private UICarousel CreateUICarousel()
        {
            GameObject carouselGameObject =
                AssetDatabase.LoadAssetAtPath<GameObject>(UICarouselTestConstants.UI_CAROUSEL_ASSET_PATH);

            return Object.Instantiate(carouselGameObject).GetComponent<UICarousel>();
        }

        [Test]
        public void CheckCarouselInitialized()
        {
            UICarousel uiCarousel = CreateUICarousel();

            Assert.NotNull(uiCarousel);
            Assert.AreEqual(0, uiCarousel.CarouselValues.Count);
        }

        [Test]
        public void SetCarouselValues()
        {
            UICarousel uiCarousel = CreateUICarousel();

            List<LocExpression> locList = CreateLocExpressions(new string[] { "One", "Two", "Three" });
            uiCarousel.SetCarouselList(locList);
            Assert.AreEqual(3, uiCarousel.CarouselValues.Count);
        }

        [Test]
        public void WriteOverCarouselValues()
        {
            UICarousel uiCarousel = CreateUICarousel();

            List<LocExpression> firstLocList = CreateLocExpressions(new string[] { "One", "Two", "Three" });
            uiCarousel.SetCarouselList(firstLocList);
            Assert.AreEqual(3, uiCarousel.CarouselValues.Count);

            List<LocExpression> secondLocList = CreateLocExpressions(new string[] { "Four", "Five", "six", "Seven" });
            uiCarousel.SetCarouselList(secondLocList);
            Assert.AreEqual(4, uiCarousel.CarouselValues.Count);
        }

        /// <summary>
        /// Helper method to create single term loc expressions for the carousel lists.
        /// </summary>
        /// <param name="terms">The list of terms to create LocExpressions from.</param>
        /// <returns>A list of LocExpressions.</returns>
        private List<LocExpression> CreateLocExpressions(IEnumerable<string> terms)
        {
            return terms.Select(term =>
            {
                LocExpression newNonLocalizedTerm =
                    new SingleTermLocExpression(new NonLocalizedString(term));
                return newNonLocalizedTerm;
            }).ToList();
        }
#endif
    }
}