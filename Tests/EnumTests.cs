// Copyright Isto Inc.
using Isto.Core.Enums;
using NUnit.Framework;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Isto.Core.Tests
{
    [Category("Enums")]
    public class ExpandableEnumTests
    {
        // An enum inheriting directly from ExpandableEnum and backed by a string
        public class LatinEnum : ExpandableEnum<LatinEnum, string>
        {
            public static readonly LatinEnum PRIMUS = new LatinEnum(nameof(PRIMUS));
            public static readonly LatinEnum SECUNDUS = new LatinEnum(nameof(SECUNDUS));
            public static readonly LatinEnum TERTIUS = new LatinEnum(nameof(TERTIUS));

            internal protected LatinEnum(string value) : base(value, value)
            {
            }
        }

        // A similar enum but relies on StringEnum as a base
        public class RadioAlphabetEnum : StringEnum<RadioAlphabetEnum>
        {
            // In case we need to complete this list:
            // Alfa, Bravo, Charlie, Delta, Echo, Foxtrot, Golf, Hotel, India, Juliett, Kilo, Lima, Mike, November,
            // <PERSON>, Papa, Quebec, Romeo, Sierra, Tango, Uniform, Victor, Whiskey, X-<PERSON>, <PERSON>, <PERSON>ulu.
            public static readonly RadioAlphabetEnum ALFA = new RadioAlphabetEnum("A", nameof(ALFA));
            public static readonly RadioAlphabetEnum BRAVO = new RadioAlphabetEnum("B", nameof(BRAVO));
            public static readonly RadioAlphabetEnum CHARLIE = new RadioAlphabetEnum("C", nameof(CHARLIE));
            public static readonly RadioAlphabetEnum QUEBEC = new RadioAlphabetEnum("Q", nameof(QUEBEC));

            internal protected RadioAlphabetEnum(string value, string name) : base(value, name)
            {
            }
        }

        // An enum inheriting from ByteEnum
        public class TestByteEnum : ByteEnum<TestByteEnum>
        {
            public static readonly TestByteEnum EMPTY_VALUE = new TestByteEnum(0, nameof(EMPTY_VALUE));

            public static readonly TestByteEnum A = new TestByteEnum(nameof(A));
            public static readonly TestByteEnum B = new TestByteEnum(nameof(B));
            public static readonly TestByteEnum C = new TestByteEnum(nameof(C));

            public TestByteEnum() : base()
            {
            }

            // Auto-value constructor - optional
            internal protected TestByteEnum(string name) : base(name)
            {
            }

            // Specific value constructor - mandatory
            internal protected TestByteEnum(byte value, string name) : base(value, name)
            {
            }
        }

        // An enum inheriting from Int32Enum
        public class TestEnum : Int32Enum<TestEnum>
        {
            // Not a good value design but for testing purposes we will use this
            public static readonly TestEnum EMPTY_VALUE = new TestEnum(-1, nameof(EMPTY_VALUE));

            public static readonly TestEnum A = new TestEnum(nameof(A));
            public static readonly TestEnum B = new TestEnum(nameof(B));
            public static readonly TestEnum C = new TestEnum(nameof(C));

            public TestEnum() : base()
            {
            }

            // Auto-value constructor - optional
            internal protected TestEnum(string name) : base(name)
            {
            }

            // Specific value constructor - mandatory
            internal protected TestEnum(int value, string name) : base(value, name)
            {
            }
        }

        // An enum expanding on TestEnum
        public class ExtendedTestEnum : TestEnum
        {
            public static readonly ExtendedTestEnum D = new ExtendedTestEnum(nameof(D));
            public static readonly ExtendedTestEnum E = new ExtendedTestEnum(nameof(E));
            public static readonly ExtendedTestEnum F = new ExtendedTestEnum(nameof(F));

            internal protected ExtendedTestEnum(string name) : base(name)
            {
            }

            internal protected ExtendedTestEnum(int value, string name) : base(value, name)
            {
            }
        }

        // An enum expanding on our expansion of TestEnum
        public class SpecializedTestEnum : ExtendedTestEnum
        {
            public static readonly SpecializedTestEnum Z = new SpecializedTestEnum(99, nameof(Z));

            internal protected SpecializedTestEnum(int value, string name) : base(value, name)
            {
            }
        }

        // Another enum expanding on our expansion of TestEnum, this
        public class AlternateSpecializedTestEnum : ExtendedTestEnum
        {
            // I thought I could define some values according to other declared values, but it looks like because of my static constructor
            // logic in the ExpandableEnum static class the declarations are not resolved in the normal order.
            //public static readonly AlternateSpecializedTestEnum ALPHA = new AlternateSpecializedTestEnum(A.Value, nameof(ALPHA));
            //public static readonly AlternateSpecializedTestEnum BETA = new AlternateSpecializedTestEnum(B.Value, nameof(BETA));

            public static readonly AlternateSpecializedTestEnum GAMMA = new AlternateSpecializedTestEnum(nameof(GAMMA));

            // TODO: Figure out if we want to allow defining the same value or name more than once?
            // Right now having 2 declarations with the same value is "not allowed" - nothing is going to stop you but
            // we're doing some assumptions in the code. For instance equality and serialization both depend only on
            // checking the backing value so they would not differentiate AlternateSpecializedTestEnum.X from SpecializedTestEnum.Z
            //public static readonly AlternateSpecializedTestEnum X = new AlternateSpecializedTestEnum(99, nameof(X));
            public static readonly AlternateSpecializedTestEnum Z = new AlternateSpecializedTestEnum(999, nameof(Z));

            internal protected AlternateSpecializedTestEnum(string name) : base(name)
            {
            }

            internal protected AlternateSpecializedTestEnum(int value, string name) : base(value, name)
            {
            }
        }

        // This does no explicit validation atm but it allows me to easily inspect the values in the logs
        [Test]
        public void LogAllEnumTypesTest()
        {
            ExpandableEnum.LogAllTypes();
        }

        // This does no explicit validation atm but it allows me to easily inspect the values in the logs
        [Test]
        public void LogAllTestEnumValuesTest()
        {
            TestEnum.LogAllValues();
            ExpandableEnum.LogValuesAndDependencies(typeof(TestEnum));
            ExpandableEnum.LogValuesAndDependencies(typeof(ExtendedTestEnum));
            ExpandableEnum.LogStrictValues(typeof(ExtendedTestEnum));
            ExpandableEnum.LogValuesAndDependencies(typeof(SpecializedTestEnum));
            ExpandableEnum.LogValuesAndDependencies(typeof(AlternateSpecializedTestEnum));
        }

        // This does no explicit validation atm but it allows me to easily inspect the values in the logs
        [Test]
        public void LogAllRatingValuesTest()
        {
            RatingEnum.LogAllValues();
            ExpandableEnum.LogValuesAndDependencies(typeof(RatingEnum));
            ExpandableEnum.LogValuesAndDependencies(typeof(ExtendedRatingEnum));
            ExpandableEnum.LogStrictValues(typeof(ExtendedRatingEnum));
        }

        [Test]
        public void DefinitionTest()
        {
            // Verify bookkeeping is happening - this test might be overkill as it is implementation detail.
            // These would not be public if possible. I guess as long as they're public though, we should validate them.
            Debug.Assert(ExpandableEnum.ALL_TYPES.Contains(typeof(TestEnum)),
                $"ExpandableEnum.ALL_TYPES expected to contain {nameof(TestEnum)}");

            Debug.Assert(ExpandableEnum.ALL_VALUES.ContainsKey(typeof(TestEnum)),
                $"ExpandableEnum.ALL_VALUES expected to contain key {nameof(TestEnum)}");

            Debug.Assert(ExpandableEnum.ALL_VALUES[typeof(TestEnum)].Contains(TestEnum.A),
                $"ExpandableEnum.ALL_VALUES expected to contain value {nameof(TestEnum.A)} for key {nameof(TestEnum)}");

            // This is the normal definition validation method.
            Debug.Assert(ExpandableEnum.IsDefined(typeof(TestEnum), TestEnum.A),
                $"ExpandableEnum.IsDefined expected to return true for {nameof(TestEnum.A)} in {nameof(TestEnum)}");

            Debug.Assert(ExpandableEnum.IsDefined(TestEnum.A),
                $"ExpandableEnum.IsDefined expected to return true for {nameof(TestEnum.A)}");

            // This covers what happens when you have to build an enum from existing variables.
            TestEnum runtimeDeclaration = new TestEnum();
            runtimeDeclaration.SetValue(-1, nameof(TestEnum.EMPTY_VALUE)); // should be equivalent to the definition
            Debug.Assert(ExpandableEnum.IsDefined(typeof(TestEnum), runtimeDeclaration),
                $"ExpandableEnum.IsDefined expected to return true for runtime declaration {runtimeDeclaration} with type {nameof(TestEnum)}");

            Debug.Assert(ExpandableEnum.IsDefined(runtimeDeclaration),
                $"ExpandableEnum.IsDefined expected to return true for runtime declaration {runtimeDeclaration}");

            HashSet<int> values = new HashSet<int>();
            int hashSetCount = 0;

            Debug.Log($"TestEnum GetValues() enumeration:");
            // Some values are defined in TestEnum and some additional values are defined in ExtendedTestEnum and other subtypes
            // When you iterate on them, they are expected to all show up
            // Most of them have auto-defined values, which have no guarantees about what they are, but should be different
            foreach (TestEnum testEnumValue in TestEnum.GetValues())
            {
                string log = string.Format("Key:{0} Value:{1}", testEnumValue.Value, testEnumValue.Name);
                Debug.Log(log);

                Debug.Assert(!values.Contains(testEnumValue.Value), $"Found more than one TestEnum definition with the value {testEnumValue.Value}");

                if (testEnumValue == TestEnum.A)
                {
                    values.Add(testEnumValue.Value);
                    hashSetCount++;
                    Debug.Assert(testEnumValue.Name == "A", "Enum Rating was expected to define item A with name A");
                }
                else if (testEnumValue == TestEnum.B)
                {
                    values.Add(testEnumValue.Value);
                    hashSetCount++;
                    Debug.Assert(testEnumValue.Name == "B", "Enum Rating was expected to define item B with name B");
                }
                else if (testEnumValue == TestEnum.C)
                {
                    values.Add(testEnumValue.Value);
                    hashSetCount++;
                    Debug.Assert(testEnumValue.Name == "C", "Enum Rating was expected to define item C with name C");
                }
                else if (testEnumValue == ExtendedTestEnum.D)
                {
                    values.Add(testEnumValue.Value);
                    hashSetCount++;
                    Debug.Assert(testEnumValue.Name == "D", "Enum Rating was expected to define item D with name D");
                }
                else if (testEnumValue == ExtendedTestEnum.E)
                {
                    values.Add(testEnumValue.Value);
                    hashSetCount++;
                    Debug.Assert(testEnumValue.Name == "E", "Enum Rating was expected to define item E with name E");
                }
                else if (testEnumValue == ExtendedTestEnum.F)
                {
                    values.Add(testEnumValue.Value);
                    hashSetCount++;
                    Debug.Assert(testEnumValue.Name == "F", "Enum Rating was expected to define item F with name F");
                }
                else if (testEnumValue == SpecializedTestEnum.Z)
                {
                    Debug.Assert(testEnumValue.Value == 99, "SpecializedTestEnum was expected to define item Z with value 99");
                    Debug.Assert(testEnumValue.Name == "Z", "SpecializedTestEnum was expected to define item Z with value 99");
                }
                else if (testEnumValue == AlternateSpecializedTestEnum.GAMMA)
                {
                    values.Add(testEnumValue.Value);
                    hashSetCount++;
                    Debug.Assert(testEnumValue.Name == "GAMMA", "AlternateSpecializedTestEnum was expected to define item GAMMA with name GAMMA");
                }
                else if (testEnumValue == AlternateSpecializedTestEnum.Z)
                {
                    Debug.Assert(testEnumValue.Value == 999, "AlternateSpecializedTestEnum was expected to define item Z with value 999");
                    Debug.Assert(testEnumValue.Name == "Z", "AlternateSpecializedTestEnum was expected to define item Z with value 999");
                }
            }

            Debug.Assert(hashSetCount == values.Count, "Some autoincremented TestEnum items have the same value but they should all be different");

            Debug.Log($"strictly TestEnum GetValues() enumeration:");
            foreach (TestEnum rating in TestEnum.GetStrictValues(typeof(TestEnum)))
            {
                string log = string.Format("Key:{0} Value:{1}", rating.Value, rating.Name);
                Debug.Log(log);
                bool legalValue = rating.Name == "EMPTY_VALUE" || rating.Name == "A" || rating.Name == "B" || rating.Name == "C";
                Debug.Assert(legalValue, "TestEnum definitions are expected to be A, B or C, but we got something else");
            }
        }

        [Test]
        public void IndependentDefinitionTest()
        {
            // RatingEnum is declared in RatingEnum.cs to validate that enums don't just work using internal
            // declarations like we do for TestEnum. This is important since we do a lot of auto-registration magic.

            Debug.Log($"RatingEnum GetValues() enumeration:");
            // Some values are defined in RatingEnum and some additional values are defined in ExtendedRatingEnum
            // When you iterate on them, they are expected to all show up
            // Most of them have auto-defined values, which should start at 1 and auto-increment, but order can be random.
            foreach (RatingEnum rating in RatingEnum.GetValues())
            {
                string log = string.Format("Key:{0} Value:{1}", rating.Value, rating.Name);
                Debug.Log(log);

                if (rating == RatingEnum.AWFUL)
                {
                    Debug.Assert(rating.Name == "AWFUL", "Enum Rating was expected to define AWFUL");
                }
                else if (rating == RatingEnum.BAD)
                {
                    Debug.Assert(rating.Name == "BAD", "Enum Rating was expected to define BAD");
                }
                else if (rating == RatingEnum.MEDIUM)
                {
                    Debug.Assert(rating.Name == "MEDIUM", "Enum Rating was expected to define MEDIUM");
                }
                else if (rating == RatingEnum.GOOD)
                {
                    Debug.Assert(rating.Name == "GOOD", "Enum Rating was expected to define GOOD");
                }
                else if (rating == RatingEnum.AWESOME)
                {
                    Debug.Assert(rating.Name == "AWESOME", "Enum Rating was expected to define AWESOME");
                }
                else if (rating == RatingEnum.UNCATEGORIZED)
                {
                    Debug.Assert(rating.Value == 99, "Enum Rating was expected to define UNCATEGORIZED with value 99");
                    Debug.Assert(rating.Name == "UNCATEGORIZED", "Enum Rating was expected to define UNCATEGORIZED with value 99");
                }
                else if (rating == ExtendedRatingEnum.FAIL_RANK)
                {
                    Debug.Assert(rating.Name == "FAIL_RANK", "Enum Rating was expected to define FAIL_RANK");
                }
                else if (rating == ExtendedRatingEnum.SEXY_RANK)
                {
                    Debug.Assert(rating.Name == "SEXY_RANK", "Enum Rating was expected to define SEXY_RANK");
                }
                else if (rating == ExtendedRatingEnum.SPECIAL_RANK)
                {
                    Debug.Assert(rating.Value == 15, "Enum Rating was expected to define SPECIAL_RANK with value 15");
                    Debug.Assert(rating.Name == "SPECIAL_RANK", "Enum Rating was expected to define SPECIAL_RANK with value 15");
                }
            }
        }

        [Test]
        public void CastingTest()
        {
            string toString1 = TestEnum.A.ToString();
            Debug.Assert(toString1 == "A", $"ToString for {nameof(TestEnum.A)} expected to be A but was \"{toString1}\"");

            string toString2 = TestEnum.EMPTY_VALUE.ToString();
            Debug.Assert(toString2 == "EMPTY_VALUE", $"ToString for {nameof(TestEnum.EMPTY_VALUE)} expected to be EMPTY_VALUE but was \"{toString2}\"");

            Debug.Log("Getting RatingEnum with value = 99 (defined in RatingEnum)");
            int ratingValue = 99;
            RatingEnum rating = RatingEnum.GetByValue(ratingValue);
            Debug.Assert(rating != null, "RatingEnum with value 99 was not found in RatingEnum declarations");
            Debug.Assert(rating.Value == 99, "RatingEnum with key 99 was expected to have value 99");
            Debug.Assert(rating.Name == "UNCATEGORIZED", "RatingEnum with key 99 was expected to be named UNCATEGORIZED");
            Debug.Assert(rating == RatingEnum.UNCATEGORIZED, "RatingEnum with key 99 was expected to match symbol for UNCATEGORIZED");

            Debug.Log("Getting RatingEnum with name = SPECIAL_RANK (defined in ExtendedRatingEnum)");
            string ratingName = "SPECIAL_RANK";
            rating = RatingEnum.GetByName(ratingName);
            Debug.Assert(rating != null, "RatingEnum with name SPECIAL_RANK was not found in RatingEnum declarations");
            Debug.Assert(rating.Value == 15, "RatingEnum 'SPECIAL_RANK' was expected to have value 15");
            Debug.Assert(rating.Name == "SPECIAL_RANK", "RatingEnum 'SPECIAL_RANK' was expected to have name SPECIAL_RANK");
            Debug.Assert(rating == ExtendedRatingEnum.SPECIAL_RANK, "RatingEnum 'SPECIAL_RANK' was expected to match symbol for SPECIAL_RANK");
        }

        [Test]
        public void StringBasedCastingTest()
        {
            Debug.Log("Getting LatinEnum with value = PRIMUS (defined in LatinEnum)");
            string latinEnumValue = "PRIMUS";
            LatinEnum latinInstance = LatinEnum.GetFromValue(latinEnumValue);
            Debug.Assert(latinInstance != null, "LatinEnum with value PRIMUS was not found in LatinEnum declarations");
            Debug.Assert(latinInstance.Value == "PRIMUS", "LatinEnum found from value PRIMUS was expected to have value PRIMUS");
            Debug.Assert(latinInstance.Name == "PRIMUS", "LatinEnum with value PRIMUS was expected to be named PRIMUS");
            Debug.Assert(latinInstance == LatinEnum.PRIMUS, "LatinEnum with key PRIMUS was expected to equal LatinEnum declaration for PRIMUS");

            Debug.Log("Getting RadioAlphabetEnum with value = Q and name = QUEBEC (defined in RadioAlphabetEnum)");
            string radioEnumValue = "Q";
            RadioAlphabetEnum radioInstance = RadioAlphabetEnum.GetByValue(radioEnumValue);
            Debug.Assert(radioInstance != null, "RadioAlphabetEnum with value Q was not found in RadioAlphabetEnum declarations");
            Debug.Assert(radioInstance.Value == "Q", "RadioAlphabetEnum found from value Q was expected to have value Q");
            Debug.Assert(radioInstance.Name == "QUEBEC", "RadioAlphabetEnum with value Q was expected to be named QUEBEC");
            Debug.Assert(radioInstance == RadioAlphabetEnum.QUEBEC, "RadioAlphabetEnum with value Q was expected to equal RadioAlphabetEnum declaration for QUEBEC");
        }

        [Test]
        public void InternalMethodsTest()
        {
            object simulatedUnknown = TestEnum.EMPTY_VALUE;
            
            // oops, this GetName is private, and probably would be confusing to expose
            // should I test this using reflection?
            //Debug.Assert(ExpandableEnum.GetName(simulatedUnknown) == nameof(TestEnum.EMPTY_VALUE), $"Error!");

            // I guess in fact this one should also be private, can I accomplish that? the drawer needs it but it could just use reflection itself.
            Debug.Assert(ExpandableEnum.GetBackingValue(simulatedUnknown).Equals(-1),
                $"backing value -1 was expected to be found in enum declaration {nameof(TestEnum.EMPTY_VALUE)} through ExpandableEnum.GetBackingValue");
        }

        [Test]
        public void DefaultValueTest()
        {
            TestEnum defaultValueTestBase = ExpandableEnum.GetDefaultValue(typeof(TestEnum)) as TestEnum;
            Debug.Assert(defaultValueTestBase == TestEnum.EMPTY_VALUE,
                $"ExpandableEnum.GetDefaultValue for {nameof(TestEnum)} expected to be \"{nameof(TestEnum.EMPTY_VALUE)}\" but was \"{defaultValueTestBase}\"");

            ExtendedTestEnum defaultValueTestDerived = ExpandableEnum.GetDefaultValue(typeof(ExtendedTestEnum)) as ExtendedTestEnum;
            Debug.Assert(defaultValueTestDerived == ExtendedTestEnum.D,
                $"ExpandableEnum.GetDefaultValue for {nameof(ExtendedTestEnum)} expected to be \"{nameof(ExtendedTestEnum.D)}\" but was \"{defaultValueTestDerived}\"");
        }

        [Test]
        public void GetValuesTest()
        {
            TestEnum[] testStrictValues = new TestEnum[] { TestEnum.EMPTY_VALUE, TestEnum.A, TestEnum.B, TestEnum.C };
            TestEnum[] extendedTestStrictValues = new TestEnum[] { ExtendedTestEnum.D, ExtendedTestEnum.E, ExtendedTestEnum.F };
            TestEnum[] extendedTestValuesAndDependencies = new TestEnum[] { TestEnum.EMPTY_VALUE, TestEnum.A, TestEnum.B, TestEnum.C,
                                                                        ExtendedTestEnum.D, ExtendedTestEnum.E, ExtendedTestEnum.F };
            TestEnum[] testAllCompatibleValues = new TestEnum[] { TestEnum.EMPTY_VALUE, TestEnum.A, TestEnum.B, TestEnum.C,
                                                                  ExtendedTestEnum.D, ExtendedTestEnum.E, ExtendedTestEnum.F,
                                                                  SpecializedTestEnum.Z,
                                                                  AlternateSpecializedTestEnum.GAMMA, AlternateSpecializedTestEnum.Z,};

            var strictValuesTest = ExpandableEnum.GetStrictValues(typeof(ExtendedTestEnum));
            Debug.Assert(strictValuesTest.ContainsExactly(extendedTestStrictValues),
                $"ExpandableEnum.GetStrictValues for {nameof(ExtendedTestEnum)} expected to be {extendedTestStrictValues.ToFullString()} but was {strictValuesTest.ToFullString()}");

            var valuesAndDependenciesTest = ExpandableEnum.GetValuesAndDependencies(typeof(ExtendedTestEnum));
            Debug.Assert(valuesAndDependenciesTest.ContainsExactly(extendedTestValuesAndDependencies),
                $"ExpandableEnum.GetValuesAndDependencies for {nameof(ExtendedTestEnum)} expected to be {extendedTestValuesAndDependencies.ToFullString()} but was {valuesAndDependenciesTest.ToFullString()}");

            var allValuesTest = ExpandableEnum.GetAllCompatibleValues(typeof(TestEnum));
            Debug.Assert(allValuesTest.ContainsExactly(testAllCompatibleValues),
                $"ExpandableEnum.GetAllCompatibleValues for {nameof(TestEnum)} expected to be {testAllCompatibleValues.ToFullString()} but was {allValuesTest.ToFullString()}");

            var childAllValuesTest = ExpandableEnum.GetAllCompatibleValues(typeof(ExtendedTestEnum));
            Debug.Assert(childAllValuesTest.ContainsExactly(testAllCompatibleValues),
                $"ExpandableEnum.GetAllCompatibleValues for {nameof(ExtendedTestEnum)} expected to be {testAllCompatibleValues.ToFullString()} but was {childAllValuesTest.ToFullString()}");

            var genericStrictValues = TestEnum.GetStrictValues(typeof(TestEnum));
            Debug.Assert(genericStrictValues.ContainsExactly(testStrictValues),
                $"TestEnum.GetStrictValues for {nameof(TestEnum)} expected to be {testStrictValues.ToFullString()} but was {genericStrictValues.ToFullString()}");

            var genericValues = TestEnum.GetValues();
            Debug.Assert(genericValues.ContainsExactly(testAllCompatibleValues),
                $"TestEnum.GetValues() expected to be {testAllCompatibleValues.ToFullString()} but was {genericValues.ToFullString()}");

            var genericAllValues = TestEnum.GetAllValues();
            Debug.Assert(genericAllValues.ContainsExactly(testAllCompatibleValues),
                $"TestEnum.GetAllValues() expected to be {testAllCompatibleValues.ToFullString()} but was {genericAllValues.ToFullString()}");

            var genericStrictDerivedValues = ExtendedTestEnum.GetStrictValues(typeof(ExtendedTestEnum));
            Debug.Assert(genericStrictDerivedValues.ContainsExactly(extendedTestStrictValues),
                $"ExtendedTestEnum.GetStrictValues for {nameof(ExtendedTestEnum)} expected to be {extendedTestStrictValues.ToFullString()} but was {genericStrictDerivedValues.ToFullString()}");

            var genericDerivedValues = ExtendedTestEnum.GetValues();
            Debug.Assert(genericDerivedValues.ContainsExactly(testAllCompatibleValues),
                $"ExtendedTestEnum.GetValues() expected to be {testAllCompatibleValues.ToFullString()} but was {genericDerivedValues.ToFullString()}");

            var genericAllDerivedValues = ExtendedTestEnum.GetAllValues();
            Debug.Assert(genericAllDerivedValues.ContainsExactly(testAllCompatibleValues),
                $"ExtendedTestEnum.GetAllValues() expected to be {testAllCompatibleValues.ToFullString()} but was {genericAllDerivedValues.ToFullString()}");
        }

        [Test]
        public void GetFromBackingValueTest()
        {
            // Non-Generic Get

            object valueFromSelf = ExpandableEnum.GetFromValueAndType(-1, typeof(TestEnum));
            Debug.Assert(TestEnum.EMPTY_VALUE.Equals(valueFromSelf),
                $"ExpandableEnum.GetFromValueAndType for -1 and {nameof(TestEnum)} expected to be \"{nameof(TestEnum.EMPTY_VALUE)}\" but was \"{valueFromSelf}\"");

            object valueFromParent = ExpandableEnum.GetFromValueAndType(-1, typeof(ExtendedTestEnum));
            Debug.Assert(TestEnum.EMPTY_VALUE.Equals(valueFromParent),
                $"ExpandableEnum.GetFromValueAndType for -1 and {nameof(ExtendedTestEnum)} expected to be \"{nameof(TestEnum.EMPTY_VALUE)}\" but was \"{valueFromParent}\"");
            
            object valueFromGrandParent = ExpandableEnum.GetFromValueAndType(-1, typeof(SpecializedTestEnum));
            Debug.Assert(TestEnum.EMPTY_VALUE.Equals(valueFromGrandParent),
                $"ExpandableEnum.GetFromValueAndType for -1 and {nameof(ExtendedTestEnum)} expected to be \"{nameof(TestEnum.EMPTY_VALUE)}\" but was \"{valueFromGrandParent}\"");

            object strictValueFromSelf = ExpandableEnum.GetFromValueAndStrictType(-1, typeof(TestEnum));
            Debug.Assert(TestEnum.EMPTY_VALUE.Equals(strictValueFromSelf),
                $"ExpandableEnum.GetFromValueAndStrictType for -1 and {nameof(TestEnum)} expected to be \"{nameof(TestEnum.EMPTY_VALUE)}\" but was \"{strictValueFromSelf}\"");
            
            object strictValueFromParent = ExpandableEnum.GetFromValueAndStrictType(-1, typeof(ExtendedTestEnum));
            Debug.Assert(strictValueFromParent == null,
                $"ExpandableEnum.GetFromValueAndStrictType for -1 and {nameof(ExtendedTestEnum)} expected to be \"null\" but was \"{strictValueFromParent}\"");
            
            object strictValueFromGrandParent = ExpandableEnum.GetFromValueAndStrictType(-1, typeof(SpecializedTestEnum));
            Debug.Assert(strictValueFromGrandParent == null,
                $"ExpandableEnum.GetFromValueAndStrictType for -1 and {nameof(SpecializedTestEnum)} expected to be \"null\" but was \"{strictValueFromParent}\"");

            object compatibleValueFromSelf = ExpandableEnum.GetFromValueAndCompatibleType(-1, typeof(TestEnum));
            Debug.Assert(TestEnum.EMPTY_VALUE.Equals(compatibleValueFromSelf),
                $"ExpandableEnum.GetFromValueAndCompatibleType for -1 and {nameof(TestEnum)} expected to be \"{nameof(TestEnum.EMPTY_VALUE)}\" but was \"{compatibleValueFromSelf}\"");
            
            object compatibleValueFromParent = ExpandableEnum.GetFromValueAndCompatibleType(-1, typeof(ExtendedTestEnum));
            Debug.Assert(TestEnum.EMPTY_VALUE.Equals(compatibleValueFromParent),
                $"ExpandableEnum.GetFromValueAndCompatibleType for -1 and {nameof(ExtendedTestEnum)} expected to be \"{nameof(TestEnum.EMPTY_VALUE)}\" but was \"{compatibleValueFromParent}\"");

            object compatibleValueFromGrandParent = ExpandableEnum.GetFromValueAndCompatibleType(-1, typeof(SpecializedTestEnum));
            Debug.Assert(TestEnum.EMPTY_VALUE.Equals(compatibleValueFromGrandParent),
                $"ExpandableEnum.GetFromValueAndCompatibleType for -1 and {nameof(SpecializedTestEnum)} expected to be \"{nameof(TestEnum.EMPTY_VALUE)}\" but was \"{compatibleValueFromGrandParent}\"");

            object compatibleValueFromGrandParentAlt = ExpandableEnum.GetFromValueAndCompatibleType(-1, typeof(AlternateSpecializedTestEnum));
            Debug.Assert(TestEnum.EMPTY_VALUE.Equals(compatibleValueFromGrandParentAlt),
                $"ExpandableEnum.GetFromValueAndCompatibleType for -1 and {nameof(AlternateSpecializedTestEnum)} expected to be \"{nameof(TestEnum.EMPTY_VALUE)}\" but was \"{compatibleValueFromGrandParentAlt}\"");
            
            object compatibleValueFromGrandChild = ExpandableEnum.GetFromValueAndCompatibleType(99, typeof(TestEnum));
            Debug.Assert(SpecializedTestEnum.Z.Equals(compatibleValueFromGrandChild),
                $"ExpandableEnum.GetFromValueAndCompatibleType for 99 and {nameof(TestEnum)} expected to be \"{nameof(SpecializedTestEnum.Z)}\" but was \"{compatibleValueFromGrandChild}\"");
            
            object compatibleValueFromGrandChildAlt = ExpandableEnum.GetFromValueAndCompatibleType(999, typeof(TestEnum));
            Debug.Assert(AlternateSpecializedTestEnum.Z.Equals(compatibleValueFromGrandChildAlt),
                $"ExpandableEnum.GetFromValueAndCompatibleType for 999 and {nameof(TestEnum)} expected to be \"{nameof(AlternateSpecializedTestEnum.Z)}\" but was \"{compatibleValueFromGrandChildAlt}\"");

            // Generic Get

            Int32Enum<TestEnum> genericValueFromSelf = TestEnum.GetFromValue(-1);
            Debug.Assert(TestEnum.EMPTY_VALUE.Equals(genericValueFromSelf),
                $"TestEnum.GetFromValue for -1 expected to be \"{nameof(TestEnum.EMPTY_VALUE)}\" but was \"{genericValueFromSelf}\"");

            Int32Enum<TestEnum> genericValueFromParent = ExtendedTestEnum.GetFromValue(-1);
            Debug.Assert(TestEnum.EMPTY_VALUE.Equals(genericValueFromParent),
                $"ExtendedTestEnum.GetFromValue for -1 expected to be \"{nameof(TestEnum.EMPTY_VALUE)}\" but was \"{genericValueFromParent}\"");

            Int32Enum<TestEnum> genericValueFromGrandParent = SpecializedTestEnum.GetFromValue(-1);
            Debug.Assert(TestEnum.EMPTY_VALUE.Equals(genericValueFromGrandParent),
                $"SpecializedTestEnum.GetFromValue for -1 expected to be \"{nameof(TestEnum.EMPTY_VALUE)}\" but was \"{genericValueFromGrandParent}\"");

            Int32Enum<TestEnum> genericValueFromGrandChild = TestEnum.GetFromValue(99);
            Debug.Assert(SpecializedTestEnum.Z.Equals(genericValueFromGrandChild),
                $"TestEnum.GetFromValue for 99 expected to be \"{nameof(SpecializedTestEnum.Z)}\" but was \"{genericValueFromGrandChild}\"");

            Int32Enum<TestEnum> genericValueFromChild = ExtendedTestEnum.GetFromValue(99);
            Debug.Assert(SpecializedTestEnum.Z.Equals(genericValueFromChild),
                $"ExtendedTestEnum.GetFromValue for 99 expected to be \"{nameof(SpecializedTestEnum.Z)}\" but was \"{genericValueFromChild}\"");

            Int32Enum<TestEnum> genericValueFromSelf2 = SpecializedTestEnum.GetFromValue(99);
            Debug.Assert(SpecializedTestEnum.Z.Equals(genericValueFromSelf2),
                $"SpecializedTestEnum.GetFromValue for 99 expected to be \"{nameof(SpecializedTestEnum.Z)}\" but was \"{genericValueFromSelf2}\"");
        }

        [Test]
        public void GetFromNameTest()
        {
            //GetFromNameAndType
            object valueFromNameSelf = ExpandableEnum.GetFromNameAndType("A", typeof(TestEnum));
            Debug.Assert(TestEnum.A.Equals(valueFromNameSelf),
                $"ExpandableEnum.GetFromNameAndType for A and {nameof(TestEnum)} expected to be \"{nameof(TestEnum.A)}\" but was \"{valueFromNameSelf}\"");

            object valueFromNameParent = ExpandableEnum.GetFromNameAndType("A", typeof(ExtendedTestEnum));
            Debug.Assert(TestEnum.A.Equals(valueFromNameParent),
                $"ExpandableEnum.GetFromNameAndType for A and {nameof(ExtendedTestEnum)} expected to be \"{nameof(TestEnum.A)}\" but was \"{valueFromNameParent}\"");
            
            object valueFromNameGrandParent = ExpandableEnum.GetFromNameAndType("A", typeof(SpecializedTestEnum));
            Debug.Assert(TestEnum.A.Equals(valueFromNameGrandParent),
                $"ExpandableEnum.GetFromNameAndType for A and {nameof(SpecializedTestEnum)} expected to be \"{nameof(TestEnum.A)}\" but was \"{valueFromNameGrandParent}\"");

            //GetFromNameAndStrictType
            object strictValueFromNameSelf = ExpandableEnum.GetFromNameAndStrictType("A", typeof(TestEnum));
            Debug.Assert(TestEnum.A.Equals(strictValueFromNameSelf),
                $"ExpandableEnum.GetFromNameAndStrictType for A and {nameof(TestEnum)} expected to be \"{nameof(TestEnum.A)}\" but was \"{strictValueFromNameSelf}\"");
            
            object strictValueFromNameParent = ExpandableEnum.GetFromNameAndStrictType("A", typeof(ExtendedTestEnum));
            Debug.Assert(strictValueFromNameParent == null,
                $"ExpandableEnum.GetFromNameAndStrictType for A and {nameof(ExtendedTestEnum)} expected to be \"null\" but was \"{strictValueFromNameParent}\"");
            
            object strictValueFromNameGrandParent = ExpandableEnum.GetFromNameAndStrictType("A", typeof(SpecializedTestEnum));
            Debug.Assert(strictValueFromNameGrandParent == null,
                $"ExpandableEnum.GetFromNameAndStrictType for A and {nameof(SpecializedTestEnum)} expected to be \"null\" but was \"{strictValueFromNameGrandParent}\"");

            //GetFromNameAndCompatibleType
            object compatibleValueFromNameSelf = ExpandableEnum.GetFromNameAndCompatibleType("A", typeof(TestEnum));
            Debug.Assert(TestEnum.A.Equals(compatibleValueFromNameSelf),
                $"ExpandableEnum.GetFromNameAndCompatibleType for A and {nameof(TestEnum)} expected to be \"{nameof(TestEnum.A)}\" but was \"{compatibleValueFromNameSelf}\"");

            object compatibleValueFromNameParent = ExpandableEnum.GetFromNameAndCompatibleType("A", typeof(ExtendedTestEnum));
            Debug.Assert(TestEnum.A.Equals(compatibleValueFromNameParent),
                $"ExpandableEnum.GetFromNameAndCompatibleType for A and {nameof(ExtendedTestEnum)} expected to be \"{nameof(TestEnum.A)}\" but was \"{compatibleValueFromNameParent}\"");

            object compatibleValueFromNameGrandParent = ExpandableEnum.GetFromNameAndCompatibleType("A", typeof(SpecializedTestEnum));
            Debug.Assert(TestEnum.A.Equals(compatibleValueFromNameGrandParent),
                $"ExpandableEnum.GetFromNameAndCompatibleType for A and {nameof(SpecializedTestEnum)} expected to be \"{nameof(TestEnum.A)}\" but was \"{compatibleValueFromNameGrandParent}\"");

            object compatibleValueFromNameGrandParentAlt = ExpandableEnum.GetFromNameAndCompatibleType("A", typeof(AlternateSpecializedTestEnum));
            Debug.Assert(TestEnum.A.Equals(compatibleValueFromNameGrandParentAlt),
                $"ExpandableEnum.GetFromNameAndCompatibleType for A and {nameof(AlternateSpecializedTestEnum)} expected to be \"{nameof(TestEnum.A)}\" but was \"{compatibleValueFromNameGrandParentAlt}\"");

            // SpecializedTestEnum or AlternateSpecializedTestEnum's Z, undefined behavior? 99 is first in the file so that should be it
            // TODO: improve this case? is the solution just to use a stricter type or should I return a list of definitions?
            object compatibleValueFromNameGrandChild = ExpandableEnum.GetFromNameAndCompatibleType("Z", typeof(TestEnum));
            Debug.Assert(SpecializedTestEnum.Z.Equals(compatibleValueFromNameGrandChild),
                $"ExpandableEnum.GetFromNameAndCompatibleType for Z and {nameof(TestEnum)} expected to be \"{nameof(SpecializedTestEnum.Z)}\" but was \"{compatibleValueFromNameGrandChild}\"");

            object compatibleValueFromNameGrandChildAlt = ExpandableEnum.GetFromNameAndCompatibleType("GAMMA", typeof(TestEnum));
            Debug.Assert(AlternateSpecializedTestEnum.GAMMA.Equals(compatibleValueFromNameGrandChildAlt),
                $"ExpandableEnum.GetFromNameAndCompatibleType for GAMMA and {nameof(TestEnum)} expected to be \"{nameof(AlternateSpecializedTestEnum.GAMMA)}\" but was \"{compatibleValueFromNameGrandChildAlt}\"");

            //GetFromName
            object nameFromNameTypelessA = ExpandableEnum.GetFromName("A"); // A is defined as a TestEnum and a RadioAlphabetEnum
            Debug.Assert(TestEnum.A.Equals(nameFromNameTypelessA),
                $"ExpandableEnum.GetFromName for A expected to be \"{nameof(TestEnum.A)}\" but was \"{nameFromNameTypelessA}\"");
            
            object nameFromNameTypelessD = ExpandableEnum.GetFromName("D");
            Debug.Assert(ExtendedTestEnum.D.Equals(nameFromNameTypelessD),
                $"ExpandableEnum.GetFromName for D expected to be \"{nameof(ExtendedTestEnum.D)}\" but was \"{nameFromNameTypelessD}\"");
            
            object nameFromNameTypelessZ = ExpandableEnum.GetFromName("Z"); // Z is defined as a SpecializedTestEnum and a AlternateSpecializedTestEnum
            Debug.Assert(SpecializedTestEnum.Z.Equals(nameFromNameTypelessZ),
                $"ExpandableEnum.GetFromName for Z expected to be \"{nameof(SpecializedTestEnum.Z)}\" but was \"{nameFromNameTypelessZ}\"");
            
            object nameFromNameTypelessGAMMA = ExpandableEnum.GetFromName("GAMMA");
            Debug.Assert(AlternateSpecializedTestEnum.GAMMA.Equals(nameFromNameTypelessGAMMA),
                $"ExpandableEnum.GetFromName for GAMMA expected to be \"{nameof(AlternateSpecializedTestEnum.GAMMA)}\" but was \"{nameFromNameTypelessGAMMA}\"");
            
            object nameFromNameTypelessPRIMUS = ExpandableEnum.GetFromName("PRIMUS");
            Debug.Assert(LatinEnum.PRIMUS.Equals(nameFromNameTypelessPRIMUS),
                $"ExpandableEnum.GetFromName for PRIMUS expected to be \"{nameof(LatinEnum.PRIMUS)}\" but was \"{nameFromNameTypelessPRIMUS}\"");

            object nameFromNameTypelessQUEBEC = ExpandableEnum.GetFromName("QUEBEC");
            Debug.Assert(RadioAlphabetEnum.QUEBEC.Equals(nameFromNameTypelessQUEBEC),
                $"ExpandableEnum.GetFromName for QUEBEC expected to be \"{nameof(RadioAlphabetEnum.QUEBEC)}\" but was \"{nameFromNameTypelessQUEBEC}\"");

            //GetFromName<T>
            Int32Enum<TestEnum> genericValueFromNameSelf = TestEnum.GetFromName("A");
            Debug.Assert(TestEnum.A.Equals(genericValueFromNameSelf),
                $"TestEnum.GetFromName for A expected to be \"{nameof(TestEnum.A)}\" but was \"{genericValueFromNameSelf}\"");

            Int32Enum<TestEnum> genericValueFromNameParent = ExtendedTestEnum.GetFromName("A");
            Debug.Assert(TestEnum.A.Equals(genericValueFromNameParent),
                $"ExtendedTestEnum.GetFromName for A expected to be \"{nameof(TestEnum.A)}\" but was \"{genericValueFromNameParent}\"");

            Int32Enum<TestEnum> genericValueFromNameGrandParent = AlternateSpecializedTestEnum.GetFromName("A");
            Debug.Assert(TestEnum.A.Equals(genericValueFromNameGrandParent),
                $"AlternateSpecializedTestEnum.GetFromName for A expected to be \"{nameof(TestEnum.A)}\" but was \"{genericValueFromNameGrandParent}\"");

            Int32Enum<TestEnum> genericValueFromNameGrandChild = TestEnum.GetFromName("GAMMA");
            Debug.Assert(AlternateSpecializedTestEnum.GAMMA.Equals(genericValueFromNameGrandChild),
                $"TestEnum.GetFromName for A expected to be \"{nameof(AlternateSpecializedTestEnum.GAMMA)}\" but was \"{genericValueFromNameGrandChild}\"");

            Int32Enum<TestEnum> genericValueFromNameChild = ExtendedTestEnum.GetFromName("GAMMA");
            Debug.Assert(AlternateSpecializedTestEnum.GAMMA.Equals(genericValueFromNameChild),
                $"ExtendedTestEnum.GetFromName for A expected to be \"{nameof(AlternateSpecializedTestEnum.GAMMA)}\" but was \"{genericValueFromNameChild}\"");

            Int32Enum<TestEnum> genericValueFromNameSelf2 = AlternateSpecializedTestEnum.GetFromName("GAMMA");
            Debug.Assert(AlternateSpecializedTestEnum.GAMMA.Equals(genericValueFromNameSelf2),
                $"AlternateSpecializedTestEnum.GetFromName for A expected to be \"{nameof(AlternateSpecializedTestEnum.GAMMA)}\" but was \"{genericValueFromNameSelf2}\"");
        }

        [Test]
        public void ParseTest()
        {
            Int32Enum<TestEnum> genericValueFromParseSelf;
            bool genericValueFromParseSelfSuccess = TestEnum.TryParse("A", ignoreCase: false, out genericValueFromParseSelf);
            Debug.Assert(genericValueFromParseSelfSuccess && (genericValueFromParseSelf as TestEnum).Equals(TestEnum.A),
                $"TestEnum.TryParse for A and no ignoreCase expected to be \"{nameof(TestEnum.A)}\" but was \"{genericValueFromParseSelf}\"");

            Int32Enum<TestEnum> genericValueFromParseSelfIgnoreCase;
            bool genericValueFromParseSelfIgnoreCaseSuccess = TestEnum.TryParse("a", ignoreCase: true, out genericValueFromParseSelfIgnoreCase);
            Debug.Assert(genericValueFromParseSelfIgnoreCaseSuccess && (genericValueFromParseSelfIgnoreCase as TestEnum).Equals(TestEnum.A),
                $"TestEnum.TryParse for a and ignoreCase expected to be \"{nameof(TestEnum.A)}\" but was \"{genericValueFromParseSelfIgnoreCase}\"");

            Int32Enum<TestEnum> genericValueFromParseSelfWrongCase;
            bool genericValueFromParseSelfWrongCaseSuccess = TestEnum.TryParse("a", ignoreCase: false, out genericValueFromParseSelfWrongCase);
            Debug.Assert(!genericValueFromParseSelfWrongCaseSuccess && null == genericValueFromParseSelfWrongCase,
                $"TestEnum.TryParse for a and no ignoreCase expected to fail with \"null\" but was \"{genericValueFromParseSelfWrongCase}\"");

            Int32Enum<TestEnum> genericValueFromParseSelfWrongValue;
            bool genericValueFromParseSelfWrongValueSuccess = TestEnum.TryParse("G", ignoreCase: false, out genericValueFromParseSelfWrongValue);
            Debug.Assert(!genericValueFromParseSelfWrongValueSuccess && null == genericValueFromParseSelfWrongValue,
                $"TestEnum.TryParse for G expected to fail with \"null\" but was \"{genericValueFromParseSelfWrongValue}\"");

            Int32Enum<TestEnum> genericValueFromParseParent;
            bool genericValueFromParseParentSuccess = ExtendedTestEnum.TryParse("A", ignoreCase: false, out genericValueFromParseParent);
            Debug.Assert(genericValueFromParseParentSuccess && (genericValueFromParseParent as TestEnum).Equals(TestEnum.A),
                $"ExtendedTestEnum.TryParse for A and no ignoreCase expected to be \"{nameof(TestEnum.A)}\" but was \"{genericValueFromParseParent}\"");

            Int32Enum<TestEnum> genericValueFromParseGrandParent;
            bool genericValueFromParseGrandParentSuccess = AlternateSpecializedTestEnum.TryParse("A", ignoreCase: false, out genericValueFromParseGrandParent);
            Debug.Assert(genericValueFromParseGrandParentSuccess && (genericValueFromParseGrandParent as TestEnum).Equals(TestEnum.A),
                $"AlternateSpecializedTestEnum.TryParse for A and no ignoreCase expected to be \"{nameof(TestEnum.A)}\" but was \"{genericValueFromParseGrandParent}\"");

            Int32Enum<TestEnum> genericValueFromParseGrandChild;
            bool genericValueFromParseGrandChildSuccess = TestEnum.TryParse("GAMMA", ignoreCase: false, out genericValueFromParseGrandChild);
            Debug.Assert(genericValueFromParseGrandChildSuccess && (genericValueFromParseGrandChild as TestEnum).Equals(AlternateSpecializedTestEnum.GAMMA),
                $"TestEnum.TryParse for GAMMA and no ignoreCase expected to be \"{nameof(AlternateSpecializedTestEnum.GAMMA)}\" but was \"{genericValueFromParseGrandChild}\"");

            Int32Enum<TestEnum> genericValueFromParseChild;
            bool genericValueFromParseChildSuccess = ExtendedTestEnum.TryParse("GAMMA", ignoreCase: false, out genericValueFromParseChild);
            Debug.Assert(genericValueFromParseChildSuccess && (genericValueFromParseChild as ExtendedTestEnum).Equals(AlternateSpecializedTestEnum.GAMMA),
                $"ExtendedTestEnum.TryParse for GAMMA and no ignoreCase expected to be \"{nameof(AlternateSpecializedTestEnum.GAMMA)}\" but was \"{genericValueFromParseChild}\"");

            Int32Enum<TestEnum> genericValueFromParseSelf2;
            bool genericValueFromParseSelf2Success = AlternateSpecializedTestEnum.TryParse("GAMMA", ignoreCase: false, out genericValueFromParseSelf2);
            Debug.Assert(genericValueFromParseSelf2Success && (genericValueFromParseSelf2 as AlternateSpecializedTestEnum).Equals(AlternateSpecializedTestEnum.GAMMA),
                $"AlternateSpecializedTestEnum.TryParse for GAMMA and no ignoreCase expected to be \"{nameof(AlternateSpecializedTestEnum.GAMMA)}\" but was \"{genericValueFromParseSelf2}\"");

            StringEnum<RadioAlphabetEnum> valueFromParseStringName;
            bool valueFromParseStringNameSuccess = RadioAlphabetEnum.TryParse("ALFA", ignoreCase: false, out valueFromParseStringName);
            Debug.Assert(valueFromParseStringNameSuccess && (valueFromParseStringName as RadioAlphabetEnum).Equals(RadioAlphabetEnum.ALFA),
                $"RadioAlphabetEnum.TryParse for ALFA and no ignoreCase expected to be \"{nameof(RadioAlphabetEnum.ALFA)}\" but was \"{valueFromParseStringName}\"");

            StringEnum<RadioAlphabetEnum> valueFromParseStringBacked;
            bool valueFromParseStringBackedSuccess = RadioAlphabetEnum.TryParse("A", ignoreCase: false, out valueFromParseStringBacked);
            Debug.Assert(valueFromParseStringBackedSuccess && (valueFromParseStringBacked as RadioAlphabetEnum).Equals(RadioAlphabetEnum.ALFA),
                $"RadioAlphabetEnum.TryParse for A and no ignoreCase expected to be \"{nameof(RadioAlphabetEnum.ALFA)}\" but was \"{valueFromParseStringBacked}\"");
        }

        [Test]
        public void GetNameTest()
        {
            string nameFromBacking = ExpandableEnum.GetName(typeof(TestEnum), -1);
            Debug.Assert(TestEnum.EMPTY_VALUE.Name == nameFromBacking,
                $"Enum value -1 was expected to be found in {nameof(TestEnum)} through ExpandableEnum.GetName");

            string strictNameFromBacking = ExpandableEnum.GetNameStrictly(typeof(TestEnum), -1);
            Debug.Assert(TestEnum.EMPTY_VALUE.Name == strictNameFromBacking,
                $"Enum value -1 was expected to be found in {nameof(TestEnum)} through ExpandableEnum.GetStrictName");

            string compatibleNameFromBacking = ExpandableEnum.GetNameFromAllCompatibleValues(typeof(TestEnum), -1);
            Debug.Assert(TestEnum.EMPTY_VALUE.Name == compatibleNameFromBacking,
                $"Enum value -1 was expected to be found in {nameof(TestEnum)} through ExpandableEnum.GetNameFromAllCompatibleValues");

            // This method looks a bit silly, not sure if it's needed. If you've got a typed instance, you can easily access its members.
            string genericNameFromBacking = ExpandableEnum.GetName(TestEnum.A);
            Debug.Assert(TestEnum.A.Name == genericNameFromBacking,
                $"Enum declaration {nameof(TestEnum.A)} from {nameof(TestEnum)} was expected to be equal to {TestEnum.A.Name} via ExpandableEnum.GetName<T>");
        }

        [Test]
        public void GetNamesTest()
        {
            //TODO
            // TODO: add ExpandableEnum.GetStrictNames ? Do we need it?
            string[] strictNamesExpected = new string[] { ExtendedTestEnum.D.Name, ExtendedTestEnum.E.Name, ExtendedTestEnum.F.Name };
            string[] strictNamesTest = ExpandableEnum.GetNames(typeof(ExtendedTestEnum)); // does not exist yet - should "fail"
            //Debug.Assert(strictNamesTest.ContainsExactly(strictNamesExpected), $"Error!");

            string[] dependenciesNamesExpected = new string[] { TestEnum.EMPTY_VALUE.Name, TestEnum.A.Name, TestEnum.B.Name, TestEnum.C.Name,
                                                                ExtendedTestEnum.D.Name, ExtendedTestEnum.E.Name, ExtendedTestEnum.F.Name };
            string[] dependenciesNamesTest = ExpandableEnum.GetNames(typeof(ExtendedTestEnum));
            Debug.Assert(dependenciesNamesTest.ContainsExactly(dependenciesNamesExpected),
                $"List of value names obtained using ExpandableEnum.GetNames for {nameof(ExtendedTestEnum)} did not match expected names");

            string[] dependenciesNamesTestGeneric = ExpandableEnum.GetNames<ExtendedTestEnum>();
            Debug.Assert(dependenciesNamesTestGeneric.ContainsExactly(dependenciesNamesExpected),
                $"List of value names obtained using ExpandableEnum.GetNames<T> for {nameof(ExtendedTestEnum)} did not match expected names");

            string[] dependenciesNamesExpectedParent = new string[] { TestEnum.EMPTY_VALUE.Name, TestEnum.A.Name, TestEnum.B.Name, TestEnum.C.Name };
            string[] dependenciesNamesTestParent = ExpandableEnum.GetNames(typeof(TestEnum));
            Debug.Assert(dependenciesNamesTestParent.Except(dependenciesNamesExpectedParent).Count() == 0,
                $"List of value names obtained using ExpandableEnum.GetNames for {nameof(TestEnum)} did not match expected names");

            string[] dependenciesNamesTestGenericParent = ExpandableEnum.GetNames<TestEnum>();
            Debug.Assert(dependenciesNamesTestGenericParent.ContainsExactly(dependenciesNamesExpectedParent),
                $"List of value names obtained using ExpandableEnum.GetNames<T> for {nameof(TestEnum)} did not match expected names");

            // TODO: add ExpandableEnum.GetAllCompatibleNames ? Do we need it?
            string[] compatibleNamesExpected = new string[] { TestEnum.EMPTY_VALUE.Name, TestEnum.A.Name, TestEnum.B.Name, TestEnum.C.Name,
                                                              ExtendedTestEnum.D.Name, ExtendedTestEnum.E.Name, ExtendedTestEnum.F.Name,
                                                              SpecializedTestEnum.Z.Name,
                                                              AlternateSpecializedTestEnum.GAMMA.Name, AlternateSpecializedTestEnum.Z.Name,};
            string[] compatibleNamesTest = ExpandableEnum.GetNames(typeof(TestEnum)); // does not exist yet - should "fail"
            //Debug.Assert(compatibleNamesTest.ContainsExactly(compatibleNamesExpected), $"Error!");
        }
    }
}