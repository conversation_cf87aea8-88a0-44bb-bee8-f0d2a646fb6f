// Copyright Isto Inc.
using Isto.Core.AI;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.StateMachine
{
    public class MobState : MonoBehaviour
    {
        // Public Variables

        public string description;

        public float actionUpdateRate = 0.1f;
        public List<AIAction> actions;

        public AIAction CurrentAction { get { return _currentAction; } }

        // Private Variables

        private AIActionSelector _selector;

        protected AIAction _currentAction;
        private AIAction _previousAction;
        private float _actionUpdateTimer;

        private MobStateMachine _controller;

        // Methods		

        [Inject]
        public void Inject(AIActionSelector selector)
        {
            _selector = selector;
        }

        public virtual void Enter(MobStateMachine controller)
        {
            _controller = controller as MobStateMachine;
        }

        public void Exit(MobStateMachine controller)
        {
            // Clear current action
            if (_currentAction != null)
            {
                _currentAction.StopAction();
            }

            _currentAction = null;
        }

        public MobState Run(MobStateMachine controller)
        {
            _actionUpdateTimer += Time.deltaTime;

            if (_actionUpdateTimer > actionUpdateRate)
            {
                if (_currentAction == null || _currentAction.isInteruptable || (!_currentAction.isInteruptable && !_currentAction.isRunning))
                {
                    AIAction newAction = _selector.SelectNextAction(actions, _currentAction, _previousAction, controller.gameObject);

                    if (newAction != _currentAction || !_currentAction.isRunning)
                    {
                        //Debug.LogError("Changing action to: " + newAction.ToString());

                        //Stop the current running action
                        if (_currentAction != null)
                        {
                            _currentAction.StopAction();
                        }

                        _previousAction = _currentAction;
                        _currentAction = newAction;

                        //Update the sprite and start the new action running on the controller
                        if (_controller.actionIcon != null)
                        {
                            _controller.actionIcon.sprite = _currentAction.activeIcon;
                        }

                        _currentAction.StartAction(_controller);

                        _actionUpdateTimer = 0f;
                    }
                }
            }

            if (_currentAction != null)
                _currentAction.UpdateAction();

            if (_currentAction is AIChangeStateAction)
                return ((AIChangeStateAction)_currentAction).state;

            if (_currentAction is AISwitchToAgro)
                return _controller.agroState;
            else if (_currentAction is AISwitchToNeutral)
                return _controller.neutralState;
            else
                return this;
        }
    }
}