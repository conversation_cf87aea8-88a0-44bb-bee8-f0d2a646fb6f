// Copyright Isto Inc.
using Isto.Core.AI;
using Isto.Core.Items;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.StateMachine
{
    /// <summary>
    /// State has handler for inventory changes and checks if the correct item is placed in the
    /// inventory to trigger changing to this state.
    /// </summary>
    [RequireComponent(typeof(ItemContainer))]
    [RequireComponent(typeof(MobStateMachine))]
    public class MobPlayerOverrideState : MobState, IInventoryListener
    {
        public List<ItemPile> overrideTriggerItems;
        public AIPlayerAttemptOverrideAction overrideStartAction;

        private ItemContainer _inventory;
        private MobStateMachine _mobController;

        protected void Awake()
        {
            _inventory = GetComponent<ItemContainer>();
            _mobController = GetComponent<MobStateMachine>();
        }

        protected void Start()
        {
            // Add all the override items to the inventory as accepted items
            for (int i = 0; i < overrideTriggerItems.Count; i++)
            {
                _inventory.validItems.Add(overrideTriggerItems[i].item);
            }
        }

        public override void Enter(MobStateMachine controller)
        {
            base.Enter(controller);

            _currentAction = overrideStartAction;
            _currentAction.StartAction(controller);
        }

        /// <summary>
        /// Check if the inventory now contains any of the items in the override trigger items list to cause state change
        /// to player override.
        /// </summary>
        public void InventoryChanged()
        {
            for (int i = 0; i < overrideTriggerItems.Count; i++)
            {
                ItemPile triggerPile = overrideTriggerItems[i];

                //If we have enough of one of the trigger items, switch to the override state
                if (_inventory.GetCountOfItem(triggerPile.item) >= triggerPile.count)
                {
                    _inventory.Remove(triggerPile.item, triggerPile.count);

                    _mobController.ChangeState(_mobController.overrideState);

                    return;
                }
            }
        }

        public void InventoryFull()
        {
            // TODO might need to add some logic here
            Debug.LogWarning("No logic for handling full inventory on MobPlayerOverrideState");
        }

        private IEnumerator ProccessOverride(MobStateMachine controller, float overrideTime)
        {
            controller.SetOverrideSliderActive(false);

            float progress = 0f;

            while (progress < overrideTime)
            {
                if (controller.CurrentState.CurrentAction is AIMobStunnedAction)
                {
                    progress += Time.deltaTime;

                    controller.SetOverrideSliderValue(progress / overrideTime);

                    if (progress > overrideTime)
                        yield break;
                    else
                        yield return null;
                }
                else
                {
                    // Mob is no longer stunned, exit
                    controller.SetOverrideSliderActive(false);
                    yield break;
                }
            }
        }
    }
}