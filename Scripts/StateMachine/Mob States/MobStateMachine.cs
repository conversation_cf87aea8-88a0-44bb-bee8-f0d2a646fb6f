// Copyright Isto Inc.
using I2.Loc;
using Isto.Core.AI;
using Isto.Core.Beings;
using Isto.Core.Data;
using Isto.Core.Inputs;
using Isto.Core.Items;
using Isto.Core.Localization;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AI;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.StateMachine
{
    [RequireComponent(typeof(NavMeshAgent)), RequireComponent(typeof(AnimationController)), SelectionBase]
    public class MobStateMachine : InteractableItem, IItemTopmostParent, IInteractable, IActivatable, IActionOnSpawn, IDataLoadCompleteHandler, IActionOnPickup
    {
        // Public Variables

        public SpriteRenderer actionIcon;
        public string mobName;
        public LocalizedString mobNameKey;

        [Header("Mob States")]
        public MobNeutralState neutralState;
        public MobAgroState agroState;
        public MobPlayerOverrideState overrideState;

        [Header("")]
        public MobState startState;

        [Header("Player Interaction")]
        [Tooltip("Sets if the player can pickup the mob or not without stunning it.  Used for harvesters for example")]
        public bool canBeDismantled = false;
        public bool canBePickedUp = false;
        public bool canBePet = false;

        [Header("Visuals")]
        public Image progressSlider;
        public Image overrideSlider;

        [Header("Group Interaction")]
        public bool registerInMainGroup = false;

        [HideInInspector]
        public NavMeshAgent agent;

        public MobState CurrentState { get { return _currentState; } }
        public float Speed { get { return agent.speed; } }
        public float Acceleration { get { return agent.acceleration; } }
        public float RemainingDistance { get { return agent.remainingDistance - agent.stoppingDistance; } }
        public ItemContainer Inventory { get { return _inventory; } }
        public Vector3 Forward { get { return _forwardDirection; } }
        public bool IsMovingByAutomation { get { return false; } } // feature was unused and not ported to Isto.Core

        // Private Variables

        public MobGroupController GroupController { get; private set; }

        protected MobState _currentState;
        protected MobState _previousState;

        private Vector3 _forwardDirection; //direction they're facing
        private AnimationController _animationControl;
        private AnimationController.CharacterDirections _currentDirection = AnimationController.CharacterDirections.down_right;
        private ItemContainer _inventory;
        private List<UserActions> _validActionList = new List<UserActions>();
        private Vector3 _startingPosition; // Used to track how far mob has moved away from it's spawn position.

        private bool _loadComplete = false;

        // Injection

        [Inject]
        private void Inject(MobGroupController mobGroupController)
        {
            GroupController = mobGroupController;
        }

        // Lifecycle Events

        protected override void Awake()
        {
            agent = GetComponent<NavMeshAgent>();
            agent.updateRotation = false;

            _animationControl = GetComponent<AnimationController>();
            _inventory = GetComponent<ItemContainer>();

            base.Awake();
        }

        private void OnDisable()
        {
            // Disable the action icon whenever the state machine is disabled.  Typically used when placing item
            actionIcon.enabled = false;

            _currentDirection = AnimationController.CharacterDirections.down_left;

            if (registerInMainGroup)
                GroupController.RemoveFromGroup(gameObject);
        }

        private void Start()
        {
            if (registerInMainGroup)
                GroupController.AddToGroup(gameObject);

            ChangeState(startState);

            _startingPosition = transform.position;
        }

        protected override void Update()
        {
            if (!_loadComplete)
                return;

            base.Update();

            MobState nextState = _currentState.Run(this);

            if (nextState != _currentState)
                ChangeState(nextState);
        }

        // Methods

        public void OnDataLoadComplete()
        {
            _loadComplete = true;
        }

        public void MoveToPosition(Vector3 position, float speed)
        {
            //The default move type is run
            MoveToPosition(position, speed, AnimationController.AnimationType.run);
        }

        public void MoveToPosition(Vector3 position, float speed, AnimationController.AnimationType animType)
        {
            Vector3 targetPosition = FindPointOnNavMesh(position);

            //Update forward facing direction.
            _forwardDirection = (targetPosition - transform.position);

            //Set the appropriate running animation
            float angle = _animationControl.GetAngleFromForward(_forwardDirection);
            float sign = _animationControl.GetCharacterHorizontalDirection(_forwardDirection);

            AnimationController.CharacterDirections charDirection = _animationControl.GetCharacterDirection(angle, sign);
            SetCharacterAnimation(animType, charDirection);

            //Change the sprite to face the right way
            _currentDirection = _animationControl.GetCharacterDirection(angle, sign);
            _animationControl.SetCharacterDirection(_currentDirection);

            //Move The Character
            SetSpeed(speed, animType);
            agent.SetDestination(targetPosition);
            targetPosition.y = transform.position.y;
        }

        /// <summary>
        /// Determines the correct animation to play based on the forward vector passed in.  Used to set facing direction when the mob
        /// is standing still typically.
        /// </summary>
        /// <param name="forward"></param>
        /// <param name="animationState"></param>
        public void SetAnimationDirectionAndState(Vector3 forward, AnimationController.AnimationType animationState)
        {
            //Set the appropriate running animation
            float angle = _animationControl.GetAngleFromForward(forward);
            float sign = _animationControl.GetCharacterHorizontalDirection(forward);

            AnimationController.CharacterDirections charDirection = _animationControl.GetCharacterDirection(angle, sign);

            SetCharacterAnimation(animationState, charDirection);
        }

        public void SetSpeed(float speed, AnimationController.AnimationType type)
        {
            agent.speed = speed;

            _animationControl.SetAnimationSpeed(type);
        }

        public void SetAcceleration(float acceleration)
        {
            agent.acceleration = acceleration;
        }

        public void SetSliderValue(float value)
        {
            if (progressSlider != null)
            {
                progressSlider.fillAmount = value;
            }
        }

        public void SetSliderActive(bool active)
        {
            if (progressSlider != null)
            {
                progressSlider.gameObject.SetActive(active);
            }
        }

        public void SetOverrideSliderValue(float value)
        {
            if (overrideSlider != null)
            {
                overrideSlider.fillAmount = value;
            }
        }

        public void SetOverrideSliderActive(bool active)
        {
            if (overrideSlider != null)
            {
                overrideSlider.gameObject.SetActive(active);
            }
        }

        public void SetCharacterAnimation(AnimationController.AnimationType animType)
        {
            SetCharacterAnimation(animType, _currentDirection);
        }

        public void SetCharacterAnimation(AnimationController.AnimationType animType, AnimationController.CharacterDirections direction)
        {
            _animationControl.SetCharacterAnimation(animType, direction);
            _animationControl.SetCharacterDirection(direction);
        }

        /// <summary>
        /// Stops the mobs current movement and will set it's animation to Idle if true is passed in.
        /// </summary>
        /// <param name="setIdleAnimation"></param>
        public void StopMob(bool setIdleAnimation = false)
        {
            agent.ResetPath();
            agent.velocity = Vector3.zero;

            if (setIdleAnimation)
                SetCharacterAnimation(AnimationController.AnimationType.idle);
        }

        public void ChangeState(MobState nextState)
        {
            if (_currentState != null)
            {
                _currentState.Exit(this);
            }

            _previousState = _currentState;
            _currentState = nextState;

            if (_currentState != null)
            {
                _currentState.Enter(this);
            }
        }

        public void ChangeToPreviousState()
        {
            ChangeState(_previousState);
        }

        /// <summary>
        /// Looks for a valid point on the nav mesh based on the target position passed in.
        /// If the target is not no the nav mesh, it will look for a valid position at increasing
        /// large angle deviations for the intended target.
        /// </summary>
        /// <param name="targetPosition">Intended position to move to.</param>
        /// <returns>Position on nav mesh.  If no suitable position can be found, the original position will be returned.</returns>
        private Vector3 FindPointOnNavMesh(Vector3 targetPosition)
        {
            NavMeshHit hit;

            if (NavMesh.SamplePosition(targetPosition, out hit, 0.5f, agent.areaMask))
            {
                return targetPosition;
            }
            // If the point is not on the nav mesh, look for a point either to the right or left of the agent.
            else
            {
                // Get the direction to the intended target and shrink the vector slightly to look for closer options
                Vector3 directionToTarget = (targetPosition - transform.position) * 0.75f;

                int attempts = 0;
                float angleStep = 45;
                float nextAngle = angleStep;
                int direction = 1;

                while (attempts < 6)
                {
                    Quaternion rotation = Quaternion.AngleAxis(nextAngle * direction, Vector3.up);

                    //Rotate the vector
                    directionToTarget = rotation * directionToTarget;

                    Vector3 nextPosition = directionToTarget + transform.position;

                    if (NavMesh.SamplePosition(nextPosition, out hit, 0.5f, agent.areaMask))
                    {
                        return nextPosition;
                    }

                    // Double the angle step to move back to center plus swing all the way to the otherside
                    nextAngle += angleStep;
                    // Alternate the direction we rotate the vector
                    direction *= -1;
                    attempts++;
                }
            }

            return targetPosition;
        }

        public GameObject GetGameObject()
        {
            return gameObject;
        }

        public override List<UserActions> GetValidActions()
        {
            _validActionList.Clear();

            if (_currentState.CurrentAction is AIMobStunnedAction && _inventory != null)
            {
                _validActionList.Add(UserActions.GIVEITEM);
            }

            if (canBeDismantled)
                _validActionList.Add(UserActions.DISMANTLE);
            if (canBePickedUp || canBePet)
                _validActionList.Add(UserActions.INTERACT);

            return _validActionList;
        }

        public override void ShowTooltip()
        {
            // Disables tooltip by neutering this method - I think creatures have them, but I suppose not the "mobs"
            // that are still using this state machine (just the Eyes AFAIK)
            return;
        }

        // Should never be used since we've overriden the ShowTooltip
        public override string GetItemName()
        {
            return Loc.Get(mobNameKey);
        }

        // Should never be used since we've overriden the ShowTooltip
        public override string GetToolTip(UserActions action)
        {
            return gameObject.name;
        }

        public void Activate()
        {
            _loadComplete = true;
            enabled = true;
            gameObject.SetColliderState(true);

            ChangeState(startState);

            actionIcon.enabled = true;
        }

        /// <summary>
        /// Returns the distance from where the mob originally spawned
        /// </summary>
        /// <returns></returns>
        public float GetDistanceFromSpawnPosition()
        {
            return Vector3.Distance(_startingPosition, transform.position);
        }

        /// <summary>
        /// Disables this script until it is properly placed by the player.
        /// </summary>
        public void DoSpawnAction()
        {
            enabled = false;
        }

        public void DoPickupAction()
        {
            Destroy(gameObject);
        }
    }
}