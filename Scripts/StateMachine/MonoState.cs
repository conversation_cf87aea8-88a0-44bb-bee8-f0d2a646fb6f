// Copyright Isto Inc.

using UnityEngine;

namespace Isto.Core.StateMachine
{
    /// <summary>
    /// State for MonoStateMachine.  These are to be attached to GameObjects as opposed to State objects
    /// which are scriptable objects.
    /// </summary>
	public abstract class MonoState : MonoBehaviour
    {
        public abstract void Enter(MonoStateMachine controller);
        public abstract void Exit(MonoStateMachine controller);
        public abstract MonoState Run(MonoStateMachine controller);
        public virtual void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
            // Debug.LogWarning("Coming back from substate in menu.  Make sure this is intended");
        }
    }
}