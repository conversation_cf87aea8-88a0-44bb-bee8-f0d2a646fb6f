// Copyright Isto Inc.

namespace Isto.Core.Audio
{
    public interface IGameVolume
    {
        public void SetMasterVolume(float volume);
        public void SetMusicVolume(float volume);
        public void SetSFXVolume(float volume);
        public void SetDialogueVolume(float volume);
        public void SetAmbientVolume(float volume);
        public float GetMasterVolume();
        public float GetMusicVolume();
        public float GetSFXVolume();
        public float GetDialogueVolume();
        public float GetAmbientVolume();
        public void SaveVolumeSettings();
    }
}