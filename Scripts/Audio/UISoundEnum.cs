// Copyright Isto Inc.

namespace Isto.Core.Audio
{
    public class UISoundEnum : SoundEventEnum
    {
        public static readonly UISoundEnum NO_SOUND = new UISoundEnum(0, nameof(NO_SOUND));
        public static readonly UISoundEnum BUTTON_HOVER = new UISoundEnum(1, nameof(BUTTON_HOVER));
        public static readonly UISoundEnum BUTTON_CLICK = new UISoundEnum(2, nameof(BUTTON_CLICK));
        public static readonly UISoundEnum BUTTON_ERROR = new UISoundEnum(3, nameof(BUTTON_ERROR));

        public UISoundEnum(int value, string name) : base(value, name)
        {
        }
    }
}