// Copyright Isto Inc.

using FMOD;
using FMOD.Studio;
using FMODUnity;
using System;
using System.Collections.Generic;
using UnityEngine;
using Zenject;
using Debug = UnityEngine.Debug;

namespace Isto.Core.Audio
{
    public class FMODGameVolume : MonoBehaviour, IGameVolume
    {
        // OTHER FIELDS

        private static readonly string PREFS_MASTER_KEY = "masterVolume";
        private static readonly string PREFS_MUSIC_KEY = "musicVolume";
        private static readonly string PREFS_SFX_KEY = "sfxVolume";
        private static readonly string PREFS_DIALOGUE_KEY = "dialogueVolume";
        private static readonly string PREFS_AMBIENT_KEY = "ambientVolume";

        private static readonly Dictionary<string, string> BUSES_DICT = new Dictionary<string, string>()
        {
            {PREFS_MASTER_KEY, Constants.MASTER_BUS_PATH},
            {PREFS_MUSIC_KEY, Constants.MUSIC_BUS_PATH },
            {PREFS_SFX_KEY, Constants.SFX_BUS_PATH},
            {PREFS_DIALOGUE_KEY, Constants.DIALOGUE_BUS_PATH},
            {PREFS_AMBIENT_KEY, Constants.AMBIENT_BUS_PATH}
        };

        private Dictionary<string, float> _defaultVolumeDictionary = null;


        // INJECTION

        private Configuration.Settings _settings;

        [Inject]
        public virtual void Inject(Configuration.Settings settings)
        {
            _settings = settings;
        }


        // LIFECYCLE EVENTS

        private void Awake()
        {
            _defaultVolumeDictionary = new Dictionary<string, float>()
            {
                {PREFS_MASTER_KEY, _settings.DefaultMasterVolume},
                {PREFS_MUSIC_KEY, _settings.DefaultMusicVolume},
                {PREFS_SFX_KEY, _settings.DefaultSfxVolume},
                {PREFS_DIALOGUE_KEY, _settings.DefaultDialogueVolume},
                {PREFS_AMBIENT_KEY, _settings.DefaultAmbientVolume}
            };
        }

        private void Start()
        {
            InitializeAllVolumes();
        }


        // ACCESSORS

        public void SetMasterVolume(float volume)
        {
            SetVolume(PREFS_MASTER_KEY, volume);
        }

        public void SetMusicVolume(float volume)
        {
            SetVolume(PREFS_MUSIC_KEY, volume);
        }

        public void SetSFXVolume(float volume)
        {
            SetVolume(PREFS_SFX_KEY, volume);
        }

        public void SetDialogueVolume(float volume)
        {
            SetVolume(PREFS_DIALOGUE_KEY, volume);
        }

        public void SetAmbientVolume(float volume)
        {
            SetVolume(PREFS_AMBIENT_KEY, volume);
        }

        public float GetMasterVolume()
        {
            return GetVolume(PREFS_MASTER_KEY);
        }

        public float GetMusicVolume()
        {
            return GetVolume(PREFS_MUSIC_KEY);
        }

        public float GetSFXVolume()
        {
            return GetVolume(PREFS_SFX_KEY);
        }

        public float GetDialogueVolume()
        {
            return GetVolume(PREFS_DIALOGUE_KEY);
        }

        public float GetAmbientVolume()
        {
            return GetVolume(PREFS_AMBIENT_KEY);
        }

        private void SetVolume(string prefsKey, float volume)
        {
            volume = (float)Math.Round(volume, 1);
            Bus currentBus = RuntimeManager.GetBus(BUSES_DICT[prefsKey]);
            if (currentBus.setVolume(volume) != RESULT.OK)
            {
                Debug.LogError($"Cannot set the volume for the FMOD bus {BUSES_DICT[prefsKey]}. Ensure it exists in the FMOD project.");
            }
        }

        private float GetVolume(string prefsKey)
        {
            _defaultVolumeDictionary.TryGetValue(prefsKey, out float volume);

            Bus currentBus = RuntimeManager.GetBus(BUSES_DICT[prefsKey]);
            if (currentBus.getVolume(out float currentVolume) == RESULT.OK)
            {
                volume = currentVolume;
            }
            else
            {
                Debug.LogError($"Cannot read the volume for the FMOD bus {BUSES_DICT[prefsKey]}. Ensure it exists in the FMOD project.");
            }

            return (float)Math.Round(volume, 1);
        }


        // OTHER METHODS

        public void SaveVolumeSettings()
        {
            foreach (KeyValuePair<string,string> busPathKeyValuePair in BUSES_DICT)
            {
                PlayerPrefs.SetFloat(busPathKeyValuePair.Key, GetVolume(busPathKeyValuePair.Key));
            }
            PlayerPrefs.Save();
        }

        private void InitializeAllVolumes()
        {
            foreach (KeyValuePair<string,string> busPathKeyValuePair in BUSES_DICT)
            {
                InitializeVolume(busPathKeyValuePair.Key);
            }
        }

        private void InitializeVolume(string prefsKey)
        {
            _defaultVolumeDictionary.TryGetValue(prefsKey, out float defaultVolume);

            float volume = PlayerPrefs.GetFloat(prefsKey, defaultVolume);
            SetVolume(prefsKey, volume);
        }
    }
}