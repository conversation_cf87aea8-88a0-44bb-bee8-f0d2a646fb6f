// Copyright Isto Inc.

using FMOD;
using FMOD.Studio;
using FMODUnity;
using System.Linq;
using UnityEngine;
using Zenject;
using Debug = UnityEngine.Debug;
using Random = UnityEngine.Random;
using STOP_MODE = FMOD.Studio.STOP_MODE;

namespace Isto.Core.Audio
{
    public class Music : MonoBehaviour, IGameMusic
    {
        // OTHER FIELDS

        private EventInstance _runningInstance;
        private AudioTrackReferences _audioTrackReferences;


        // INJECTION

        private Configuration.Settings _settings;

        [Inject]
        public virtual void Inject(Configuration.Settings settings)
        {
            _settings = settings;
        }


        // LIFECYCLE EVENTS

        private void Awake()
        {
            _audioTrackReferences = _settings.AudioReferences;
        }

        private void OnDestroy()
        {
            _runningInstance.stop(STOP_MODE.IMMEDIATE);
            _runningInstance.release();
        }


        // ACCESSORS

        public bool IsMusicPlaying()
        {
            if (!_runningInstance.isValid())
                return false;

            _runningInstance.getPlaybackState(out PLAYBACK_STATE state);

            return state == PLAYBACK_STATE.PLAYING;
        }

        private MusicTrackEnum GetRandomMusicTrack()
        {
            int r = Random.Range(0, _audioTrackReferences.GameMusicTracks.Length);

            return MusicTrackEnum.GetByValue(r);;
        }


        // OTHER METHODS

        public void StopMusic()
        {
            if (_runningInstance.isValid())
            {
                _runningInstance.stop(STOP_MODE.ALLOWFADEOUT);
            }
        }

        public void PlayMusic(MusicTrack musicTrack)
        {
            if (musicTrack == MusicTrack.Title)
            {
                PlayMusicTrackEnum(MusicTrackEnum.TITLE);
            }
            else if (musicTrack == MusicTrack.EndGame)
            {
                PlayMusicTrackEnum(MusicTrackEnum.END_GAME);
            }
            else if (musicTrack == MusicTrack.Random)
            {
                PlayRandomMusic();
            }
            else if (musicTrack == MusicTrack.NoMusic)
            {
                StopMusic();
            }
        }

        public void PauseMusic(bool paused)
        {
            _runningInstance.setPaused(paused);
        }

        public void PlayMusic(string musicRef)
        {
            StopMusic();
            if (RuntimeManager.StudioSystem.getEvent(musicRef, out EventDescription desc) == RESULT.OK)
            {
                _runningInstance = RuntimeManager.CreateInstance(musicRef);
                _runningInstance.start();
            }
            else
            {
                Debug.LogError($"Unable to locate music event reference [{musicRef}], please check that it exists.", this);
            }
        }

        /// <summary>
        /// Gets a random music clip from the array of possibilites and starts playing by transitioning to
        /// the music on snapshot
        /// </summary>
        private void PlayRandomMusic()
        {
            if (_audioTrackReferences.GameMusicTracks.Length > 0)
            {
                MusicTrackEnum randomMusic = GetRandomMusicTrack();

                PlayMusicTrackEnum(randomMusic);
            }
        }

        private void PlayMusicTrackEnum(MusicTrackEnum musicTrack)
        {
            EventReference? soundEventRef = null;

            soundEventRef = _audioTrackReferences.GameMusicTracks.FirstOrDefault(musicRef => musicRef.musicEnum == musicTrack.Value)?.eventRef;


            if (soundEventRef.HasValue)
            {
                PlayEventReference(soundEventRef.Value);
            }
            else
            {
                Debug.LogWarning($"Cannot find EventReference for music track {musicTrack.Name}. Ensure that it has been correctly" +
                                 $"added to the Audio Track References scriptable object located in Default Settings.");
            }
        }

        public void PlayEventReference(EventReference musicRef)
        {
            StopMusic();
            if (RuntimeManager.StudioSystem.getEventByID(musicRef.Guid, out EventDescription desc) == RESULT.OK)
            {
                _runningInstance = RuntimeManager.CreateInstance(musicRef);
                _runningInstance.start();
            }
            else
            {
                Debug.LogError($"Unable to locate music event reference [{musicRef}], please check that it exists.", this);
            }
        }
    }
}