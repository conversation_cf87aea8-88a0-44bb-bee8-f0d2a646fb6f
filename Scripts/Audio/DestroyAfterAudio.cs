// Copyright Isto Inc.
using UnityEngine;

namespace Isto.Core.Audio
{
    /// <summary>
    /// Component that waits for an audio clip to finish playing an then destroys the gameObject
    /// </summary>
    [RequireComponent(typeof(AudioSource))]
    public class DestroyAfterAudio : MonoBehaviour
    {
        // Private Variables

        private AudioSource _audioSource;
        private bool _started;

        // Lifecycle Events

        private void Awake()
        {
            _audioSource = GetComponent<AudioSource>();
            _started = false;
        }

        private void Update()
        {
            if (_audioSource.isPlaying && !_started)
            {
                _started = true;
            }
            else if (!_audioSource.isPlaying && _started)
            {
                Destroy(gameObject);
            }
        }
    }
}