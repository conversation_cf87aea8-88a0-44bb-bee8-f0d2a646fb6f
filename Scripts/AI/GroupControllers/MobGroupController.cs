// Copyright Isto Inc.
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.AI
{
    /// <summary>
    /// Class for tracking the attacking behaviours of active mobs.  <PERSON><PERSON>'s check with this class to ensure they are able to attack
    /// at any given time or adjust movement
    /// </summary>
    public class MobGroupController
    {
        private HashSet<GameObject> _activeMobs;
        private HashSet<PushBackLungeAttack> _attackActions;

        private int _maxSimultaniousAttacks = 3;

        public MobGroupController()
        {
            _activeMobs = new HashSet<GameObject>();
            _attackActions = new HashSet<PushBackLungeAttack>();
        }

        public void AddToGroup(GameObject pushBack)
        {
            _activeMobs.Add(pushBack);
        }

        public void RemoveFromGroup(GameObject pushBack)
        {
            _activeMobs.Remove(pushBack);
        }

        public void RegisterAttack(PushBackLungeAttack pushBackLungeAttack)
        {
            _attackActions.Add(pushBackLungeAttack);
        }

        public void UnregisterAttack(PushBackLungeAttack pushBackLungeAttack)
        {
            _attackActions.Remove(pushBackLungeAttack);
        }

        /// <summary>
        /// Checks if any other push backs are currently attacking.
        /// </summary>
        /// <param name="requestingAttack">The push back that wants to attack</param>
        /// <returns>True if no one else is attacking, false otherwise</returns>
        public bool CanAttack(PushBackLungeAttack requestingAttack)
        {
            int currentAttacks = 0;

            foreach (PushBackLungeAttack attacker in _attackActions)
            {
                if (attacker.IsAttacking)
                    currentAttacks++;
            }

            return currentAttacks <= _maxSimultaniousAttacks;
        }

        /// <summary>
        /// Gets the position of the nearest registered mob from the passed in position that is within
        /// the max distance parameter
        /// </summary>
        /// <param name="position">Position to find nearest mob from</param>
        /// <param name="maxDistance">Max radius to check for nearest</param>
        /// <param name="nearest">Out param will contain nearest position if one found, otherwise will contain Vector3.zero</param>
        /// <returns>World position of the nearest mob from the position</returns>
        public bool IsMobNearFromPosition(Vector3 position, float maxDistance, out Vector3 nearest)
        {
            bool nearMob = false;
            nearest = Vector3.zero;

            float nearestDistance = maxDistance;

            foreach (GameObject nextMob in _activeMobs)
            {
                float nextDistance = Vector3.Distance(nextMob.transform.position, position);

                if (nextDistance != 0f && nextDistance < nearestDistance)
                {
                    nearestDistance = nextDistance;
                    nearest = nextMob.transform.position;

                    nearMob = true;
                }
            }

            return nearMob;
        }
    }
}