// Copyright Isto Inc.
using System;
using UnityEngine;

namespace Isto.Core.AI
{
    /// <summary>
    /// Abstract scriptable that evaluates the weight for an action it's applied to.  Returns a AIConsiderationResult that has the weight
    /// and multiplier which is used by the AIActionSelector to determine the next action the AI will perform.
    /// </summary>
    public abstract class AIConsideration : ScriptableObject
    {
        /// <summary>
        /// Evaluates the AIAction's chances for being performed.  
        /// </summary>
        /// <param name="mob">The GameObject the Action is attached to.</param>
        /// <param name="action">The action being evaulated that this consdieration is associated with.</param>
        /// <returns></returns>
        public abstract AIConsiderationResult Evaluate(GameObject mob, AIAction action);
    }

    [Serializable]
    public class AIConsiderationResult
    {
        public float multiplier = 1;        //Gives the ability to zero out an action if the multiplier is zero
        public float weight = 1;            //Weight is the chance the action will be performed, in the range [0,100]

        public AIConsiderationResult()
        {
            this.weight = 0f;
            this.multiplier = 0f;
        }

        public AIConsiderationResult(float weight, float multiplier)
        {
            this.weight = weight;
            this.multiplier = multiplier;
        }

        public void Clear()
        {
            multiplier = 1;
            weight = 0;
        }
    }
}