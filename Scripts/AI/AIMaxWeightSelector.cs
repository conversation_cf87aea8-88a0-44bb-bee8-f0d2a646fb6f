// Copyright Isto Inc.
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.AI
{
    /// <summary>
    /// AIActionSelector that choses actions based on the Max Weight for the considerations of an action
    /// </summary>
	public class AIMaxWeightSelector : AIActionSelector
    {
        //Private Variables

        private List<AIActionUtility> _evaulationResults = new List<AIActionUtility>();
        private List<AIActionUtility> _filterBuffer = new List<AIActionUtility>();              // Buffer for holding filtered Actions

#if AI_LOGGING
        private StringBuilder _debugOutput = new StringBuilder(); 
#endif

        // Methods

        public override AIAction SelectNextAction(
            List<AIAction> behaviours, AIAction activeAction, AIAction previousAction, GameObject mob)
        {
            _evaulationResults.Clear();

            int actionCount = behaviours.Count;

#if AI_LOGGING
            _debugOutput.Remove(0, _debugOutput.Length);
            _debugOutput.AppendFormat("{0}. Selecting from {1} actions.\n", mob.GetComponent<MobStateMachine>().CurrentState, behaviours.Count);
#endif

            for (int i = 0; i < actionCount; i++)
            {
                AIActionUtility highestResult = GetHighestConsiderationResultForAction(behaviours[i],
                   activeAction, previousAction, mob);

                _evaulationResults.Add(highestResult);
            }

            List<AIActionUtility> filteredList = FilterResultsList(_evaulationResults, 0.80f);

            if (filteredList.Count == 0)
            {
#if AI_LOGGING
                Debug.LogWarning(_debugOutput.ToString()); 
#endif
                throw new UnityException("No possible action for AI, something is wrong");
            }

#if AI_LOGGING
            _debugOutput.AppendLine("\nPossible actions to choose from: ");
            for (int i = 0; i < filteredList.Count; i++)
            {
                _debugOutput.Append(" " + filteredList[i].action.ToString());
            }
#endif


            return ChooseRandomAction(filteredList);
        }

        /// <summary>
        /// Takes a list of AIActionUtility objects and chooses one at random using the weights for each action
        /// as the odds of it happening against the total weight of all actions.
        /// </summary>
        /// <param name="actions">List of AIActionUtility objects to base selection on.</param>
        /// <returns>AIAction</returns>
        private AIAction ChooseRandomAction(List<AIActionUtility> actions)
        {
            //Calculate the total weights for all remaining actions to use for selecting a weighted random action
            float totalWeights = 0f;

            for (int i = 0; i < actions.Count; i++)
            {
                totalWeights += actions[i].weight * actions[i].multiplier;
            }

            //Select random action index
            float randomValue = Random.value * totalWeights;

            AIAction result = null;

            for (int i = 0; i < actions.Count; i++)
            {
                float weight = actions[i].weight * actions[i].multiplier;

                if (randomValue < weight)
                {
                    result = actions[i].action;
                    break;
                }

                randomValue -= weight;
            }

            if (result == null)
                result = actions[actions.Count - 1].action;

#if AI_LOGGING
            string actionSelected = string.Format("ACTION SELECTED: {0}\n", result.ToString());
            _debugOutput.Insert(0, actionSelected, 1);
            Debug.Log(_debugOutput.ToString());
#endif

            return result;
        }

        /// <summary>
        /// Evaluates all the considerations and returns a result with the highest weight and combined multiplier
        /// from all the considerations evaulated.
        /// </summary>
        /// <param name="considerations">List of AIConsiderations to be evaulated</param>
        /// <returns>AIConsiderationResult that contains the totals calulated for all the considerations.</returns>
        public AIActionUtility GetHighestConsiderationResultForAction(AIAction actionToEvaluate,
            AIAction current, AIAction previous, GameObject mob)
        {
            float maxWeight = 0f;
            float totalMultiplier = 1f;

#if AI_LOGGING
            _debugOutput.AppendLine("Action: " + actionToEvaluate.ToString());
#endif

            for (int j = 0; j < actionToEvaluate.considerations.Count; j++)
            {
                AIConsideration currentConsideration = actionToEvaluate.considerations[j];

                AIConsiderationResult result = currentConsideration.Evaluate(mob, actionToEvaluate);

#if AI_LOGGING
                _debugOutput.AppendFormat("\tConsideration: {0}, weight: {1}, multiplier {2}", currentConsideration.name, result.weight, result.multiplier);
#endif

                if (maxWeight < result.weight)
                    maxWeight = result.weight;

                totalMultiplier *= result.multiplier;

                //If any multipier is zero, this action will not be selected so stop evaluating considerations for it.
                if (totalMultiplier == 0)
                    break;
            }

            if (actionToEvaluate == current)
                maxWeight += actionToEvaluate.interia;

#if AI_LOGGING
            _debugOutput.AppendLine("\n\t\tHighest weight for action: " + maxWeight);
#endif

            return new AIActionUtility() { action = actionToEvaluate, weight = maxWeight, multiplier = totalMultiplier };
        }

        public List<AIActionUtility> FilterResultsList(List<AIActionUtility> results, float minPercent)
        {
            float maxWeight = 0f;
            for (int i = 0; i < results.Count; i++)
            {
                float ponderedWeight = results[i].weight * results[i].multiplier;
                if (ponderedWeight > maxWeight)
                    maxWeight = ponderedWeight;
            }
            float minWeightForConsideration = maxWeight * minPercent;

            _filterBuffer.Clear();

            for (int i = 0; i < results.Count; i++)
            {
                AIActionUtility actionUtility = results[i];

                if (actionUtility.multiplier != 0 && actionUtility.weight * actionUtility.multiplier > minWeightForConsideration)
                    _filterBuffer.Add(actionUtility);
            }

            return _filterBuffer;
        }
    }
}