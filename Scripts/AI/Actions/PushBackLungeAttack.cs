// Copyright Isto Inc.
using Isto.Core.Beings;
using Isto.Core.StateMachine;
using System;
using System.Collections;
using UnityEngine;
using Zenject;

namespace Isto.Core.AI
{
    public class PushBackLungeAttack : AIAction
    {
        // Public Variables

        public float damage = 20;
        public float impactForce = 50;
        public AnimationCurve attackMoveCurve = AnimationCurve.Linear(0, 0, 1, 1);
        public float lungeAttackSpeed = 3f;

        public float lungeDistance = 3;
        [Tooltip("How much +- the lunge distance will be randomly chosen")]
        public float lungeVariance = 2f;
        public float coolDown = 1f;

        [Header("Collision Checks")]
        [Tooltip("Checks that objects on these layers are not in the path of the lunge")]
        public LayerMask collisionLayers;
        [Tooltip("The layers that the lunge attack will cause damage to")]
        public LayerMask doesDamageToLayers;

        [Header("Effects")]
        [SerializeField] private ParticleSystem[] _smokeParticleSystems = default;
        [SerializeField] private LineRenderer _attackLine = default;

        public bool IsAttacking { get; private set; }

        // Private Vairables

        private AutomationPlayerController _player;

        private Health _mobHealth;
        private Vector3 _startPosition;
        private Vector3 _endPosition;
        private Vector3 _attackDirection;
        private bool _readyForNextAttack;
        private RaycastHit _hitBuffer;

        [Inject]
        public void Inject(AutomationPlayerController playerController)
        {
            _player = playerController;
        }

        private void Awake()
        {
            _mobHealth = GetComponent<Health>();
        }

        private void OnEnable()
        {
            _mobHealth.TookDamage += OnTookDamage;
            _mobHealth.Killed += OnKilled;

            if (_mobHealth is IStunnable stun)
                stun.Stunned += OnStunned;
        }

        private void OnStunned(object sender, HealthEventArgs e)
        {
            StopAllCoroutines();

            isInteruptable = true;
            isRunning = false;
        }

        private void OnDisable()
        {
            _mobHealth.TookDamage -= OnTookDamage;
            _mobHealth.Killed -= OnKilled;

            if (_mobHealth is IStunnable stun)
                stun.Stunned -= OnStunned;
        }

        // Collision Events

        public void OnCollisionEnter(Collision collision)
        {
            HandleCollision(collision.gameObject);
        }

        public void OnTriggerEnter(Collider other)
        {
            HandleCollision(other.gameObject);
        }

        private void HandleCollision(GameObject hitObject)
        {
            if (!isRunning || !IsAttacking)
                return;

            if (collisionLayers.IsInLayerMask(hitObject.layer) || hitObject.layer == Layers.PLAYER)
            {
                // Since we hit something, complete the attack
                StopAllCoroutines();

                StartCoroutine(CompleteAttackCoroutine());
            }

            // Allowing damage to be applied to either player or other mobs
            if (doesDamageToLayers.IsInLayerMask(hitObject.layer))
            {
                Health health = hitObject.GetComponentInParent<Health>();

                if (health != null)
                {
                    Vector3 damageForce = _attackDirection * impactForce;

                    health.TakeDamage(damage, damageForce, Health.DamageTypeEnum.PHYSICAL, Health.DamageSourceEnum.MOB);
                }
            }
        }

        // Event Handlers

        private void OnTookDamage(object sender, HealthEventArgs e)
        {
            StopAllCoroutines();

            if (isRunning)
                StartCoroutine(CompleteAttackCoroutine());
        }

        private void OnKilled(object sender, EventArgs args)
        {
            StopAllCoroutines();

            isInteruptable = true;
            isRunning = false;
        }

        // State Lifecycle methods

        public override void StartAction(MobStateMachine controller)
        {
            base.StartAction(controller);

            _controller.GroupController.RegisterAttack(this);

            StartAttack();
        }

        private void StartAttack()
        {
            IsAttacking = true;

            _readyForNextAttack = false;

            _startPosition = transform.position;

            _endPosition = GetEndPosition();

            _attackDirection = (_endPosition - _startPosition).normalized;

            _controller.StopMob();
            _controller.SetAnimationDirectionAndState(_attackDirection, AnimationController.AnimationType.pre_attack);

            if (_attackLine != null)
            {
                _attackLine.enabled = true;
                _attackLine.positionCount = 2;
                _attackLine.SetPosition(0, _startPosition);
                _attackLine.SetPosition(1, _endPosition);
            }

            EmitSmoke();

            isInteruptable = false;
        }

        private void EmitSmoke()
        {
            for (int i = 0; i < _smokeParticleSystems.Length; i++)
            {
                if (_smokeParticleSystems[i].gameObject.activeInHierarchy)
                    _smokeParticleSystems[i].Emit(40);
            }
        }

        public override void UpdateAction()
        {
            if (_readyForNextAttack)
            {
                StartAttack();
            }

            CheckMobImpact();
        }

        private bool CheckMobImpact()
        {
            if (!IsAttacking)
                return false;

            // Rigidbody checked to see if force was applied to the rigidbody and stop attack
            Rigidbody body = GetComponent<Rigidbody>();

            // If rigid body has velocity, it had a force applied, so stop attack
            if (Mathf.Abs(body.linearVelocity.x) > 0.1f || Mathf.Abs(body.linearVelocity.z) > 0.1f)
            {
                StopAllCoroutines();
                StartCoroutine(CompleteAttackCoroutine());

                return true;
            }

            return false;
        }

        public override void StopAction()
        {
            _controller.GroupController.UnregisterAttack(this);

            HideAttackLine();

            base.StopAction();
        }

        private void HideAttackLine()
        {
            if (_attackLine != null)
            {
                _attackLine.enabled = false;
                _attackLine.positionCount = 0;
            }
        }

        // Called from Animation event
        private void PreAttackComplete()
        {
            if (!CheckMobImpact())
            {
                _controller.SetAnimationDirectionAndState(_attackDirection, AnimationController.AnimationType.attack);

                StartCoroutine(LungeForward());
            }
        }

        private IEnumerator LungeForward()
        {
            float attackTimer = 0f;
            float totalattackTime = Vector3.Distance(_startPosition, _endPosition) / lungeAttackSpeed;

            while (attackTimer < totalattackTime)
            {
                attackTimer += Time.deltaTime;

                _controller.agent.Warp(Vector3.Lerp(_startPosition, _endPosition, attackMoveCurve.Evaluate(attackTimer / totalattackTime)));

                yield return null;
            }

            yield return CompleteAttackCoroutine();
        }

        private IEnumerator CompleteAttackCoroutine()
        {
            _controller.StopMob(false);
            _controller.SetAnimationDirectionAndState(_attackDirection, AnimationController.AnimationType.post_attack);

            IsAttacking = false;

            HideAttackLine();

            yield return new WaitForSeconds(0.25f);

            float timer = 0f;

            while (timer < coolDown)
            {
                // Look at player during cooldown
                Vector3 dirToPlayer = _player.transform.position - transform.position;
                _controller.SetAnimationDirectionAndState(dirToPlayer, AnimationController.AnimationType.idle);

                timer += Time.deltaTime;

                yield return null;
            }

            _readyForNextAttack = true;
            isInteruptable = true;

            // Adding one extra frame to give the AI a chance to choose a different action before attacking again.
            yield return null;
        }

        /// <summary>
        /// Finds the end position for the lunge attack.  Performs a Raycast to make sure nothing is between the mob and the player.
        /// </summary>
        /// <returns>World Position where the lunge should end.</returns>
        private Vector3 GetEndPosition()
        {
            // Perform raycast to make sure there isn't something between the mob and the player
            Ray towardsPlayer = new Ray(transform.position, _player.transform.position - transform.position);

            float totalLungeDistance = lungeDistance + UnityEngine.Random.Range(-lungeVariance, lungeVariance);

            // If something was hit, return that point instead
            if (Physics.Raycast(towardsPlayer, out _hitBuffer, totalLungeDistance, collisionLayers))
            {
                Vector3 hitPoint = _hitBuffer.point;
                hitPoint.y = 0f;

                return hitPoint;
            }

            return transform.position + (towardsPlayer.direction * totalLungeDistance);
        }
    }
}

