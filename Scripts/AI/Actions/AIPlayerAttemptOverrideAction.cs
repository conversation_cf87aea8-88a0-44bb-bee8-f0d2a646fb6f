// Copyright Isto Inc.
using Isto.Core.StateMachine;
using UnityEngine;

namespace Isto.Core.AI
{
    public class AIPlayerAttemptOverrideAction : AIAction
    {
        // Public Variables

        public float overrideTime;

        // Private Variables

        private float _overrideTimer;

        // Lifecycle Events

        public override void StartAction(MobStateMachine controller)
        {
            base.StartAction(controller);

            _controller.SetOverrideSliderActive(true);
            _controller.SetOverrideSliderValue(0f);

            // This action will run till completion
            isInteruptable = false;
        }

        public override void UpdateAction()
        {
            _overrideTimer += Time.deltaTime;

            if (_overrideTimer > overrideTime)
                isInteruptable = true;
            else
                _controller.SetOverrideSliderValue(_overrideTimer / overrideTime);
        }

        public override void StopAction()
        {
            _controller.SetOverrideSliderActive(false);

            base.StopAction();
        }
    }
}