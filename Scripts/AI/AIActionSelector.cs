// Copyright Isto Inc.
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.AI
{
    /// <summary>
    /// Abstract class for selecting which action to perform for an AI.  Different Selectors could be used, especially in testing so
    /// this class allows creating various selectors.  SelectNextAction must be implemented which takes a list of AIActions and chooses the
    /// next action to perform.
    /// </summary>
    public abstract class AIActionSelector
    {
        public abstract AIAction SelectNextAction(
            List<AIAction> behaviours, AIAction currentAction, AIAction previousAction, GameObject mob);
    }

    public struct AIActionUtility
    {
        public AIAction action;
        public float weight;
        public float multiplier;
    }

}