// Copyright Isto Inc.
using Isto.Core.StateMachine;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.AI
{
    /// <summary>
    /// Abstract class for any action that is performed by the AI.  Contains a list of considerations which determine when this action is
    /// chosen to run.  Implementing classes must implement the UpdateAction which is called every frame on the action when it is active.
    /// </summary>
	public abstract class AIAction : MonoBehaviour
    {
        [Header("Properties")]
        public Sprite activeIcon;
        public bool isInteruptable;
        public bool isRunning;
        [Tooltip("Bonus given to this actions weighting if it is currently running.")]
        public float interia = 0f;

        [Header("Considerations")]
        public List<AIConsideration> considerations = new List<AIConsideration>();

        public float LastRunTime { get { return _lastRunTime; } }

        protected MobStateMachine _controller;
        protected float _lastRunTime = 0f;

        public virtual void StartAction(MobStateMachine controller)
        {
            isRunning = true;

            if (controller.actionIcon != null)
            {
                controller.actionIcon.sprite = activeIcon;
            }

            _controller = controller;
        }

        public virtual void StopAction()
        {
            _controller.actionIcon.sprite = null;

            isRunning = false;

            _lastRunTime = Time.time;
        }

        public abstract void UpdateAction();

    }
}