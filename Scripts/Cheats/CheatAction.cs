// Copyright Isto Inc.
using System;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Cheats
{
    /// <summary>
    /// Abstract class for developer console action.  Concrete class will handle what happens when it's activated, updated every frame and deactivated.
    /// </summary>
    public abstract class CheatAction : ScriptableObject
    {
        public string ResultMessage { get; private set; }

        public List<string> aliases;

        public KeyCode hotkey;

        public int numberOfParameters = 0;
        public bool continuous = false;
        public bool closeMenuAfterActivation = false;

        protected DiContainer _projectContextContainer;

        /// <summary>
        /// Injection is supported on all CheatActions but beware because most gameplay elements you might want to
        /// affect won't be part of the scope of the ProjectContext so they still need to be found later.
        /// This is because they are injected from CheatSettings or Cheats.
        /// But the new CheatMenuState should be part of the essentials and get injected with that context.
        /// Perhaps we could give the essentials DiContainer to the actions and let them resolve references at runtime.
        /// </summary>
        [Inject]
        private void Inject(DiContainer container)
        {
            _projectContextContainer = container;
        }

        /// <summary>
        /// Called when the player submits the command.
        /// After this is called, if the action is continuous, OnUpdate will be invoked each frame,
        /// and the next activation will be replaced with Deactivate, ignoring any parameters.
        /// Throws exceptions.
        /// </summary>
        /// <exception cref="UnityException">The error message</exception>
        public abstract void Activate(params object[] args);

        /// <summary>
        /// Called every frame after activation if the command is continuous.
        /// </summary>
        public abstract void OnUpdate();

        /// <summary>
        /// Called instead of Activate if the command is continuous and currently running.
        /// Throws exceptions.
        /// </summary>
        /// <exception cref="UnityException">The error message</exception>
        public abstract void Deactivate();

        /// <summary>
        /// Attempts to predict a valid list of parameters for this action.
        /// </summary>
        /// <param name="inputs">The current parsed array of parameters</param>
        /// <param name="output">The complete list of predicted parameters as a single string.</param>
        /// <returns>true if the autocompletion was successful</returns>
        public virtual bool TryAutoCompleteParameters(string[] inputs, out string output)
        {
            output = "";
            return false;
        }

        protected Vector3? ArgToDirection(object arg)
        {
            Vector3? value = null;
            string argAsString = arg.ToString();

            if (UnityUtils.TryParseToVector3(argAsString, out Vector3 argAsVector))
            {
                value = argAsVector;
            }
            else
            {
                switch (argAsString)
                {
                    case "NorthEast":
                    case "northeast":
                    case "NE":
                    case "ne":
                    case "Forward":
                    case "forward":
                    case "F":
                    case "f":
                        value = Vector3.forward;
                        break;
                    case "SouthWest":
                    case "southwest":
                    case "SW":
                    case "sw":
                    case "Back":
                    case "back":
                    case "B":
                    case "b":
                        value = Vector3.back;
                        break;
                    case "SouthEast":
                    case "southeast":
                    case "SE":
                    case "se":
                    case "Right":
                    case "right":
                    case "R":
                    case "r":
                        value = Vector3.right;
                        break;
                    case "NorthWest":
                    case "northwest":
                    case "NW":
                    case "nw":
                    case "Left":
                    case "left":
                    case "L":
                    case "l":
                        value = Vector3.left;
                        break;
                    case "East":
                    case "east":
                    case "E":
                    case "e":
                        value = Vector3.forward + Vector3.right;
                        break;
                    case "North":
                    case "north":
                    case "N":
                    case "n":
                        value = Vector3.forward + Vector3.left;
                        break;
                    case "South":
                    case "south":
                    case "S":
                    case "s":
                        value = Vector3.back + Vector3.right;
                        break;
                    case "West":
                    case "west":
                    case "W":
                    case "w":
                        value = Vector3.back + Vector3.left;
                        break;
                    case "Up":
                    case "up":
                    case "U":
                    case "u":
                        value = Vector3.up;
                        break;
                    case "Down":
                    case "down":
                    case "D":
                    case "d":
                        value = Vector3.down;
                        break;
                    default:
                        Debug.LogWarning($"DeveloperConsoleAction.ArgToDirection: can't convert arg \"{arg}\"");
                        break;
                }
            }

            return value;
        }

        protected bool TryAutoCompleteDirection(string[] inputs, out string output)
        {
            output = "";

            if (inputs.Length != 1)
                return false;

            // We try to be as friendly as possible for mistypes.
            if (inputs[0].StartsWith("NorthE", StringComparison.CurrentCultureIgnoreCase)
                || inputs[0].StartsWith("NE", StringComparison.CurrentCultureIgnoreCase))
            {
                output = "Northeast";
                return true;
            }
            else if (inputs[0].StartsWith("NorthW", StringComparison.CurrentCultureIgnoreCase)
                || inputs[0].StartsWith("NW", StringComparison.CurrentCultureIgnoreCase))
            {
                output = "Northwest";
                return true;
            }
            else if (inputs[0].StartsWith("SouthE", StringComparison.CurrentCultureIgnoreCase)
                || inputs[0].StartsWith("SE", StringComparison.CurrentCultureIgnoreCase))
            {
                output = "Southeast";
                return true;
            }
            else if (inputs[0].StartsWith("SouthW", StringComparison.CurrentCultureIgnoreCase)
                || inputs[0].StartsWith("SE", StringComparison.CurrentCultureIgnoreCase))
            {
                output = "Southwest";
                return true;
            }
            else if (inputs[0].StartsWith("N", StringComparison.CurrentCultureIgnoreCase))
            {
                output = "North";
                return true;
            }
            else if (inputs[0].StartsWith("S", StringComparison.CurrentCultureIgnoreCase))
            {
                output = "South";
                return true;
            }
            else if (inputs[0].StartsWith("E", StringComparison.CurrentCultureIgnoreCase))
            {
                output = "East";
                return true;
            }
            else if (inputs[0].StartsWith("W", StringComparison.CurrentCultureIgnoreCase))
            {
                output = "West";
                return true;
            }
            else if (inputs[0].StartsWith("U", StringComparison.CurrentCultureIgnoreCase))
            {
                output = "Up";
                return true;
            }
            else if (inputs[0].StartsWith("D", StringComparison.CurrentCultureIgnoreCase))
            {
                output = "Down";
                return true;
            }
            else if (inputs[0].StartsWith("L", StringComparison.CurrentCultureIgnoreCase))
            {
                output = "Left";
                return true;
            }
            else if (inputs[0].StartsWith("R", StringComparison.CurrentCultureIgnoreCase))
            {
                output = "Right";
                return true;
            }
            else if (inputs[0].StartsWith("F", StringComparison.CurrentCultureIgnoreCase))
            {
                output = "Forward";
                return true;
            }
            else if (inputs[0].StartsWith("B", StringComparison.CurrentCultureIgnoreCase))
            {
                output = "Back";
                return true;
            }

            return false;
        }

        protected bool? ArgToBool(object arg)
        {
            bool? value = null;
            string argAsString = arg.ToString();
            if (bool.TryParse(argAsString, out bool argAsBool))
            {
                value = argAsBool;
            }
            else if (int.TryParse(argAsString, out int argAsInt))
            {
                value = Convert.ToBoolean(argAsInt);
            }
            else if (argAsString.Equals("T", StringComparison.CurrentCultureIgnoreCase))
            {
                value = true;
            }
            else if (argAsString.Equals("F", StringComparison.CurrentCultureIgnoreCase))
            {
                value = false;
            }
            else
            {
                Debug.LogWarning($"DeveloperConsoleAction.ArgToBool: can't convert arg \"{arg}\"");
            }

            return value;
        }

        protected bool TryAutoCompleteBoolean(string[] inputs, out string output)
        {
            output = "";

            if (inputs.Length != 1)
                return false;

            // We try to be as friendly as possible for mistypes.
            // Also, 0 is valid input so don't ignore it. For now, let's "complete" it to reinforce validity
            if (inputs[0].StartsWith("F", StringComparison.CurrentCultureIgnoreCase)
            || (int.TryParse(inputs[0].ToString(), out int inputAsInt) && inputAsInt == 0))
            {
                output = "False";
            }
            // Anything else probably boils down to trying to write something that evaluates to True
            // This includes autocompleting from ""
            else
            {
                output = "True";
            }

            return true;
        }

        protected bool TryAutoCompleteFromList(string[] inputs, List<string> validOutputs, out string output)
        {
            output = "";

            if (inputs.Length != 1)
                return false;

            string partialItem = inputs[0];

            for (int i = 0; i < validOutputs.Count; i++)
            {
                if (validOutputs[i].Equals(partialItem, StringComparison.CurrentCultureIgnoreCase))
                {
                    // If input is already valid then user is looking to cycle options
                    i++;
                    if (i > validOutputs.Count - 1)
                    {
                        i = 0;
                    }
                    output = validOutputs[i];
                    break;
                }
                else if (validOutputs[i].StartsWith(partialItem, StringComparison.CurrentCultureIgnoreCase))
                {
                    // If there is a partial match then we can make it complete
                    output = validOutputs[i];
                    break;
                }
            }

            if (output.Contains(" "))
            {
                output = '"' + output + '"';
            }

            return !String.IsNullOrEmpty(output);
        }
    }
}