// Copyright Isto Inc.
using System;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Cheats
{
    /// <summary>
    /// Scriptable object that holds the parameters for default menus/skins/sprites to show
    /// </summary>
    [CreateAssetMenu(fileName = "Scriptables/New Cheats Data", menuName = "Scriptables/New Cheats Data")]
    public class CheatCodeSetup : ScriptableObject
    {
        [Multiline]
        public string comment = "This is for defining cheat codes a user can enter during the title screen.";

        // Consider refactoring this to use the same action data type as dev console commands so we can reuse the action
        // logic between the two systems.
        public enum CheatEffect { DoNothing, ToggleCatrioTheme }

        [Serializable]
        public class KeyboardCheatData
        {
            public string internalName;
            public CheatEffect effect;
            //public List<CheatAction> cheatEffects;
            public List<KeyCode> buttonPresses;
        }

        // Public Variables

        public List<KeyboardCheatData> keyboardCheats;
    }
}