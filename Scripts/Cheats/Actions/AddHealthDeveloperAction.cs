// Copyright Isto Inc.

using Isto.Core.Beings;
using UnityEngine;

namespace Isto.Core.Cheats
{
    [CreateAssetMenu(fileName = "New Health Restore Action", menuName = "Scriptables/Dev Mode Actions/Player Health Restore Action")]
    public class AddHealthDeveloperAction : CheatAction
    {
        public float amount = 50f;

        public override void Activate(params object[] args)
        {
            PlayerHealth health = FindObjectOfType<PlayerHealth>();

            if (health != null)
            {
                health.Heal(amount);

                Debug.LogFormat("Adding {0} player health", amount);
            }
            else
            {
                throw new UnityException("Cannot find Player health in scene.  Add Health action can only be performed in a running game");
            }
        }

        public override void Deactivate()
        {
            // Not used
        }

        public override void OnUpdate()
        {
            // Not used
        }
    }
}