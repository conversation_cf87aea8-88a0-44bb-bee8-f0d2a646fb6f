// Copyright Isto Inc.

using Isto.Core.Research;
using UnityEngine;

namespace Isto.Core.Cheats
{
    [CreateAssetMenu(fileName = "New Set Research Tree Complete Action", menuName = "Scriptables/Dev Mode Actions/Set Research Tree Complete Action")]
    public class SetResearchTreeCompleteDeveloperAction : CheatAction
    {
        public override void Activate(params object[] args)
        {
            bool? active = ArgToBool(args[0]);
            if (active == null)
            {
                throw new UnityException("Argument error. Useage: SetResearchTreeComplete True");
            }

            var research = GameObject.FindObjectOfType<PlayerResearchModule>();
            if (active.Value)
            {
                research.CompleteAllResearch();
            }
            else
            {
                research.DeleteAllResearch();
            }

        }

        public override void Deactivate()
        {
            // Not used
        }

        public override void OnUpdate()
        {
            // Not used
        }

        public override bool TryAutoCompleteParameters(string[] inputs, out string output)
        {
            return TryAutoCompleteBoolean(inputs, out output);
        }
    }
}