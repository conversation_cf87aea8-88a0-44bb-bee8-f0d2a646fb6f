// Copyright Isto Inc.

using Isto.Core.Research;
using UnityEngine;

namespace Isto.Core.Cheats
{
    [CreateAssetMenu(fileName = "New Set Research Time Action", menuName = "Scriptables/Dev Mode Actions/Set Research Time Action")]
    public class SetResearchTimeDeveloperAction : CheatAction
    {
        public static readonly float MIN_FUNCTIONAL_RESEARCH_TIME = 0.01f;

        public override void Activate(params object[] args)
        {
            if (float.TryParse(args[0].ToString(), out float time))
            {
                if (time < MIN_FUNCTIONAL_RESEARCH_TIME)
                    time = MIN_FUNCTIONAL_RESEARCH_TIME;

                var research = GameObject.FindObjectOfType<PlayerResearchModule>();
                research.ResearchTimeOverride = time;
            }
            else
            {
                throw new UnityException("Argument error. Useage: SetResearchTime 300.0");
            }
        }

        public override void Deactivate()
        {
            // Not used
        }

        public override void OnUpdate()
        {
            // Not used
        }
    }
}