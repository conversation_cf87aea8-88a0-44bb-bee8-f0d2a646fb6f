// Copyright Isto Inc.

using Isto.Core.Skins;
using Isto.Core.Themes;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Isto.Core.Cheats
{
    [CreateAssetMenu(fileName = "New Set Theme Action", menuName = "Scriptables/Dev Mode Actions/Set Theme Action")]
    public class SetThemeDeveloperAction : CheatAction
    {
        public ThemeSetup _themeSetup;
        public MasterSkinList _masterList;

        public override void Activate(params object[] args)
        {
            SetTheme((string)args[0]);
        }

        public override void Deactivate()
        {
            // Not used
        }

        public override void OnUpdate()
        {
            // Not used
        }

        public override bool TryAutoCompleteParameters(string[] inputs, out string output)
        {
            List<string> validOutputs = _themeSetup.themes.Select(x => x.internalName).ToList();
            return TryAutoCompleteFromList(inputs, validOutputs, out output);
        }

        private void SetTheme(string themeName)
        {
            ThemeManager themeManager = GameObject.FindObjectOfType<ThemeManager>();
            if (themeManager == null)
                throw new UnityException("SetThemeDeveloperAction could not find ThemeManager object in scene");

            ThemeSetup themes = Resources.Load<ThemeSetup>("Themes");
            if (themes == null)
                throw new UnityException("SetThemeDeveloperAction could not find ThemeSetup object at Resources/Themes");

            ThemeDefinition themeDef = themes.GetTheme(themeName);
            if (themeDef == null)
                throw new UnityException($"Argument error. No theme found with name \"{themeName}\". Useage example: SetTheme Normal");

            themeManager.CurrentTheme = themeDef;
        }
    }
}