// Copyright Isto Inc.

using UnityEngine;
using Rewired.Integration.UnityUI;

namespace Isto.Core.Cheats
{
    [CreateAssetMenu(fileName = "New UI Repeat Rate Override Action", menuName = "Scriptables/Dev Mode Actions/UI Repeat Rate Override Action")]
    public class SetUIRepeatRate : CheatAction
    {
        public override void Activate(params object[] args)
        {
            RewiredStandaloneInputModule inputManager = FindObjectOfType<RewiredStandaloneInputModule>();

            if (float.TryParse(args[0].ToString(), out float actions))
                inputManager.inputActionsPerSecond = actions;

            if (float.TryParse(args[1].ToString(), out float repeatDelay))
                inputManager.repeatDelay = repeatDelay;        
        }

        public override void Deactivate()
        {
            throw new System.NotImplementedException();
        }

        public override void OnUpdate()
        {
            throw new System.NotImplementedException();
        }
	}
}