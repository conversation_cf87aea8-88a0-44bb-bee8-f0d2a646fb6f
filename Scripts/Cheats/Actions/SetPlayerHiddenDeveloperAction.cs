// Copyright Isto Inc.

using Isto.Core.Cameras;
using UnityEngine;

namespace Isto.Core.Cheats
{
    [CreateAssetMenu(fileName = "New Set Player Hidden Action", menuName = "Scriptables/Dev Mode Actions/Set Player Hidden Action")]
    public class SetPlayerHiddenDeveloperAction : CheatAction
    {
        private CameraController _camera;

        //Note: known issue where the player shows a bit of arm or leg while running up or down
        public override void Activate(params object[] args)
        {
            bool? hidden = ArgToBool(args[0]);
            if (hidden == null)
            {
                throw new UnityException("Argument error. Useage: SetPlayerHidden True");
            }

            _camera = GameObject.FindObjectOfType<CameraController>();
            _camera.HideLayers(hidden.Value);
        }

        public override void Deactivate()
        {
            // Not used
        }

        public override void OnUpdate()
        {
            // Not used
        }

        public override bool TryAutoCompleteParameters(string[] inputs, out string output)
        {
            return TryAutoCompleteBoolean(inputs, out output);
        }
    }
}