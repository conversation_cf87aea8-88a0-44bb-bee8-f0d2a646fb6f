// Copyright Isto Inc.

using Isto.Core.Achievements;
using Isto.Core.Enums;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Cheats
{
    [CreateAssetMenu(fileName = "New Unlock Achievement Action", menuName = "Scriptables/Dev Mode Actions/Player Unlock Achievement Action")]
    public class UnlockAchievementDeveloperAction : CheatAction
    {
        public override void Activate(params object[] args)
        {
            string achievementName = args[0].ToString();

            if (!IAchievements.CoreAchievementsEnum.TryParse(achievementName, ignoreCase: true, out Int32Enum<IAchievements.CoreAchievementsEnum> achievement))
            {
                throw new UnityException($"Achievement {achievementName} could not be parsed into a IAchievements.CoreAchievementsEnum value");
            }

            ProjectContext context = GameObject.FindObjectOfType<ProjectContext>();
            DiContainer container = context.Container;
            IAchievements achievements = container.Resolve<IAchievements>();

            achievements.TriggerAchievement(achievement as IAchievements.CoreAchievementsEnum);
        }

        public override void Deactivate()
        {
        }

        public override void OnUpdate()
        {
        }

        public override bool TryAutoCompleteParameters(string[] inputs, out string output)
        {
            List<string> validOutputs = new List<string>(IAchievements.CoreAchievementsEnum.GetAllNames());
            return TryAutoCompleteFromList(inputs, validOutputs, out output);
        }
    }
}