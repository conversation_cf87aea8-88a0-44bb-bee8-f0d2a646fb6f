// Copyright Isto Inc.

using Isto.Core.Beings;
using UnityEngine;

namespace Isto.Core.Cheats
{
    [CreateAssetMenu(fileName = "New Death Action", menuName = "Scriptables/Dev Mode Actions/Player Death Action")]
    public class DeathDeveloperAction : CheatAction
    {
        private float killingAmount = 120f;

        public override void Activate(params object[] args)
        {
            PlayerHealth health = FindObjectOfType<PlayerHealth>();

            if (health != null)
            {
                health.TakeDamage(killingAmount, Health.DamageTypeEnum.PHYSICAL, Health.DamageSourceEnum.MOB);

                Debug.LogFormat("Dealing {0} damage to player", killingAmount);
            }
            else
            {
                throw new UnityException("Cannot find Player health in scene. Death action can only be performed in a running game");
            }
        }

        public override void Deactivate()
        {
            // Not used
        }

        public override void OnUpdate()
        {
            // Not used
        }
    }
}