// Copyright Isto Inc.

using Isto.Core.Beings;
using Isto.Core.Items;
using System;
using System.Linq;
using UnityEngine;
using Zenject;

namespace Isto.Core.Cheats
{
    [CreateAssetMenu(fileName = "New Get Item Action", menuName = "Scriptables/Dev Mode Actions/Player Get Item Action")]
    public class GetItemDeveloperAction : CheatAction
    {
        private MasterItemList _masterList;
        private IOrderedEnumerable<Item> _orderedMasterList;

        private PlayerInventory _inventory;

        [Inject]
        public void Inject(MasterItemList masterList)
        {
            _masterList = masterList;

            _orderedMasterList = _masterList.items.OrderBy(item => item.itemID);
        }

        public override void Activate(params object[] args)
        {
            string itemName = args[0].ToString().ToLower();

            _inventory = FindObjectOfType<PlayerInventory>();

            CoreItem item = _masterList.GetItemByID<CoreItem>(itemName);

            // If null, try getting by Name
            if (item == null)
                item = _masterList.GetItemByName<CoreItem>(itemName);

            if (item != null)
            {
                if (int.TryParse(args[1].ToString(), out int count))
                {
                    _inventory.Add(item, count);
                }
            }
            else
            {
                throw new UnityException($"Item {itemName} not found in master item list");
            }
        }

        public override void Deactivate()
        {
        }

        public override void OnUpdate()
        {
        }

        public override bool TryAutoCompleteParameters(string[] inputs, out string output)
        {
            output = "";

            if (inputs.Length != 1)
                return false;

            for (int i = 0; i < _orderedMasterList.Count(); i++)
            {
                Item nextItem = _orderedMasterList.ElementAt(i);

                if (nextItem.itemID.Equals(inputs[0], StringComparison.CurrentCultureIgnoreCase) && i + 1 < _masterList.items.Count)
                {
                    output = _orderedMasterList.ElementAt(i + 1).itemID;
                    return true;
                }

                if (nextItem.itemID.StartsWith(inputs[0], StringComparison.CurrentCultureIgnoreCase))
                {
                    output = nextItem.itemID;
                    return true;
                }
            }

            return false;
        }
    }
}