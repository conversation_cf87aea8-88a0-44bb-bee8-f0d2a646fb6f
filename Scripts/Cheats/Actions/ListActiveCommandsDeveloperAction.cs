// Copyright Isto Inc.

using Isto.Core.UI;
using UnityEngine;

namespace Isto.Core.Cheats
{
    [CreateAssetMenu(fileName = "New List Active Commands Action", menuName = "Scriptables/Dev Mode Actions/List Active Commands Action")]
    public class ListActiveCommandsDeveloperAction : CheatAction
    {
        public override void Activate(params object[] args)
        {
            var console = GameObject.FindObjectOfType<CheatMenuState>();
            console.ShowAllActiveCommands();
        }

        public override void Deactivate()
        {
            // Not used
        }

        public override void OnUpdate()
        {
            // Not used
        }
    }
}