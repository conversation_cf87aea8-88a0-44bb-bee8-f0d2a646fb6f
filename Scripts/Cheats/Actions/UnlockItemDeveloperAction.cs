// Copyright Isto Inc.

using Isto.Core.Beings;
using Isto.Core.Items;
using System;
using UnityEngine;
using Zenject;

namespace Isto.Core.Cheats
{
    [CreateAssetMenu(fileName = "New Unlock Item Action", menuName = "Scriptables/Dev Mode Actions/Player Unlock Item Action")]
    public class UnlockItemDeveloperAction : CheatAction
    {
        [Inject] private MasterItemList _masterList;
        private PlayerProgress _unlocks;

        public override void Activate(params object[] args)
        {
            string itemName = args[0].ToString();

            // _masterList = FindObjectOfType<EssentialsSceneInstaller>().itemList;
            _unlocks = FindObjectOfType<PlayerProgress>();

            CoreItem item = _masterList.GetItemByID<CoreItem>(itemName);

            // If null, try getting by Name
            if (item == null)
                item = _masterList.GetItemByName<CoreItem>(itemName);

            if (item != null)
            {
                _unlocks.UnlockItem(item);
            }
            else
            {
                throw new UnityException($"Item {itemName} not found in master item list");
            }
        }

        public override void Deactivate()
        {
        }

        public override void OnUpdate()
        {
        }

        public override bool TryAutoCompleteParameters(string[] inputs, out string output)
        {
            output = "";

            if (inputs.Length != 1)
                return false;

            for (int i = 0; i < _masterList.items.Count; i++)
            {
                Item nextItem = _masterList.items[i];

                if (nextItem.itemID.Equals(inputs[0], StringComparison.CurrentCultureIgnoreCase) && i + 1 < _masterList.items.Count)
                {
                    output = _masterList.items[i + 1].itemID;
                    return true;
                }

                if (nextItem.itemID.StartsWith(inputs[0], StringComparison.CurrentCultureIgnoreCase))
                {
                    output = nextItem.itemID;
                    return true;
                }
            }

            return false;
        }
    }
}