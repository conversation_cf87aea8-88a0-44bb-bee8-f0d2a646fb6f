// Copyright Isto Inc.

using Rewired;
using UnityEngine;

namespace Isto.Core.Cheats
{
    [CreateAssetMenu(fileName = "New Joystick Sensitivity Override Action", menuName = "Scriptables/Dev Mode Actions/Joystick Sensitivity Override Action")]
    public class SetJoystickSensitivity : CheatAction
    {
        public override void Activate(params object[] args)
        {
            Player player = ReInput.players.GetPlayer(0);

            InputBehavior behavior = player.controllers.maps.GetInputBehavior("UI Behavior");

            if(float.TryParse(args[0].ToString(), out float sense))
            {
                behavior.joystickAxisSensitivity = sense;
            }
        }

        public override void Deactivate()
        {
            
        }

        public override void OnUpdate()
        {
            
        }
    }
}