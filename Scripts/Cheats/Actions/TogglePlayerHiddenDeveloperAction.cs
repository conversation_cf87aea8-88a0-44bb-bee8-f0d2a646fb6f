// Copyright Isto Inc.

using Isto.Core.Cameras;
using UnityEngine;

namespace Isto.Core.Cheats
{
    [CreateAssetMenu(fileName = "New Toggle Player Hidden Action", menuName = "Scriptables/Dev Mode Actions/Toggle Player Hidden Action")]
    public class TogglePlayerHiddenDeveloperAction : CheatAction
    {
        private CameraController _camera;

        //Note: known issue where the player shows a bit of arm or leg while running up or down
        public override void Activate(params object[] args)
        {
            _camera = GameObject.FindObjectOfType<CameraController>();
            _camera.HideLayers(true);
        }

        public override void Deactivate()
        {
            _camera.HideLayers(false);
        }

        public override void OnUpdate()
        {
            // Not used
        }
    }
}