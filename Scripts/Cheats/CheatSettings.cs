// Copyright Isto Inc.

using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Zenject;

namespace Isto.Core.Cheats
{
    [CreateAssetMenu(fileName = "New CheatSettings", menuName = "Scriptables/New Cheat Settings")]
    public class CheatSettings : ScriptableObject
    {

        // UNITY HOOKUP
        [Header("Global Setting - For Build Process")]
        [SerializeField]
        private bool _cheatsEnabled = true;

        [<PERSON><PERSON>("Startup Commands")]
        public List<CheatAction> startUpActions;

        [Header("Possible Commands")] // These remain accessible to players in the release build
        public List<CheatAction> playerCommands;

        [Header("Possible Debug-Only Commands")] // These are not available in a release build
        public List<CheatAction> debugCommands;

        [Header("Possible KeyCode Commands")]
        public bool disableAllKeyCodeCommands = true;
        public List<CheatAction> keyCodeCommands;

        [<PERSON><PERSON>("Garbage Collect Inducer")]
        public bool showForceGCTool = false;

        // OTHER FIELDS


        // PROPERTIES

        public bool CheatsEnabled
        {
            get { return _cheatsEnabled; }
            set { _cheatsEnabled = value; }
        }

        // INJECTION

        [Inject]
        private void Inject(DiContainer container)
        {
            List<CheatAction> actions = new List<CheatAction>();
            actions = actions.Union(startUpActions).Union(playerCommands).Union(debugCommands).Union(keyCodeCommands).ToList();

            foreach (CheatAction action in actions)
            {
                container.Inject(action);
            }
        }


        // ACCESSORS


        // OTHER METHODS

    }
}