// Copyright Isto Inc.

using Isto.Core.Game;
using Isto.Core.Inputs;
using Isto.Core.UI;
using UnityEngine;
using Zenject;

namespace Isto.Core.Cheats
{
    /// <summary>
    /// Handles opening and closing of the Developer console.
    /// Part of the same prefab at the moment, however we should eventually split them up.
    /// </summary>
	public class CheatMenuLoader : MonoBehaviour
    {
        private float _spamTimer;


        // Injection

        private IControls _controls;
        private GameState _gameState;
        private SimpleGameMenuStateMachine _menuStateMachine;
        private CheatMenuState _cheatMenu;
        private CheatSettings _cheatSettings;

        [Inject]
        public void Inject(IControls controls, GameState gameState, SimpleGameMenuStateMachine menuStateMachine,
            CheatMenuState cheatMenu, CheatSettings cheatSettings)
        {
            _controls = controls;
            _gameState = gameState;
            _menuStateMachine = menuStateMachine;
            _cheatMenu = cheatMenu;
            _cheatSettings = cheatSettings;
        }


        // Lifecycle Events

        private void Awake()
        {
        }

        /// <summary>
        /// Checks for user input to open/close the dev console and switches the controls to the correct mode.
        /// </summary>
        void Update()
        {
            if (!_cheatSettings.CheatsEnabled)
                return;

            bool cheatsAllowedByGameMode = _gameState.CurrentGameMode == null || _gameState.CurrentGameMode.DeveloperConsoleEnabled;

            if (!cheatsAllowedByGameMode)
                return;

            bool cheatsAllowedByUser = PlayerPrefs.GetInt(UISettingsGameplaySubState.CHEAT_MENU_PLAYER_PREFS_KEY, defaultValue: 0) == 1; // default is off

            if (!cheatsAllowedByUser)
                return;

            _spamTimer += Time.deltaTime;
            // Note that requesting the state of this action doesn't do anything if the action is not currently
            // enabled as part of an active category - currently TOGGLEDEVCONSOLE is part of Development?
            // In Rewired it didn't look like it, but not enabling Development does stop this action from working...
            bool consoleButtonDown = _controls.GetButtonDown(UserActions.TOGGLEDEVCONSOLE);
            if (_spamTimer > Constants.BUTTON_SPAM_DELAY && consoleButtonDown)
            {
                if (_cheatMenu.IsOpen)
                {
                    _menuStateMachine.CloseMenu();
                }
                else
                {
                    _menuStateMachine.OpenMenu(GameMenusEnum.CHEATER);
                }

                _spamTimer = 0f;
            }
        }
    }
}