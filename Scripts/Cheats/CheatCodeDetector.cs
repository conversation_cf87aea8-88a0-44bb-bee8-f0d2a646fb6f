// Copyright Isto Inc.
using Isto.Core.Analytics;
using Isto.Core.Game;
using Isto.Core.Themes;
using Isto.Core.UI;
using System.Collections.Generic;
using System.Text;
using UnityEngine;
using UnityEngine.Serialization;
using Zenject;

namespace Isto.Core.Cheats
{
    public class CheatCodeDetector : MonoBehaviour
    {
        public static bool CheatsBlocked = false;

        [FormerlySerializedAs("titleMenuCheats")]
        public CheatCodeSetup cheatsData;

        public bool verbose = false;
        public bool runInTitleMenu = true;

        private List<KeyCode> _inputHistory = new List<KeyCode>(); //It's a queue but I want to access contents easily
        private HashSet<KeyCode> _validInputs = new HashSet<KeyCode>();

        private int _maxQueueSize = 0;
        private bool _waitingForPopup = false;

        private GameState _gameState;
        private ThemeSetup _themeSetup;
        private ThemeManager _themeManager;
        private IAnalyticsHandler _analytics;
        private UIModalChoicePopup _modalChoicePopup;

        [Inject]
        public void Inject(GameState gameState, ThemeSetup themeSetup, ThemeManager themeManager, IAnalyticsHandler analytics,
                           UIModalChoicePopup modalChoicePopup)
        {
            _gameState = gameState;
            _themeSetup = themeSetup;
            _themeManager = themeManager;
            _analytics = analytics;
            _modalChoicePopup = modalChoicePopup;
        }

        private void Start()
        {
            if (cheatsData.IsNullOrDestroyed())
            {
                Debug.LogError($"CheatActivator needs a CheatsSetup assigned in the inspector");
                return;
            }

            foreach (CheatCodeSetup.KeyboardCheatData cheat in cheatsData.keyboardCheats)
            {
                if (cheat.buttonPresses.Count > _maxQueueSize)
                    _maxQueueSize = cheat.buttonPresses.Count;

                foreach (KeyCode button in cheat.buttonPresses)
                {
                    _validInputs.Add(button);
                }
            }
        }

        private void Update()
        {
            if (cheatsData.IsNullOrDestroyed())
                return;

            // Currently not meant to run anywhere else than title menu
            if (!runInTitleMenu)
                return;

            if (runInTitleMenu && (!_gameState.IsInTitleScene() || CheatsBlocked))
                return;

            bool someKeyPressed = Input.anyKeyDown;
            bool validKeyPressed = false;

            foreach (KeyCode input in _validInputs)
            {
                if (Input.GetKeyDown(input))
                {
                    _inputHistory.Add(input);
                    if (_inputHistory.Count > _maxQueueSize)
                        _inputHistory.RemoveAt(0);
                    validKeyPressed = true;
                    if (verbose)
                        Debug.Log("[cheat] input history = " + DebugListInputHistory());
                    break;
                }
            }

            if (someKeyPressed && !validKeyPressed)
            {
                _inputHistory.Clear(); // For us it doesn't matter what that input was but it is an interruption
                if (verbose)
                    Debug.Log("[cheat] input history = RESET");
            }

            if (validKeyPressed)
                CheckForActivatedCheats();
        }

        private void CheckForActivatedCheats()
        {
            foreach (CheatCodeSetup.KeyboardCheatData cheat in cheatsData.keyboardCheats)
            {
                if (cheat.buttonPresses.Count <= _inputHistory.Count && CheckForActivation(cheat))
                {
                    Activate(cheat);
                    break; // Should not have 2 cheats with the same code
                }
            }
        }

        private string DebugListInputHistory()
        {
            StringBuilder builder = new StringBuilder();
            builder.Append("<");
            foreach (KeyCode input in _inputHistory)
            {
                builder.Append(input.ToString() + ",");
            }
            builder.Append(">");
            return builder.ToString();
        }

        private bool CheckForActivation(CheatCodeSetup.KeyboardCheatData cheat)
        {
            bool activated = true;

            // Compare each button press from the most recent
            for (int i = 0; i < cheat.buttonPresses.Count; i++)
            {
                KeyCode a = cheat.buttonPresses[(cheat.buttonPresses.Count - 1) - i];
                KeyCode b = _inputHistory[(_inputHistory.Count - 1) - i];
                if (a != b)
                {
                    activated = false;
                    break;
                }
            }

            return activated;
        }

        private void Activate(CheatCodeSetup.KeyboardCheatData cheat)
        {
            if (_waitingForPopup)
                return;

            if (verbose)
                Debug.Log($"Activating cheat code {cheat.internalName} with effect {cheat.effect}");

            switch (cheat.effect)
            {
                case CheatCodeSetup.CheatEffect.ToggleCatrioTheme:
                    if (_themeManager.CurrentTheme.internalName == Constants.CATRIO_THEME_NAME
                     || _themeManager.CurrentTheme.internalName == Constants.ATRIOC_THEME_NAME)
                    {
                        _themeManager.CurrentTheme = _themeSetup.GetTheme(Constants.DEFAULT_THEME_NAME);
                    }
                    else
                    {
                        _modalChoicePopup.DisplayChoice(UIModalChoicePopup.ChoicePopupType.YesNoChoice,
                                                        "Employee Question: Is your name Atrioc?",
                                                        OnAtriocPopupYes1, OnAtriocPopupNo,
                                                        "", "Catrio Settings: Employee Name");
                        _waitingForPopup = true;
                    }
                    break;
                case CheatCodeSetup.CheatEffect.DoNothing:
                default:
                    break;
            }

            _analytics.RegisterSecretCheatActivated(cheat.internalName);
        }

        private void OnAtriocPopupYes1()
        {
            _modalChoicePopup.DisplayChoice(UIModalChoicePopup.ChoicePopupType.NoChoice,
                                            "Difficulty set to: PURE DEATH",
                                            OnAtriocPopupYes2, null,
                                            "Welcome, Atrioc.", "Catrio Settings: Difficulty", "OK");
        }
        private void OnAtriocPopupYes2()
        {
            _modalChoicePopup.DisplayChoice(UIModalChoicePopup.ChoicePopupType.NoChoice,
                                            "Custom skin set to: PATRIOTIC CANADIAN \n" +
                                            "Note: This skin is permanent and can never be changed.",
                                            OnAtriocPopupYes3, null,
                                            "New Skin Unlocked!", "Catrio Settings", "Atrio Forever!");
        }

        private void OnAtriocPopupYes3()
        {
            _modalChoicePopup.DisplayChoice(UIModalChoicePopup.ChoicePopupType.NoChoice,
                                            "We did add something small in game just for you. \n Thanks for playing and enjoy. \n \n- Steve + The Isto Team",
                                            OnInfoPopupClosed, null,
                                            "Just Kidding", "Gotem", "Finish");
        }
        private void OnAtriocPopupNo()
        {
            _themeManager.CurrentTheme = _themeSetup.GetTheme(Constants.CATRIO_THEME_NAME);
            _waitingForPopup = false;
        }

        private void OnInfoPopupClosed()
        {
            _themeManager.CurrentTheme = _themeSetup.GetTheme(Constants.ATRIOC_THEME_NAME);
            _waitingForPopup = false;
        }
    }
}