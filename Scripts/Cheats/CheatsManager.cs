// Copyright Isto Inc.

using UnityEngine;
using Zenject;

namespace Isto.Core.Cheats
{

    /// <summary>
    /// This class is meant to handle configuration and state for the cheat system.
    /// It lives in the project context to take place of the developer console we used to have there, even though we
    /// seldom had a reason for it.
    /// This is not really doing anything at the moment but by being here it's ready to be leveraged when we see a need
    /// for cheat logic that would make sense at this scope.
    /// </summary>
    public class CheatsManager : <PERSON>o<PERSON><PERSON><PERSON><PERSON>
    {
        // UNITY HOOKUP

        public CheatSettings cheatsData;

        // TODO: should this maybe be inside CheatSettings?
        public CheatCodeSetup cheatCodesData;

        // OTHER FIELDS


        // PROPERTIES


        // EVENTS


        // INJECTION

        /// <summary>
        /// This class gets injected at the project context level.
        /// </summary>
        [Inject]
        void Inject()
        {
        }


        // LIFECYCLE EVENTS

        private void Awake()
        {

        }

        private void OnDestroy()
        {

        }

        private void OnEnable()
        {

        }

        private void OnDisable()
        {

        }

        private void Start()
        {

        }

        private void FixedUpdate()
        {

        }

        private void Update()
        {

        }

        private void LateUpdate()
        {

        }


        // EVENT HANDLING

        private void RegisterEvents()
        {

        }

        private void UnregisterEvents()
        {

        }


        // ACCESSORS


        // OTHER METHODS

    }
}