// Copyright Isto Inc.
using System;
using UnityEngine;
using UPool;

namespace Isto.Core.Pooling
{
    /// <summary>
    /// A Generic Object Pool that can handle both traditional C# objects and Unity GameObjects with IPoolable MonoBehaviours.
    /// This is a fork of the UPool.Pool class that is intended to allow you to rip objects out of it and the pool will rebuild
    /// over time. This behavior is meant to help us transition progressively from locally managed objects to pooled objects.
    /// <seealso cref="PoolableObject"/>
    /// </summary>
    /// <typeparam name="T">An object implementing the IPoolable interface</typeparam>
    public class FlexiblePool<T> : AbstractPool where T : IPoolable
    {
        public static readonly Vector3 AUTO_CONTAINER_POSITION = new Vector3(-9999, 0, 0);

        public Transform Container => _container;

        private Transform _container;
        private bool _isGameObject = false;

        public override Type PoolType
        {
            get
            {
                return typeof(T);
            }
        }

        /// <summary>
        /// Creates a pool of the specified size for the given object type
        /// </summary>
        /// <param name="initialSize">Initial size of the pool</param>
        public FlexiblePool(int initialSize)
        {
            if (typeof(MonoBehaviour).IsAssignableFrom(typeof(T)))
            {
                throw new ArgumentException(string.Format("Type {0} derives from MonoBehaviour. In order to Pool MonoBehaviours you must provide a template; use Pool(int, GameObject).", typeof(T).ToString()));
            }

            _generator = new DefaultGenerator<T>(this);

            InitializePool(initialSize);
        }

        /// <summary>
        /// Creates a pool of the specified size for the given GameObject template
        /// </summary>
        /// <param name="initialSize">Initial size of the pool</param>
        /// <param name="template">The GameObject that will be pooled</param>
        /// <param name="container">The container that the pooled GameObjects will reside in when not in use. If one is not provided a container will be generated</param>
        /// <param name="hideContainerInHierarchy">If True, hides the container GameObject in the scene hierarchy to avoid clutter</param>
        public FlexiblePool(int initialSize, GameObject template, Transform container = null, bool hideContainerInHierarchy = true)
        {
            if (!typeof(MonoBehaviour).IsAssignableFrom(typeof(T)))
            {
                throw new ArgumentException(string.Format("Type {0} does not derive from MonoBehaviour. In order to Pool GameObjects, T should be a MonoBehaviour derivative.", typeof(T).ToString()));
            }
            if (template == null)
            {
                throw new ArgumentException("The template GameObject cannot be null.");
            }
            if (template.GetComponent<T>() == null)
            {
                throw new ArgumentException(string.Format("The template GameObject must have a Component of type {0}", typeof(T).ToString()));
            }

            _container = container != null ? container : CreateContainer(template.name);

            if (hideContainerInHierarchy)
            {
                _container.hideFlags = HideFlags.HideInHierarchy;
            }

            _generator = new UnityGenerator(this, template, _container);
            _isGameObject = true;

            InitializePool(initialSize);
        }

        /// <summary>
        /// Aquires an unallocated object from the pool and provides it for use. If no unallocated objects are available, a new one will be created.
        /// </summary>
        /// <returns>An object of type T for use. If T is a MonoBehaviour, the Transform will be reset.</returns>
        public new T Acquire()
        {
            T obj = (T)base.Acquire();

            if (_isGameObject)
            {
                Transform objTransform = (obj as MonoBehaviour).transform;
                objTransform.SetParent(null);
                objTransform.rotation = Quaternion.identity;
                objTransform.localScale = Vector3.one;
                objTransform.position = Vector3.up * -5f;   // Spawn underground as it's position will get set after Acquiring
            }

            return obj;
        }

        /// <summary>
        /// Returns an object to the pool making it available for re-use.
        /// </summary>
        /// <param name="obj">The object to return to the pool. Must have originated from the pool.</param>
        public override void Recycle(IPoolable obj)
        {
            base.Recycle(obj);

            if (_isGameObject)
            {
                Transform objTransform = (obj as MonoBehaviour).transform;
                objTransform.SetParent(_container);
                //objTransform.localPosition = Vector3.zero;
                objTransform.localPosition = Vector3.up * -5f;   // Spawn underground as it's position will get set after Acquiring
            }
        }

        /// <summary>
        /// Cleans up the Pool, prepping it for garbage collection. This will orphan any objects that are currently allocated.
        /// If you would like to also destroy the allocated objects see <see cref="DestroyAndDeallocateAll"/>
        /// </summary>
        public override void Destroy()
        {
            base.Destroy();

            if (_container != null)
            {
                UnityEngine.Object.Destroy(_container.gameObject);
            }
        }

        /// <summary>
        /// Destroys all allocated objects and cleans up the Pool, prepping it for garbage collection.
        /// </summary>
        public override void DestroyAndDeallocateAll()
        {
            base.DestroyAndDeallocateAll();

            if (_container != null)
            {
                UnityEngine.Object.Destroy(_container.gameObject);
            }
        }

        /// <summary>
        /// Creates a container that the pooled GameObjects will reside in when not in use.
        /// </summary>
        /// <param name="objectName">Name of the GameObject that is being pooled.</param>
        /// <returns>A reference to the container Transform</returns>
        private Transform CreateContainer(string objectName)
        {
            GameObject container = new GameObject(string.Format("{0}Pool", objectName));
            container.transform.position = AUTO_CONTAINER_POSITION;
            return container.transform;
        }
    }
}
