// Copyright Isto Inc.
using Isto.Core.Game;
using Isto.Core.Scenes;
using UnityEngine;
using UnityEngine.SceneManagement;
using Zenject;

namespace Isto.Core.Automation
{
    /// <summary>
    /// Responsible for telling the Automation System to Tick when necessary.
    /// </summary>
	public class AutomationSystemController : MonoBehaviour
    {
        // UNITY HOOKUP

        [SerializeField] private float _timePerTick = 0.1f;
        [Tooltip("Scales the time per tick by this amount to make game time run faster or slower")]
        [SerializeField] private float _timeFactor = 1f;


        // OTHER FIELDS

        private float _tickTimer = 0f;


        // PROPERTIES

        public AutomationSystem System { get { return _system; } }
        public bool SystemPaused { get; private set; }


        // INJECTION

        private AutomationSystem _system;
        private GameScenesReference _gameScenesReference;

        [Inject]
        public void Inject(AutomationSystem automationSystem, GameScenesReference gameScenesReference)
        {
            _system = automationSystem;
            _gameScenesReference = gameScenesReference;
        }


        // LIFECYCLE EVENTS

        private void OnEnable()
        {
            RegisterEvents();
        }

        private void OnDisable()
        {
            UnregisterEvents();
        }

        public void Update()
        {
            if (!GameState.GameDataLoaded || SystemPaused)
                return;

            if (!GameState.AutomationSystemCanStart)
                return;

            _tickTimer += Time.deltaTime;

            if (_tickTimer >= _timePerTick)
            {
                // If time per tick == 0 then just use delta time
                float deltaTime = _timePerTick > 0 ? _timePerTick : Time.deltaTime;

                _system.TickSystem(deltaTime * _timeFactor);

                _tickTimer = _tickTimer - _timePerTick;
            }
        }

        
        // EVENT HANDLING
        
        private void RegisterEvents()
        {
            SceneManager.sceneLoaded += OnSceneLoaded;
            SceneManager.sceneUnloaded += OnSceneUnloaded;
        }

        private void UnregisterEvents()
        {
            SceneManager.sceneLoaded -= OnSceneLoaded;
            SceneManager.sceneUnloaded -= OnSceneUnloaded;
        }

        private void OnSceneLoaded(Scene newScene, LoadSceneMode sceneMode)
        {
            if(_gameScenesReference.IsDialogueScene(newScene.name))
            {
                SystemPaused = true;
            }
        }

        private void OnSceneUnloaded(Scene unloadedScene)
        {
            if (_gameScenesReference.IsDialogueScene(unloadedScene.name))
            {
                SystemPaused = false;
            }
        }


        // ACCESSORS

        public void SetTickFactor(float factor)
        {
            _timeFactor = factor;
        }


        // OTHER METHODS

        public void PauseSystem()
        {
            SystemPaused = true;
        }

        public void UnPauseSystem()
        {
            SystemPaused = false;
        }

#if UNITY_EDITOR
        [ContextMenu("Print Grid")]
        public void PrintAutomationSystem()
        {
            Debug.Log(_system.Grid.ToString());
        }
#endif
    }
}