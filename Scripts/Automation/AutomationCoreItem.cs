// Copyright Isto Inc.
using System;
using UnityEngine;
using UnityEngine.AddressableAssets;
using Zenject;

namespace Isto.Core.Automation
{
    /// <summary>
    /// Represents an item in the automation system.  The item is basically a string with the ID of the type of item and the count of that item.
    /// The ID is items ID in the MasterItemList
    /// </summary>
	public class AutomationCoreItem
    {
        // Events
        public event EventHandler<CoreItemMoveEventArgs> MoveStart;
        public event EventHandler<CoreItemMoveEventArgs> MoveComplete;
        public event EventHandler<CoreItemMoveEventArgs> Launch; //Unsquish happens on move end

        // Used when an item is consumed by an Item processor so it's visuals should be updated.  
        public event EventHandler Consumed;

        // Public Variables
        public bool IsMoving { get; private set; }
        public bool IsConsumed { get; private set; }

        // This is a coreitem mutex in case several processors are competing for one item
        public bool IsReserved { get; set; }

        public Vector3 TargetPosition { get { return _currentMoveArgs.mover != null ? _currentMoveArgs.targetPosition : position; } }
        public float MoveTotalTime { get { return _currentMoveArgs.mover != null ? _currentMoveArgs.moveTime : 0.01f; } }
        public IItemMoveType MoveType { get; private set; }
        public AssetReference DisplayAssetOverride { get; private set; }
        public AutomationGridSpace GridSpace { get; private set; }

        public string ID;
        public int count;
        public Vector3 position;

        private CoreItemMoveEventArgs _currentMoveArgs;

        public AutomationCoreItem(CoreItemParams itemParams)
        {
            ID = itemParams.itemID;
            count = itemParams.count;
            position = itemParams.position;
        }

        public void StartItemMove(Vector3 destination, float moveTime, IItemMoveType moveType)
        {
            IsMoving = true;
            MoveType = moveType;

            _currentMoveArgs = new CoreItemMoveEventArgs() { moveTime = moveTime, targetPosition = destination, mover = moveType };

            MoveStart?.Invoke(this, _currentMoveArgs);
        }

        public void EndItemMove()
        {
            IsMoving = false;

            MoveComplete?.Invoke(this, _currentMoveArgs);
        }

        public void LaunchItem()
        {
            Launch?.Invoke(this, new CoreItemMoveEventArgs() { });
        }

        public IItemMoveType GetMovingType()
        {
            if (!IsMoving)
                return null;
            else
                return MoveType;
        }

        /// <summary>
        /// This marks the item as consumed and fires an event, but doesn't actually remove the item.
        /// </summary>
        public void ConsumeItem()
        {
            Consumed?.Invoke(this, EventArgs.Empty);
            IsConsumed = true;
        }

        public void SetDisplayAsset(AssetReference displayAsset)
        {
            DisplayAssetOverride = displayAsset;
        }

        public void SetGridSpace(AutomationGridSpace space)
        {
            GridSpace = space;
        }

        public class Factory : PlaceholderFactory<CoreItemParams, AutomationCoreItem> { }
    }

    public struct CoreItemParams
    {
        public string itemID;
        public int count;
        public Vector3 position;

        public CoreItemParams(string itemID, int count, Vector3 position)
        {
            this.itemID = itemID;
            this.count = count;
            this.position = position;
        }
    }

    public struct CoreItemMoveEventArgs
    {
        public Vector3 targetPosition;
        public float moveTime;
        public IItemMoveType mover;
    }
}