// Copyright Isto Inc.
using UnityEngine;


namespace Isto.Core.Automation
{
    public class AutomationLaunchMove : IItemMoveType
    {
        public bool Complete { get; private set; }
        public Vector3 FinalPosition { get { return _targetPosition; } }

        private AutomationCoreItem _item;
        private Vector3 _targetPosition;
        private Vector3 _startPosition;
        private float _moveTotalTime;
        private float _moveTimer;

        //Animation Juice
        private float _pauseAmount = 0.2f; //The time we want to pause before launching
        private bool _launchStarted;
        private float _gravity;
        private Vector3 _positionDelta = new Vector3();

        public AutomationLaunchMove(AutomationCoreItem item, Vector3 targetPosition, float moveTime)
        {
            _item = item;
            _targetPosition = targetPosition;

            int multiplier = GetMoveTimeMultiplier(Vector3.Distance(item.position, targetPosition));

            _moveTotalTime = (multiplier * moveTime) - _pauseAmount;

            _moveTimer = 0f;
            _startPosition = item.position;

            _item.StartItemMove(_targetPosition, _moveTotalTime, this);
            _gravity = GetGravity();
        }

        public void Tick(float deltaTime)
        {
            if (Complete)
                return;

            _moveTimer += deltaTime;

            if (_moveTimer < _pauseAmount)
                return;

            else
            {
                if (!_launchStarted)
                {
                    _launchStarted = true;
                    _item.LaunchItem();
                }

                float x = CalculatePosition(0, _moveTotalTime, _targetPosition.x, _startPosition.x, _moveTimer - _pauseAmount);
                float y = CalculatePosition(_gravity, _moveTotalTime, _targetPosition.y, _startPosition.y, _moveTimer - _pauseAmount);
                float z = CalculatePosition(0, _moveTotalTime, _targetPosition.z, _startPosition.z, _moveTimer - _pauseAmount);

                if (_moveTimer > _moveTotalTime + _pauseAmount)
                {
                    EndMovement();
                }
                else
                {
                    _positionDelta.Set(x, y, z);
                    _item.position = _startPosition + _positionDelta;
                }
            }
        }

        private void EndMovement()
        {
            _launchStarted = false;
            _item.position = _targetPosition;
            Complete = true;
            _item.EndItemMove();
        }

        private float GetGravity()
        {
            float totalDist = Vector3.Distance(_startPosition, _targetPosition);
            if (totalDist < 8)
                return -Physics.gravity.magnitude * 5;
            else if (totalDist < 10)
                return -Physics.gravity.magnitude * 4;
            else
                return -Physics.gravity.magnitude * 3;
        }

        private float CalculatePosition(float a, float T, float yf, float yi, float t)
        {
            float T2 = Mathf.Pow(T, 2);
            float t2 = Mathf.Pow(t, 2);
            float y = (((yf - yi - (0.5f * a * T2)) / T) * t) + ((0.5f) * a * t2);
            return y;
        }

        public static int GetMoveTimeMultiplier(float distance)
        {
            if (distance <= 3)
            {
                return 1;
            }
            else if (distance <= 8)
            {
                return 2;
            }
            else
            {
                return 3;
            }
        }

        public void Cancel()
        {
            EndMovement();
        }
    }
}