// Copyright Isto Inc.
using Isto.Core.Data;
using Isto.Core.Items;

namespace Isto.Core.Automation
{
    public class AutomationProcessorProxy : IItemProcessor
    {
        public string ProcessorID => "ProcessorProxy";
        public string LinkedProcessorID => _linkedProcessor != null ? _linkedProcessor.ProcessorID : "Empty";
        public IItemProcessor LinkedProcessor => _linkedProcessor;

        public bool Visible => false;

        private IItemProcessor _linkedProcessor;

        public AutomationProcessorProxy(IItemProcessor linkedProcessor)
        {
            _linkedProcessor = linkedProcessor;
        }

        public void Tick(float deltaTime)
        {
            // Does nothing
        }

        public AutomationProcessorData GetSaveData()
        {
            return null;
        }

        public int IsAvailable(AutomationCoreItem movingItem)
        {
            return _linkedProcessor.IsAvailable(movingItem);
        }

        public void LoadData(AutomationProcessorData data)
        {
            // Isn't saved
        }
    }
}