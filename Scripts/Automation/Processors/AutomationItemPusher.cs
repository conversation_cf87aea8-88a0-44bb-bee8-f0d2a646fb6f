// Copyright Isto Inc.
using Isto.Core.Data;
using Isto.Core.Items;
using System;
using UnityEngine;
using Zenject;

namespace Isto.Core.Automation
{
    /// <summary>
    /// WARNING: We make assumptions in some places that this is necessarily a pushback splitter.
    /// In particular MenuLoadSaveGame.CreateProcessor needs to know to give pushbacks their position offset when spawning them from data.
    /// </summary>
    public class AutomationItemPusher : IItemProcessor, IDirectionalItem, IAutomationGridSpaceUser
    {
        // Events

        public event EventHandler<ItemMoveArgs> ItemPushed;

        // Public Variables

        public string ProcessorID { get; private set; }
        public bool Visible { get { return true; } }
        public int CurrentCount { get { return _intervalCounter; } }

        // Debug info accessors
        public Vector3 SourceSpacePosition => _processorSpace.position + _forward;
        public string TargetItemID => _targetItem?.ID ?? "null";
        public string SourceItemID => _system.DoesSpaceExist(SourceSpacePosition) ? SourceSpace.ActiveItem?.ID ?? "null" : "null";
        public bool WaitingToPush => _waitingToPush;

        // Private Properties

        // Don't cache these two references, they can get destroyed and re-created
        // Space that items pass over
        private AutomationGridSpace SourceSpace { get { return _system.GetOrCreateGridSpace(SourceSpacePosition); } }
        // Space to push items onto
        private AutomationGridSpace DestinationSpace { get { return _system.GetOrCreateGridSpace(SourceSpacePosition + _forward * _pushDistance); } }

        // Private Variables

        [Inject] private AutomationSystem _system = default;

        private Vector3 _forward = Vector3.right;
        private AutomationGridSpace _processorSpace; // Space that this pusher sits on
        private AutomationCoreItem _targetItem;

        private int _pushDistance;
        private int _pushInterval;
        private int _intervalCounter;

        private bool _waitingToPush;

        public AutomationItemPusher(AutomationItemPusherParams p)
        {
            _pushDistance = p.distance;
            _pushInterval = p.pushInterval;
            _intervalCounter = p.currentCount;

            ProcessorID = p.processorTypeId;
        }

        public void SetGridSpace(AutomationGridSpace space)
        {
            _processorSpace = space;
        }

        public int IsAvailable(AutomationCoreItem movingItem)
        {
            return 0;
        }

        public void SetForwardDirection(Vector3 forward)
        {
            _forward = forward;
        }

        public Vector3 GetDirection()
        {
            return _forward;
        }

        public void Tick(float deltaTime)
        {
            if (_waitingToPush)
                return;

            AutomationGridSpace sourceSpace = null;

            if (_system.DoesSpaceExist(SourceSpacePosition))
            {
                sourceSpace = SourceSpace;
            }

            if (_targetItem == null && sourceSpace?.ActiveItem != null && ItemMovingToThisSpace(sourceSpace.ActiveItem))
            {
                _targetItem = sourceSpace.ActiveItem;
                _intervalCounter++;
            }
            else if (_targetItem != null)
            {
                if (sourceSpace?.ActiveItem == null)
                {
                    _targetItem = null;
                }
                else if (sourceSpace.ActiveItem != _targetItem)
                {
                    _targetItem.MoveComplete -= OnIncomingMoveComplete;
                    _targetItem = sourceSpace.ActiveItem;
                    _intervalCounter++;
                }
            }

            if (ShouldPush(deltaTime))
            {
                _stuckTimer = 0f;

                if (_targetItem.IsMoving)
                {
                    _targetItem.MoveComplete += OnIncomingMoveComplete;
                    _waitingToPush = true;
                }
                else
                {
                    TryPushItem();
                }
            }
        }

        private float _stuckTimer = 0f;

        private bool ShouldPush(float deltaTime)
        {
            if (_intervalCounter == 0 || _targetItem == null)
                return false;

            // If not moving to our space or on our space, don't try and push
            if (_targetItem.TargetPosition != SourceSpacePosition)
                return false;

            if (_targetItem.IsReserved || _targetItem.IsConsumed)
                return false;

            // Not part of the plan but if ever it happens at least the pushback will sorta work instead of throw exceptions
            if (_pushInterval == 0)
                return true;

            if ((_intervalCounter % _pushInterval) == 0)
                return true;

            _stuckTimer += deltaTime;

            if (_stuckTimer > _system.ItemMoveTime * 2)
            {
                return true;
            }

            return false;
        }

        private void OnIncomingMoveComplete(object sender, CoreItemMoveEventArgs e)
        {
            _waitingToPush = false;
            _targetItem.MoveComplete -= OnIncomingMoveComplete;

            // Check if something else didn't pick up the item while we were waiting for our chance
            if (!_targetItem.IsMoving && !_targetItem.IsReserved && !_targetItem.IsConsumed)
            {
                TryPushItem();
            }
        }

        private bool ItemMovingToThisSpace(AutomationCoreItem item) => item.TargetPosition == SourceSpacePosition;

        private bool TryPushItem()
        {
            bool pushSuccess = false;
            AutomationGridSpace sourceSpace = SourceSpace;
            AutomationGridSpace destinationSpace = DestinationSpace;

            if (_system.CanMoveItemToGridSpace(destinationSpace, _targetItem, out int spaceAvailable))
            {
                _targetItem.IsReserved = true;
                bool movingAll = spaceAvailable == _targetItem.count;
                pushSuccess = AutomationItemMover.TryItemMove(_targetItem, SourceSpace, destinationSpace, spaceAvailable, _system, GetMover, out IItemMoveType activeItemMover);
                _targetItem.IsReserved = false;

                if (pushSuccess)
                {
                    ItemPushed?.Invoke(this, new ItemMoveArgs(activeItemMover));
                    _intervalCounter = 0;
                    if (movingAll)
                        _targetItem = null;
                }
            }

            return pushSuccess;
        }

        public IItemMoveType GetMover(AutomationCoreItem item, Vector3 destination, AutomationGridSpace nextSpace)
        {
            return new AutomationLinearMove(_targetItem, destination, _system.ItemMoveTime);
        }

        private bool ItemReadyToBePushed()
        {
            if (_targetItem == null || _targetItem.IsMoving)
                return false;

            return _targetItem.position == SourceSpacePosition && _waitingToPush;
        }

        // Data Management

        public AutomationProcessorData GetSaveData()
        {
            AutomationItemPusherData moverData = new AutomationItemPusherData()
            {
                direction = _forward,
                distance = _pushDistance,
                pushInterval = _pushInterval,
                currentCount = _intervalCounter,
                typeId = ProcessorID,
            };

            return moverData;
        }

        public void LoadData(AutomationProcessorData data)
        {
            if (data is AutomationItemPusherData moverData)
            {
                SetForwardDirection(moverData.direction);
                _pushDistance = moverData.distance;
                _pushInterval = moverData.pushInterval;
                _intervalCounter = moverData.currentCount;
                ProcessorID = moverData.typeId;

                // If we're importing older versions of the pushback data it will have bad default values
                // I override it with what's currently set in the prefab data. Eventually we can make a better fix
                if (_pushInterval == 0)
                    _pushInterval = 2;
            }
        }

        public class Factory : PlaceholderFactory<AutomationItemPusherParams, AutomationItemPusher> { }
    }

    public class AutomationItemPusherParams
    {
        public int distance;
        public int pushInterval;
        public int currentCount;
        public string processorTypeId;

        public AutomationItemPusherParams(int distance, int pushInterval, int currentCount, string processorTypeId)
        {
            this.distance = distance;
            this.pushInterval = pushInterval;
            this.currentCount = currentCount;
            this.processorTypeId = processorTypeId;
        }
    }
}