// Copyright Isto Inc.
using Isto.Core.Data;
using Isto.Core.Items;
using UnityEngine;
using Zenject;

namespace Isto.Core.Automation
{
    public class AutomationItemDispenser : IItemProcessor, IAutomationGridSpaceUser, IAutomationInventory, IDirectionalItem, IAutomationActionOnRemoved
    {
        public string ProcessorID { get; private set; }
        public bool Visible { get; private set; }
        public float DropTimerPercent { get { return 1 - (_timeToNextDrop / _timePerDrop); } }
        public float TimePerDrop { get { return _timePerDrop; } }
        public bool IsFacingUpDirection => _forward.x == -1 || _forward.z == 1;
        public float MaxDropRate => 32f;
        public float MinDropRate => 1f;

        private float _maxTimePerDrop;
        private float _minTimePerDrop;
        private AutomationGridSpace _space;
        private AutomationGridSpace _proxySpace;

        // Injected

        [Inject] private MasterItemList _masterItemList = default;
        [Inject] private AutomationSystem _system = default;
        [Inject] private AutomationCoreItem.Factory _itemFactory = default;

        // Constructed

        private Inventory _inventory;
        private Vector3 _forward;
        private float _timePerDrop;
        private float _timeToNextDrop;

        public AutomationItemDispenser(ItemDispenserParams p)
        {
            if (p.pileCount > 1)
                Debug.LogError("Automation Item Dispenser is not setup for inventories with more than one pile.  Incorrect behavior will probably happen!");

            _inventory = new Inventory(p.pileCount, p.stackSize);
            _timePerDrop = p.timePerDrop;
            _timeToNextDrop = p.timeToNextDrop;
            _forward = p.forward;

            ProcessorID = p.typeID;

            Visible = true;
        }

        public IInventory GetInventory()
        {
            return _inventory;
        }

        public virtual int IsAvailable(AutomationCoreItem movingItem)
        {
            if (_inventory.GetContainerPercentUsed() == 1)
                return 0;

            int spaceAvailable = _inventory.GetSpaceAvailable(movingItem.ID);
            int itemsAlreadyIncoming = _space.GetCountOfItemsIncoming(movingItem.ID);

            return spaceAvailable - itemsAlreadyIncoming;
        }

        public void SetGridSpace(AutomationGridSpace space)
        {
            _space = space;
            CreateProxyProcessor();
        }

        private void CreateProxyProcessor()
        {
            // Only need the proxy if facing down as it's used to accept items from the back of the dispenser.  If facing up, this is the same space
            // as this processor is already on
            if (!IsFacingUpDirection)
            {
                _proxySpace = _system.GetOrCreateGridSpace(_space.position - _forward);
                _proxySpace.itemProcessor = new AutomationProcessorProxy(this);
            }
        }

        public void Tick(float deltaTime)
        {
            if (!_space.Powered)
                return;

            // Check for existing items first
            AutomationGridSpace itemDropSpace = _proxySpace != null ? _proxySpace : _space;
            AutomationCoreItem activeItem = itemDropSpace.ActiveItem;

            if (activeItem != null && !activeItem.IsMoving)
            {
                CoreItem item = _masterItemList.GetItemByID<CoreItem>(activeItem.ID);

                int deposited = _inventory.Add(item, activeItem.count);

                if (deposited == activeItem.count)
                {
                    _system.DeleteCoreItem(activeItem);
                }
                else if (deposited != 0)
                {
                    itemDropSpace.ActiveItem.count -= deposited;
                }
            }

            _timeToNextDrop -= deltaTime;

            if (_timeToNextDrop <= 0)
            {
                if (TryDropItem())
                    _timeToNextDrop = _timePerDrop;
            }
        }

        public void SetVisibility(bool isVisible)
        {
            Visible = isVisible;
        }

        public void SetDropTime(float value)
        {
            //Note that the two values' max's are reversed.
            _maxTimePerDrop = 60f / MinDropRate;
            _minTimePerDrop = (60f / MaxDropRate) * 0.99f; //Stephens note: adjusting the max just a fraction to avoid little dips in the fuel depot

            value *= 0.99f; // STEPHENS NOTES: making it just a fraction faster to avoid little dips in the fuel depot
            float prevRatio = _timeToNextDrop / _timePerDrop;

            _timePerDrop = Mathf.Clamp(value, _minTimePerDrop, _maxTimePerDrop);
            _timeToNextDrop = _timePerDrop * prevRatio;
        }

        public void SetForwardDirection(Vector3 forward)
        {
            _forward = forward;
        }

        public Vector3 GetDirection()
        {
            return _forward;
        }

        private bool TryDropItem()
        {
            if (_inventory.GetTotalNumberOfItems() > 0)
            {
                CoreItem itemToDrop = _inventory.Items[0].item;

                Vector3 nextSpacePosition = _space.position + (IsFacingUpDirection ? _forward * 2 : _forward);
                AutomationGridSpace dropSpace = _system.GetOrCreateGridSpace(nextSpacePosition);

                int moveDistance = IsFacingUpDirection ? 2 : 1;

                AutomationCoreItem outputItem = _itemFactory.Create(new CoreItemParams(itemToDrop.itemID, 1, _space.position));

                if (_system.CanMoveItemToGridSpace(dropSpace, outputItem, out int spaceAvailable))
                {
                    _system.TryAddCoreItemToSpace(dropSpace.position, outputItem);

                    dropSpace.MoveItemToSpace(new AutomationLinearMove(outputItem, dropSpace.position, _system.ItemMoveTime * moveDistance));

                    outputItem = null;

                    _inventory.Remove(itemToDrop, 1);

                    return true;
                }
            }

            return false;
        }

        public void Removed()
        {
            if (!IsFacingUpDirection)
            {
                // Remove the proxy processor
                _system.TryRemoveProcessor(_space.position - _forward);
            }
        }

        // Data Management

        public AutomationProcessorData GetSaveData()
        {
            AutomationItemDispenserData containerData = new AutomationItemDispenserData()
            {
                typeId = ProcessorID,
                inventoryPileCount = _inventory.PileCount,
                inventoryPileSize = _inventory.PileSize,
                visible = Visible,
                direction = _forward,
                timePerDrop = _timePerDrop,
                timeToNextDrop = _timeToNextDrop
            };

            containerData.inventory = ItemData.ConvertItemPileList(_inventory.Items);

            return containerData;
        }

        public void LoadData(AutomationProcessorData data)
        {
            if (data is AutomationItemDispenserData containerData)
            {
                Visible = containerData.visible;

                ItemData.LoadItemsIntoInventory(containerData.inventory.ToArray(), _inventory, _masterItemList);

                _forward = containerData.direction;
                _timePerDrop = containerData.timePerDrop;
                _timeToNextDrop = containerData.timeToNextDrop;
            }
        }

        public struct ItemDispenserParams
        {
            public string typeID;
            public int pileCount;
            public int stackSize;
            public float timePerDrop;
            public float timeToNextDrop;
            public Vector3 forward;

            public ItemDispenserParams(string typeID, int pileCount, int stackSize, float timePerDrop, float timeToNextDrop, Vector3 forward)
            {
                this.typeID = typeID;
                this.pileCount = pileCount;
                this.stackSize = stackSize;
                this.timePerDrop = timePerDrop;
                this.timeToNextDrop = timeToNextDrop;
                this.forward = forward;
            }
        }

        public class Factory : PlaceholderFactory<ItemDispenserParams, AutomationItemDispenser> { }
    }
}