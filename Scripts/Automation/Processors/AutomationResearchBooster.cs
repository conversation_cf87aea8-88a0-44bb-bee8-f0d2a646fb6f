// Copyright Isto Inc.
using Isto.Core.Beings;
using Isto.Core.Data;
using Isto.Core.Items;
using Isto.Core.Research;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Zenject;

namespace Isto.Core.Automation
{
    public class AutomationResearchBooster : IItemProcessor, IAutomationGridSpaceUser, IAutomationInventory, IAutomationActionOnRemoved
    {
        public string ProcessorID { get; private set; }

        public bool Visible => true;

        public bool Cooking { get; private set; }

        public float CookingPercent { get; private set; }

        public float BoostFactor { get; private set; }

        [Inject] private MasterItemList _masterItemList = default;
        [Inject] private AutomationSystem _system = default;
        [Inject] private PlayerResearchModule _researchModule = default;
        [Inject] private PlayerInventory _playerInventory = default;

        private int maxNumberPerItem = 5;

        private IResearcher _researcher;
        private RigidInventory _inventory;
        private List<AutomationGridSpace> _spaces;
        private AutomationResearchBoosterEntrance _otherEntrance;
        private Vector3 _position;
        private Vector3 _otherEntranceOffset = new Vector3(-2, 0, 0);

        private List<ResearchBoosts> _boosts;

        public AutomationResearchBooster(AutomationResearchBoosterParams boosterParams)
        {
            ProcessorID = boosterParams.ID;
            _researcher = boosterParams.researcher;

            _spaces = new List<AutomationGridSpace>();

            List<BoosterItemRate> items = boosterParams.mappings.itemMappings;

            _inventory = new RigidInventory(items.Count, maxNumberPerItem);
            _boosts = new List<ResearchBoosts>();

            for (int i = 0; i < items.Count; i++)
            {
                _inventory.SetItemConstraintForPile(i, items[i].boosterItem);
                _boosts.Add(new ResearchBoosts(items[i].boosterItem.itemID, items[i].boostAmount, items[i].cookTime, 0f));
            }
        }

        public void Tick(float deltaTime)
        {
            //if (!_space.Powered) return;

            for (int i = 0; i < _spaces.Count; i++)
            {
                AutomationGridSpace nextSpace = _spaces[i];

                if (nextSpace.ActiveItem != null && !nextSpace.ActiveItem.IsMoving)
                {
                    CoreItem item = _masterItemList.GetItemByID<CoreItem>(nextSpace.ActiveItem.ID);

                    int deposited = _inventory.Add(item, nextSpace.ActiveItem.count);

                    if (deposited == nextSpace.ActiveItem.count)
                    {
                        _system.DeleteCoreItem(nextSpace.ActiveItem);

                        StartCookingItem(item);
                    }
                    else
                    {
                        Debug.LogWarning("Unable to deposit item into inventory of research booster.  IsAvailable check shouldve prevented this.  Deleting item");

                        _system.DeleteCoreItem(nextSpace.ActiveItem);
                    }
                }
            }

            ProcessCooking(deltaTime);
        }

        private void StartCookingItem(CoreItem item)
        {
            for (int i = 0; i < _boosts.Count; i++)
            {
                if (_boosts[i].boostItemID.Equals(item.itemID, StringComparison.OrdinalIgnoreCase) && _boosts[i].timeRemaing == 0f)
                {
                    _boosts[i].timeRemaing = _boosts[i].cookTime;

                    _inventory.Remove(item);
                }
            }
        }

        public void PlayerDepositItem(int juiceNum)
        {
            ResearchBoosts boostInfo = GetBoostAtIndex(juiceNum);
            CoreItem boostItem = _masterItemList.GetItemByID<CoreItem>(boostInfo.boostItemID);
            for (int i = 0; i < _boosts.Count; i++)
            {
                if (_boosts[i].boostItemID.Equals(boostItem.itemID, StringComparison.OrdinalIgnoreCase) && _boosts[i].timeRemaing == 0f)
                {
                    _boosts[i].timeRemaing = _boosts[i].cookTime;

                    _playerInventory.Remove(boostItem);
                }
            }

        }
        private void ProcessCooking(float deltaTime)
        {
            // if (!_researchModule.IsResearching() || _researchModule.ResearchComplete) return;
            if (_researchModule.ResearchComplete)
                return;

            Cooking = _boosts.Exists(x => x.timeRemaing > 0);

            if (Cooking)
            {
                UpdateCookTimes(deltaTime);

                BoostFactor = GetTotalBoostAmount();
            }

            // Check if any new boosts should be started
            for (int i = 0; i < _inventory.Items.Count; i++)
            {
                if (_inventory.Items[i].count > 0)
                    StartCookingItem(_inventory.Items[i].item);
            }
        }

        private void UpdateCookTimes(float deltaTime)
        {
            float totalActiveCookTimes = 0f;
            float totalRemainingTimes = 0f;

            for (int i = 0; i < _boosts.Count; i++)
            {
                bool wasCooking = _boosts[i].timeRemaing > 0f;

                if (wasCooking)
                {
                    totalActiveCookTimes += _boosts[i].cookTime;
                    totalRemainingTimes += _boosts[i].timeRemaing;
                }

                _boosts[i].timeRemaing = Mathf.Clamp(_boosts[i].timeRemaing - deltaTime, 0f, _boosts[i].cookTime);

                if (wasCooking && _boosts[i].timeRemaing == 0)
                {
                    // Try start cooking a new item
                    int removed = _inventory.Remove(_boosts[i].boostItemID);

                    if (removed == 1)
                        _boosts[i].timeRemaing = _boosts[i].cookTime;
                }
            }

            CookingPercent = totalActiveCookTimes > 0 ? 1 - (totalRemainingTimes / totalActiveCookTimes) : 0f;
        }

        private float GetTotalBoostAmount()
        {
            float total = 0f;

            for (int i = 0; i < _boosts.Count; i++)
            {
                if (_boosts[i].timeRemaing > 0f)
                    total += _boosts[i].boost;
            }

            return total;
        }

        public IInventory GetInventory()
        {
            return _inventory;
        }

        public AutomationProcessorData GetSaveData()
        {
            return new AutomationResearchBoosterData()
            {
                cookingPercent = CookingPercent,
                inventory = ItemData.ConvertItemPileList(_inventory.Items),
                typeId = ProcessorID,
                boosts = _boosts.ToArray()
            };
        }

        public void LoadData(AutomationProcessorData data)
        {
            if (data is AutomationResearchBoosterData boostData)
            {
                ProcessorID = boostData.typeId;

                // Null check to support older data
                if (boostData.boosts != null)
                    _boosts = boostData.boosts.ToList();

                ItemData.LoadItemsIntoInventory(boostData.inventory.ToArray(), _inventory, _masterItemList);
            }
        }

        public int IsAvailable(AutomationCoreItem movingItem)
        {
            bool acceptsItem = _boosts.Exists(x => x.boostItemID.Equals(movingItem.ID, StringComparison.OrdinalIgnoreCase));

            if (!acceptsItem)
                return 0;

            //int spaceAvailable = maxNumberPerItem - _inventory.GetCountOfItem(movingItem.ID);
            int spaceAvailable = _inventory.GetSpaceAvailable(movingItem.ID);

            if (spaceAvailable == 0)
                return 0;

            int itemsAlreadyIncomingCount = 0;

            for (int i = 0; i < _spaces.Count; i++)
            {
                for (int j = 0; j < _spaces[i].AllItemsOnSpace.Count; j++)
                {
                    itemsAlreadyIncomingCount += _spaces[i].GetCountOfItemsIncoming(movingItem.ID);
                }
            }

            //Debug.Log($"IsAvaiable:{spaceAvailable > itemsAlreadyIncomingCount}. SpaceAvail:{spaceAvailable}, ItemsIncoming:{itemsAlreadyIncomingCount}");

            return spaceAvailable - itemsAlreadyIncomingCount;
        }

        public void SetGridSpace(AutomationGridSpace space)
        {
            _position = space.position;

            _spaces.Add(space);

            _otherEntrance = new AutomationResearchBoosterEntrance(this);

            if (_system.TryAddItemProcessor(space.position + _otherEntranceOffset, _otherEntrance))
            {
                _spaces.Add(_system.GetOrCreateGridSpace(space.position + _otherEntranceOffset));
            }
            else
            {
                Debug.LogWarning("coudln't add other entrance for Research Booster. Position: " + space.position + _otherEntrance);
            }
        }

        public void Removed()
        {
            _system.TryRemoveProcessor(_position + _otherEntranceOffset);
        }

        public ResearchBoosts GetBoostAtIndex(int index)
        {
            if (index < 0 || index >= _boosts.Count)
                return null;

            return _boosts[index];
        }

        public CoreItem GetBoostItemAtIndex(int index)
        {
            if (index < 0 || index >= _boosts.Count)
                return null;

            return _masterItemList.GetItemByID<CoreItem>(_boosts[index].boostItemID);
        }

        public class Factory : PlaceholderFactory<AutomationResearchBoosterParams, AutomationResearchBooster> { }
    }

    public class AutomationResearchBoosterParams
    {
        public string ID;
        public IResearcher researcher;
        public ResearchBoosterItemMappings mappings;

        public AutomationResearchBoosterParams(string ID, IResearcher researcher, ResearchBoosterItemMappings mappings)
        {
            this.ID = ID;
            this.researcher = researcher;
            this.mappings = mappings;
        }
    }
}