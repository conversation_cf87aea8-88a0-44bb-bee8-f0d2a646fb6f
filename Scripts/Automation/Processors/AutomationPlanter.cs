// Copyright Isto Inc.
using Isto.Core.Data;
using Isto.Core.Items;
using System;
using UnityEngine;
using Zenject;

namespace Isto.Core.Automation
{
    public class AutomationPlanter : IItemProcessor, IAutomationGridSpaceUser, IAutomationInventory, IDirectionalItem
    {
        // Events

        public event EventHandler<ItemMoveArgs> ItemMoved;

        // Public Properties

        public string ProcessorID { get; private set; }

        public bool Visible => true;

        public Recipe Recipe { get; private set; }

        public bool Growing { get { return GrowingPercent > 0f && GrowingPercent < 1f; } }

        public float GrowingPercent { get { return _space == null ? 0f : (_space.Resource == null ? 0f : _space.Resource.GrowthPercent); } }

        // Private Variables

        [Inject] private MasterItemList _masterItemList = default;
        [Inject] private AutomationSystem _system = default;

        private AutomationPlanterParams _params;
        private AutomationGridSpace _space;
        private RigidInventory _rigidInventory;
        private bool _growingComplete;
        private float _pauseBetweenGrowing;
        private AutomationCoreItem _targetItem;

        public AutomationPlanter(AutomationPlanterParams planterParams)
        {
            ProcessorID = planterParams.ID;

            _params = planterParams;

            _rigidInventory = new RigidInventory(3, 5);

            SetRecipe(_params.recipe);
        }

        public void Tick(float deltaTime)
        {
            CheckForIncomingItem();
            CheckToPullItemFromAdjacentSpaces();

            // If done growing or complete and resource is null, it was harvested so reset flags
            if ((_growingComplete || Growing) && _space.Resource == null)
            {
                _growingComplete = false;
            }
            // If done growing and resource still there, just return
            else if (_growingComplete)
                return;

            ProcessGrowing(deltaTime);
        }

        private void ProcessGrowing(float deltaTime)
        {
            if (Recipe == null)
                return;

            if (_space.Resource == null && _rigidInventory.HasAllInputs(Recipe))
            {
                _pauseBetweenGrowing += deltaTime;

                if (_pauseBetweenGrowing > 1f)
                {
                    StartGrowing();
                    _pauseBetweenGrowing = 0f;
                }
            }
        }

        public void StartGrowing()
        {
            for (int i = 0; i < Recipe.inputs.Count; i++)
            {
                _rigidInventory.Remove(Recipe.inputs[i].item, Recipe.inputs[i].count);
            }

            // Assumes that the seed item is the first item in the recipe
            if (Recipe.inputs[0].item.harvestableSource != null)
            {
                HarvestableItem growingItem = Recipe.outputs[0].item.harvestableSource;

                if (!_system.TryAddResource(_params.worldPosition, growingItem, true, true, 0f))
                    Debug.LogWarning("Unable to add resource onto planter space at " + _params.worldPosition);
            }
            else
            {
                Debug.LogWarning("Recipe input does not have HarvestableParent setup, should be a seed item");
            }
        }

        /// <summary>
        /// Checks if any item has been moved onto this space and if so tries to add it to the inventory
        /// </summary>
        private void CheckForIncomingItem()
        {
            if (_space.ActiveItem != null && !_space.ActiveItem.IsMoving && !_space.ActiveItem.IsReserved)
            {
                CoreItem item = _masterItemList.GetItemByID<CoreItem>(_space.ActiveItem.ID);

                int deposited = _rigidInventory.Add(item, _space.ActiveItem.count);

                if (deposited == _space.ActiveItem.count)
                {
                    _system.DeleteCoreItem(_space.ActiveItem);
                }
                else if (deposited != 0)
                {
                    _space.ActiveItem.count -= deposited;
                }
            }
        }

        private void CheckToPullItemFromAdjacentSpaces()
        {
            Vector3 spaceInFront = _params.worldPosition + _params.forward;

            if (_system.TryGetExistingGridSpace(spaceInFront, out AutomationGridSpace forwardSpace))
            {
                bool itemOnForwardSpace = forwardSpace.ActiveItem != null;
                bool planterAcceptsItem = itemOnForwardSpace ? _rigidInventory.GetSpaceAvailable(_masterItemList.GetItemByID<CoreItem>(forwardSpace.ActiveItem.ID)) > 0 : false;

                if (planterAcceptsItem && _space.ActiveItem == null)
                {
                    if (forwardSpace.ActiveItem.IsMoving)
                    {
                        _targetItem = forwardSpace.ActiveItem;
                        _targetItem.MoveComplete += OnIncomingMoveComplete;
                    }
                    else
                    {
                        StartPullOfItem(forwardSpace.ActiveItem);
                    }
                }
            }
        }

        private void OnIncomingMoveComplete(object sender, CoreItemMoveEventArgs e)
        {
            if (_targetItem == null)
                return;

            AutomationCoreItem item = _targetItem;
            _targetItem = null;

            item.MoveComplete -= OnIncomingMoveComplete;

            if (!item.IsMoving && !item.IsReserved && !item.IsConsumed)
            {
                StartPullOfItem(item);
            }
        }

        private void StartPullOfItem(AutomationCoreItem item)
        {
            item.IsReserved = true;
            AutomationGridSpace prevSpace = item.GridSpace;
            prevSpace.CancelAllMoves();

            if (AutomationItemMover.TryItemMove(item, prevSpace, _space, _rigidInventory.GetSpaceAvailable(item.ID), _system, GetMover, out IItemMoveType createdItemMover))
            {
                ItemMoved?.Invoke(this, new ItemMoveArgs(createdItemMover));
            }
            else
                Debug.LogError("Couldn't move item to expected free space");

            item.IsReserved = false;
        }

        public IItemMoveType GetMover(AutomationCoreItem item, Vector3 destination, AutomationGridSpace nextSpace)
        {
            return new AutomationLinearMove(item, destination, _system.ItemMoveTime);
        }

        public void SetRecipe(Recipe recipe)
        {
            if (recipe != Recipe)
            {
                if (Growing)
                {
                    _system.TryRemoveResource(_params.worldPosition);

                    // Put recipe items back in inventory
                    for (int i = 0; i < Recipe.inputs.Count; i++)
                    {
                        _rigidInventory.Add(Recipe.inputs[i]);
                    }

                    _growingComplete = true;
                }

                _rigidInventory.SetPileCount(recipe.inputs.Count);

                // Setup inventory with new items
                for (int i = 0; i < recipe.inputs.Count; i++)
                {
                    if (recipe.inputs[i].item != _rigidInventory.Items[i].item)
                    {
                        ItemPile removedItems = _rigidInventory.RemoveAt(i);

                        if (removedItems.count > 0)
                            _system.TryAddCoreItem(_space.position - Constants.FORWARD, removedItems.item, removedItems.count);

                        _rigidInventory.SetItemConstraintForPile(i, recipe.inputs[i].item);
                    }
                }
            }

            Recipe = recipe;
        }

        public int IsAvailable(AutomationCoreItem movingItem)
        {
            CoreItem item = _masterItemList.GetItemByID<CoreItem>(movingItem.ID);

            if (item != null)
            {
                int sameItemsIncoming = _space.GetCountOfItemsIncoming(item.itemID);
                return _rigidInventory.GetSpaceAvailable(item) - sameItemsIncoming;
            }

            return 0;
        }

        public void SetGridSpace(AutomationGridSpace space)
        {
            _space = space;
        }

        public IInventory GetInventory()
        {
            return _rigidInventory;
        }

        public AutomationProcessorData GetSaveData()
        {
            AutomationPlanterData data = new AutomationPlanterData()
            {
                typeId = ProcessorID,
                position = _params.worldPosition,
                recipeID = Recipe.outputs[0].item.itemID,
                inventory = ItemData.ConvertItemPileList(_rigidInventory.Items),
                direction = _params.forward
            };

            return data;
        }

        public void LoadData(AutomationProcessorData data)
        {
            if (data is AutomationPlanterData planterData)
            {
                Recipe = _masterItemList.GetItemByID<CoreItem>(planterData.recipeID)?.GetRecipe();

                ItemData.LoadItemsIntoInventory(planterData.inventory.ToArray(), _rigidInventory, _masterItemList);

                ProcessorID = data.typeId;

                _params = new AutomationPlanterParams(data.typeId, Recipe, data.position, data.direction);

                SetRecipe(Recipe);
            }
        }

        public void SetForwardDirection(Vector3 forward)
        {
            _params.forward = forward;
        }

        public Vector3 GetDirection()
        {
            return _params.forward;
        }

        public class Factory : PlaceholderFactory<AutomationPlanterParams, AutomationPlanter> { }
    }

    public class AutomationPlanterParams
    {
        public string ID;
        public Recipe recipe;
        public Vector3 worldPosition;
        public Vector3 forward;

        public AutomationPlanterParams(string planterID, Recipe recipe, Vector3 worldPosition, Vector3 forward)
        {
            ID = planterID;
            this.recipe = recipe;
            this.worldPosition = worldPosition;
            this.forward = forward;
        }
    }
}