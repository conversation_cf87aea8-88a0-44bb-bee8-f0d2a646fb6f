// Copyright Isto Inc.
using Isto.Core.Data;
using Isto.Core.Items;
using UnityEngine;
using Zenject;

namespace Isto.Core.Automation
{
    /// <summary>
    /// Simple Automation Processor to reserve a space in the automation system but doesn't have any interaction with it.
    /// Light Bulbs for instance would use this to hold a space but they don't interact with the system other than blocking
    /// items from moving onto the space
    /// </summary>
    public class AutomationGenericProcessor : IItemProcessor
    {
        public string ProcessorID { get; private set; }

        public bool Visible { get; private set; }

        public Vector3 Direction { get; set; }

        public AutomationGenericProcessor(string itemId)
        {
            Visible = true; // Be culling by default - we rarely don't
            ProcessorID = itemId;
        }

        public AutomationProcessorData GetSaveData()
        {
            return new AutomationProcessorData()
            {
                typeId = ProcessorID,
                direction = Direction
            };
        }

        public void LoadData(AutomationProcessorData data)
        {
            ProcessorID = data.typeId;
            Direction = data.direction;
        }

        public int IsAvailable(AutomationCoreItem movingItem)
        {
            return 0;
        }

        public void Tick(float deltaTime)
        {

        }

        public void SetVisible(bool isVisible)
        {
            Visible = isVisible;
        }

        public class Factory : PlaceholderFactory<string, AutomationGenericProcessor> { }
    }
}