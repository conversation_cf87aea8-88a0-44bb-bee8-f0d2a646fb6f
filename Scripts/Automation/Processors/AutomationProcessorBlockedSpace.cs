// Copyright Isto Inc.
using Isto.Core.Data;
using Isto.Core.Items;

namespace Isto.Core.Automation
{
    public class AutomationProcessorBlockedSpace : IItemProcessor
    {
        public string ProcessorID => "ProcessorBlockedSpace";
        public string LinkedProcessorID => _linkedProcessor != null ? _linkedProcessor.ProcessorID : "Empty";
        public IItemProcessor LinkedProcessor => _linkedProcessor;

        public bool Visible => false;

        private IItemProcessor _linkedProcessor;

        public AutomationProcessorBlockedSpace(IItemProcessor linkedProcessor)
        {
            _linkedProcessor = linkedProcessor;
        }

        public void Tick(float deltaTime)
        {
            // Does nothing
        }

        public AutomationProcessorData GetSaveData()
        {
            return null;
        }

        public int IsAvailable(AutomationCoreItem movingItem)
        {
            return 0;
        }

        public void LoadData(AutomationProcessorData data)
        {
            // Isn't saved
        }
    }
}