// Copyright Isto Inc.
using Isto.Core.Data;
using Isto.Core.Items;
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AddressableAssets;
using Zenject;

namespace Isto.Core.Automation
{
    public class AutomationCreatureHive : IItemProcessor, IAutomationGridSpaceUser, IAutomationInventory, IAutomationActionOnRemoved
    {
        public string ProcessorID { get; private set; }

        public Transform CreatureContainer { get { return _creatureContainer; } }

        public string StockPiledItemID { get => _stockPiledItemID; set { _stockPiledItemID = value; } }

        public bool Visible => true;

        public Vector3 CreatureRange => _creatureRange;

        public Vector3 ItemDroppingPosition => _itemDroppingPosition;

        // A hive isn't directional in automation, but our world representation might be directional so we can store that info
        public Vector3 HiveDirection => _hiveDirection;

        [Inject] private MasterItemList _masterItemList = default;

        private AutomationGridSpace _space;
        private string _stockPiledItemID;
        private Inventory _inventory;

        // Creature tracking
        private int _maxCreatureCount;
        private float _timePerSpawn;
        private float _spawnTimer;
        private AssetReference _creatureRef;
        private Transform _creatureContainer;
        private Vector3 _creatureRange;
        private Vector3 _itemDroppingPosition;
        private Vector3 _hiveDirection;
        private int _pendingSpawns = 0;
        private bool _spawningPaused = false; // used for testing - not meant to persist

        // Info about any initial creatures that should already exist and should be created as soon as possible
        private AutomationCreatureHiveCreatureData[] _initialCreatureData = null;

        public AutomationCreatureHive(AutomationCreatureHiveParams p)
        {
            ProcessorID = p.ID;
            _timePerSpawn = p.timePerSpawn;
            _maxCreatureCount = p.maxCreatureCount;
            _creatureRef = new AssetReference(p.CreatureAssetGUID);
            _creatureContainer = new GameObject(p.ID + " Creature Container").transform;
            _stockPiledItemID = p.StockPiledItemId;
            _inventory = new Inventory(p.pileCount, p.pileSize);
            _spawnTimer = _timePerSpawn;
            _creatureRange = p.creatureRange;
            _itemDroppingPosition = p.itemDroppingPosition;
            _hiveDirection = p.hiveDirection;
        }

        public void Tick(float deltaTime)
        {
            if (_initialCreatureData != null)
            {
                for (int i = 0; i < _initialCreatureData.Length; i++)
                {
                    SpawnCreature(_initialCreatureData[i]);
                }
                _initialCreatureData = null;
            }

            _spawnTimer -= deltaTime;

            if (ShouldSpawnCreature())
            {
                _spawnTimer = _timePerSpawn;

                SpawnCreature();
            }
        }

        public void SpawnInitialCreatures(AutomationCreatureHiveCreatureData[] currentData)
        {
            // We actually can't spawn them yet so just save the info and create them on the first tick
            _initialCreatureData = currentData;
        }

        private void SpawnCreature(AutomationCreatureHiveCreatureData data = null)
        {
            if (_creatureRef.RuntimeKeyIsValid())
            {
                _pendingSpawns++;
                _creatureRef.InstantiateAsync(_space.position, Quaternion.identity, _creatureContainer).Completed += obj =>
                {
                    _pendingSpawns--;
                    if (obj.Status == UnityEngine.ResourceManagement.AsyncOperations.AsyncOperationStatus.Succeeded && obj.Result != null)
                    {
                        IAutomationGridSpaceDisplay[] gridDisplays = obj.Result.GetComponents<IAutomationGridSpaceDisplay>();

                        for (int i = 0; i < gridDisplays.Length; i++)
                        {
                            gridDisplays[i].SetGridSpace(_space);
                        }

                        if (data != null && data.creatureInventory != null)
                        {
                            ItemData[] inventory = data.creatureInventory;

                            if (obj.Result.TryGetComponent(out IAutomationInventory creature))
                            {
                                IInventory creatureInventory = creature.GetInventory();
                                for (int i = 0; i < inventory.Length; i++)
                                {
                                    CoreItem item = _masterItemList.GetItemByID<CoreItem>(inventory[i].id);
                                    creatureInventory.Add(item, inventory[i].count);
                                }
                            }
                        }
                    }
                };
            }
        }

        private bool ShouldSpawnCreature()
        {
            if (_spawningPaused)
                return false;

            bool validTime = _spawnTimer < 0f;

            if (!validTime)
                return false;

            bool underMax = (_creatureContainer.childCount + _pendingSpawns) < _maxCreatureCount;

            return underMax;
        }

        public IInventory GetInventory()
        {
            return _inventory;
        }

        public int IsAvailable(AutomationCoreItem movingItem) => 0;

        void IAutomationActionOnRemoved.Removed()
        {
            if (_creatureContainer != null)
                GameObject.Destroy(_creatureContainer.gameObject);
        }

        public AutomationProcessorData GetSaveData()
        {
            return new AutomationCreatureHiveData()
            {
                // AutomationProcessorData
                typeId = ProcessorID,
                direction = _hiveDirection,
                position = _space.position,

                // AutomationCreatureHiveData
                timePerSapwn = _timePerSpawn,
                maxCreatureCount = _maxCreatureCount,
                creatureAssetGUID = _creatureRef.AssetGUID,
                stockpiledItemID = _stockPiledItemID,
                inventory = ItemData.ConvertItemPileList(_inventory.Items),
                inventoryPileCount = _inventory.PileCount,
                inventoryPileSize = _inventory.PileSize,
                creatureCount = _creatureContainer.childCount,
                creatureRange = _creatureRange,
                itemDroppingPosition = _itemDroppingPosition,
                currentCreaturesData = GetCreaturesData(),
            };
        }

        private AutomationCreatureHiveCreatureData[] GetCreaturesData()
        {
            int creatureCount = _creatureContainer.childCount;
            AutomationCreatureHiveCreatureData[] data = new AutomationCreatureHiveCreatureData[creatureCount];
            for (int i = 0; i < creatureCount; i++)
            {
                if (_creatureContainer.GetChild(i).TryGetComponent(out IAutomationInventory creature))
                {
                    data[i] = new AutomationCreatureHiveCreatureData();
                    IInventory inventory = creature.GetInventory();
                    if (inventory != null)
                    {
                        int creatureItemPileCount = inventory.Items.Count;
                        data[i].creatureInventory = new ItemData[creatureItemPileCount];
                        for (int j = 0; j < creatureItemPileCount; j++)
                        {
                            data[i].creatureInventory[j] = new ItemData(inventory.Items[j]);
                        }
                    }

                }
            }
            return data;
        }

        public void LoadData(AutomationProcessorData data)
        {
            if (data is AutomationCreatureHiveData hiveData)
            {
                ItemData.LoadItemsIntoInventory(hiveData.inventory.ToArray(), _inventory, _masterItemList);
            }
        }

        public void SetGridSpace(AutomationGridSpace space)
        {
            _space = space;
        }

        public bool TryGetPosition(out Vector3 position)
        {
            if (_space != null)
            {
                position = _space.position;
                return true;
            }

            position = Vector3.zero;
            return false;
        }

        public void PauseSpawning()
        {
            _spawningPaused = true;
        }

        public void UnpauseSpawning()
        {
            _spawningPaused = false;
        }
        
        public bool IsSpawningPaused()
        {
            return _spawningPaused;
        }

        public class Factory : PlaceholderFactory<AutomationCreatureHiveParams, AutomationCreatureHive> { }
    }

    public struct AutomationCreatureHiveParams
    {
        public string ID;
        public string CreatureAssetGUID;
        public string StockPiledItemId;
        public int pileCount;
        public int pileSize;
        public int maxCreatureCount;
        public float timePerSpawn;
        public Vector3 creatureRange;
        public Vector3 itemDroppingPosition;
        public Vector3 hiveDirection;

        public AutomationCreatureHiveParams(string iD, string creatureAssetGUID, string stockPileitemId, int pileCount, int pileSize, int maxCreatureCount, float timePerSpawn, Vector3 creatureRange, Vector3 itemDroppingPosition, Vector3 hiveDirection)
        {
            ID = iD;
            CreatureAssetGUID = creatureAssetGUID;
            StockPiledItemId = stockPileitemId;
            this.pileCount = pileCount;
            this.pileSize = pileSize;
            this.maxCreatureCount = maxCreatureCount;
            this.timePerSpawn = timePerSpawn;
            this.creatureRange = creatureRange;
            this.itemDroppingPosition = itemDroppingPosition;
            this.hiveDirection = hiveDirection;
        }
    }

    public class AutomationCreatureHiveData : AutomationProcessorData, IInventoryData
    {
        public int creatureCount;
        public string creatureAssetGUID;
        public string stockpiledItemID;
        public int inventoryPileSize;
        public int inventoryPileCount;
        public List<ItemData> inventory; // stores hive inventory aka honey
        public int maxCreatureCount;
        public float timePerSapwn;
        public Vector3 creatureRange;
        public Vector3 itemDroppingPosition;
        public AutomationCreatureHiveCreatureData[] currentCreaturesData;

        public AutomationCreatureHiveData() { }

        public List<ItemData> GetInventoryData()
        {
            return inventory;
        }
    }

    [Serializable]
    public class AutomationCreatureHiveCreatureData
    {
        public ItemData[] creatureInventory; // stores creature inventory aka picker pal harvest progress
    }
}