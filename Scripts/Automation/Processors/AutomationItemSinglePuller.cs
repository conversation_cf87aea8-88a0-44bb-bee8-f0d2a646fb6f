// Copyright Isto Inc.
using Isto.Core.Data;
using System;
using UnityEngine;
using Zenject;

namespace Isto.Core.Automation
{
    /// <summary>
    /// Item Processor responsible for moving items from one grid space to another, but it moves items across itself (from behind
    /// to the front) instead of only moving items that land on its own space (though it can still move those as well).
    /// </summary>
    public class AutomationItemSinglePuller : AutomationItemMover
    {
        // Public Properties
        public Vector3 PositionBehind { get { return Position - GetDirection(); } }

        private AutomationGridSpace SpaceBehind
        {
            get
            {
                if (_system.TryGetExistingGridSpace(PositionBehind, out AutomationGridSpace space))
                    return space;
                else
                    return null;
            }
        }

        // Methods

        public AutomationItemSinglePuller(int moveDistance, MovementType moveType, string processorTypeId)
             : base(moveDistance, moveType, processorTypeId)
        {

        }

        public override int IsAvailable(AutomationCoreItem movingItem)
        {
            AutomationGridSpace bSpace = SpaceBehind;
            if (bSpace != null && bSpace.ActiveItem != null && (ItemFilter == null || ItemFilter.IsAccepted(bSpace.ActiveItem)) && !bSpace.ActiveItem.IsMoving)
                // Our source tile is occupied with something we already want and it's available, so refuse incoming?
                return 0;

            return base.IsAvailable(movingItem);
        }

        public override void Tick(float deltaTime)
        {
            if (!CanTick())
                return;

            AutomationCoreItem item = GetActiveItem();

            if (!item.IsMoving)
            {
                MoveItem(item);
            }
            // If item is moving, wait for it to end its current movement before grabbing it otherwise we'll move it too early.
            else
            {
                // No need for this event if the item is on our real tile (and not on our pulling target tile) AFAIK
                if (GetGridSpace().ActiveItem != null)
                    return;

                // Also verify that we're only taking stuff that was coming onto our pulling target tile
                if (item.TargetPosition != SpaceBehind.position)
                    return;

                _targetItem = item;
                _targetItem.MoveComplete += OnIncomingMoveComplete;
            }
        }

        protected override AutomationCoreItem GetActiveItem()
        {
            AutomationCoreItem item = GetGridSpace().ActiveItem;
            if (item == null)
                item = SpaceBehind?.ActiveItem;
            return item;
        }

        private AutomationCoreItem _erroredItem;

        protected override bool CanTick()
        {
            bool result = true;
            AutomationCoreItem item = this.GetGridSpace().ActiveItem;
            if (item == null)
                item = SpaceBehind?.ActiveItem;

            if (!GetGridSpace().Powered)
                result = false;
            else if (_targetItem != null)
            {
                if (_targetItem.IsConsumed && _targetItem.IsMoving && _erroredItem != _targetItem)
                {
                    Debug.LogError($"{TimeSpan.FromSeconds(Time.timeSinceLevelLoad).ToString(@"mm\m\:ss\s")} Invalid Item State! {_targetItem.ID} is marked as Cosumed but also Moving. Mover:{_targetItem.MoveType}, " +
                        $"Position:{_targetItem.position}, TargetPosition:{_targetItem.TargetPosition}. At grid space: {Position}");

                    _erroredItem = _targetItem;

#if !UNITY_EDITOR
                    if (_targetItem.GridSpace != null)
                    {
                        Debug.LogError("Trying to force stuck item back onto space");
                        _targetItem.GridSpace.MoveItemToSpace(_targetItem.MoveType);
                    } 
#endif
                }

                result = false;
            }
            else if (!CanTakeItem(item))
                result = false;

            return result;
        }

        AutomationCoreItem _targetItem = null;
        private void OnIncomingMoveComplete(object sender, CoreItemMoveEventArgs e)
        {
            if (_targetItem == null)
                return;

            AutomationCoreItem item = _targetItem;
            _targetItem = null;

            item.MoveComplete -= OnIncomingMoveComplete;

            if (!item.IsMoving && !item.IsReserved && !item.IsConsumed)
            {
                MoveItem(item);
            }
        }

        // Data Management

        public override AutomationProcessorData GetSaveData()
        {
            AutomationItemMoverData baseData = base.GetSaveData() as AutomationItemMoverData;
            return new AutomationItemSinglePullerData()
            {
                typeId = baseData.typeId,
                direction = baseData.direction,
                visible = baseData.visible,
                distance = baseData.distance,
                moveType = baseData.moveType
            };
        }

        public override void LoadData(AutomationProcessorData data)
        {
            AutomationItemSinglePullerData moverData = data as AutomationItemSinglePullerData;
            base.LoadData(moverData);
        }

        public new class Factory : PlaceholderFactory<int, MovementType, string, AutomationItemSinglePuller> { }
    }
}