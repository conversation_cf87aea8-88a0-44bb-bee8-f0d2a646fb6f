// Copyright Isto Inc.
using Isto.Core.Data;
using Isto.Core.Items;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Automation
{
    public class AutomationItemContainer : IItemProcessor, IAutomationGridSpaceUser, IAutomationInventory
    {
        public string ProcessorID { get; private set; }

        public bool Visible { get; private set; }

        [Inject] private MasterItemList _masterItemList = default;
        [Inject] private AutomationSystem _system = default;

        private AutomationGridSpace _space;
        private Inventory _inventory;
        private bool _pullItemsFromNeighborSpaces;
        private AutomationCoreItem _targetItem;
        private Vector3 _nextOffsetForPull = Vector3.right;

        public AutomationItemContainer(AutomationItemContainerParams conatinerParams)
        {
            if (conatinerParams.isRigidInventory)
            {
                _inventory = new RigidInventory(conatinerParams.pileCount, conatinerParams.pileSize);
            }
            else
            {
                _inventory = new Inventory(conatinerParams.pileCount, conatinerParams.pileSize);
            }

            ProcessorID = conatinerParams.typeID;

            Visible = true;

            _pullItemsFromNeighborSpaces = conatinerParams.pullsItemsFromNeighbors;
        }

        public IInventory GetInventory()
        {
            return _inventory;
        }

        public void Resize(int pileCount, int pileSize)
        {
            if (_inventory == null)
                return;

            _inventory.SetPileCount(pileCount);
            _inventory.PileSize = pileSize;
        }

        public virtual int IsAvailable(AutomationCoreItem movingItem)
        {
            if (_inventory.GetContainerPercentUsed() == 1)
                return 0;

            int spaceAvailable = _inventory.GetSpaceAvailable(movingItem.ID);
            int itemsAlreadyIncoming = _space.GetCountOfItemsIncoming(movingItem.ID);

            return spaceAvailable - itemsAlreadyIncoming;
        }

        public void SetGridSpace(AutomationGridSpace space)
        {
            _space = space;
        }

        public void Tick(float deltaTime)
        {
            CheckForIncomingItem();

            if (_pullItemsFromNeighborSpaces)
            {
                CheckToPullItemFromAdjacentSpaces();
            }
        }

        public void SetVisibility(bool isVisible)
        {
            Visible = isVisible;
        }

        // Item Movement

        /// <summary>
        /// Checks if any item has been moved onto this space and if so tries to add it to the inventory
        /// </summary>
        private void CheckForIncomingItem()
        {
            if (_space.ActiveItem != null && !_space.ActiveItem.IsMoving && !_space.ActiveItem.IsReserved)
            {
                CoreItem item = _masterItemList.GetItemByID<CoreItem>(_space.ActiveItem.ID);

                int deposited = _inventory.Add(item, _space.ActiveItem.count);

                if (deposited == _space.ActiveItem.count)
                {
                    _system.DeleteCoreItem(_space.ActiveItem);
                }
                else if (deposited != 0)
                {
                    _space.ActiveItem.count -= deposited;
                }
            }
        }

        private void CheckToPullItemFromAdjacentSpaces()
        {
            if (_targetItem != null || _space.ActiveItem != null)
                return;

            for (int i = 0; i < 4; i++)
            {
                if (_system.TryGetExistingGridSpace(_space.position + _nextOffsetForPull, out AutomationGridSpace nextSpace))
                {
                    bool itemOnSpace = nextSpace.ActiveItem != null;
                    bool invAcceptsItem = itemOnSpace ? _inventory.GetSpaceAvailable(_masterItemList.GetItemByID<CoreItem>(nextSpace.ActiveItem.ID)) > 0 : false;

                    if (invAcceptsItem && _space.ActiveItem == null)
                    {
                        if (nextSpace.ActiveItem.IsMoving)
                        {
                            _targetItem = nextSpace.ActiveItem;
                            _targetItem.MoveComplete += OnIncomingMoveComplete;
                        }
                        else
                        {
                            StartPullOfItem(nextSpace.ActiveItem);
                            _nextOffsetForPull = Quaternion.Euler(0, 90, 0) * _nextOffsetForPull;
                            return;
                        }
                    }
                }

                _nextOffsetForPull = Quaternion.Euler(0, 90, 0) * _nextOffsetForPull;
            }
        }

        private void OnIncomingMoveComplete(object sender, CoreItemMoveEventArgs e)
        {
            if (_targetItem == null)
                return;

            AutomationCoreItem item = _targetItem;
            _targetItem = null;

            item.MoveComplete -= OnIncomingMoveComplete;

            if (!item.IsMoving && !item.IsReserved && !item.IsConsumed)
            {
                StartPullOfItem(item);
            }
        }

        private void StartPullOfItem(AutomationCoreItem item)
        {
            item.IsReserved = true;
            AutomationGridSpace prevSpace = item.GridSpace;
            prevSpace.CancelAllMoves();

            if (!AutomationItemMover.TryItemMove(item, prevSpace, _space, item.count, _system, GetMover, out IItemMoveType createdItemMover))
                Debug.LogError("Couldn't move item to expected free space");

            item.IsReserved = false;
        }

        public IItemMoveType GetMover(AutomationCoreItem item, Vector3 destination, AutomationGridSpace nextSpace)
        {
            return new AutomationLinearMove(item, destination, _system.ItemMoveTime);
        }

        // Data Management

        public AutomationProcessorData GetSaveData()
        {
            if (ProcessorID.Equals(MasterItemList.ITEM_ENTRANCE_ID))
                return null;

            AutomationItemContainerData containerData = new AutomationItemContainerData()
            {
                typeId = ProcessorID,
                inventoryPileCount = _inventory.PileCount,
                inventoryPileSize = _inventory.PileSize,
                visible = Visible,
                isRigidInventory = _inventory is IRigidInventory,
                pullsItemsFromNeighbors = _pullItemsFromNeighborSpaces
            };

            containerData.inventory = ItemData.ConvertItemPileList(_inventory.Items).ToArray();

            return containerData;
        }

        public void LoadData(AutomationProcessorData data)
        {
            if (data is AutomationItemContainerData containerData)
            {
                Visible = containerData.visible;

                ItemData.LoadItemsIntoInventory(containerData.inventory, _inventory, _masterItemList);
            }
        }

        public class Factory : PlaceholderFactory<AutomationItemContainerParams, AutomationItemContainer> { }
    }

    public struct AutomationItemContainerParams
    {
        public int pileCount;
        public int pileSize;
        public string typeID;
        public bool isRigidInventory;
        public bool pullsItemsFromNeighbors;

        public AutomationItemContainerParams(int pileCount, int pileSize, string typeID, bool isRigidInventory, bool pullsItemsFromNeighbors)
        {
            this.pileCount = pileCount;
            this.pileSize = pileSize;
            this.typeID = typeID;
            this.isRigidInventory = isRigidInventory;
            this.pullsItemsFromNeighbors = pullsItemsFromNeighbors;
        }
    }

    public class ItemPileComparer : IComparer<ItemPile>
    {
        public int Compare(ItemPile x, ItemPile y)
        {
            if (x.item == null)
            {
                return 1;
            }
            else if (y.item == null)
            {
                return -1;
            }
            else
            {
                int value = string.Compare(x.item.itemName, y.item.itemName);
                return value;
            }
        }
    }
}
