// Copyright Isto Inc.
using Isto.Core.Data;
using Isto.Core.Items;
using System;
using UnityEngine;
using Zenject;

namespace Isto.Core.Automation
{
    public class AutomationTimedItemPusher : IItemProcessor, IDirectionalItem, IAutomationGridSpaceUser
    {
        // Events

        public event EventHandler<ItemMoveArgs> ItemPushed;

        // Public Variables

        public string ProcessorID { get; private set; }
        public bool Visible { get { return true; } }

        // Debug info accessors
        public Vector3 SourceSpacePosition => _processorSpace.position + _forward;
        public float CooldownTimer { get { return _pushTimer; } }
        public string TargetItemID => _targetItem?.ID ?? "null";
        public string SourceItemID => _system.DoesSpaceExist(SourceSpacePosition) ? SourceSpace.ActiveItem?.ID ?? "null" : "null";
        public bool WaitingToPush => _waitingToPush;

        // Private Properties

        // Don't cache these two references, they can get destroyed and re-created
        // Space that items pass over
        private AutomationGridSpace SourceSpace { get { return _system.GetOrCreateGridSpace(SourceSpacePosition); } }
        // Space to push items onto
        private AutomationGridSpace DestinationSpace { get { return _system.GetOrCreateGridSpace(SourceSpacePosition + _forward * _pushDistance); } }

        // Private Variables

        [Inject] private AutomationSystem _system = default;

        private Vector3 _forward = Vector3.right;
        private AutomationGridSpace _processorSpace; // Space that this pusher sits on
        private AutomationCoreItem _targetItem;

        private int _pushDistance;
        private float _pushCoolDownTime;
        //private int _pushInterval;
        //private int _intervalCounter;

        private bool _waitingToPush;
        private float _pushTimer;

        public AutomationTimedItemPusher(AutomationTimedItemPusherParams p)
        {
            _pushDistance = p.distance;
            _pushCoolDownTime = p.pushCoolDownTime;

            ProcessorID = p.processorTypeId;
        }

        public void SetGridSpace(AutomationGridSpace space)
        {
            _processorSpace = space;
        }

        public int IsAvailable(AutomationCoreItem movingItem)
        {
            return 0;
        }

        public void SetForwardDirection(Vector3 forward)
        {
            _forward = forward;
        }

        public Vector3 GetDirection()
        {
            return _forward;
        }

        public void Tick(float deltaTime)
        {
            if (_waitingToPush)
                return;

            _pushTimer -= deltaTime;

            AutomationGridSpace sourceSpace = null;

            if (_system.DoesSpaceExist(SourceSpacePosition))
            {
                sourceSpace = SourceSpace;
            }

            if (_targetItem == null && sourceSpace?.ActiveItem != null && ItemMovingToThisSpace(sourceSpace.ActiveItem))
            {
                _targetItem = sourceSpace.ActiveItem;
            }
            else if (_targetItem != null)
            {
                if (sourceSpace?.ActiveItem == null)
                {
                    _targetItem = null;
                }
                else if (sourceSpace.ActiveItem != _targetItem)
                {
                    _targetItem.MoveComplete -= OnIncomingMoveComplete;
                    _targetItem = sourceSpace.ActiveItem;
                }
            }

            if (ShouldPush())
            {
                if (_targetItem.IsMoving)
                {
                    _targetItem.MoveComplete += OnIncomingMoveComplete;
                    _waitingToPush = true;
                }
                else
                {
                    TryPushItem();
                }
            }
        }

        private bool ShouldPush()
        {
            if (_targetItem == null)
                return false;

            // If not moving to our space or on our space, don't try and push
            if (_targetItem.TargetPosition != SourceSpacePosition)
                return false;

            return _pushTimer < 0f;
        }

        private void OnIncomingMoveComplete(object sender, CoreItemMoveEventArgs e)
        {
            _targetItem.MoveComplete -= OnIncomingMoveComplete;
            _waitingToPush = false;

            TryPushItem();
        }

        private bool ItemMovingToThisSpace(AutomationCoreItem item) => item.TargetPosition == SourceSpacePosition;

        private bool TryPushItem()
        {
            _pushTimer = _pushCoolDownTime;

            AutomationGridSpace sourceSpace = SourceSpace;
            AutomationGridSpace destinationSpace = DestinationSpace;

            if (_system.CanMoveItemToGridSpace(destinationSpace, _targetItem, out int spaceAvailable))
            {
                bool movingAll = spaceAvailable == _targetItem.count;

                if (AutomationItemMover.TryItemMove(_targetItem, SourceSpace, destinationSpace, spaceAvailable, _system, GetMover, out IItemMoveType activeItemMover))
                {
                    ItemPushed?.Invoke(this, new ItemMoveArgs(activeItemMover));

                    if (movingAll)
                        _targetItem = null;

                    return true;
                }
            }

            return false;
        }

        public IItemMoveType GetMover(AutomationCoreItem item, Vector3 destination, AutomationGridSpace nextSpace)
        {
            return new AutomationLinearMove(_targetItem, destination, _system.ItemMoveTime);
        }

        // Data Management

        public AutomationProcessorData GetSaveData()
        {
            AutomationItemPusherData moverData = new AutomationItemPusherData()
            {
                direction = _forward,
                distance = _pushDistance,
                pushCooldownTime = _pushCoolDownTime != 0 ? _pushCoolDownTime : 1f,         // Setting a default of 1 incase of loading from previous saves without this data set, which would default to 0
                typeId = ProcessorID
            };

            return moverData;
        }

        public void LoadData(AutomationProcessorData data)
        {
            if (data is AutomationItemPusherData moverData)
            {
                SetForwardDirection(moverData.direction);
                _pushDistance = moverData.distance;
                _pushCoolDownTime = moverData.pushCooldownTime;
                ProcessorID = moverData.typeId;
            }
        }

        public class Factory : PlaceholderFactory<AutomationTimedItemPusherParams, AutomationTimedItemPusher> { }
    }

    public class AutomationTimedItemPusherParams
    {
        public int distance;
        public string processorTypeId;
        public float pushCoolDownTime;

        public AutomationTimedItemPusherParams(int distance, float pushCoolDownTime, string processorTypeId)
        {
            this.distance = distance;
            this.pushCoolDownTime = pushCoolDownTime;
            this.processorTypeId = processorTypeId;
        }
    }
}