// Copyright Isto Inc.
using Isto.Core.Data;
using Isto.Core.Items;
using System;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Automation
{
    public class AutomationFactory : IItemProcessor, IDirectionalItem, IAutomationGridSpaceUser, IAutomationInventory, IAutomationActionOnRemoved
    {
        public event EventHandler<AutomationItemEventArgs> ItemCreated;
        public event EventHandler<FactoryPickedUpItemArgs> ItemPickedUp;

        // Ideally this configuration would be exposed but that's not urgent right now since the existing game items should be stable.
        public static int FACTORY_PILE_COUNT = 10;
        public static int FACTORY_PILE_SIZE = 99;
        // For modern factories 0,0,0 is the middle of a 3x3 area.
        // Center tile (0,0,0) and output tile (0,0,1) are excluded from the footprint because they are part of the output mechanism.
        public static readonly Vector3[] FACTORY_FOOTPRINT =
        {
            // order is important, should be according to priority of freeing the tile of undesirable items
            // mechanism blocking tiles first
            new Vector3(0, 0, -1), // behind
            new Vector3(1, 0, 0), // right
            new Vector3(-1, 0, 0), // left
            // then corners, which are not used in automation
            new Vector3(1, 0, 1),
            new Vector3(1, 0, -1),
            new Vector3(-1, 0, 1),
            new Vector3(-1, 0, -1)
        };

        // Ideally this configuration would be exposed but that's not urgent right now since the existing game items should be stable.
        // For modern deer 0,0,0 is the bottom left or right corner of a 2x2 area.
        // The real factory space is next to the output area, the back spaces are empty, and the last one is a ProcessorProxy.
        // ProcessorProxy tile is excluded from the footprints because it is part of the output mechanism (found items will go there).
        // Z = 1
        public static readonly Vector3[] DEER_FACTORY_FOOTPRINT_NE = { new Vector3(0, 0, 0), new Vector3(-1, 0, 0), new Vector3(-1, 0, -1) };
        // Z = -1
        public static readonly Vector3[] DEER_FACTORY_FOOTPRINT_SW = { new Vector3(0, 0, 0), new Vector3(-1, 0, 0), new Vector3(-1, 0, 1) };
        // X = 1
        public static readonly Vector3[] DEER_FACTORY_FOOTPRINT_SE = { new Vector3(0, 0, 0), new Vector3(0, 0, 1), new Vector3(-1, 0, 1) };
        // X = -1
        public static readonly Vector3[] DEER_FACTORY_FOOTPRINT_NW = { new Vector3(0, 0, 0), new Vector3(0, 0, 1), new Vector3(1, 0, 1) };


        public Recipe Recipe { get { return _recipe; } }

        public bool Cooking { get; private set; }

        public bool WaitingToDropItem { get { return _outputItem != null; } }

        public float CookingPercent { get { return _recipe == null ? 0f : _currentCookingTime / _recipe.cookTime; } }

        public string ProcessorID { get; private set; }

        public bool Visible { get { return true; } }

        [Inject] private MasterItemList _masterItemList = default;
        [Inject] private AutomationSystem _system = default;
        [Inject] private AutomationCoreItem.Factory _itemFactory = default;

        private AutomationGridSpace _space;
        private AutomationGridSpace _proxySpace;
        private Vector3 _forward = Vector3.right;
        private RigidInventory _inventory;
        private Recipe _recipe;
        private AutomationCoreItem _outputItem;
        private float _currentCookingTime;
        private Vector3Int _entranceOffset;
        private bool _allowIntakeWhileCooking;
        private bool _createDisplayItemOnOutput;
        private AutomationCoreItem _targetItem;
        private bool _pullFromSpaceInfront;
        private Vector3[] _footprint;

        public AutomationFactory(AutomationFactoryParams factoryParams)
        {
            int pileCount = factoryParams.recipe == null ? 1 : factoryParams.recipe.inputs.Count;

            _inventory = new RigidInventory(pileCount, factoryParams.pileSize);

            SetRecipe(factoryParams.recipe);

            ProcessorID = factoryParams.Id;
            _entranceOffset = factoryParams.entranceOffset;
            _allowIntakeWhileCooking = factoryParams.allowIntakeWhileCooking;
            _createDisplayItemOnOutput = factoryParams.createDisplayItemOnOutput;
            _pullFromSpaceInfront = factoryParams.pullFromSpaceInfront;
            _footprint = factoryParams.footPrint?.ToArray();
        }

        /// <summary>
        /// This will potentially delete items from the inventory if changing to a new recipe.  Make sure you've dealt with that first
        /// </summary>
        /// <param name="recipe"></param>
        public void SetRecipe(Recipe recipe)
        {
            if (recipe == _recipe)
                return;

            if (Cooking && recipe != _recipe)
            {
                Cooking = false;
                _currentCookingTime = 0f;
                _outputItem = null;
            }

            if (recipe != null)
            {
                _inventory.SetPileCount(recipe.inputs.Count);

                for (int i = 0; i < recipe.inputs.Count; i++)
                {
                    _inventory.SetItemConstraintForPile(i, recipe.inputs[i].item);
                }
            }
            else
            {
                _inventory.SetPileCount(1);
                _inventory.SetItemConstraintForPile(0, null);
            }

            _recipe = recipe;
        }

        public Vector3 GetDirection()
        {
            return _forward;
        }

        public int IsAvailable(AutomationCoreItem item)
        {
            if (!_space.Powered)
                return 0;

            // If not allowing items to be input while cooking, check if anything is already coming in as well
            AutomationCoreItem activeItem = _proxySpace != null ? _proxySpace.ActiveItem : _space.ActiveItem;

            if ((!_allowIntakeWhileCooking && Cooking) || (!_allowIntakeWhileCooking && activeItem != null))
                return 0;

            int spaceAvailable;

            if (Recipe != null && Recipe.IsIngredient(item.ID))
            {
                int recipeAmountRequired = int.MaxValue;

                for (int i = 0; i < Recipe.inputs.Count; i++)
                {
                    if (Recipe.inputs[i].item != null && Recipe.inputs[i].item.itemID.Equals(item.ID))
                    {
                        recipeAmountRequired = Recipe.inputs[i].count;
                    }
                }

                int amountInInventory = _inventory.GetCountOfItem(item.ID);

                spaceAvailable = Mathf.Clamp(_inventory.GetSpaceAvailable(item.ID), 0, recipeAmountRequired - amountInInventory);
            }
            else
                spaceAvailable = 0;

            bool nothingIncoming = activeItem == null;

            return nothingIncoming ? spaceAvailable : 0;
        }

        public void SetForwardDirection(Vector3 forward)
        {
            _forward = forward;
        }

        public void SetGridSpace(AutomationGridSpace space)
        {
            _space = space;

            CreateProxyProcessor();
            CreateBlockers();
        }

        private void CreateProxyProcessor()
        {
            // Only need the proxy if the entrance offset is non-zero
            if (_entranceOffset != Vector3Int.zero)
            {
                _proxySpace = _system.GetOrCreateGridSpace(_space.position + _entranceOffset);

                _proxySpace.itemProcessor = new AutomationProcessorProxy(this);
            }
        }

        // We don't want stuff to be thrown on the tiles that are visually part of the factory but are not part of the automation
        // To solve that we pad the automation footprint with blocker tiles until we match the visual footprint
        private void CreateBlockers()
        {
            // Normal factories are centered and need corner blockers around them
            if (_entranceOffset == Vector3Int.zero)
            {
                AutomationGridSpace blockerSpace;
                blockerSpace = _system.GetOrCreateGridSpace(_space.position + Vector3.back + Vector3.left);
                blockerSpace.itemProcessor = new AutomationProcessorBlockedSpace(this);
                blockerSpace = _system.GetOrCreateGridSpace(_space.position + Vector3.back + Vector3.right);
                blockerSpace.itemProcessor = new AutomationProcessorBlockedSpace(this);
                blockerSpace = _system.GetOrCreateGridSpace(_space.position + Vector3.forward + Vector3.left);
                blockerSpace.itemProcessor = new AutomationProcessorBlockedSpace(this);
                blockerSpace = _system.GetOrCreateGridSpace(_space.position + Vector3.forward + Vector3.right);
                blockerSpace.itemProcessor = new AutomationProcessorBlockedSpace(this);
            }
            // Offset factories are mini deer, they are 2x2 and they have 2 corners that need blocking
            else
            {
                AutomationGridSpace blockerSpace;
                for (int i = 0; i < _footprint.Length; i++)
                {
                    blockerSpace = _system.GetOrCreateGridSpace(_space.position + _footprint[i]);
                    if (blockerSpace.itemProcessor == null)
                        blockerSpace.itemProcessor = new AutomationProcessorBlockedSpace(this);
                }
            }
        }

        public void Removed()
        {
            if (_entranceOffset != Vector3Int.zero)
            {
                // Remove the proxy processor
                _system.TryRemoveProcessor(_space.position + _entranceOffset);
                // Remove the blockers (the proxy processor space should not be part of it by design)
                for (int i = 0; i < _footprint.Length; i++)
                {
                    // The processor's position might be included in the footprint, make sure we don't go recursive
                    if (_footprint[i] == Vector3.zero)
                        continue;

                    _system.TryRemoveProcessor(_space.position + _footprint[i]);
                }
            }
            else
            {
                // Remove the blockers
                _system.TryRemoveProcessor(_space.position + Vector3.back + Vector3.left);
                _system.TryRemoveProcessor(_space.position + Vector3.back + Vector3.right);
                _system.TryRemoveProcessor(_space.position + Vector3.forward + Vector3.left);
                _system.TryRemoveProcessor(_space.position + Vector3.forward + Vector3.right);
            }
        }

        // Intended to be used as the factory is about to be destroyed, which is why we can simply dump out the ingredients
        public void AbortCooking()
        {
            if (_recipe == null)
                return;

            if (Cooking)
            {
                if (_currentCookingTime >= _recipe.cookTime)
                {
                    AutomationCoreItem item = _itemFactory.Create(new CoreItemParams(_recipe.outputs[0].item.itemID, _recipe.outputs[0].count, _space.position));
                    if (!_system.TryAddCoreItemToSpace(_space.position, item, autoCollectItem: true, triggerEvent: true))
                    {
                        Debug.LogError($"Factory tried to dump recipe output {item.ID} to space at {_space.position} but could not.");
                    }
                }
                else
                {
                    foreach (ItemPile pile in _recipe.inputs)
                    {
                        AutomationCoreItem item = _itemFactory.Create(new CoreItemParams(pile.item.itemID, pile.count, _space.position));
                        if (!_system.TryAddCoreItemToSpace(_space.position, item, autoCollectItem: true, triggerEvent: true))
                        {
                            Debug.LogError($"Factory tried to return ingredient {item.ID} to space at {_space.position} but could not.");
                        }
                    }
                }

                _currentCookingTime = 0f;
                Cooking = false;
            }
        }

        public void Tick(float deltaTime)
        {
            if (!_space.Powered)
                return;

            if (_pullFromSpaceInfront && !WaitingToDropItem)
                CheckToPullItemFromAdjacentSpaces();

            AutomationGridSpace pullingSpace = _proxySpace != null ? _proxySpace : _space;
            AutomationCoreItem activeItem = pullingSpace?.ActiveItem;

            if (activeItem != null && !activeItem.IsMoving && !activeItem.IsReserved && !activeItem.IsConsumed)
            {
                CoreItem item = _masterItemList.GetItemByID<CoreItem>(activeItem.ID);

                int deposited = _inventory.Add(item, activeItem.count);

                if (deposited == activeItem.count) // depleted source
                {
                    //Debug.Log("POSITION:" + _space.ActiveItem.position);
                    //Debug.Log(_space.ActiveItem.MoveType);

                    //Send the item to the Factory.cs script, which will animate the sucking up
                    CoreItem core = _masterItemList.GetItemByID<CoreItem>(activeItem.ID);

                    ItemPickedUp?.Invoke(this, new FactoryPickedUpItemArgs() { item = core, position = activeItem.position, moveType = activeItem.MoveType });
                    _system.DeleteCoreItem(activeItem);
                }
                else if (deposited != 0) // removed part of stack
                {
                    pullingSpace.ActiveItem.count -= deposited;
                }
                else // deposited == 0, nothing was picked up
                {
                    // Try to push item outside of factory to make space for desired items
                    AutomationGridSpace dropSpace = _system.GetOrCreateGridSpace(_space.position + _forward);
                    if (_system.CanMoveItemToGridSpace(dropSpace, activeItem, out int spaceAvailable))
                    {
                        if (dropSpace.TrySetItem(activeItem))
                        {
                            pullingSpace.ClearItem(activeItem);
                            dropSpace.MoveItemToSpace(new AutomationLinearMove(activeItem, dropSpace.position, _system.ItemMoveTime));
                        }
                    }
                }
            }

            CheckForStuckItems();

            ProcessCooking(deltaTime);
        }

        private void CheckToPullItemFromAdjacentSpaces()
        {
            Vector3 spaceInFront = (_proxySpace != null ? _proxySpace.position : _space.position) - _forward;

            if (_system.TryGetExistingGridSpace(spaceInFront, out AutomationGridSpace forwardSpace))
            {
                bool itemOnForwardSpace = forwardSpace.ActiveItem != null;
                bool noItemsIncoming = _proxySpace.ActiveItem == null && _space.ActiveItem == null;
                // Note: planter is not a factory - this is only applicable to the deer
                // TODO: make sure this blocks the factory from accepting other inputs if it is taking something in
                // example: springboard facing right, next to a minideer facing left, the board does not drop on the deer's dish but on its body space...
                // --> if you have continuous supply to the springboard, that will attempt to double drop food onto the deer (and the springed food will get stuck in the air)
                bool planterAcceptsItem = itemOnForwardSpace ? _inventory.GetSpaceAvailable(_masterItemList.GetItemByID<CoreItem>(forwardSpace.ActiveItem.ID)) > 0 : false;

                if (planterAcceptsItem && noItemsIncoming)
                {
                    if (forwardSpace.ActiveItem.IsMoving)
                    {
                        _targetItem = forwardSpace.ActiveItem;
                        _targetItem.MoveComplete += OnIncomingMoveComplete;
                    }
                    else
                    {
                        StartPullOfItem(forwardSpace.ActiveItem);
                    }
                }
            }
        }

        private void OnIncomingMoveComplete(object sender, CoreItemMoveEventArgs e)
        {
            if (_targetItem == null)
                return;

            AutomationCoreItem item = _targetItem;
            _targetItem = null;

            item.MoveComplete -= OnIncomingMoveComplete;

            if (!item.IsMoving && !item.IsReserved && !item.IsConsumed)
            {
                StartPullOfItem(item);
            }
        }

        private void StartPullOfItem(AutomationCoreItem item)
        {
            item.IsReserved = true;
            AutomationGridSpace prevSpace = item.GridSpace;
            prevSpace.CancelAllMoves();

            AutomationGridSpace targetSpace = _proxySpace != null ? _proxySpace : _space;
            int spaceAvail = _inventory.GetSpaceAvailable(item.ID);

            //if (!AutomationItemMover.TryItemMove(item, prevSpace, targetSpace, item.count, _system, GetMover, out IItemMoveType createdItemMover))
            //Debug.LogError($"Couldn't move item to expected free space. From:{prevSpace.position} to:{targetSpace.position}");

            AutomationItemMover.TryItemMove(item, prevSpace, targetSpace, spaceAvail, _system, GetMover, out IItemMoveType createdItemMover);

            item.IsReserved = false;
        }

        public IItemMoveType GetMover(AutomationCoreItem item, Vector3 destination, AutomationGridSpace nextSpace)
        {
            return new AutomationLinearMove(item, destination, _system.ItemMoveTime);
        }

        private void CheckForStuckItems()
        {
            AutomationGridSpace processingTile = _proxySpace != null ? _proxySpace : _space;
            if (_footprint == null || processingTile.ActiveItem != null)
                return;

            AutomationGridSpace secondaryTile;
            AutomationCoreItem item;
            for (int i = 0; i < _footprint.Length; i++)
            {
                if (_system.TryGetExistingGridSpace(_space.position + _footprint[i], out secondaryTile))
                {
                    if (secondaryTile?.ActiveItem != null && !secondaryTile.ActiveItem.IsMoving && !secondaryTile.ActiveItem.IsReserved && !secondaryTile.ActiveItem.IsConsumed)
                    {
                        item = secondaryTile.ActiveItem;

                        // If the item is not junk, abort! The factory should be waiting on these.
                        if (!ShouldItemBeDiscarded(secondaryTile))
                            continue;

                        // we're clogging our main tile on purpose with the items from all over our footprint. so ignore the type constraints
                        // the items will be pushed out
                        if (processingTile.TrySetItem(item, ignoreProcessor: true))
                        {
                            secondaryTile.ClearItem(item);
                            processingTile.MoveItemToSpace(new AutomationLinearMove(item, processingTile.position, _system.ItemMoveTime * 0.5f));
                            item.SetGridSpace(processingTile);
                            break;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// This method checks the item on the specified tile and tells you if it has to be removed from the factory's area or kept
        /// </summary>
        /// <param name="itemTile">A tile which has an ActiveItem on it</param>
        /// <returns>true if the item has to be discarded</returns>
        private bool ShouldItemBeDiscarded(AutomationGridSpace itemTile)
        {
            bool result = false;

            // If it's on a blocked space, any item gets taken out
            if (itemTile.itemProcessor is AutomationProcessorBlockedSpace)
                result = true;
            // If it's the factory space, we should be a mini deer with a proxy - when we have a proxy, the factory space is basically a blocked space
            else if (itemTile == _space && _proxySpace != null)
                result = true;
            // If it's on another space, take it out only if it's incompatible (I'm assuming if there is no recipe set, everything is incompatible)
            else if (Recipe == null || !Recipe.IsIngredient(itemTile.ActiveItem.ID))
                result = true;

            return result;
        }

        private void ProcessCooking(float deltaTime)
        {
            if (_recipe == null)
                return;

            // If already cooking, update timers and keep cooking
            if (Cooking)
            {
                _currentCookingTime = Mathf.Clamp(_currentCookingTime + deltaTime, 0, _recipe.cookTime);

                if (_currentCookingTime >= _recipe.cookTime)
                    CookingComplete();
            }
            // If not cooking and the factory has the required items for the recipe, try and start cooking
            else if (_recipe != null && _inventory.HasAllInputs(_recipe))
            {
                TryToCook();
            }
        }

        private void TryToCook()
        {
            if (Cooking)
                return;

            if (_inventory.HasAllInputs(_recipe))
            {
                Cooking = true;
                _currentCookingTime = 0;

                // Remove the recipe items from the inventory
                for (int i = 0; i < _recipe.inputs.Count; i++)
                {
                    _inventory.Remove(_recipe.inputs[i].item, _recipe.inputs[i].count);
                }
            }
        }

        private void CookingComplete()
        {
            if (_recipe.outputs.Count > 0)
            {
                if (!WaitingToDropItem)
                {
                    _outputItem = _itemFactory.Create(new CoreItemParams(_recipe.outputs[0].item.itemID, _recipe.outputs[0].count, _space.position));
                }

                if (TryDropItem())
                {
                    _currentCookingTime = 0f;
                    Cooking = false;
                }
            }
            else
            {
                _currentCookingTime = 0f;
                Cooking = false;
            }
        }

        private bool TryDropItem()
        {
            AutomationGridSpace nextSpace = _system.GetOrCreateGridSpace(_space.position + _forward);

            if (_system.CanMoveItemToGridSpace(nextSpace, _outputItem, out int spaceAvailable))
            {
                ItemCreated?.Invoke(this, new AutomationItemEventArgs(_outputItem, _recipe.outputs[0].item));

                //Debug.Log($"Factory moving {_outputItem.ID}, from {_space.position} to {nextSpace.position}");

                _system.TryAddCoreItemToSpace(nextSpace.position, _outputItem, false, _createDisplayItemOnOutput);

                nextSpace.MoveItemToSpace(new AutomationLinearMove(_outputItem, nextSpace.position, _system.ItemMoveTime));

                _outputItem = null;

                return true;
            }

            return false;
        }

        public IInventory GetInventory()
        {
            return _inventory;
        }

        public void CreateDisplayPrefabWhenItemCreated(bool createPrefab)
        {
            _createDisplayItemOnOutput = createPrefab;
        }

        public override string ToString()
        {
            return $"Factory.  Recipe: {_recipe?.name}";
        }

        public AutomationProcessorData GetSaveData()
        {
            return new AutomationFactoryData()
            {
                direction = _forward,
                recipeItemId = Recipe != null ? Recipe.outputs[0].item.itemID : "",
                cookingPercent = CookingPercent,
                itemWaitingForOutput = _outputItem == null ? null : new ItemData(_outputItem.ID, "", _outputItem.count),
                inventory = ItemData.ConvertItemPileList(_inventory.Items),
                typeId = ProcessorID,
                entranceOffset = _entranceOffset,
                pullFromSpaceInfront = _pullFromSpaceInfront
            };
        }

        public void LoadData(AutomationProcessorData data)
        {
            AutomationFactoryData factoryData = data as AutomationFactoryData;

            ProcessorID = factoryData.typeId;
            _entranceOffset = Vector3Int.RoundToInt(factoryData.entranceOffset);
            _pullFromSpaceInfront = factoryData.pullFromSpaceInfront;

            SetForwardDirection(data.direction);

            bool isMiniDeerFactory = ProcessorID.Contains("MiniDeerCaptured");

            SetRecipe(_masterItemList.GetItemByID<CoreItem>(factoryData.recipeItemId)?.GetRecipe(isMiniDeerFactory));

            ItemData.LoadItemsIntoInventory(factoryData.inventory.ToArray(), _inventory, _masterItemList);

            if (factoryData.cookingPercent != 0)
            {
                _currentCookingTime = _recipe.cookTime * factoryData.cookingPercent;
                Cooking = true;
            }

            if (factoryData.itemWaitingForOutput != null)
                _outputItem = _itemFactory.Create(new CoreItemParams(factoryData.itemWaitingForOutput.id, factoryData.itemWaitingForOutput.count, factoryData.position));

            if (isMiniDeerFactory)
            {
                _entranceOffset = -Vector3Int.RoundToInt(data.direction);
            }

            _createDisplayItemOnOutput = true;
        }

        public class Factory : PlaceholderFactory<AutomationFactoryParams, AutomationFactory> { }

        public static void GetFootprint(Vector3 direction, ref List<Vector3> footprintTilePositions)
        {
            footprintTilePositions.Clear();
            footprintTilePositions.AddRange(FACTORY_FOOTPRINT);

            float angleDegrees = GetAngleRotationFromDefaultDirection(direction);

            for (int i = 0; i < footprintTilePositions.Count; i++)
                footprintTilePositions[i] = (Quaternion.Euler(0f, angleDegrees, 0f) * footprintTilePositions[i]).GetSnappedPosition(1);
        }

        private static float GetAngleRotationFromDefaultDirection(Vector3 currentDropDirection)
        {
            if (currentDropDirection.x == 1f) // right
                return 90f;
            else if (currentDropDirection.z == -1f) // back
                return 180f;
            else if (currentDropDirection.x == -1f) // left
                return 270f;
            else // dropDirection.z == 1f // front
                return 0f;
        }

        public static void GetMiniDeerFootprint(Vector3 direction, ref List<Vector3> footprintTilePositions)
        {
            footprintTilePositions.Clear();

            if (direction.z == 1)
            {
                footprintTilePositions.AddRange(DEER_FACTORY_FOOTPRINT_NE);
            }
            else if (direction.z == -1)
            {
                footprintTilePositions.AddRange(DEER_FACTORY_FOOTPRINT_SW);
            }
            else if (direction.x == 1)
            {
                footprintTilePositions.AddRange(DEER_FACTORY_FOOTPRINT_SE);
            }
            else if (direction.x == -1)
            {
                footprintTilePositions.AddRange(DEER_FACTORY_FOOTPRINT_NW);
            }
        }
    }
    public class FactoryPickedUpItemArgs : EventArgs
    {
        public CoreItem item;
        public Vector3 position;
        public IItemMoveType moveType;
    }

    public struct AutomationFactoryParams
    {
        public int pileCount;
        public int pileSize;
        public Recipe recipe;
        public string Id;
        public Vector3Int entranceOffset;
        public bool allowIntakeWhileCooking;
        public bool createDisplayItemOnOutput;
        public bool pullFromSpaceInfront;
        public List<Vector3> footPrint;

        public AutomationFactoryParams(int pileCount, int pileSize, Recipe recipe, string id, List<Vector3> footPrint, Vector3 entranceOffset, bool allowIntakeWhileCooking = true, bool createDisplayItemOnOutput = true, bool pullFromSpaceInfront = false)
        {
            this.pileCount = pileCount;
            this.pileSize = pileSize;
            this.recipe = recipe;
            Id = id;
            this.footPrint = footPrint;
            this.entranceOffset = new Vector3Int(Mathf.RoundToInt(entranceOffset.x), Mathf.RoundToInt(entranceOffset.y), Mathf.RoundToInt(entranceOffset.z));
            this.allowIntakeWhileCooking = allowIntakeWhileCooking;
            this.createDisplayItemOnOutput = createDisplayItemOnOutput;
            this.pullFromSpaceInfront = pullFromSpaceInfront;
        }

        public AutomationFactoryParams(int pileCount, int pileSize, Recipe recipe, string id, List<Vector3> footPrint)
        {
            this.pileCount = pileCount;
            this.pileSize = pileSize;
            this.recipe = recipe;
            Id = id;
            this.footPrint = footPrint;
            this.entranceOffset = Vector3Int.zero;
            this.allowIntakeWhileCooking = true;
            this.createDisplayItemOnOutput = true;
            this.pullFromSpaceInfront = false;
        }
    }
}
