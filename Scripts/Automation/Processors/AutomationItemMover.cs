// Copyright Isto Inc.
using Isto.Core.Data;
using Isto.Core.Items;
using System;
using UnityEngine;
using Zenject;

namespace Isto.Core.Automation
{
    /// <summary>
    /// Item Processor responsible for moving items from one grid space to another.  Various movement types can be setup which
    /// must inherit from IItemMoveType and added to GetMover() method.
    /// When a move is started the item is considered to be on the target space's grid position but it's own internal position updates moving
    /// towards that space.
    /// </summary>
    public class AutomationItemMover : IItemProcessor, IDirectionalItem, IAutomationGridSpaceUser
    {
        public enum MovementType { Linear, Launch }

        // Events

        public event EventHandler DistanceChanged;
        public event EventHandler<ItemMoveArgs> ItemMoved;

        // Public Properties

        public bool Visible { get { return _visible; } }
        public Vector3 Position { get { return _space != null ? _space.position : Vector3.zero; } }
        public int MoveDistance { get; private set; }
        public MovementType MoveType { get; private set; }
        public string ProcessorID { get; private set; }
        public IAutomationItemFilter ItemFilter { get; set; }

        protected AutomationSystem _system = default;

        private bool _visible = true;
        private Vector3 _forward = Vector3.right;
        private AutomationGridSpace _space;
        private Vector3Int _moveToPosition;

        // Methods

        public AutomationItemMover(int moveDistance, MovementType moveType, string processorTypeId)
        {
            MoveDistance = moveDistance;
            MoveType = moveType;
            ProcessorID = processorTypeId;
        }

        [Inject]
        public void Inject(AutomationSystem system)
        {
            _system = system;
        }

        public void SetVisible(bool visible)
        {
            _visible = visible;
        }

        public Vector3 GetDirection()
        {
            return _forward;
        }

        public void SetForwardDirection(Vector3 forward)
        {
            _forward = forward;

            if (_space != null)
            {
                _moveToPosition = Vector3Int.RoundToInt(_space.position + _forward * MoveDistance);
            }
        }

        public virtual int IsAvailable(AutomationCoreItem movingItem)
        {
            // We carry one stack at a time so we care if there is already a stack here but we don't care about the count

            if (_space.ActiveItem != null)
                // We are occupied
                return 0;

            //if (ItemFilter != null && !ItemFilter.IsAccepted(movingItem))
            // Do we want to refuse items we don't want?
            // Or do we accept them so that the factory can get stuck if the player is careless?
            //return 0;

            // We are not busy, accept the stack
            return movingItem.count;
        }

        public void SetGridSpace(AutomationGridSpace space)
        {
            _space = space;

            _moveToPosition = Vector3Int.RoundToInt(_space.position + _forward * MoveDistance);
        }

        public AutomationGridSpace GetGridSpace()
        {
            return _space;
        }

        public void SetDistance(int distance)
        {
            MoveDistance = distance;
            DistanceChanged?.Invoke(this, EventArgs.Empty);
        }

        public virtual void Tick(float deltaTime)
        {
            if (!CanTick())
                return;

            AutomationCoreItem targetItem = GetActiveItem();

            if (!targetItem.IsMoving)
            {
                MoveItem(targetItem);
            }
        }

        protected virtual AutomationCoreItem GetActiveItem()
        {
            return _space?.ActiveItem;
        }

        protected virtual bool CanTick()
        {
            bool result = true;

            // We are powered if our tile is in range from the power grid.
            // However, certain larger items might have more than one [auxiliary] item processors in the automation system...
            // Should they each check for power individually or should they only depend on power reaching the main processor's tile?
            if (!_space.Powered)
                result = false;
            else if (!CanTakeItem(GetActiveItem()))
                result = false;

            return result;
        }

        protected bool CanTakeItem(AutomationCoreItem item)
        {
            bool result = true;
            if (item == null)
                result = false;
            else if (item.IsReserved || item.IsConsumed)
                result = false;
            else if (ItemFilter != null && !ItemFilter.IsAccepted(item))
                result = false;
            return result;
        }

        /// <summary>
        /// Moves the AutomationCoreItem to the target spaces item list and starts updating it's position based on the set 
        /// IItemMoveType for this mover.  Once the move starts, it's the destinations space responsibility now
        /// </summary>
        /// <param name="item"></param>
        public void MoveItem(AutomationCoreItem item)
        {
            AutomationGridSpace nextSpace = _system.GetOrCreateGridSpace(_moveToPosition);

            if (_system.CanMoveItemToGridSpace(nextSpace, item, out int amount))
            {
                item.IsReserved = true;
                AutomationGridSpace prevSpace = item.GridSpace;
                prevSpace.CancelAllMoves();

                if (AutomationItemMover.TryItemMove(item, prevSpace, nextSpace, amount, _system, GetMover, out IItemMoveType createdItemMover))
                {
                    ItemMoved?.Invoke(this, new ItemMoveArgs(createdItemMover));
                }
                else
                    Debug.LogError("Couldn't move item to expected free space");

                item.IsReserved = false;
            }
        }

        public static bool TryItemMove(AutomationCoreItem item, AutomationGridSpace sourceSpace, AutomationGridSpace nextSpace, int amount, AutomationSystem system,
            Func<AutomationCoreItem, Vector3, AutomationGridSpace, IItemMoveType> moverFactory, out IItemMoveType createdItemMover)
        {
            if (item.count > amount)
            {
                AutomationCoreItem singleItem = new AutomationCoreItem(new CoreItemParams(item.ID, amount, sourceSpace.position));

                if (system.TryAddCoreItemToSpace(nextSpace, singleItem, true))
                {
                    singleItem.position = sourceSpace.position;
                    singleItem.SetGridSpace(nextSpace);

                    createdItemMover = moverFactory.Invoke(singleItem, nextSpace.position, nextSpace);
                    nextSpace.MoveItemToSpace(createdItemMover);

                    item.count -= amount;

                    return true;
                }
            }
            else if (nextSpace.TrySetItem(item))
            {
                item.position = sourceSpace.position;
                sourceSpace.ClearItem(item);

                createdItemMover = moverFactory.Invoke(item, nextSpace.position, nextSpace);
                nextSpace.MoveItemToSpace(createdItemMover);
                item.SetGridSpace(nextSpace);

                return true;
            }

            createdItemMover = null;
            return false;
        }

        public IItemMoveType GetMover(AutomationCoreItem item, Vector3 destination, AutomationGridSpace nextSpace)
        {
            switch (MoveType)
            {
                case MovementType.Linear:
                    return new AutomationLinearMove(item, destination, _system.ItemMoveTime);
                case MovementType.Launch:
                    if (nextSpace.itemProcessor is AutomationFactory)
                    {
                        //I've added an arbitrary heigh to look better when splooshing into the factory 
                        destination.y += 1f;
                    }

                    return new AutomationLaunchMove(item, destination, _system.ItemMoveTime);
                default:
                    Debug.LogWarning("No handler for MovementType : " + MoveType);
                    return new AutomationLinearMove(item, destination, _system.ItemMoveTime);
            }
        }

        // Data Management

        public virtual AutomationProcessorData GetSaveData()
        {
            return new AutomationItemMoverData()
            {
                typeId = ProcessorID,
                direction = _forward,
                visible = _visible,
                distance = MoveDistance,
                moveType = MoveType
            };
        }

        public virtual void LoadData(AutomationProcessorData data)
        {
            AutomationItemMoverData moverData = data as AutomationItemMoverData;

            SetForwardDirection(moverData.direction);
            MoveDistance = moverData.distance;
            MoveType = moverData.moveType;
            ProcessorID = moverData.typeId;
            _visible = moverData.visible;
        }

        public class Factory : PlaceholderFactory<int, MovementType, string, AutomationItemMover> { }
    }
}