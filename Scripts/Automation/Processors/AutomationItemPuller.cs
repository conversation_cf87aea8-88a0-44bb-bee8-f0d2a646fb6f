// Copyright Isto Inc.
using Isto.Core.Data;
using Isto.Core.Items;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using Zenject;

namespace Isto.Core.Automation
{
    public class AutomationItemPuller : IItemProcessor, IDirectionalItem, IAutomationGridSpaceUser
    {
        public event EventHandler<AutomationPullEventArgs> PullStart;
        public event EventHandler PullEnd;

        public string ProcessorID { get; private set; }
        public bool DestinationBlocked { get; private set; }
        public bool Visible { get; private set; }
        public AutomationGridSpace Space { get { return _space; } }

        [Inject] private AutomationSystem _autoSystem = default;

        private AutomationGridSpace _space;
        private Vector3 _forward;
        private float _timeBetweenPulls = 15f;
        private float _pullTimer;
        private int _pullDistance = 5;
        private List<string> _validResourcesToHarvest;
        private List<Vector3> _affectedPositions = new List<Vector3>();
        private AutomationGridSpace _itemHoldingSpace;

        private bool CanHarvest(string s) => _validResourcesToHarvest.Contains(s);

        public AutomationItemPuller(AutomationItemPullerParams pullerParams)
        {
            Visible = true;

            ProcessorID = pullerParams.ID;

            _validResourcesToHarvest = new List<string>();

            for (int i = 0; i < pullerParams.validResources.Count; i++)
            {
                _validResourcesToHarvest.Add(pullerParams.validResources[i]);
            }

            _forward = pullerParams.forward;
            _pullDistance = pullerParams.pullDistance;

            if (pullerParams.pullWhenPlaced)
                _pullTimer = _timeBetweenPulls;
        }

        private void SetupAffectedPositionsList()
        {
            // Adding 3 positions to give pull a spread
            AddPositions(_space.position + _forward, _pullDistance, _forward, _affectedPositions);

            // Removing logic for pulling in triangle shape, just pulling in one line now 
            //Vector3 nextPosition = _space.position + _forward + (Quaternion.AngleAxis(90, Vector3.up) * _forward);
            //AddPositions(nextPosition, _pullDistance - 1, _forward, _affectedPositions);

            //nextPosition = _space.position + _forward + (Quaternion.AngleAxis(-90, Vector3.up) * _forward);
            //AddPositions(nextPosition, _pullDistance - 1, _forward, _affectedPositions);
        }

        private void AddPositions(Vector3 startPosition, int distance, Vector3 forward, List<Vector3> positionList)
        {
            for (int i = 1; i <= distance; i++)
            {
                positionList.Add(startPosition + forward * i);
            }
        }

        public Vector3 GetDirection()
        {
            return _forward;
        }

        public int IsAvailable(AutomationCoreItem movingItem)
        {
            return 0;
        }

        public AutomationProcessorData GetSaveData()
        {
            AutomationItemPullerData data = new AutomationItemPullerData()
            {
                typeId = ProcessorID,
                direction = _forward,
                pullDistance = _pullDistance,
                timeBetweenPulls = _timeBetweenPulls,
                affectedHarvestableItemIDs = _validResourcesToHarvest.ToArray(),
                visible = Visible
            };

            return data;
        }

        public void LoadData(AutomationProcessorData data)
        {
            if (data is AutomationItemPullerData pullerData)
            {
                ProcessorID = pullerData.typeId;
                _forward = pullerData.direction;
                _pullDistance = pullerData.pullDistance;
                _timeBetweenPulls = pullerData.timeBetweenPulls;
                _validResourcesToHarvest = new List<string>(pullerData.affectedHarvestableItemIDs);
                Visible = pullerData.visible;
            }
        }

        public void SetForwardDirection(Vector3 forward)
        {
            _forward = forward;
        }

        public void SetGridSpace(AutomationGridSpace space)
        {
            _space = space;

            _itemHoldingSpace = new AutomationGridSpace(_space.position + _forward);

            SetupAffectedPositionsList();
        }

        public void Tick(float deltaTime)
        {
            _pullTimer += deltaTime;

            if (_pullTimer >= _timeBetweenPulls)
            {
                if (CanPull())
                {
                    DestinationBlocked = false;

                    PerformPull();
                    _pullTimer = 0f;
                }
                else
                {
                    DestinationBlocked = true;

                    // Delay till next move time to check if item has been moved without waiting for full time between pulls
                    _pullTimer -= _autoSystem.ItemMoveTime;
                }
            }
        }

        public void DisableVisualCulling()
        {
            Visible = false;
        }

        private bool CanPull()
        {
            AutomationGridSpace destinationSpace = _autoSystem.GetOrCreateGridSpace(_space.position + _forward);

            if (destinationSpace.AllItemsOnSpace.Count > 0)
                return false;

            return true;
        }

        private async void PerformPull()
        {
            //We know the direction of pull will always be the opposite of the pulls forward
            Vector3 directionOfPull = new Vector3(-_forward.x, 0, -_forward.z);

            PullStart?.Invoke(this, new AutomationPullEventArgs(directionOfPull));

            //Debug.Log("StartingPull");

            // Dividing by time scale in case playing at a different speed, ie. for trailer recording
            await Task.Delay(Mathf.RoundToInt(1000 / Time.timeScale));

            //Debug.Log("StartingAnimation");

            // Add thespace in front in case there's a harvestable there it'll get harvested as well
            _affectedPositions.Add(_space.position + _forward);

            //Find affected items, and trigger animations
            NotifyAffectedItems(true, directionOfPull);

            while (Time.timeScale == 0f)
                await Task.Delay(100);

            // Dividing by time scale inscase playing at a different speed, ie. for trailer recording
            await Task.Delay(Mathf.RoundToInt(1500 / Time.timeScale));

            // Harvest anything on the spaces

            HarvestItemsInRange();

            // Remove space in front since that's where we're already pulling items to
            _affectedPositions.Remove(_space.position + _forward);

            //await Task.Delay(Mathf.RoundToInt(900 / Time.timeScale));

            MoveItemsToPuller();

            // This caused "ArgumentOutOfRangeException: The value needs to be either -1 (signifying an infinite timeout), 0 or a positive integer."
            // Might have happened when I paused / saved the game? Was timescale 0? Why is it an issue on this line but not the previous ones? Happened only once.
            //await Task.Delay(Mathf.RoundToInt(700 / Time.timeScale));

            PullEnd?.Invoke(this, EventArgs.Empty);

            await Task.Delay(Mathf.RoundToInt(_autoSystem.ItemMoveTime / 2f));

            AutomationGridSpace destinationSpace = _autoSystem.GetOrCreateGridSpace(_space.position + _forward);

            for (int i = 0; i < destinationSpace.AllItemsOnSpace.Count; i++)
            {
                destinationSpace.AllItemsOnSpace[i].IsReserved = false;
            }
        }

        private void NotifyAffectedItems(bool start, Vector3 directionOfPull)
        {
            for (int i = 0; i < _affectedPositions.Count; i++)
            {
                Vector3 nextPosition = _affectedPositions[i];

                if (_autoSystem.DoesSpaceExist(nextPosition))
                {
                    AutomationGridSpace nextSpace = _autoSystem.GetOrCreateGridSpace(nextPosition);

                    if (nextSpace.Resource != null && nextSpace.Resource.IsFullyGrown && CanHarvest(nextSpace.Resource.ID))
                    {
                        //Start pull
                        if (start)
                        {
                            nextSpace.Resource.StartTornatoadPull(directionOfPull);
                        }
                    }
                }
            }
        }

        private void HarvestItemsInRange()
        {
            //Debug.Log("Harvesting Items In Range");

            Queue<AutomationCoreItem> harvestedItems = new Queue<AutomationCoreItem>();

            for (int i = 0; i < _affectedPositions.Count; i++)
            {
                Vector3 nextPosition = _affectedPositions[i];

                if (_autoSystem.DoesSpaceExist(nextPosition))
                {
                    AutomationGridSpace nextSpace = _autoSystem.GetOrCreateGridSpace(nextPosition);

                    if (nextSpace.Resource != null && nextSpace.Resource.IsFullyGrown && CanHarvest(nextSpace.Resource.ID))
                    {
                        if (nextSpace.Resource.TakeDamage(nextSpace.Resource.Health, ref harvestedItems))
                        {
                            while (harvestedItems.Count > 0)
                            {
                                AutomationCoreItem nextItem = harvestedItems.Dequeue();

                                nextItem.position = nextSpace.position;

                                _autoSystem.TryAddCoreItemToSpace(_itemHoldingSpace, nextItem);
                            }
                        }
                    }

                    if (nextSpace.ActiveItem != null && IsItemMover(nextSpace))
                    {
                        nextSpace.ActiveItem.IsReserved = true;
                        nextSpace.CancelItemMove(nextSpace.ActiveItem);
                    }
                }
            }
        }

        private void MoveItemsToPuller()
        {
            AutomationGridSpace destinationSpace = _autoSystem.GetOrCreateGridSpace(_space.position + _forward);

            // First pull any items already on holding space
            for (int k = 0; k < _itemHoldingSpace.AllItemsOnSpace.Count; k++)
            {
                AutomationCoreItem item = _itemHoldingSpace.AllItemsOnSpace[k];

                Vector3 currentItemPosition = item.position;

                if (destinationSpace.TrySetItem(item, true))
                {
                    // Resetting item position because it gets set in TrySetItem to destinationspace position but we want to move it there
                    item.position = currentItemPosition;

                    IItemMoveType linearMove = new AutomationLinearMove(item, destinationSpace.position, _autoSystem.ItemMoveTime / 2);
                    destinationSpace.MoveItemToSpace(linearMove);
                }
            }

            _itemHoldingSpace.ClearAllItems();

            for (int i = 0; i < _affectedPositions.Count; i++)
            {
                Vector3 nextPosition = _affectedPositions[i];

                if (_autoSystem.DoesSpaceExist(nextPosition))
                {
                    AutomationGridSpace nextSpace = _autoSystem.GetOrCreateGridSpace(nextPosition);

                    if (IsItemMover(nextSpace))
                        nextSpace.CancelAllMoves();

                    AutomationCoreItem[] allSpaceItems = nextSpace.AllItemsOnSpace.ToArray();

                    for (int j = 0; j < allSpaceItems.Length; j++)
                    {
                        AutomationCoreItem item = allSpaceItems[j];

                        if (!item.IsMoving)
                        {
                            Vector3 droppedPosition = item.position;

                            if (destinationSpace.TrySetItem(item, true))
                            {
                                nextSpace.ClearItem(item);

                                // Resetting item position because it gets set in TrySetItem to destinationspace position but we want to move it there
                                item.position = droppedPosition;

                                IItemMoveType moveType = new AutomationLinearMove(item, destinationSpace.position, _autoSystem.ItemMoveTime / 2);
                                destinationSpace.MoveItemToSpace(moveType);
                            }
                        }
                    }
                }
            }
        }

        private bool IsItemMover(AutomationGridSpace space)
        {
            if (space.itemProcessor is AutomationItemMover _)
                return true;

            return false;
        }

        public class Factory : PlaceholderFactory<AutomationItemPullerParams, AutomationItemPuller> { }
    }

    public class AutomationItemPullerParams
    {
        public string ID;
        public List<string> validResources = new List<string>();
        public Vector3 forward;
        public bool pullWhenPlaced = false;
        public int pullDistance = 5;
    }
}