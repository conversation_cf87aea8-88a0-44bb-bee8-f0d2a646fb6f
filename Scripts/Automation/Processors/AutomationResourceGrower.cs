// Copyright Isto Inc.
using Isto.Core.Data;
using Isto.Core.Items;
using System;
using UnityEngine;
using Zenject;

namespace Isto.Core.Automation
{
    public class AutomationResourceGrower : IItemProcessor
    {
        // Events

        public event EventHandler GrowingCompleted;

        // Public Variables

        public string ProcessorID { get; private set; }
        public float GrowPoints { get; private set; }

        public bool Visible => false;

        // Private Variables

        [Inject] private MasterItemList _masteritemList = default;
        [Inject] private AutomationSystem _system = default;
        [Inject] private AutomationResource.Factory _resourceFactory;

        private AutomationResourceGrowerParams _params;

        public AutomationResourceGrower(AutomationResourceGrowerParams growParams, string processorTypeID)
        {
            _params = growParams;

            ProcessorID = processorTypeID;
        }

        public AutomationProcessorData GetSaveData()
        {
            return new AutomationProcessorData() { typeId = ProcessorID };
        }

        public int IsAvailable(AutomationCoreItem movingItem)
        {
            if (movingItem == null)
                return 0;

            return movingItem.ID.Equals(_params.seedItemID, StringComparison.CurrentCultureIgnoreCase) ? 1 : 0;
        }

        public void LoadData(AutomationProcessorData data)
        {
            Debug.LogError("NOT IMPLEMENTED");
        }

        public void Tick(float deltaTime)
        {
            GrowPoints += _params.growthRate * Time.deltaTime;

            if (GrowPoints > _params.growthRequired)
            {
                if (_system.TryRemoveResource(_params.worldPosition))
                {
                    HarvestableItem newItem = _masteritemList.GetItemByID<HarvestableItem>(_params.readyItemID);

                    _system.TryAddResource(_params.worldPosition, newItem, true);

                    // Remove this processor as item is done growing
                    _system.TryRemoveProcessor(_params.worldPosition);

                    GrowingCompleted?.Invoke(this, EventArgs.Empty);
                }
                else
                {
                    Debug.LogError("Couldn't remove AutomationResourceGrower from world position " + _params.worldPosition);
                }
            }
        }

        public class Factory : PlaceholderFactory<AutomationResourceGrowerParams, string, AutomationResourceGrower> { }
    }

    public class AutomationResourceGrowerParams
    {
        public float growthRate;
        public float growthRequired;
        public string seedItemID;
        public string readyItemID;
        public Vector3 worldPosition;

        public AutomationResourceGrowerParams(float growthRate, float growthRequired, string readyItemID, string seedItemID, Vector3 worldPosition)
        {
            this.growthRate = growthRate;
            this.growthRequired = growthRequired;
            this.readyItemID = readyItemID;
            this.worldPosition = worldPosition;
            this.seedItemID = seedItemID;
        }
    }
}