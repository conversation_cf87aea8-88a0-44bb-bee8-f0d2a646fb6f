// Copyright Isto Inc.
using Isto.Core.Data;
using Isto.Core.Items;
using System;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Automation
{
    [Serializable]
    public class AutomationHarvester : IItemProcessor, IDirectionalItem, IAutomationGridSpaceUser
    {
        public event EventHandler DroppedItem;

        public bool Harvesting { get { return _space.Resource != null && !_hasItemToDrop && _space != null && _space.Powered; } }

        public string ProcessorID { get; private set; }

        public bool Visible { get { return true; } }

        public bool DestroyOnHarvestComplete => _destroyOnHarvestComplete;

        public float harvestDamagePerSecond = 55f;

        [Inject] private MasterItemList _masteritemList = default;
        [Inject] private AutomationSystem _system = default;
        [Inject] private AutomationCoreItem.Factory _itemsFactory = default;

        private Vector3 _forward = Vector3.right;
        private AutomationGridSpace _space;
        private HarvestableItem _resourceItem;
        private Queue<AutomationCoreItem> _harvestedItemQueue = new Queue<AutomationCoreItem>();
        private bool _hasItemToDrop = false;
        private bool _destroyOnHarvestComplete;

        public AutomationHarvester(AutomationHarvesterParams harvesterParams)
        {
            harvestDamagePerSecond = harvesterParams.harvestDps;
            ProcessorID = harvesterParams.harvesterID;
            _destroyOnHarvestComplete = harvesterParams.destroyOnHarvestComplete;
        }

        public void SetGridSpace(AutomationGridSpace space)
        {
            _space = space;

            if (_space.Resource != null)
            {
                _resourceItem = _masteritemList.GetItemByID<HarvestableItem>(_space.Resource.ID);
            }

            if (_harvestedItemQueue.Count > 0)
            {
                AutomationCoreItem[] queuedItems = _harvestedItemQueue.ToArray();
                for (int i = 0; i < queuedItems.Length; i++)
                {
                    queuedItems[i].position = _space.position;
                }
            }
        }

        public void Tick(float deltaTime)
        {
            if (!_space.Powered)
                return;

            if (_hasItemToDrop)
                TryDropItems();

            if (_space.Resource != null && !_hasItemToDrop)
            {
                if (_space.Resource.TakeDamage(harvestDamagePerSecond * deltaTime, ref _harvestedItemQueue))
                {
                    _hasItemToDrop = true;

                    TryDropItems();
                }
            }
        }

        public int IsAvailable(AutomationCoreItem movingItem)
        {
            return 0;
        }

        public void SetForwardDirection(Vector3 forward)
        {
            this._forward = forward;
        }

        public Vector3 GetDirection()
        {
            return _forward;
        }

        private void TryDropItems()
        {
            AutomationGridSpace nextSpace = _system.GetOrCreateGridSpace(_space.position + _forward);

            while (_harvestedItemQueue.Count > 0)
            {
                AutomationCoreItem nextItem = _harvestedItemQueue.Dequeue();

                if (_system.CanMoveItemToGridSpace(nextSpace, nextItem, out int spaceAvailable))
                {
                    nextItem.position = _space.position;
                    _system.TryAddCoreItemToSpace(nextSpace.position, nextItem);
                    nextSpace.MoveItemToSpace(new AutomationLaunchMove(nextItem, nextSpace.position, _system.ItemMoveTime));
                }
                else
                {
                    // No space for item currently, so put it back in Queue and return
                    _harvestedItemQueue.Enqueue(nextItem);
                    return;
                }
            }

            DroppedItem?.Invoke(this, EventArgs.Empty);

            // If all items dropped, reset flag
            _hasItemToDrop = false;

            if (_destroyOnHarvestComplete)
                _system.TryRemoveProcessor(_space.position);
        }

        // Data management

        public AutomationProcessorData GetSaveData()
        {
            AutomationHarvesterData data = new AutomationHarvesterData()
            {
                direction = _forward,
                harvesterDPS = harvestDamagePerSecond,
                typeId = ProcessorID,
                destroyOnHarvestComplete = _destroyOnHarvestComplete
            };

            foreach (AutomationCoreItem item in _harvestedItemQueue)
            {
                data.itemsQueue.Add(new ItemData(item.ID, "", item.count));
            }

            return data;
        }

        public void LoadData(AutomationProcessorData data)
        {
            AutomationHarvesterData harvesterData = data as AutomationHarvesterData;

            ProcessorID = harvesterData.typeId;

            // Adding this as we weren't saving this previously and it should be set for all TreeChompers
            if (ProcessorID.Equals("TreeChomper"))
            {
                _destroyOnHarvestComplete = true;
            }

            _forward = harvesterData.direction;
            harvestDamagePerSecond = harvesterData.harvesterDPS;

            foreach (ItemData itemData in harvesterData.itemsQueue)
            {
                CoreItemParams itemParams = new CoreItemParams(itemData.id, itemData.count, data.position);
                AutomationCoreItem item = _itemsFactory.Create(itemParams);
                _harvestedItemQueue.Enqueue(item);
            }

            _hasItemToDrop = harvesterData.itemsQueue.Count > 0;
        }

        public class Factory : PlaceholderFactory<AutomationHarvesterParams, AutomationHarvester> { }
    }

    public struct AutomationHarvesterParams
    {
        public float harvestDps;
        public string harvesterID;
        public bool destroyOnHarvestComplete;

        public AutomationHarvesterParams(float harvestDps, string harvesterID, bool destroyOnHarvestComplete = false)
        {
            this.harvestDps = harvestDps;
            this.harvesterID = harvesterID;
            this.destroyOnHarvestComplete = destroyOnHarvestComplete;
        }
    }
}