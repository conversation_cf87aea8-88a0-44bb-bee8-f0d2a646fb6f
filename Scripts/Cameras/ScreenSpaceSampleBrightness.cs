// Copyright Isto Inc.
using System.Collections.Generic;
using Unity.Collections;
using UnityEngine;
using UnityEngine.Rendering;

namespace Isto.Core.Cameras
{
    [RequireComponent(typeof(Camera))]
    public class ScreenSpaceSampleBrightness : MonoBehaviour
    {
        // Public Variables

        [Tooltip("The number of times the RenderTexture will be sampled to calculate the brightness level.")]
        public int sampleSize = 16;

        // Private Variables

        protected Queue<AsyncGPUReadbackRequest> _requests = new Queue<AsyncGPUReadbackRequest>();
        protected RenderTexture _camRenderTexture;
        private NativeArray<Color32> _colorBuffer;

        // Lifecycle Events

        private void Awake()
        {
            Camera cam = GetComponent<Camera>();

            if (cam.targetTexture == null)
            {
                _camRenderTexture = new RenderTexture(Screen.width, Screen.height, 0, RenderTextureFormat.ARGB32);
                cam.targetTexture = _camRenderTexture;
            }

            _colorBuffer = new NativeArray<Color32>(Screen.width * Screen.height * 2, Allocator.Persistent);
        }

        void Update()
        {
            while (_requests.Count > 0)
            {
                var req = _requests.Peek();

                if (req.hasError)
                {
                    Debug.Log("GPU readback error detected.");
                    _requests.Dequeue();
                }
                else if (req.done)
                {
                    req.GetData<Color32>().CopyTo(_colorBuffer);

                    _requests.Dequeue();
                }
                else
                {
                    break;
                }
            }
        }

        private void OnDestroy()
        {
            if (_camRenderTexture != null)
                _camRenderTexture.Release();

            if (_colorBuffer.IsCreated)
                _colorBuffer.Dispose();
        }

        /// <summary>
        /// OnRenderImage is called after all rendering is complete to render image by the camera.  Method adds a GPU readback request
        /// onto the queue to read from the RenderTexture used by the camera.  Does not change the RenderTexture, just Blits it to the
        /// desination texture.
        /// </summary>
        /// <param name="source"></param>
        /// <param name="destination"></param>
        void OnRenderImage(RenderTexture source, RenderTexture destination)
        {
            if (_requests.Count < 8)
                _requests.Enqueue(AsyncGPUReadback.Request(source));

            Graphics.Blit(source, destination);
        }

        /// <summary>
        /// Samples the buffer and returns and average brightness for the colors in the buffer.
        /// </summary>
        /// <param name="buffer"></param>
        /// <returns></returns>
        private float CalculateBrightness(NativeArray<Color32> buffer, int sampleCount)
        {
            int increment = buffer.Length / sampleCount;

            float brightness = 0f;

            for (int i = 0; i < buffer.Length; i += increment)
            {
                brightness += buffer[i].r + buffer[i].g + buffer[i].b;
            }

            // Divide by number of samples and by 3 to average RGB values
            return brightness / (sampleCount * 3 * 256);
        }

        /// <summary>
        /// Takes a screen space coordinate and checks the brightness in that position.
        /// </summary>
        /// <param name="viewPortPosition">Expects Vector2 with coordinates [0,1] for screen space position.</param>
        /// <returns></returns>
        public float LightAtViewPortPosition(Vector2 viewPortPosition)
        {
            if (viewPortPosition.x > 1 || viewPortPosition.y > 1)
                Debug.LogError("Invalid position passed to screen space check for light. " + viewPortPosition);

            // Multiplying by 2 because color buffer data has additional information every other element.   ¯\_(ツ)_/¯
            int x = (int)(viewPortPosition.x * 2 * _camRenderTexture.width);
            int y = (int)(viewPortPosition.y * _camRenderTexture.height * _camRenderTexture.width * 2);

            int index = x + y;
            float brightness = 0f;

            for (int i = -sampleSize; i < sampleSize; i = i + 2)
            {
                if (index + i < _colorBuffer.Length && index + i > 0)
                    brightness += (_colorBuffer[index + i].r + _colorBuffer[index + i].g + _colorBuffer[index + i].b);
            }

            brightness = brightness / (3f * 256f * sampleSize);

            return brightness;
        }

        /// <summary>
        /// Gets the current light brightness at the world position.  Only works for world positions currently on screen.  Any position outside of the viewport
        /// will return 0f.
        /// </summary>
        /// <param name="worldPosition"></param>
        /// <returns>[0,1] for light brightness at position.  If position off screen, returns 0.</returns>
        public float LightAtWorldPosition(Vector3 worldPosition)
        {
            Vector2 viewPortPosition = Camera.main.WorldToViewportPoint(worldPosition);

            // If position outside of view port space, it is off screen so return 0.
            if (viewPortPosition.x < 0 || viewPortPosition.x > 1 || viewPortPosition.y < 0 || viewPortPosition.y > 1)
                return 0f;

            return LightAtViewPortPosition(viewPortPosition);
        }
    }
}