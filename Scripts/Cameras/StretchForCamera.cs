// Copyright Isto Inc.
using UnityEngine;

namespace Isto.Core.Cameras
{
    [ExecuteInEditMode]
    public class StretchForCamera : MonoBehaviour
    {
        public float scaleFactor = 1.193f; //An arbitrary number we found that looks good with our cameras current angle

        // Use this for initialization
        void Start()
        {
            Vector3 scale = this.transform.localScale;
            scale.y = scale.x * scaleFactor;
            this.transform.localScale = scale;
        }
    }
}
