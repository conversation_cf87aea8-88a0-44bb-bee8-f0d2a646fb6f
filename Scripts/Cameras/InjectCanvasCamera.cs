// Copyright Isto Inc.
using UnityEngine;
using Zenject;

namespace Isto.Core.Cameras
{
    /// <summary>
    /// Component to inject main camera into canvas on startup
    /// </summary>
    [RequireComponent(typeof(Canvas))]
    public class InjectCanvasCamera : MonoBehaviour
    {
        private Camera _worldCamera;

        [Inject]
        public void Inject(CameraController cameraController)
        {
            _worldCamera = cameraController.MainCamera;
        }

        private void Awake()
        {
            Canvas canvas = GetComponent<Canvas>();
            canvas.worldCamera = _worldCamera;
        }
    }
}