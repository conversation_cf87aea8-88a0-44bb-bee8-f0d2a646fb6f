// Copyright Isto Inc.

using UnityEngine;

namespace Isto.Core.Cameras
{
    public static class CameraExtensions
    {
        // Defaulting to a reasonable configuration intended for the save slot screenshot we'll shot in the save UI.
        // Note: If we are widescreen, I'm not sure but I think it's simply cutting off the sides - it looks OK to me.
        public static readonly int DEFAULT_SAVE_FILE_SCREENSHOT_WIDTH = 1920;
        public static readonly int DEFAULT_SAVE_FILE_SCREENSHOT_HEIGHT = 1080;
        public static readonly int DEFAULT_SAVE_FILE_SCREENSHOT_DEPTH = 16;

        // I thought using some or all of the following settings made sense, but the screenshots didn't look good.
        // Commenting them for now. We'll see when we come back to the screenshots if they need just some better
        // settings or if we don't need to worry about settings and can erase this note.
        // Among other things, the defaults make for a much bigger thumb.png file, how important that could be will
        // depend on how many save slots we want to support.
        //public static readonly GraphicsFormat DEFAULT_SAVE_FILE_SCREENSHOT_COLOR_FORMAT = GraphicsFormat.R8G8B8A8_UNorm;
        //public static readonly GraphicsFormat DEFAULT_SAVE_FILE_SCREENSHOT_DEPTH_FORMAT = GraphicsFormat.D16_UNorm;
        //public static readonly RenderTextureFormat DEFAULT_SAVE_FILE_SCREENSHOT_RENDER_FORMAT = RenderTextureFormat.RGBAUShort;

        /// <summary>
        /// This method has the camera render into a RenderTexture which in turn is read into the Texture2D.
        /// This screen capture logic was extracted from our CameraController class.
        /// </summary>
        /// <param name="screenCapCamera"></param>
        /// <returns></returns>
        public static Texture2D GetScreenCapture(this Camera screenCapCamera)
        {
            bool spawnedTempRT = false;
            if (screenCapCamera.targetTexture == null)
            {
                screenCapCamera.targetTexture = new RenderTexture(DEFAULT_SAVE_FILE_SCREENSHOT_WIDTH,
                                                                  DEFAULT_SAVE_FILE_SCREENSHOT_HEIGHT,
                                                                  DEFAULT_SAVE_FILE_SCREENSHOT_DEPTH);
                spawnedTempRT = true;
            }

            // The Render Texture in RenderTexture.active is the one that will be read by ReadPixels.
            RenderTexture prevRT = RenderTexture.active;
            RenderTexture currentRT = screenCapCamera.targetTexture;
            RenderTexture.active = currentRT;

            // Render the camera's view.
            screenCapCamera.Render();

            // Make a new texture and read the active Render Texture into it.
            Texture2D image = new Texture2D(currentRT.width, currentRT.height);
            image.ReadPixels(source: new Rect(x: 0, y: 0, currentRT.width, currentRT.height), destX: 0, destY: 0);
            image.Apply();

            // Cleanup
            RenderTexture.active = prevRT;

            if (spawnedTempRT)
            {
                screenCapCamera.targetTexture = null;
            }

            return image;
        }

    }
}