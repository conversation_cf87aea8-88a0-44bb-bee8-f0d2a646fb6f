// Copyright Isto Inc.
using Isto.Core.StateMachine;

namespace Isto.Core.Cameras
{
    /// <summary>
    /// Camera state that does not move camera
    /// </summary>
    public class CameraFixedState : MonoState
    {
        public override void Enter(MonoStateMachine controller)
        {
        }

        public override void Exit(MonoStateMachine controller)
        {
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
            throw new System.NotImplementedException();
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            return this;
        }
    }
}