// Copyright Isto Inc.
using System.Collections.Generic;
using Unity.Collections;
using UnityEngine;
using UnityEngine.Rendering;

namespace Isto.Core.Cameras
{
    /// <summary>
    /// Samples the RenderTexture of the attached camera and calculates a brightness level based on the 
    /// colors in the texture.  Has to be attached to a GameObject with a Camera to have OnRenderImage called.
    /// </summary>
    [RequireComponent(typeof(Camera))]
    public class SampleBrightness : MonoBehaviour
    {
        // Public Variables

        [Tooltip("The number of times the RenderTexture will be sampled to calculate the brightness level.")]
        public int sampleSize = 16;
        [Tooltip("Factor that is used to smooth light sampling over several frames.  Higher values mean less smoothing")]
        public float sensitivity = 1f;

        public float Level { get; private set; }

        // Private Variables

        protected Queue<AsyncGPUReadbackRequest> _requests = new Queue<AsyncGPUReadbackRequest>();
        protected RenderTexture _camRenderTexture;

        // Lifecycle Events

        private void Awake()
        {
            Camera cam = GetComponent<Camera>();

            if (cam.targetTexture == null)
            {
                _camRenderTexture = new RenderTexture(32, 32, 1, RenderTextureFormat.ARGB32);
                cam.targetTexture = _camRenderTexture;
            }

            Level = 0.5f;
        }

        void Update()
        {
            while (_requests.Count > 0)
            {
                var req = _requests.Peek();

                if (req.hasError)
                {
                    Debug.Log("GPU readback error detected.");
                    _requests.Dequeue();
                }
                else if (req.done)
                {
                    NativeArray<Color32> buffer = req.GetData<Color32>();

                    float currentLevel = CalculateBrightness(buffer, sampleSize);

                    // Scaling sensitity so it goes up faster but down slower.
                    float adjustedSensitivity = Level > currentLevel ? sensitivity : sensitivity * 5f;

                    Level = Mathf.MoveTowards(Level, currentLevel, adjustedSensitivity * Time.deltaTime);

                    _requests.Dequeue();
                }
                else
                {
                    break;
                }
            }
        }

        private void OnDestroy()
        {
            if (_camRenderTexture != null)
                _camRenderTexture.Release();
        }

        /// <summary>
        /// OnRenderImage is called after all rendering is complete to render image by the camera.  Method adds a GPU readback request
        /// onto the queue to read from the RenderTexture used by the camera.  Does not change the RenderTexture, just Blits it to the
        /// desination texture.
        /// </summary>
        /// <param name="source"></param>
        /// <param name="destination"></param>
        void OnRenderImage(RenderTexture source, RenderTexture destination)
        {
            if (_requests.Count < 8)
                _requests.Enqueue(AsyncGPUReadback.Request(source));

            Graphics.Blit(source, destination);
        }

        /// <summary>
        /// Samples the buffer and returns and average brightness for the colors in the buffer.
        /// </summary>
        /// <param name="buffer"></param>
        /// <returns></returns>
        private float CalculateBrightness(NativeArray<Color32> buffer, int sampleCount)
        {
            int increment = buffer.Length / sampleCount;

            float brightness = 0f;

            for (int i = 0; i < buffer.Length; i += increment)
            {
                brightness += buffer[i].r + buffer[i].g + buffer[i].b;
            }

            // Divide by number of samples and by 3 to average RGB values
            return brightness / (sampleCount * 3 * 256);
        }
    }
}