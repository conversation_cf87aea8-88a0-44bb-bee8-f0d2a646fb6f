// Copyright Isto Inc.
using Cinemachine;
using Isto.Core.Beings;
using Isto.Core.Configuration;
using Isto.Core.Inputs;
using Isto.Core.UI;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.PostProcessing;
using Zenject;

namespace Isto.Core.Cameras
{
    /// <summary>
    /// State machine that drives camera behaviour
    /// </summary>
    public class CameraController : MonoBehaviour
    {
        // Public variables

        public Camera MainCamera { get { return _mainCamera; } }
        public float DefaultCameraDistance { get { return _defaultCameraDistance; } }
        public float MinZoomDistance { get { return _minCameraZoom; } }
        public float MaxZoomDistance { get { return _maxCameraZoom; } }
        public bool AreLayersHidden { get { return _mainCamera.cullingMask != _defaultCameraCullingLayers; } }

        public enum CameraShakeTriggers { none, shakeNormal, shakeLight, shakeVeryLight, shakeHeavy }

        [SerializeField] private float _defaultCameraDistance = 325;
        [SerializeField] private float _minCameraZoom = 200;
        [SerializeField] private float _maxCameraZoom = 500;
        [Tooltip("How fast the camera zooms when controlled by the player")]
        [SerializeField] private float _playerCameraZoomSpeed = 50f;
        [SerializeField] private float _defaultZoomSpeed = 50f;
        [SerializeField] private Camera _screenCapCamera = null;
        [SerializeField] private PostProcessingBehaviour _screenCapPostProcessing;
        [SerializeField] private Camera _mainCamera = null;
        [SerializeField] private LayerMask _layersToHide = default; //Mostly the layers that contain the player

        private Dictionary<CameraShakeTriggers, CameraShakeParams> _shakeParams = new Dictionary<CameraShakeTriggers, CameraShakeParams>()
        {
            { CameraShakeTriggers.none, new CameraShakeParams(0f, 0f) },
            { CameraShakeTriggers.shakeNormal, new CameraShakeParams(0.2f, 0.5f) },
            { CameraShakeTriggers.shakeLight, new CameraShakeParams(0.15f, 0.25f) },
            { CameraShakeTriggers.shakeVeryLight, new CameraShakeParams(0.05f, 0.05f) },
            { CameraShakeTriggers.shakeHeavy, new CameraShakeParams(0.3f, 2f) }
        };

        private CinemachineVirtualCamera _followPlayerCamera = null;
        private IControls _controls;
        private AutomationPlayerController _player;
        private IPlayerStartPointProvider _spawnInfo;

        private CinemachineVirtualCamera _currentCamera;
        private CinemachineBasicMultiChannelPerlin _cameraNoise;

        private int _defaultCameraCullingLayers;
        private int _defaultWaterCameraCullingLayers;

        [Inject]
        public void Inject(IControls controls, [InjectOptional] IPlayerStartPointProvider startPointProvider, AutomationPlayerController player,
            [InjectOptional(Id = "PlayerFollowCam")] CinemachineVirtualCamera followPlayerCam)
        {
            _followPlayerCamera = followPlayerCam;
            _controls = controls;
            _player = player;
            _spawnInfo = startPointProvider;
        }


        //just used for atrio racing. DONT USE anywhere else
        public void SetMinMaxCameraZoom(float min, float max)
        {
            _minCameraZoom = min;
            _maxCameraZoom = max;
        }

        public void OnEnable()
        {
            GlobalGameplayEvents.MenuInteraction += OnMenuInteraction;
        }

        public void OnDisable()
        {
            GlobalGameplayEvents.MenuInteraction -= OnMenuInteraction;
        }

        protected void Awake()
        {
            _currentCamera = _followPlayerCamera;

            _defaultCameraCullingLayers = _mainCamera.cullingMask;

            HideLayers(true);
        }

        protected void Update()
        {
            if (!UnityUtils.IsCameraZoomSupported())
                return;

            if (_followPlayerCamera == null)
                return;

            if (_currentCamera == _followPlayerCamera)
            {
                float zoom = _controls.GetAxis(Controls.MovementAxis.Zoom);

                if (zoom != 0f)
                {
                    CinemachineFramingTransposer transposer = _followPlayerCamera.GetCinemachineComponent<CinemachineFramingTransposer>();
                    float targetZoom = zoom < 0 ? _maxCameraZoom : _minCameraZoom;

                    transposer.m_CameraDistance = Mathf.MoveTowards(transposer.m_CameraDistance, targetZoom, _playerCameraZoomSpeed * Time.deltaTime);
                }
            }
        }

        // In some situations the player object might be invisible and being moved around,
        // but we might want the camera to transition cleanly to the spawn point in anticipation of the player being restored.
        public void SetFollowCameraAtSpawnPoint()
        {
            _followPlayerCamera.Follow = _spawnInfo.PlayerSpawnPoint;
            _followPlayerCamera.LookAt = _spawnInfo.PlayerSpawnPoint;
            SwitchToCamera(_followPlayerCamera);
        }

        public void SetCameraFollowObject(GameObject targetObject)
        {
            _followPlayerCamera.Follow = targetObject.transform;
            _followPlayerCamera.LookAt = targetObject.transform;
            SwitchToCamera(_followPlayerCamera);
        }

        public void SetCameraFollowPlayer()
        {
            SetCameraFollowObject(_player.gameObject);
        }

        /// <summary>
        /// Changes the camera to at set position state and move the camera to the specified position over the duration time.
        /// </summary>
        /// <param name="position">Position to move the Camera Rig to</param>
        /// <param name="zoom">Distance to zoom if needed.</param>
        /// <param name="duration">Duration for the move to take.  If parameter not set, uses default move time for camera</param>
        public void SetMoveToPosition(Vector3 position, float zoom, float duration = -1)
        {
            float moveTime = duration == -1 ? Constants.DEFAULT_CAMERA_MOVE_TIME : duration;
        }

        public void DoNothing()
        {
        }

        /// <summary>
        /// Triggers a one time camera shake using the shake triggers default duration
        /// </summary>
        /// <param name="trig"></param>
        public void AnimateCameraShake(CameraShakeTriggers trig)
        {
            if (_shakeParams.ContainsKey(trig))
            {
                AnimateCameraShake(trig, _shakeParams[trig].duration);
            }
        }

        /// <summary>
        /// Triggers a camera shake with intensity of trigger and runs it for duration
        /// </summary>
        /// <param name="trig">Type of shake</param>
        /// <param name="duration">Time in seconds for shake to occure</param>
        public void AnimateCameraShake(CameraShakeTriggers trig, float duration)
        {
            // If already shaking, just return
            if (_cameraNoise != null && _cameraNoise.m_AmplitudeGain != 0f)
            {
                return;
            }

            _cameraNoise = _currentCamera.GetCinemachineComponent<CinemachineBasicMultiChannelPerlin>();

            if (_cameraNoise != null && _shakeParams.ContainsKey(trig))
            {
                _cameraNoise.m_AmplitudeGain = _shakeParams[trig].amplitude;

                Invoke(nameof(EndCameraShakeInvoked), duration);
            }
            else
            {
                if (_cameraNoise == null)
                    Debug.LogWarning($"No camera noise setup on virtual camera: {_currentCamera.name}");
                else
                    Debug.LogWarning("No shake params setup for CameraShakeTrigger." + trig);
            }
        }

        public void EndCameraShakeInvoked()
        {
            if (_cameraNoise == null)
                return;

            _cameraNoise.m_AmplitudeGain = 0f;
            _cameraNoise = null;
        }

        /// <summary>
        /// Zooms the camera the specified distance over the set time following the set curve
        /// </summary>
        /// <param name="targetCameraDistance">The target distance from the player after the zoom completes.  Values should be between 100 and 1000.  1000 being far away.</param>
        /// <param name="zoomTime">Time for the zoom to take</param>
        /// <param name="zoomCurve">Animation curve for the zoom to follow</param>
        public IEnumerator ZoomCameraCoroutine(float targetCameraDistance, float zoomTime, AnimationCurve zoomCurve)
        {
            float timer = 0f;

            CinemachineFramingTransposer transposer = _currentCamera.GetCinemachineComponent<CinemachineFramingTransposer>();

            if (transposer != null)
            {
                float startDistance = transposer.m_CameraDistance;

                while (timer < zoomTime)
                {
                    timer += Time.deltaTime;

                    transposer.m_CameraDistance = Mathf.Lerp(startDistance, targetCameraDistance, zoomCurve.Evaluate(timer / zoomTime));

                    yield return null;
                }
            }
        }

        public void SetCameraZoomDistance(float distance)
        {
            // Setting default distance in-case this was called before UISceneStartState is over, usually set in PlayMode Tests
            _defaultCameraDistance = Mathf.Clamp(distance, _minCameraZoom, _maxCameraZoom);

            CinemachineFramingTransposer transposer = _currentCamera.GetCinemachineComponent<CinemachineFramingTransposer>();

            if (transposer != null)
            {
                float targetZoom = Mathf.Clamp(distance, _minCameraZoom, _maxCameraZoom);
                transposer.m_CameraDistance = targetZoom;
            }
        }

        public Texture2D GetScreenCapture()
        {
            if (_screenCapCamera == null)
            {
                Debug.LogError("No screen capture camera setup on Camera Controller. Needed for saving thumbnail so save slots.");
                return null;
            }

            if (_screenCapPostProcessing != null)
                _screenCapPostProcessing.enabled = true;

            Texture2D image = _screenCapCamera.GetScreenCapture();

            if (_screenCapPostProcessing != null)
                _screenCapPostProcessing.enabled = false;

            return image;
        }

        public void SwitchToCamera(CinemachineVirtualCamera virtualCamera)
        {
            if (_currentCamera != null)
                _currentCamera.Priority = 1;

            _currentCamera = virtualCamera;
            _currentCamera.Priority = 10;
        }

        /// <summary>
        /// Sets the camera distance to the default value
        /// </summary>
        public void ResetCameraDistance()
        {
            StartCoroutine(ResetCameraZoomCoroutine());
        }

        private IEnumerator ResetCameraZoomCoroutine()
        {
            CinemachineFramingTransposer transposer = _followPlayerCamera.GetCinemachineComponent<CinemachineFramingTransposer>();

            if (transposer != null)
            {
                while (transposer.m_CameraDistance != _defaultCameraDistance)
                {
                    transposer.m_CameraDistance = Mathf.MoveTowards(transposer.m_CameraDistance, _defaultCameraDistance, _defaultZoomSpeed * Time.deltaTime);
                    yield return null;
                }
            }
        }

        private void OnMenuInteraction(object sender, MenuInteractionEventArgs e)
        {
            bool isIgnoredMenuType = e.menu == GameMenusEnum.CRAFTING || e.menu == GameMenusEnum.GAME;

            if (!isIgnoredMenuType && e.action == MenuInteractionEventArgs.Action.Open)
            {
                HideLayers(true);
            }

            if (!isIgnoredMenuType && e.action == MenuInteractionEventArgs.Action.Close)
            {
                HideLayers(false);
            }
        }

        public void HideLayers(bool hide)
        {
            if (!hide)
            {
                _mainCamera.cullingMask = _defaultCameraCullingLayers;
            }
            else
            {
                _mainCamera.cullingMask = ~_layersToHide;
            }
        }

        public void DisableMainCamera()
        {
            _mainCamera.gameObject.SetActive(false);
        }

        public void EnableMainCamera()
        {
            _mainCamera.gameObject.SetActive(true);
        }

        [ContextMenu("Get View area diagonal width")]
        public float GetScreenDiagonalSizeWorldCoord()
        {
            Vector3 bottomCorner = UnityUtils.GetViewPortPositionOnGround(new Vector2(0, 0));
            Vector3 topCorner = UnityUtils.GetViewPortPositionOnGround(new Vector2(1, 1));

            //Debug.Log("View area width: " + Vector3.Distance(bottomCorner, topCorner));

            return Vector3.Distance(bottomCorner, topCorner);
        }

        public class CameraShakeParams
        {
            public float duration;
            public float amplitude;

            public CameraShakeParams(float duration, float amplitude)
            {
                this.duration = duration;
                this.amplitude = amplitude;
            }
        }
    }
}