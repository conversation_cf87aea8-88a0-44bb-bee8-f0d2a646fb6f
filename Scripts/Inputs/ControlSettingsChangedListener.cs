// Copyright Isto Inc.
using UnityEngine;
using Zenject;

namespace Isto.Core.Inputs
{
    public class ControlSettingsChangedListener : MonoBehaviour
    {
        public Behaviour[] enabledForController;
        public Behaviour[] enabledForKeyboard;
        public GameObject[] activeForController;
        public GameObject[] activeForKeyboard;

        private bool _eventRegistered = false;


        private IControls _controls;

        [Inject]
        public void Inject(IControls controls)
        {
            _controls = controls;
        }

        private void Start()
        {
            Events_OnSettingsChanged();
            RegisterEvents();
        }


        private void OnDestroy()
        {
            UnregisterEvents();
        }

        private void RegisterEvents()
        {
            if (_eventRegistered)
                return;

            Events.Subscribe(Events.SETTINGS_CHANGED, Events_OnSettingsChanged);

            _eventRegistered = true;
        }

        private void UnregisterEvents()
        {
            if (!_eventRegistered)
                return;

            Events.UnSubscribe(Events.SETTINGS_CHANGED, Events_OnSettingsChanged);

            _eventRegistered = false;
        }

        private void Events_OnSettingsChanged()
        {
            bool useController = _controls.UsingJoystick();

            for (int i = 0; i < enabledForController.Length; i++)
            {
                enabledForController[i].enabled = useController;
            }

            for (int i = 0; i < enabledForKeyboard.Length; i++)
            {
                enabledForKeyboard[i].enabled = !useController;
            }

            for (int i = 0; i < activeForController.Length; i++)
            {
                activeForController[i].SetActive(useController);
            }

            for (int i = 0; i < activeForKeyboard.Length; i++)
            {
                activeForKeyboard[i].SetActive(!useController);
            }
        }
    }
}