// Copyright Isto Inc.

using Isto.Core.Enums;

namespace Isto.Core.Inputs
{
    /// <summary>
    /// Contains all the user actions that are currently built into Core along with their respective action categories
    /// in Rewired. Everything listed does NOT have to be assigned in Rewired as a lot are project specific.
    /// e.g. GameplayBuilding UserActions are used for games that require the user to make buildings.
    /// </summary>
    public class UserActions : StringEnum<UserActions>
    {
        // Default
        public static readonly UserActions NONE = new UserActions("None", nameof(NONE));

        // ACTION CATEGORY: GAMEPLAY
        public static readonly UserActions DISMANTLE = new UserActions("Dismantle", nameof(DISMANTLE));
        public static readonly UserActions INTERACT = new UserActions("Interact", nameof(INTERACT));
        public static readonly UserActions USEITEM = new UserActions("UseItem", nameof(USEITEM));
        public static readonly UserActions USEEQUIPPEDITEM1 = new UserActions("UseEquippedItem1", nameof(USEEQUIPPEDITEM1));
        public static readonly UserActions USEEQUIPPEDITEM2 = new UserActions("UseEquippedItem2", nameof(USEEQUIPPEDITEM2));
        public static readonly UserActions USEEQUIPPEDITEM3 = new UserActions("UseEquippedItem3", nameof(USEEQUIPPEDITEM3));
        public static readonly UserActions USEEQUIPPEDITEM4 = new UserActions("UseEquippedItem4", nameof(USEEQUIPPEDITEM4));
        public static readonly UserActions USEEQUIPPEDITEM5 = new UserActions("UseEquippedItem5", nameof(USEEQUIPPEDITEM5));
        public static readonly UserActions USEEQUIPPEDITEM6 = new UserActions("UseEquippedItem6", nameof(USEEQUIPPEDITEM6));
        public static readonly UserActions USEEQUIPPEDITEM7 = new UserActions("UseEquippedItem7", nameof(USEEQUIPPEDITEM7));
        public static readonly UserActions USEEQUIPPEDITEM8 = new UserActions("UseEquippedItem8", nameof(USEEQUIPPEDITEM8));
        public static readonly UserActions USEMAINEQUIPPEDITEM = new UserActions("UseMainEquippedItem", nameof(USEMAINEQUIPPEDITEM));
        public static readonly UserActions USESECONDARYEQUIPPEDITEM = new UserActions("UseSecondaryEquippedItem", nameof(USESECONDARYEQUIPPEDITEM));
        public static readonly UserActions DROPITEM = new UserActions("DropItem", nameof(DROPITEM));
        public static readonly UserActions GIVEITEM = new UserActions("GiveItem", nameof(GIVEITEM));
        public static readonly UserActions TOGGLEGAMEMENU = new UserActions("ToggleGameMenu", nameof(TOGGLEGAMEMENU));
        public static readonly UserActions TOGGLEBUGREPORTER = new UserActions("ToggleBugReporter", nameof(TOGGLEBUGREPORTER));

        // ACTION CATEGORY: GameplayBuilding
        public static readonly UserActions ROTATELEFT = new UserActions("RotateLeft", nameof(ROTATELEFT));
        public static readonly UserActions ROTATERIGHT = new UserActions("RotateRight", nameof(ROTATERIGHT));
        public static readonly UserActions CANCELBUILDING = new UserActions("CancelBuilding", nameof(CANCELBUILDING));

        // ACTION CATEGORY: GameplayDismantling
        public static readonly UserActions CANCELDISMANTLING = new UserActions("CancelDismantling", nameof(CANCELDISMANTLING));

        // ACTION CATEGORY: UI
        public static readonly UserActions UIHORIZONTAL = new UserActions("UIHorizontal", nameof(UIHORIZONTAL));
        public static readonly UserActions UIVERTICAL = new UserActions("UIVertical", nameof(UIVERTICAL));
        public static readonly UserActions UISUBMIT = new UserActions("UISubmit", nameof(UISUBMIT));
        public static readonly UserActions UICANCEL = new UserActions("UICancel", nameof(UICANCEL));
        public static readonly UserActions UITABLEFT = new UserActions("UITabLeft", nameof(UITABLEFT));
        public static readonly UserActions UITABRIGHT = new UserActions("UITabRight", nameof(UITABRIGHT));
        public static readonly UserActions UIMODIFIERACTION = new UserActions("UIModifierAction", nameof(UIMODIFIERACTION));
        public static readonly UserActions UISETTINGSRESETALLTODEFAULTS = new UserActions("UISettingsResetAllToDefaults", nameof(UISETTINGSRESETALLTODEFAULTS));
        public static readonly UserActions PAUSE = new UserActions("Pause", nameof(PAUSE));

        // ACTION CATEGORY: DeveloperConsole
        public static readonly UserActions TOGGLEDEVCONSOLE = new UserActions("ToggleDevConsole", nameof(TOGGLEDEVCONSOLE));
        public static readonly UserActions FORCEGC = new UserActions("ForceGC", nameof(FORCEGC));

        protected internal UserActions(string value, string name) : base(value, name)
        {
        }
    }
}