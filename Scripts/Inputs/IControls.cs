// Copyright Isto Inc.
using UnityEngine;

namespace Isto.Core.Inputs
{
    /// <summary>
    /// Interface for any class that will control user input handling.  
    /// </summary>
	public interface IControls
    {
        string GetAxisName(Controls.MovementAxis axis);

        string GetButtonName(UserActions action);

        float GetAxis(Controls.MovementAxis axis);

        /// <summary>
        /// Gets the axis value without any smoothing applied to it.  Working when Timescale is set to zero
        /// </summary>
        /// <param name="axis"></param>
        /// <returns></returns>
        float GetRawAxis(Controls.MovementAxis axis);

        /// <summary>
        /// Returns true if button is clicked
        /// </summary>
        /// <param name="action"></param>
        /// <returns></returns>
        bool GetButton(UserActions action, bool ignoreMouse = false);

        /// <summary>
        /// Returns true if the button was pressed this frame
        /// </summary>
        /// <param name="action"></param>
        /// <returns></returns>
        bool GetButtonDown(UserActions action);

        bool GetButtonUp(UserActions action);

        /// <summary>
        /// Returns true if the button is clicked but not held
        /// </summary>
        /// <param name="action"></param>
        /// <returns></returns>
        bool GetButtonTap(UserActions action, bool ignoreKeyboard = false);

        /// <summary>
        /// Returns true if the button is held down longer than a set amount of time (usually stored in constants.
        /// </summary>
        /// <param name="action"></param>
        /// <returns></returns>
        bool GetDoubleClick(UserActions action);

        /// <summary>
        /// Not as straightforward as it seems.
        /// Will pick up actions basec on axis movement, button presses, or keyboard presses, but not mouse button presses?
        /// </summary>
        /// <returns>true if one of the valid buttons is pressed</returns>
        bool GetAnyButton();

        /// <summary>
        /// Shorthand for polling for start, select, or A.
        /// </summary>
        /// <returns>true if one of the valid buttons is pressed</returns>
        bool GetStartGameAction();

        /// <summary>
        /// This is a special case because it circumvents action mappings.
        /// It will directly poll for the Y button from an xbox controller or the F1 key from a keyboard.
        /// </summary>
        /// <returns>true if the pre-determined button is pressed</returns>
        bool GetResetMappingsButtonDown();

        Vector3 GetPointerPositionOnGround();

        bool GetPointerOverUI();

        Vector2 GetPointerPosition();

        Vector3 GetCanvasMousePosition(Canvas parent);

        Ray GetRayToPointerPosition();

        Sprite GetIconForAction(UserActions action);

        void SetControlMode(Controls.Mode mode);

        void DisableControls(float time);

        void EnableControls();

        Controls.Mode GetControlMode();

        void SetInputDevice(Controls.InputDevice input);

        bool UsingJoystick();

        Controls.InputDevice GetLastInputDevice();

        void VibrateController(Controls.VibrationMode mode);

        bool IsMouseOverUIRectTransform(RectTransform rectTransform);
    }
}