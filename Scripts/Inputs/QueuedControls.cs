// Copyright Isto Inc.
using Isto.Core.Beings;
using Rewired;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Inputs
{
    // Not really in use AFAIk, probably intended to be used for tests?
    // if we don't think this system was a good idea after all, we should delete this class.
    public class QueuedControls : MonoBehaviour, IControls
    {
        public List<ControlInput> inputs;
        public float overrideMouseSearchRadius = 0.5f;

        private ControllerGlyphs _glyphs;

        private int _index;
        private bool _moveToNextInput;
        private Player _player;
        private Controls.Mode _currentMode;
        private float _nextActionTime;


        [Inject]
        public void Inject(ControllerGlyphs glyphs)
        {
            _glyphs = glyphs;
        }

        public void Awake()
        {
            _index = 0;
            _moveToNextInput = false;

            // Add dummy input to end of list so we don't overrun inputs
            inputs.Add(new ControlInput() { action = UserActions.INTERACT, position = Vector3.zero, timeBeforeAction = float.PositiveInfinity });

            // Get the Rewired Player object for this player and keep it for the duration of the character's lifetime
            _player = ReInput.players.GetPlayer(0);
        }

        public void Start()
        {
            PlayerItemInteraction itemInteraction = FindObjectOfType<PlayerItemInteraction>();

            if (!itemInteraction.IsNullOrDestroyed())
            {
                itemInteraction.mouseSearchRadius = overrideMouseSearchRadius;
            }

            _nextActionTime = inputs.Count > 0 ? inputs[0].timeBeforeAction : Time.time;
        }

        public void LateUpdate()
        {
            if (_moveToNextInput)
            {
                _index++;
                _nextActionTime = Time.time + inputs[_index].timeBeforeAction;
                _moveToNextInput = false;
            }
        }

        public void DisableControls(float time)
        {

        }

        public void EnableControls()
        {
        }

        public bool GetAnyButton()
        {
            if (_nextActionTime >= inputs[_index].timeBeforeAction)
                return true;

            return false;
        }

        public float GetAxis(Controls.MovementAxis axis)
        {
            return 0;
        }

        public string GetAxisName(Controls.MovementAxis axis)
        {
            return "";
        }

        public bool GetButton(UserActions action, bool ignoreMouse = false)
        {
            return IsCurrentAction(inputs[_index], action);
        }

        public bool GetButtonDown(UserActions action)
        {
            return IsCurrentAction(inputs[_index], action);
        }

        public string GetButtonName(UserActions action)
        {
            return "";
        }

        public bool GetButtonTap(UserActions action, bool ignoreKeyboard = false)
        {
            return IsCurrentAction(inputs[_index], action);
        }

        public bool GetButtonUp(UserActions action)
        {
            return IsCurrentAction(inputs[_index], action);
        }

        public Vector3 GetCanvasMousePosition(Canvas parent)
        {
            return Vector3.zero;
        }

        public Controls.Mode GetControlMode()
        {
            return _currentMode;
        }

        public bool GetDoubleClick(UserActions action)
        {
            return IsCurrentAction(inputs[_index], action);
        }

        public Sprite GetIconForAction(UserActions action)
        {
            return _glyphs.GetGlyph(action.ToString(), Controls.InputDevice.Mouse);
        }

        public Controls.InputDevice GetLastInputDevice()
        {
            return Controls.InputDevice.Mouse;
        }

        public Vector2 GetPointerPosition()
        {
            return Vector2.zero;
        }

        public Vector3 GetPointerPositionOnGround()
        {
            return inputs[_index].position;
        }

        public float GetRawAxis(Controls.MovementAxis axis)
        {
            return 0f;
        }

        public Ray GetRayToPointerPosition()
        {
            Vector3 mousePosition = GetPointerPositionOnGround();

            Vector2 mouseScreen = Camera.main.WorldToScreenPoint(mousePosition);

            //Debug.Log($"MousePosition: {mousePosition}, MouseScreenPosition: {mouseScreen}");

            return Camera.main.ScreenPointToRay(mouseScreen);
        }

        public void SetControlMode(Controls.Mode mode)
        {
            _currentMode = mode;

            switch (mode)
            {
                case Controls.Mode.Gameplay:
                    _player.controllers.maps.SetAllMapsEnabled(false);
                    _player.controllers.maps.SetMapsEnabled(true, "GameplayMouseKey");
                    break;
                case Controls.Mode.UI:
                    _player.controllers.maps.SetAllMapsEnabled(false);
                    _player.controllers.maps.SetMapsEnabled(true, "UIMouseKey");
                    break;
                case Controls.Mode.IntroUI:          // Used for the intro screen as both input modes should be active
                    _player.controllers.maps.SetAllMapsEnabled(false);
                    _player.controllers.maps.SetMapsEnabled(true, "UIJoy");
                    _player.controllers.maps.SetMapsEnabled(true, "UIMouseKey");
                    break;
                case Controls.Mode.Building:
                    _player.controllers.maps.SetAllMapsEnabled(false);
                    _player.controllers.maps.SetMapsEnabled(true, "GameplayBuildingMouseKey");
                    break;
                case Controls.Mode.Dismantling:
                    _player.controllers.maps.SetAllMapsEnabled(false);
                    _player.controllers.maps.SetMapsEnabled(true, "GameplayDismantlingMouseKey");
                    break;
                case Controls.Mode.DeveloperConsole:
                    _player.controllers.maps.SetAllMapsEnabled(false);
                    _player.controllers.maps.SetMapsEnabled(true, "DeveloperConsole");
                    break;
                case Controls.Mode.BugReportingTool:
                    _player.controllers.maps.SetAllMapsEnabled(false);
                    _player.controllers.maps.SetMapsEnabled(true, "BugReportingTool");
                    break;
                case Controls.Mode.Disabled:
                    _player.controllers.maps.SetAllMapsEnabled(false);
                    break;
                default:
                    break;
            }

            Events.RaiseEvent(Events.SETTINGS_CHANGED);
        }

        public void SetInputDevice(Controls.InputDevice input)
        {

        }

        public bool UsingJoystick()
        {
            // This controls setup is only for keyboard/mouse
            return false;
        }

        public void VibrateController(Controls.VibrationMode mode)
        {
        }

        private bool IsCurrentAction(ControlInput nextInput, UserActions action)
        {
            if (Time.time >= _nextActionTime && action == nextInput.action)
            {
                Debug.Log($"Performing action {nextInput.action} at {nextInput.position}");

                _moveToNextInput = true;

                return true;
            }

            return false;
        }

        // Gizmos

        public void OnDrawGizmos()
        {
            if (inputs != null && inputs.Count > 0)
            {
                for (int i = 0; i < inputs.Count; i++)
                {
                    Vector3 currentPosition = inputs[i].position;

                    Gizmos.color = inputs[i].sphereColor;

                    // Adding some randomness so overlapping spheres are more obvious
                    Gizmos.DrawSphere(currentPosition, 0.25f);

                    GUIStyle textStyle = new GUIStyle();
                    textStyle.normal.textColor = Color.yellow;
                    //UnityEditor.Handles.Label(currentPosition + Vector3.up, inputs[i].action.ToString(), textStyle);

                    if (i > 0)
                    {
                        Vector3 previousPosition = inputs[i - 1].position;

                        // Draw line between points
                        Gizmos.DrawLine(previousPosition, currentPosition);

                        if (previousPosition != currentPosition)
                        {
                            // UnityEditor.Handles.ArrowHandleCap(0, currentPosition + (previousPosition - currentPosition).normalized, Quaternion.LookRotation(currentPosition - previousPosition), 0.5f, EventType.Repaint);
                        }
                    }
                }
            }
        }

        public bool IsMouseOverUIRectTransform(RectTransform rectTransform)
        {
            return true;
        }

        public bool GetPointerOverUI()
        {
            // for now this feature is not testable - at least it's not pertinent to check in unit tests
            // I... think this class is intended for unit tests?
            // or maybe it was meant for cutscenes or for recording user inputs...
            return false;
        }

        public bool GetStartGameAction()
        {
            throw new System.NotImplementedException();
        }

        public bool GetResetMappingsButtonDown()
        {
            throw new System.NotImplementedException();
        }
    }
}