// Copyright Isto Inc.

using I2.Loc;
using Isto.Core.Configuration;
using Rewired;
using Rewired.Integration.UnityUI;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using Zenject;

#if PLATFORM_GAMECORE
using Rewired.Platforms.GameCore;
#endif

namespace Isto.Core.Inputs
{
    /// <summary>
    /// Handles all input processing from user input.  Other class call this class to see if a button was pressed or axis moved.
    /// </summary>
    public partial class Controls : MonoBehaviour, IControls, ILocalizationParamsManager
    {
        public enum MovementAxis
        {
            MoveHorizontal,
            MoveVertical,
            SecondaryHorizontal,
            SecondaryVertical,
            Zoom,
            UIHorizontal,
            UIVertical,
            UISecondaryHorizontal,
            UISecondaryVertical
        }

        public enum Mode
        {
            Gameplay,
            Building,
            UI,
            IntroUI,
            DeveloperConsole,
            Disabled,
            Dismantling,
            BugReportingTool
        }

        // Warning : This is being serialized in the action remapping prefabs from the settings menu.
        public enum InputDevice
        {
            Joystick,
            Keyboard,
            Mouse
        }

        public enum VibrationMode
        {
            Short,
            Long
        }

        public enum RewiredCategory
        {
            // The first category is actually Default at index 0 so keep that in mind
            // But we don't really use it and we've been fine without it being part of this list, so I'm not adding it.
            GameplayJoy,
            GameplayMouseKey,
            UIJoy,
            UIMouseKey,
            GameplayBuildingJoy,
            GameplayBuildingMouseKey,
            GameplayDismantlingJoy,
            GameplayDismantlingMouseKey,
            DeveloperConsole,
            Development
        }

        public static int DEFAULT_REWIRED_PLAYER_ID = 0;

        public static bool UsingController { get; private set; }

        public bool IsControllerAvailable => _player?.controllers.joystickCount > 0;

        public float defaultSpamTime = 0.2f;
        public float quickPressMaxTime = 0.10f;

#if UNITY_EDITOR
        public bool ControllerInputOnly = false;
#endif

        // Private Variables

        private Rewired.Player _player;
        private int playerID = DEFAULT_REWIRED_PLAYER_ID;
        private Mode _currentMode;
        private InputDevice _lastInputDevice;
        private bool _disabled;
        private Coroutine _disableControlsRoutine;

        private Dictionary<UserActions, int> _actionToControlIDMap = new Dictionary<UserActions, int>();
        private Dictionary<MovementAxis, int> _axisToControlIDMap = new Dictionary<MovementAxis, int>();

        // UISubmit is a special case when it's on the mouse so it will not work as is.
        // I think select could also be considered for the controller, as well as spacebar for keyboard?
        private List<UserActions> _startGameActions = new List<UserActions>()
        {
            UserActions.UISUBMIT, // A and maybe left click? if supported in project?
            //UserActions.PAUSE, // Maybe tab? depending on project? This would be crafting menu and not pause menu.
            UserActions.TOGGLEGAMEMENU, // Start or Escape
            UserActions.INTERACT, // maybe need this to grab A if in a different control group?
        };

        // Injection

        private Settings _settings;
        private ControllerGlyphs _glyphs;
        private RewiredStandaloneInputModule _inputModule;

        [Inject]
        public void Inject(Settings settings, ControllerGlyphs glyphs, RewiredStandaloneInputModule inputModule)
        {
            _settings = settings;
            _glyphs = glyphs;
            _inputModule = inputModule;
        }


        // Lifecycle Methods

        private void Awake()
        {
            // Get the Rewired Player object for this player and keep it for the duration of the character's lifetime
            _player = ReInput.players.GetPlayer(playerID);

            foreach (UserActions action in UserActions.GetValues())
            {
                int actionID = ReInput.mapping.GetActionId(action.Value);

                _actionToControlIDMap.Add(action, actionID);
            }

            foreach (MovementAxis action in Enum.GetValues(typeof(MovementAxis)))
            {
                int actionID = ReInput.mapping.GetActionId(action.ToString());

                _axisToControlIDMap.Add(action, actionID);
            }

            LocalizationManager.ParamManagers.Add(this);
        }

        private void OnDestroy()
        {
            LocalizationManager.ParamManagers.Remove(this);
        }

        private void OnEnable()
        {
            // Assign each Joystick to our Player initially in case they were connected before we got enabled
            AssignAllControllers(force: false);

            RegisterEvents();
        }

        private void OnDisable()
        {
            UnregisterEvents();
        }

        private void Start()
        {
#if PLATFORM_GAMECORE && !UNITY_EDITOR || NO_MOUSE_SUPPORT
            _lastInputDevice = InputDevice.Joystick;
            SetUseJoystick(true);
#else
            if (_settings.UseController)
            {
                if (_player.controllers.joystickCount == 0)
                {
                    Debug.Log("No controller detected, switching to Keyboard / Mouse input");

                    SetUseJoystick(false);

                    SetControlMode(_currentMode);
                }
                else
                {
                    SetUseJoystick(true);
                }
            }

            _lastInputDevice = InputDevice.Mouse;
#endif
        }

        private void Update()
        {
            if (!_disabled)
            {
                CheckUserUsingController();
            }
        }


        // Events

        private void RegisterEvents()
        {
            ReInput.ControllerConnectedEvent += OnControllerConnected;
            ReInput.ControllerDisconnectedEvent += OnControllerDisconnected;
        }

        private void UnregisterEvents()
        {
            ReInput.ControllerConnectedEvent -= OnControllerConnected;
            ReInput.ControllerDisconnectedEvent -= OnControllerDisconnected;
        }

        /// <summary>
        /// Event handler called when a controller is connected.  Updates settings and refreshes control mode to enable the controller.
        /// </summary>
        /// <param name="args"></param>
        private void OnControllerConnected(ControllerStatusChangedEventArgs args)
        {
            if (args.controllerType != ControllerType.Joystick)
            {
                Debug.LogError("[ReInput.OnControllerConnected] skip - non Joystick controller *************");
                return; // skip if this isn't a Joystick - TODO: test this
            }

            string cname = args.name;
            int cid = args.controllerId; // joystick.id
            Debug.LogError($"[ReInput.OnControllerConnected] joystick #{cid} connected");

            // TODO: testing to see if being more brutal about always assigning all controllers to the player fix my issue
            // of the latest controller not always being picked up
            //AssignAllControllers(force: true);
            _player.controllers.AddController(args.controller, removeFromOtherPlayers: true);

            if (_player.controllers.joystickCount == 1)
            {
                // Not sure if I should remove this event completely, keep it as is for future use, or keep it but fix it to work as imagined
                // as the code that is using it right now expects this to be what INPUT_MODE_CHANGED is
                Events.RaiseEvent(Events.CONTROLLER_INPUT);
            }
            
            SetUseJoystick(true); // Events.INPUT_MODE_CHANGED
            SetControlMode(_currentMode); // Events.SETTINGS_CHANGED
        }

        /// <summary>
        /// Event handler called when a controller is disconnected.  If there are no more controllers connected will revert control mode to
        /// use keyboard and mouse.
        /// </summary>
        /// <param name="args"></param>
        private void OnControllerDisconnected(ControllerStatusChangedEventArgs args)
        {
            if (args.controllerType != ControllerType.Joystick)
            {
                Debug.LogError("[ReInput.OnControllerDisconnected] skip - this isn't a Joystick *************");
                return; // skip if this isn't a Joystick
            }
            else
            {
                Debug.LogError("[ReInput.OnControllerDisconnected] normal controller disconnect event received");
            }

            // not sure there is any reason to remove it for a simple single player game.
            // TODO: testing to see if pairings update better without this removal
            _player.controllers.RemoveController(args.controller);

#if !PLATFORM_GAMECORE
            if (_player.controllers.joystickCount == 0)
            {
                SetUseJoystick(false); // Events.INPUT_MODE_CHANGED
                SetControlMode(_currentMode); // Events.SETTINGS_CHANGED

                Events.RaiseEvent(Events.MOUSEKEYBOARD_INPUT);
            }
#endif
        }


        // Methods

        private void AssignAllControllers(bool force = false)
        {
            if (force)
            {
                Debug.LogError($"[AssignAllControllers] forcing assignation of {ReInput.controllers.joystickCount} joysticks");
            }
            
            foreach (Joystick j in ReInput.controllers.Joysticks)
            {
                if (!force && ReInput.controllers.IsJoystickAssigned(j))
                {
                    continue; // Joystick is already assigned
                }

                //Debug.LogError($"[AssignAllControllers] ReWired: assigning controller {j.id} to default player - type={j.type} - connected={j.isConnected} - enabled={j.enabled}");

                // Assign Joystick to Player
                _player.controllers.AddController(j, removeFromOtherPlayers: true);

                // test log extreme state inspection

#if PLATFORM_GAMECORE
                //ulong xboxUser = 1;
                GameCoreControllerExtension ext = j.GetExtension<GameCoreControllerExtension>();

                if(ext != null)
                {
                    byte[] idBytes = new byte[32];
                    ext.GetNativeDeviceId(idBytes);
                    GXDKAppLocalDeviceId deviceId = new GXDKAppLocalDeviceId();
                    deviceId._a = BitConverter.ToInt32(idBytes, 0);
                    deviceId._b = BitConverter.ToInt32(idBytes, 4);
                    deviceId._c = BitConverter.ToInt32(idBytes, 8);
                    deviceId._d = BitConverter.ToInt32(idBytes, 12);
                    deviceId._e = BitConverter.ToInt32(idBytes, 16);
                    deviceId._f = BitConverter.ToInt32(idBytes, 20);
                    deviceId._g = BitConverter.ToInt32(idBytes, 24);
                    deviceId._h = BitConverter.ToInt32(idBytes, 28);
                    //uint nbControllers = GXDKInput.GetNumActiveGamepads();
                    string type = GXDKInput.GetControllerType(deviceId);
                    uint joystick = GXDKInput.GetJoystickId(deviceId);
                    ulong gxdkNativeUserId = GXDKInput.GetUserIdForGamepad(joystick);
                    ulong rewiredNativeUserId = ext.nativeUserId;

                    //Debug.LogError($"[AssignAllControllers] GXDK: {type} type device (with id#{joystick}) of gxdkNativeUserId#{gxdkNativeUserId} (rewiredNativeUserId#{rewiredNativeUserId})");
                }
#endif
            }

            /*
            Debug.LogError($"[AssignAllControllers] RESULT ***************");
            foreach (Joystick j in _player.controllers.Joysticks)
            {
                Debug.LogError($"[AssignAllControllers] ReWired: controller {j.id} paired to player #{_player.id}");
            }*/

            //PrintControlsState();
        }

        public void PrintControlsState()
        {
#if PLATFORM_GAMECORE
            Debug.LogError($"[PrintControlsState] JOYSTICK AND USER INFO ***************");
            foreach (Player p in ReInput.players.AllPlayers)
            {
                foreach (Joystick j in p.controllers.Joysticks)
                {
                    Debug.LogError($"[PrintControlsState] ReWired: player#{p.id} --> joy#{j.id}");
                }
            }

            Player sysp = ReInput.players.GetSystemPlayer();
            foreach (Joystick j in sysp.controllers.Joysticks)
            {
                Debug.LogError($"[PrintControlsState] ReWired: system player (#{sysp.id}) --> joy#{j.id}");
            }

            List<ulong> results = new List<ulong>();
            GameCoreInput.ControllerAssignment.GetUserIdsForPlayer(_player, results);
            foreach (ulong user in results)
            {
                Debug.LogError($"[PrintControlsState] ReWired: default player (#{_player.id}) --> user#{user}");
            }

            foreach (Joystick j in _player.controllers.Joysticks)
            {
                //ulong xboxUser = 1;
                GameCoreControllerExtension ext = j.GetExtension<GameCoreControllerExtension>();
                byte[] idBytes = new byte[32];
                if ( ext != null )
                {
                    ext.GetNativeDeviceId(idBytes);
                    GXDKAppLocalDeviceId deviceId = new GXDKAppLocalDeviceId();
                    deviceId._a = BitConverter.ToInt32(idBytes, 0);
                    deviceId._b = BitConverter.ToInt32(idBytes, 4);
                    deviceId._c = BitConverter.ToInt32(idBytes, 8);
                    deviceId._d = BitConverter.ToInt32(idBytes, 12);
                    deviceId._e = BitConverter.ToInt32(idBytes, 16);
                    deviceId._f = BitConverter.ToInt32(idBytes, 20);
                    deviceId._g = BitConverter.ToInt32(idBytes, 24);
                    deviceId._h = BitConverter.ToInt32(idBytes, 28);
                    //uint nbControllers = GXDKInput.GetNumActiveGamepads();
                    string type = GXDKInput.GetControllerType(deviceId);
                    uint joystick = GXDKInput.GetJoystickId(deviceId);
                    ulong gxdkNativeUserId = GXDKInput.GetUserIdForGamepad(joystick);
                    ulong rewiredNativeUserId = ext.nativeUserId;

                    Debug.LogError($"[PrintControlsState] GXDK: gamepad#{joystick} --> user#{gxdkNativeUserId} (#{rewiredNativeUserId} in rewired)");
                }
            }
#endif
        }

        public int GetIDForAction(UserActions action)
        {
            if (_actionToControlIDMap.TryGetValue(action, out int actionId))
            {
                return actionId;

            }
            else
            {
                Debug.Log($"{action.Value}: ID not found");
                return -1;
            }
        }

        public int GetIDForAxis(MovementAxis movementAxis)
        {
            if (_axisToControlIDMap.TryGetValue(movementAxis, out int actionId))
                return actionId;
            else
                return -1;
        }

        /// <summary>
        /// Checks if the player has a joystick connected and is pressing buttons on it.  If so, will switch to controller input.
        /// </summary>
        private void CheckUserUsingController()
        {
#if PLATFORM_GAMECORE && !UNITY_EDITOR || NO_MOUSE_SUPPORT
            return;
#endif
            // If using mouse, check if a controller button has been pressed and switch to controller mode
            if (!_settings.UseController && IsControllerAvailable)
            {
                bool buttonPressed = false;

                for (int i = 0; i < _player.controllers.joystickCount; i++)
                {
                    buttonPressed |= _player.controllers.Joysticks[i].GetAnyButton();

                    ControllerPollingInfo info = _player.controllers.Joysticks[i].PollForFirstAxis();

                    buttonPressed |= info.success;
                }

                if (buttonPressed)
                {
                    SetUseJoystick(true);

                    SetControlMode(_currentMode);
                }
            }
            // If using controller, check if mouse or keyboard pressed and switch to key/mouse mode
            else if (_settings.UseController)
            {
#if UNITY_EDITOR
                if (ControllerInputOnly)
                    return;
#endif

                // If keyboard or mouse is used, switch back to mouse input
                if (_player.controllers.Keyboard.GetAnyButton() || _player.controllers.Mouse.GetAnyButton() || _player.controllers.Mouse.screenPositionDelta.sqrMagnitude > 0f)
                {
                    SetUseJoystick(false);

                    SetControlMode(_currentMode);
                }
            }
        }

        /// <summary>
        /// Gets the screen space coordinates for the mouse cursor.
        /// </summary>
        /// <returns>Vector2 Screen Space mouse position</returns>
        public Vector2 GetPointerPosition()
        {
            return Input.mousePosition;
        }

        public Vector3 GetPointerPositionOnGround()
        {
            Vector3 mousePosition = Input.mousePosition;

            Ray cameraRay = GetRayToPointerPosition();

            if (Physics.Raycast(cameraRay, out RaycastHit hit, 2000f, Layers.GROUND_LAYERS_MASK))
            {
                //Debug.DrawRay(Camera.main.transform.position, cameraRay.direction * hit.distance, Color.blue, 3f);
                //Debug.LogFormat("Mouse position: {0}, Hit Point: {1}", Camera.main.ScreenToWorldPoint(mousePosition), hit.point);

                return hit.point;
            }
            else
            {
                return Camera.main.ScreenToWorldPoint(mousePosition);
            }
        }

        /// <summary>
        /// Gets a ray from the camera position through the current mouse pointer position
        /// </summary>
        /// <returns>Ray from camera through mouse position</returns>
        public Ray GetRayToPointerPosition()
        {
            Vector3 mousePosition = Input.mousePosition;

            return Camera.main.ScreenPointToRay(mousePosition);
        }

        public Vector3 GetCanvasMousePosition(Canvas parent)
        {
            Vector2 movePos;

            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                parent.transform as RectTransform,
                GetPointerPosition(),
                parent.worldCamera,
                out movePos);

            return parent.transform.TransformPoint(movePos);
        }

        /// <summary>
        /// Get the state of the assocated button for the action passed in.
        /// </summary>
        /// <param name="action">UserAction to be checked.</param>
        /// <param name="ignoreMouse"></param>
        /// <returns></returns>
        public bool GetButton(UserActions action, bool ignoreMouse = false)
        {
            bool pressed = _player.GetButton(GetIDForAction(action));

            switch (action.Name)
            {
                case nameof(UserActions.INTERACT):
                    pressed &= UsingController || !EventSystem.current.IsPointerOverGameObject();
                    break;
                case nameof(UserActions.UISUBMIT):
                    if (ignoreMouse && pressed)
                    {
                        pressed &= GetActionSource(action.Value) != ControllerType.Mouse;
                    }

                    // Make sure UISubmit event is only true when over a UI object
                    pressed &= UsingController || EventSystem.current.IsPointerOverGameObject();

                    break;
                default:
                    break;
            }

            if (pressed)
            {
                _lastInputDevice = GetDeviceForAction(action);
            }

            return pressed;
        }

        public bool GetButtonDown(UserActions action)
        {

            bool pressed = _player.GetButtonDown(GetIDForAction(action));

            switch (action.Name)
            {
                case nameof(UserActions.INTERACT):
                case nameof(UserActions.DISMANTLE):
                    pressed &= UsingController || !EventSystem.current.IsPointerOverGameObject();
                    break;
                default:
                    break;
            }

            if (pressed)
            {
                _lastInputDevice = GetDeviceForAction(action);
            }

            return pressed;
        }

        public bool GetButtonUp(UserActions action)
        {
            return _player.GetButtonUp(GetIDForAction(action));
        }

        /// <summary>
        /// (Not working) Gets if the button has been pressed but has not been held for londer than a very short period
        /// of time.
        /// </summary>
        /// <param name="action"></param>
        /// <returns></returns>
        public bool GetButtonTap(UserActions action, bool ignoreKeyboard = false)
        {
            // If the time the button has been pressed is less than the quick press max time, return true
            //float timePressed = _player.GetButtonTimePressed(action.Value);

            //return timePressed != 0 && timePressed < quickPressMaxTime;

            //bool pressed = _player.GetButtonDown(GetIDForAction(action));

            // Since we're not really checking for a "Tap", for now I'd like to simulate it with "Up"
            // because otherwise the methods using this don't expect to fire continuously, and the ui flickers.
            bool pressed = _player.GetButtonUp(GetIDForAction(action));

            if (!pressed)
                return false;

            bool quickPressed = _player.GetButtonTimePressed(GetIDForAction(action)) < quickPressMaxTime;

            if (quickPressed && ignoreKeyboard)
            {
                quickPressed &= GetActionSource(action.Value) != ControllerType.Keyboard;
            }

            if (quickPressed)
            {
                _lastInputDevice = GetDeviceForAction(action);
            }

            return quickPressed;
        }

        /// <summary>
        /// Returns true if any button is pressed for the current control mode
        /// </summary>
        /// <returns></returns>
        public bool GetAnyButton()
        {
            return _player.GetAnyButton();
        }

        public bool GetDoubleClick(UserActions action)
        {
            return _player.GetButtonDoublePressDown(GetIDForAction(action));
        }

        public float GetAxis(MovementAxis axis)
        {
            if (_disabled)
                return 0f;

            float value = _player.GetAxis(GetIDForAxis(axis));

            //Debug.Log($"Axis:{axis.ToString()} Value:{value}");

            if (value != 0f)
            {
                _lastInputDevice = GetDeviceForAction(axis);
            }

            return value;
        }

        public string GetAxisName(MovementAxis axis)
        {
            ActionElementMap aem = GetActionElementMap(axis.ToString());

            if (aem != null)
            {
                return aem.elementIdentifierName;
            }

            return "";
        }

        public string GetButtonName(UserActions action)
        {
            ActionElementMap aem = GetActionElementMap(action.Value);

            if (aem != null)
                return aem.elementIdentifierName;

            return "";
        }

        public Sprite GetIconForAction(UserActions action)
        {
            InputDevice lastDevice = UsingController ? InputDevice.Joystick : InputDevice.Mouse;

            return _glyphs.GetGlyph(GetIDForAction(action), lastDevice);
        }

        public float GetRawAxis(MovementAxis axis)
        {
            float rawValue = _player.GetAxisRaw(axis.ToString());

            return rawValue;
        }

        public float GetRawAxis(UserActions axis)
        {
            float rawValue = _player.GetAxisRaw(axis.Value);

            return rawValue;
        }

        // Note:
        // I've read a post from guavaman where he seems to highly discourage directly enabling maps, recommending to instead
        // use ControllerMapEnabler.
        // "There are several warnings about using the "Direct" method of enabling and disabling Controller Maps in that link because
        // if you don't understand exactly how the system works, you're guaranteed to have issues trying to manage enabled state manually.
        // That's why Controller Map Enabler was developed and why it's recommended instead of using the "Direct" method."
        // We might be fine and we might know what we're doing but I have not had the chance to verify this yet -Frank
        public void SetControlMode(Mode mode)
        {
#if PLAYER_LOGGING
            Debug.Log($"Setting control mode to {mode} from {_currentMode}.");
#endif

            _currentMode = mode;

            if (_disabled)
                return;

            _inputModule.allowMouseInput = !_settings.UseController;

            Cursor.visible = !_settings.UseController;

#if FORCE_CURSOR_VISIBLE
            Cursor.visible = true;
#endif

            switch (mode)
            {
                case Mode.Gameplay:
                    _player.controllers.maps.SetAllMapsEnabled(false);
                    if (_settings.UseController)
                    {
                        _player.controllers.maps.SetMapsEnabled(true, "GameplayJoy");
                    }
                    else
                    {
                        _player.controllers.maps.SetMapsEnabled(true, "GameplayMouseKey");
                    }
                    _player.controllers.maps.SetMapsEnabled(true, "Development");
                    break;
                case Mode.UI:
                    _player.controllers.maps.SetAllMapsEnabled(false);
                    if (_settings.UseController)
                    {
                        _player.controllers.maps.SetMapsEnabled(true, "UIJoy");
                    }
                    else
                    {
                        _player.controllers.maps.SetMapsEnabled(true, "UIMouseKey");
                    }
                    break;
                case Mode.IntroUI:          // Used for the intro screen as both input modes should be active
                    _player.controllers.maps.SetAllMapsEnabled(false);
                    _player.controllers.maps.SetMapsEnabled(true, "UIJoy");
                    _player.controllers.maps.SetMapsEnabled(true, "UIMouseKey");
                    break;
                case Mode.Building:
                    _player.controllers.maps.SetAllMapsEnabled(false);
                    if (_settings.UseController)
                        _player.controllers.maps.SetMapsEnabled(true, "GameplayBuildingJoy");
                    else
                        _player.controllers.maps.SetMapsEnabled(true, "GameplayBuildingMouseKey");
                    break;
                case Mode.Dismantling:
                    _player.controllers.maps.SetAllMapsEnabled(false);
                    if (_settings.UseController)
                        _player.controllers.maps.SetMapsEnabled(true, "GameplayDismantlingJoy");
                    else
                        _player.controllers.maps.SetMapsEnabled(true, "GameplayDismantlingMouseKey");
                    break;
                case Mode.DeveloperConsole:
                    _player.controllers.maps.SetAllMapsEnabled(false);
                    _player.controllers.maps.SetMapsEnabled(true, "DeveloperConsole");
                    break;
                case Mode.BugReportingTool:
                    _player.controllers.maps.SetAllMapsEnabled(false);
                    _player.controllers.maps.SetMapsEnabled(true, "BugReportingTool");
                    break;
                case Mode.Disabled:
                    _player.controllers.maps.SetAllMapsEnabled(false);
                    _inputModule.allowMouseInput = false;
                    break;
                default:
                    break;
            }
            Events.RaiseEvent(Events.SETTINGS_CHANGED);
        }

        public void DisableControls(float time)
        {
#if PLAYER_LOGGING
            Debug.Log($"Setting controls to disabled for {time}.");
#endif

            if (_disableControlsRoutine != null)
                StopCoroutine(_disableControlsRoutine);

            _player.controllers.maps.SetAllMapsEnabled(false);
            _disabled = true;

            _disableControlsRoutine = StartCoroutine(EnableAfterDelayRoutine(time));
        }

        private IEnumerator EnableAfterDelayRoutine(float time)
        {
            float timer = 0f;

            while (timer < time)
            {
                timer += Time.unscaledDeltaTime;
                yield return null;
            }

            RestoreControls();
        }

        public void EnableControls()
        {
            if (_disableControlsRoutine != null)
                StopCoroutine(_disableControlsRoutine);

            RestoreControls();
        }

        private void RestoreControls()
        {
            _disabled = false;

            SetControlMode(_currentMode);
        }

        public Mode GetControlMode()
        {
            return _currentMode;
        }

        public bool UsingJoystick()
        {
            return _settings.UseController;
        }

        public void SetInputDevice(InputDevice input)
        {
#if PLATFORM_GAMECORE && !UNITY_EDITOR || NO_MOUSE_SUPPORT
            return;
#endif

            bool wasUsingJoystick = _settings.UseController;

            switch (input)
            {
                case InputDevice.Joystick:
                    SetUseJoystick(true);
                    break;
                case InputDevice.Keyboard:
                case InputDevice.Mouse:
                    SetUseJoystick(false);
                    break;
                default:
                    throw new UnityException("No handler for InputDevice of type: " + input.ToString());
            }

            if (wasUsingJoystick != _settings.UseController)
                Events.RaiseEvent(Events.SETTINGS_CHANGED);
        }

        private InputDevice GetDeviceForAction(UserActions action)
        {
            ControllerType inputType = GetActionSource(action.Value);

            switch (inputType)
            {
                case ControllerType.Keyboard:
                    return InputDevice.Keyboard;
                case ControllerType.Mouse:
                    return InputDevice.Mouse;
                case ControllerType.Joystick:
                case ControllerType.Custom:
                    return InputDevice.Joystick;
                default:
                    return InputDevice.Keyboard;
            }
        }

        private InputDevice GetDeviceForAction(MovementAxis axis)
        {
            ControllerType inputType = GetActionSource(axis.ToString());

            switch (inputType)
            {
                case ControllerType.Keyboard:
                    return InputDevice.Keyboard;
                case ControllerType.Mouse:
                    return InputDevice.Mouse;
                case ControllerType.Joystick:
                case ControllerType.Custom:
                    return InputDevice.Joystick;
                default:
                    return InputDevice.Keyboard;
            }
        }

        public InputDevice GetLastInputDevice()
        {
            return _lastInputDevice;
        }

        /// <summary>
        /// Causes controller to vibrate
        /// </summary>
        /// <param name="mode"></param>
        public void VibrateController(VibrationMode mode)
        {
            // If not using Joystick, just return
            if (GetLastInputDevice() != InputDevice.Joystick || !_settings.UseVibration)
                return;

            switch (mode)
            {
                case VibrationMode.Short:
                    _player.SetVibration(0, 0.75f, 0.10f);
                    break;
                case VibrationMode.Long:
                    _player.SetVibration(0, 1f, 0.75f);
                    break;
                default:
                    break;
            }
        }

        private ActionElementMap GetActionElementMap(string action)
        {
            Controller activeController;

            if (_settings.UseController)
                activeController = _player.controllers.GetController(ControllerType.Joystick, 0);
            else
                activeController = _player.controllers.GetController(ControllerType.Keyboard, 0);

            InputAction inputAction = ReInput.mapping.GetAction(action);

            ActionElementMap aem = _player.controllers.maps.GetFirstElementMapWithAction(activeController, inputAction.id, true);

            return aem;
        }

        private ControllerType GetActionSource(string action)
        {
            // Check what the source of the action was and if mouse, set pressed to false
            IList<InputActionSourceData> inputs = _player.GetCurrentInputSources(action);

            if (inputs.Count > 0)
                return inputs[0].controllerType;
            else
            {
                Debug.LogWarning("Should not be checking for Action Source on an action that has not occured this frame");

                return ControllerType.Custom;  // Return custom type as this is an error
            }
        }

        private void SetUseJoystick(bool useJoystick)
        {
#if NO_CONTROLLER_SUPPORT
            useJoystick = false;
#elif NO_MOUSE_SUPPORT
            useJoystick = true;
#endif
            _settings.SetUseJoystick(useJoystick);
            UsingController = useJoystick;
            Events.RaiseEvent(Events.INPUT_MODE_CHANGED);
        }

        public bool IsMouseOverUIRectTransform(RectTransform rectTransform)
        {
            if (_lastInputDevice == InputDevice.Joystick)
                return true;

            Vector2 localMousePoint = rectTransform.InverseTransformPoint(Input.mousePosition);
            bool isMouseIn = rectTransform.rect.Contains(localMousePoint);

            return isMouseIn;
        }

        public bool GetPointerOverUI()
        {
            return EventSystem.current.IsPointerOverGameObject();
        }

        public string GetParameterValue(string Param)
        {
            string value = null;

            if (Param.StartsWith(Constants.INSERT_TOKEN_ACTION_NAME))
            {
                string[] tokenParts = Param.Split(Constants.LOC_PARAM_TOKEN_SPLITTER);
                string name = tokenParts[1];
                UserActions action = UserActions.GetByName(name);
                value = action.Value;
            }
            else if (Param.StartsWith(Constants.INSERT_TOKEN_ACTION_ICON))
            {
                string[] tokenParts = Param.Split(Constants.LOC_PARAM_TOKEN_SPLITTER);
                string name = tokenParts[1];
                string deviceCode = tokenParts[2];
                UserActions action = UserActions.GetByName(name);

                if (action != null)
                {
                    int id = GetIDForAction(action);

                    InputDevice targetDevice = InputDevice.Mouse;
                    if (deviceCode == Constants.TOKEN_PARAM_KEYBOARD)
                    {
                        targetDevice = InputDevice.Keyboard;
                    }
                    else if (deviceCode == Constants.TOKEN_PARAM_JOYSTICK)
                    {
                        targetDevice = InputDevice.Joystick;
                    }
                    else
                    {
                        targetDevice = InputDevice.Mouse;
                    }

                    Sprite icon = _glyphs.GetGlyph(id, targetDevice);
                    value = icon.name; // intended to get the correct glyph from the sprite sheet for TMPs
                }
            }

            return value;
        }

        public bool GetStartGameAction()
        {
            for(int i = 0; i < _startGameActions.Count; i++)
            {
                bool pressed = _player.GetButtonDown(GetIDForAction(_startGameActions[i]));
                
                if (pressed)
                {
                    return true;
                }
            }

            return false;
        }

        private const int CONTROLLER_TOPMOST_FACE_BUTTON = 3; // Y on a xbox controller
        public bool GetResetMappingsButtonDown()
        {
            // PC
            if (Input.GetKeyDown(KeyCode.F1))
            {
                return true;
            }

            // Xbox
            var joys = _player.controllers.Joysticks;
            for (int i = 0; i < joys.Count; i++)
            {
                if (joys[i].GetButtonDown(CONTROLLER_TOPMOST_FACE_BUTTON))
                {
                    return true;
                }
            }

            return false;
        }
    }
}