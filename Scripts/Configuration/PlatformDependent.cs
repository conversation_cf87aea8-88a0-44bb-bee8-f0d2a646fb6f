// Copyright Isto Inc.
using Isto.Core.Game;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Configuration
{
    public class PlatformDependent : MonoBehaviour
    {
        public List<RuntimePlatform> compatiblePlatforms;

        [Header("Testing utilities")]
        [Tooltip("Incompatible object will destroy itself at injection stage as if current game mode was not in " +
                 "compatible list")]
        public bool forceIncompatible = false;

        [Inject]
        public void Inject(GameState gameState)
        {
            if (!forceIncompatible && (compatiblePlatforms == null || compatiblePlatforms.Count == 0))
            {
                Debug.LogWarning($"Platform Dependent object \"{gameObject.name}\" exists by default because compatiblePlatforms is empty", this);
            }
            else
            {
                if (forceIncompatible || IsCompatibleWithCurrentPlatform())
                {
                    if (forceIncompatible)
                    {
                        Debug.LogWarning($"Platform Dependent object \"{gameObject.name}\" forced to not exist");
                    }

                    GameObject.Destroy(this.gameObject);
                }
            }
        }

        private bool IsCompatibleWithCurrentPlatform()
        {
            bool remove = true;

            if (forceIncompatible)
            {
                return remove;
            }

            RuntimePlatform currentPlatform = Application.platform;

            for (int i = 0; i < compatiblePlatforms.Count; i++)
            {
                RuntimePlatform compatiblePlatform = compatiblePlatforms[i];

                if (currentPlatform == compatiblePlatform)
                {
                    remove = false;
                    break;
                }
            }

            return remove;
        }
    }
}