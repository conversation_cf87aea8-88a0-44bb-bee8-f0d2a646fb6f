// Copyright Isto Inc.
using Isto.Core.Items;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Configuration
{
    /// <summary>
    /// Scriptable object that holds the parameters for startup for the player inventory and unlocks
    /// </summary>
    [CreateAssetMenu(fileName = "Scriptables/New Game Setup", menuName = "Scriptables/New Game Setup")]
    public class ScenarioSetup : ScriptableObject
    {
        [Header("Items and Equipment")]
        public ItemPileList items;
        public ItemList equipped;

        [Header("Upgrades")]
        public List<UpgradeItem> startingUpgrades;
    }
}