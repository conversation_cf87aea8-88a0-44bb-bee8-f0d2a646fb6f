// Copyright Isto Inc.
using Isto.Core.Enums;

namespace Isto.Core.Configuration
{
    /// <summary>
    /// This represents the intent for a build. It used to be information we sent to analytics.
    /// Extend this enumeration here or in your project if needed.
    /// This is only serialized in VersionInfo as a means to select the next build's type. You can change it freely.
    /// </summary>
    public class BuildTypeEnum : Int32Enum<BuildTypeEnum>
    {
        // This value is mostly there to be a default for older VersionData that did not record this setting.
        public static readonly BuildTypeEnum UNKNOWN = new BuildTypeEnum(nameof(UNKNOWN));

        public static readonly BuildTypeEnum DEVELOPER = new BuildTypeEnum(nameof(DEVELOPER));
        public static readonly BuildTypeEnum PLAYTEST = new BuildTypeEnum(nameof(PLAYTEST));
        public static readonly BuildTypeEnum DEMO = new BuildTypeEnum(nameof(DEMO));
        public static readonly BuildTypeEnum EARLYACCESS = new BuildTypeEnum(nameof(EARLYACCESS));
        public static readonly BuildTypeEnum RELEASE = new BuildTypeEnum(nameof(RELEASE));

        public BuildTypeEnum(string name) : base(name)
        {
        }
    }
}