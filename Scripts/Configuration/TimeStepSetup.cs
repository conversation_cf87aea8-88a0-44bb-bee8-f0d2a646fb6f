// Copyright Isto Inc.
using System;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Configuration
{
    /// <summary>
    /// Scriptable object that holds the parameters for startup for the player inventory and unlocks
    /// </summary>
    [CreateAssetMenu(fileName = "Scriptables/New Time Step Setup", menuName = "Scriptables/New Time Step Setup")]
    public class TimeStepSetup : ScriptableObject
    {
        [Serializable]
        public class PlatformSpecificSettings
        {
            public RuntimePlatform platform;
            public float fixedTimeStep;
        }

        // Public Variables

        public List<PlatformSpecificSettings> settings;
    }
}