// Copyright Isto Inc.
using I2.Loc;
using UnityEngine;
using UnityEngine.Serialization;

namespace Isto.Core.Configuration
{
    /// <summary>
    /// Scriptable object that holds the definition of the a game mode that can be selected at startup.
    /// This will determine how the start menu behaves, what the high level game flow is, and what are the available gameplay settings.
    /// In turn, the gameplay settings will determine current stipulations of play (goal, constraints, starting setup).
    /// </summary>
    [CreateAssetMenu(fileName = "Scriptables/New Core Game Mode Definition", menuName = "Scriptables/New Core Game Mode Definition")]
    public class GameModeDefinition : ScriptableObject
    {
        public LocalizedString nameLoc;
        public LocalizedString descriptionLoc;

        [FormerlySerializedAs("InternalName")]
        public string internalName;

        [FormerlySerializedAs("Allowed")]
        public bool allowed;
        [FormerlySerializedAs("ShowIntroCutscene")]
        public bool showIntroCutscene;

        [FormerlySerializedAs("DeveloperConsoleEnabled")]
        public bool developerConsoleEnabled;
        [FormerlySerializedAs("CanSaveGame")]
        public bool canSaveGame;
        [FormerlySerializedAs("CanLoadGame")]
        public bool canLoadGame;
        [FormerlySerializedAs("ShowRestartButton")]
        public bool showRestartButton;

        public GameModeDefinition NextModeUponCompletion;

        public virtual LocalizedString NameLoc => nameLoc;
        public virtual LocalizedString DescriptionLoc => descriptionLoc;
        public virtual string InternalName => internalName;
        public virtual bool Allowed => allowed;
        public virtual bool ShowIntroCutscene => showIntroCutscene;
        public virtual bool DeveloperConsoleEnabled => developerConsoleEnabled;
        public virtual bool CanSaveGame => canSaveGame;
        public virtual bool CanLoadGame => canLoadGame;
        public virtual bool ShowRestartButton => showRestartButton;
    }
}