// Copyright Isto Inc.

using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Isto.Core.Enums;
using UnityEngine.Serialization;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Isto.Core.Configuration
{
    [CreateAssetMenu(fileName = ASSET_NAME, menuName = "Scriptables/New Game Version Info")]
    public class VersionInfo : ScriptableObject
    {
        [Serializable]
        public class VersionData : IComparable
        {
            public string FullVersionText { get { return VersionText + SEPARATOR + BuildText; } }
            public string VersionText { get { return ShortVersionText + SEPARATOR + patchNo.ToString(); } }
            public string ShortVersionText { get { return majorRevision.ToString() + SEPARATOR + minorRevision.ToString(); } }
            public string BuildText { get { return buildNo.ToString() + versionSuffix; } }

            public string Tag;
            public int majorRevision;
            public int minorRevision;
            public int patchNo;
            public int buildNo;
            public string versionSuffix;
            [EnumDropdown(typeof(TargetStoreEnum))]
            public int targetStore;
            [EnumDropdown(typeof(BuildTypeEnum))]
            public int buildType;
            [Tooltip("UTC")]
            public string timeOfDay;
            public string shortDate;
            public string longDate;

            // Generate a new version.
            // Major updates are intended mostly for launch or really major changes.
            // Minor updates are intended for new features.
            public VersionData VersionUpdate(bool major)
            {
                if (major)
                {
                    majorRevision++;
                    minorRevision = 0;
                }
                else
                {
                    minorRevision++;
                }
                patchNo = 0;
                buildNo = 0;
                return this;
            }

            // Patch an existing version, keeping the same version number.
            // Intended for minor changes and for hotfixes.
            public VersionData PatchUpdate()
            {
                patchNo++;
                buildNo = 0;
                return this;
            }

            // Make a new build for the current version/patch.
            // Intended mostly for the development cycle and internal builds.
            public VersionData BuildUpdate()
            {
                buildNo++;
                return this;
            }

            public VersionData SetCode(string suffix)
            {
                versionSuffix = suffix;
                return this;
            }

            public VersionData ClearCode()
            {
                versionSuffix = "-";
                return this;
            }

            public VersionData SetCurrentTime()
            {
                timeOfDay = DateTime.Now.ToUniversalTime().TimeOfDay.ToString();
                shortDate = DateTime.Today.ToShortDateString();
                longDate = DateTime.Today.ToLongDateString();
                return this;
            }

            public VersionData ClearTime()
            {
                timeOfDay = "-";
                shortDate = "-";
                longDate = "-";
                return this;
            }

            public VersionData SetTag(string tag)
            {
                Tag = tag;
                return this;
            }

            public VersionData TagBuild()
            {
                Tag = "build " + FullVersionText;
                return this;
            }

            public VersionData TagPatch()
            {
                Tag = "patch " + FullVersionText;
                return this;
            }

            public VersionData TagUpdate()
            {
                Tag = "update " + FullVersionText;
                return this;
            }

            public VersionData TagRelease()
            {
                Tag = "major update " + FullVersionText;
                return this;
            }

            // We send slightly different builds of the same game version to different stores so we need to differentiate those
            public VersionData OverrideSuffix(string suffix)
            {
                versionSuffix = suffix;
                Tag = "variant " + FullVersionText;
                timeOfDay = DateTime.Now.ToUniversalTime().TimeOfDay.ToString();
                shortDate = DateTime.Today.ToShortDateString();
                longDate = DateTime.Today.ToLongDateString();
                return this;
            }

            public int CompareTo(object obj)
            {
                VersionData other = obj as VersionData;

                if (other == null)
                    return FullVersionText.CompareTo(obj);
                if (majorRevision != other.majorRevision)
                    return majorRevision.CompareTo(other.majorRevision);
                if (minorRevision != other.minorRevision)
                    return minorRevision.CompareTo(other.minorRevision);
                if (patchNo != other.patchNo)
                    return patchNo.CompareTo(other.patchNo);
                if (buildNo != other.buildNo)
                    return buildNo.CompareTo(other.buildNo);

                // These fields should not matter anymore but keeping them for retrocompatibility
                if (shortDate != other.shortDate)
                    return shortDate.CompareTo(other.shortDate);
                if (timeOfDay != other.timeOfDay)
                    return timeOfDay.CompareTo(other.timeOfDay);

                return 0;
            }

            public VersionData Clone()
            {
                VersionData clone = new VersionData();
                clone.Tag = this.Tag;
                clone.majorRevision = this.majorRevision;
                clone.minorRevision = this.minorRevision;
                clone.patchNo = this.patchNo;
                clone.buildNo = this.buildNo;
                clone.targetStore = this.targetStore;
                clone.buildType = this.buildType;
                clone.versionSuffix = this.versionSuffix;
                clone.timeOfDay = this.timeOfDay;
                clone.shortDate = this.shortDate;
                clone.longDate = this.longDate;
                return clone;
            }
        }


        // UNITY HOOKUP

        [FormerlySerializedAs("_current")]
        [SerializeField]
        private VersionData _activeVersion;

        // Sorted by time
        [NonReorderable]
        [Tooltip("Always has to be in order of when they were created, most recent build being last")]
        public List<VersionData> versionHistory;


        // CONSTANTS

        private const string ASSET_NAME = "GameVersionInfo";
        private static readonly string ASSET_PATH = $"Assets/Resources/{ASSET_NAME}.asset";

        private static readonly string SEPARATOR = ".";


        // PROPERTIES

        public VersionData LastBuild => versionHistory?.LastOrDefault() ?? new VersionData();
        public VersionData ActiveVersion => _activeVersion;

        public string TargetStoreCode
        {
            get
            {
                string code = ""; // legitimate intended value for platform-agnostic builds
                TargetStoreEnum store = TargetStoreEnum.GetFromValue(ActiveVersion.targetStore) as TargetStoreEnum;

                if (store == TargetStoreEnum.STEAM)
                {
                    code = "SP"; // only one steam build type for now
                }
                else if (store == TargetStoreEnum.EPIC)
                {
                    code = "EP"; // only one epic build type for now
                }
                else if (store == TargetStoreEnum.MICROSOFT)
                {
                    // not really making UWP builds yet, and we may only ever do PC builds

                    // BuildTarget.StandaloneWindows
                    code = "MP";

                    // BuildTarget.WSAPlayer (UWP)
                    // code = "MU";
                }
                else if (store == TargetStoreEnum.XBOX)
                {
                    code = "XD"; // aka xbox default - not intended to be used, but in case we do something unplanned
#if UNITY_EDITOR
                    // Note: for BuildTarget.WSAPlayer (UWP) - see MICROSOFT target store
                    if (EditorUserBuildSettings.activeBuildTarget == BuildTarget.GameCoreXboxOne)
                    {
                        code = "XO";
                    }
                    else if (EditorUserBuildSettings.activeBuildTarget == BuildTarget.GameCoreXboxSeries)
                    {
                        code = "XS";
                    }
                    else
                    {
                        Debug.LogError($"Unexpected BuildTarget {EditorUserBuildSettings.activeBuildTarget.ToString()}"
                                     + $" for TargetStore XBOX");
                    }
#endif
                }
                else
                {
                    // assumed to be "NONE"
                }

                return code;
            }
        }

        public BuildTypeEnum Type
        {
            get
            {
                return BuildTypeEnum.GetFromValue(ActiveVersion.buildType) as BuildTypeEnum;
            }
            set
            {
                ActiveVersion.buildType = value.Value;
            }
        }

        public TargetStoreEnum Store
        {
            get
            {
                return TargetStoreEnum.GetFromValue(ActiveVersion.targetStore) as TargetStoreEnum;
            }
            set
            {
                ActiveVersion.targetStore = value.Value;
            }
        }


        // LIFECYCLE

        private void OnValidate()
        {
            // These fields mean nothing until we're ready to publish a build and archive it in the version history
            // Make sure user can't fiddle with them in the meantime - they'll be auto-generated.
            ResetCurrentTimePunch();
        }

        // OTHER METHODS

        private void ResetCurrentTimePunch()
        {
            _activeVersion.ClearTime().ClearCode().SetTag("Current WIP Version");
        }

        [ContextMenu("MajorUpdate")]
        public void MajorUpdate()
        {
            ActiveVersion.VersionUpdate(major: true);
        }

        [ContextMenu("MinorUpdate")]
        public void MinorUpdate()
        {
            ActiveVersion.VersionUpdate(major: false);
        }

        [ContextMenu("PatchUpdate")]
        public void PatchUpdate()
        {
            ActiveVersion.PatchUpdate();
        }

        [ContextMenu("AutoIncrementBuild")]
        public void AutoIncrementBuild()
        {
            bool launched = ActiveVersion.majorRevision > LastBuild.majorRevision;
            bool updated = ActiveVersion.minorRevision > LastBuild.minorRevision;
            bool patched = ActiveVersion.patchNo > LastBuild.patchNo;

            versionHistory.Add(_activeVersion.Clone().SetCurrentTime().SetCode(TargetStoreCode));

            if (launched)
            {
                LastBuild.TagRelease();
            }
            else if (updated)
            {
                LastBuild.TagUpdate();
            }
            else if (patched)
            {
                LastBuild.TagPatch();
            }
            else
            {
                LastBuild.TagBuild();
            }

            _activeVersion.BuildUpdate();

            Debug.Log("Archiving version " + LastBuild.FullVersionText);
            Debug.Log("Incrementing next build version to " + ActiveVersion.FullVersionText);
#if UNITY_EDITOR
            EditorUtility.SetDirty(this);
#endif
        }

        [ContextMenu("ResetToLastBuild")]
        public void ResetToLastBuild()
        {
            // In practice being exactly on last build is bad, so we immediately autoincrement
            _activeVersion = LastBuild.Clone().BuildUpdate();
            ResetCurrentTimePunch();
        }

        [ContextMenu("PrintCurrentVersion")]
        public void PrintCurrentVersion()
        {
            Debug.Log("Current version is " + ActiveVersion.FullVersionText + " (" + ActiveVersion.Tag + ")");
        }

        /// <summary>
        /// Conveninent way to access the VersionInfo file for editor-only logic.
        /// At runtime, use the injection.
        /// </summary>
        public static VersionInfo LoadFromResources()
        {
            // TODO: use AssetDatabase.LoadAssetAtPath instead
            VersionInfo version = Resources.Load<VersionInfo>(ASSET_NAME);
            if (version == null)
            {
                Debug.LogError("Could not find VersionInfo file in the project resource folders");
            }
            return version;
        }
    }
}