// Copyright Isto Inc.
using Isto.Core.Enums;
using Isto.Core.Game;
using System;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Configuration
{
    public class BuildTypeDependent : MonoBehaviour
    {
        [Serializable]
        public class BuildTypeData
        {
            [EnumDropdown(typeof(BuildTypeEnum))]
            public int enumValue;

            public BuildTypeEnum GetEnum()
            {
                return BuildTypeEnum.GetFromValue(enumValue) as BuildTypeEnum;
            }
        }

        public List<BuildTypeData> compatibleBuildTypes;

        [Header("Testing utilities")]
        [Tooltip("Incompatible object will destroy itself at injection stage as if current game mode was not in " +
                 "compatible list")]
        public bool forceIncompatible = false;

        [Inject]
        public void Inject(GameState gameState)
        {
            if (!forceIncompatible && (compatibleBuildTypes == null || compatibleBuildTypes.Count == 0))
            {
                Debug.LogWarning($"Build Type Dependent object \"{gameObject.name}\" exists by default because compatibleBuildTypes is empty", this);
            }
            else
            {
                if (forceIncompatible || IsCompatibleWithCurrentBuildType())
                {
                    if (forceIncompatible)
                    {
                        Debug.LogWarning($"Build Type Dependent object \"{gameObject.name}\" forced to not exist");
                    }

                    GameObject.Destroy(this.gameObject);
                }
            }
        }

        private bool IsCompatibleWithCurrentBuildType()
        {
            bool remove = true;

            if (forceIncompatible)
            {
                return remove;
            }

            VersionInfo version = VersionInfo.LoadFromResources();
            Debug.Assert(version != null, "VersionInfo not found in project resources", this.gameObject);

            BuildTypeEnum currentBuildType = version.Type;
            Debug.Assert(currentBuildType != BuildTypeEnum.UNKNOWN,
                        "VersionInfo has BuildTypeEnum UNKNOWN which is not supported", this.gameObject);

            for (int i = 0; i < compatibleBuildTypes.Count; i++)
            {
                BuildTypeData compatibleType = compatibleBuildTypes[i];

                if (compatibleType == null || compatibleType.GetEnum() == BuildTypeEnum.UNKNOWN)
                {
                    continue;
                }

                if (currentBuildType == compatibleType.GetEnum())
                {
                    remove = false;
                    break;
                }
            }

            return remove;
        }
    }
}