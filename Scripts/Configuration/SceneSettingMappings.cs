// Copyright Isto Inc.
using Isto.Core.Scenes;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Configuration
{
    /// <summary>
    /// Scriptable object that associates game mode with game scene and game settings.
    /// Beware that the game settings established in this object can be overriden from the EssentialsSceneInstaller.
    /// </summary>
    [CreateAssetMenu(fileName = "GlobalSettings", menuName = "Scriptables/Tools/GlobalSettingsAsset")]
    public class SceneSettingMappings : ScriptableObject
    {
        [Serializable]
        public class Mappping
        {
            public GameModeDefinition gameMode;
            [SceneReference] public string sceneName;
            [SceneReference] public string parentSceneName;
            public GameplaySettings settings;
            public bool sandboxUseOnly;
        }

        public List<Mappping> settingsMappings;

        [Header("Default Settings")]
        [Tooltip("If mapping cannot resolve, use this settings asset for it")]
        public GameplaySettings defaultSettings;
        [Header("Default Scene")]
        [Tooltip("If mapping cannot resolve, use this scene for it")]
        [SceneReference] public string defaultScene;

        [SceneReference] public string defaultParentSceneName;

        public List<GameModeDefinition> GetGameModes()
        {
            List<GameModeDefinition> gameModes = new List<GameModeDefinition>();
            foreach (var mapping in settingsMappings)
            {
                if (mapping.gameMode != null)
                    gameModes.Add(mapping.gameMode);
            }
            return gameModes;
        }

        public bool TryGetScene(GameModeDefinition mode, out Mappping sceneSettings)
        {
            for (int i = 0; i < settingsMappings.Count; i++)
            {
                if (settingsMappings[i].gameMode.InternalName == mode.InternalName)
                {
                    sceneSettings = settingsMappings[i];

                    if (string.IsNullOrEmpty(sceneSettings.parentSceneName))
                    {
                        sceneSettings.parentSceneName = defaultParentSceneName;
                    }
                    
                    return true;
                }
            }

            sceneSettings = new Mappping
            {
                sceneName = defaultScene,
                parentSceneName = defaultParentSceneName
            };
            return false;
        }

        public bool TryGetSettings<TGameplaySettings>(GameModeDefinition mode, out TGameplaySettings settings) where TGameplaySettings : GameplaySettings
        {
            for (int i = 0; i < settingsMappings.Count; i++)
            {
                if (settingsMappings[i].gameMode.InternalName == mode.InternalName)
                {
                    settings = settingsMappings[i].settings as TGameplaySettings;

                    return true;
                }
            }

            settings = defaultSettings as TGameplaySettings;
            return false;
        }

        // Booting directly from scene doesn't tell us what game mode we're in. But it's convenient for testing in engine.
        // For now finding the first compatible settings in the list will do.
        // During gameplay, what is important is the game setting, not the game mode, so that will be missing and that should be fine.
        public bool TryGetSettings<TGameplaySettings>(string sceneName, out TGameplaySettings settings) where TGameplaySettings : GameplaySettings
        {
            for (int i = 0; i < settingsMappings.Count; i++)
            {
                if (settingsMappings[i].sceneName.Equals(sceneName))
                {
                    settings = settingsMappings[i].settings as TGameplaySettings;
                    return true;
                }
            }

            settings = defaultSettings as TGameplaySettings;
            return false;
        }

        // Turns out GameMode is important to know to allow saving and loading game files properly, so
        // allow same trick as for resolving the settings above
        public bool TryGetGameMode(string sceneName, out GameModeDefinition mode)
        {
            for (int i = 0; i < settingsMappings.Count; i++)
            {
                if (settingsMappings[i].sceneName.Equals(sceneName))
                {
                    mode = settingsMappings[i].gameMode;
                    return true;
                }
            }

            mode = null; // At the moment I don't know if we need to make work anything that would happen from an unmapped scene
            return false;
        }

        public void OnValidate()
        {
            HashSet<GameModeDefinition> normalBuildScenePairs = new HashSet<GameModeDefinition>();

            for (int i = 0; i < settingsMappings.Count; i++)
            {
                if (!settingsMappings[i].sandboxUseOnly)
                {
                    if (normalBuildScenePairs.Contains(settingsMappings[i].gameMode))
                        Debug.LogError("Duplicate Settings Mapping found in SettingsMappings, this is not supported");
                    else
                        normalBuildScenePairs.Add(settingsMappings[i].gameMode);
                }
            }
        }
    }
}