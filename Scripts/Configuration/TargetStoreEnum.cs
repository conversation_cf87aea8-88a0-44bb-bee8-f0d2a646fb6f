// Copyright Isto Inc.
using Isto.Core.Enums;

namespace Isto.Core.Configuration
{
    /// <summary>
    /// This represents the store for a build. It determines what suffix goes in the version code and the watermark.
    /// Extend this enumeration here or in your project if needed.
    /// This is serialized in VersionInfo to track our next build type and our build history.
    /// </summary>
    public class TargetStoreEnum : Int32Enum<TargetStoreEnum>
    {
        // "No store" builds are used for ad hoc deployment or other direct or semi-direct options
        public static readonly TargetStoreEnum NONE = new TargetStoreEnum(0, nameof(NONE));

        // PC stores
        public static readonly TargetStoreEnum STEAM = new TargetStoreEnum(1, nameof(STEAM));
        public static readonly TargetStoreEnum EPIC = new TargetStoreEnum(2, nameof(EPIC));
        public static readonly TargetStoreEnum MICROSOFT = new TargetStoreEnum(3, nameof(MICROSOFT));
        
        // Consoles
        public static readonly TargetStoreEnum XBOX = new TargetStoreEnum(40, nameof(XBOX));
        public static readonly TargetStoreEnum PLAYSTATION = new TargetStoreEnum(50, nameof(PLAYSTATION));
        public static readonly TargetStoreEnum NINTENDO = new TargetStoreEnum(60, nameof(NINTENDO));

        public TargetStoreEnum(int value, string name) : base(value, name)
        {
        }
    }
}