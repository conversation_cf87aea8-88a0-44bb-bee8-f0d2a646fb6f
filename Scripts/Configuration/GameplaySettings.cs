// Copyright Isto Inc.
using Isto.Core.Enums;
using Isto.Core.Scenes;
using UnityEngine;
using UnityEngine.Serialization;

namespace Isto.Core.Configuration
{
    [CreateAssetMenu(fileName = "New Gameplay Settings", menuName = "Scriptables/Tools/CoreGameplaySettings")]
    public class GameplaySettings : ScriptableObject
    {
        #region Deprecated
        public enum Loadout
        {
            Default,
            Automation,
            AIInteraction,
            Crafting,
            Speedrun,
            Everything,
            AICombat,
            SteveWorking,
            GameMechanics,
            TestingResearch,
            CreativeMode
        }
        #endregion

        // Extend this enumeration in your project.
        // Is it safe to edit this list?
        public class LoadoutEnum : Int32Enum<LoadoutEnum>
        {
            public static readonly LoadoutEnum DEFAULT = new LoadoutEnum((int)Loadout.Default, nameof(DEFAULT));
            public static readonly LoadoutEnum AUTOMATION = new LoadoutEnum((int)Loadout.Automation, nameof(AUTOMATION));

            public LoadoutEnum(int value, string name) : base(value, name)
            {
            }
        }

        [Header("Scenes")]
        [SceneReference]
        public string titleScreenSceneName;

        [Header("Basic Configuration")]
        public bool endGamePopupLarge = false;
        public bool showWelcomePopup = false;
        public bool showBuildingConnectAnimations = true;
        public bool showDialogue = true;

        // Note: from a quick check it seems feedbackOnStory and feedbackUrl are not related
        // feedbackOnStory opens a special popup driven by the Feedback class (hardcoded in it and instantiated by it)
        // feedbackUrl is used in UIDialogueMenuState from prefab UI-SubMenu_DialoguePopUp in our Essentials scene
        // as of this writing the hardcoded link is for a google form asking about atrio in general and not story in particular
        // we'll probably want to do that in all our games
        public string feedbackUrl = "https://docs.google.com/forms/d/e/1FAIpQLSeJoSIvdEM4a5XTYKo8yzJ6s2AIUQ5s0uihAIBvzSX5kvnwPQ/viewform?usp=sf_link";

        // Public Variables
        [Header("Game Scenarios")]
        [FormerlySerializedAs("mode")]
        [EnumDropdown(typeof(LoadoutEnum))]
        public int selectedScenario;

        [Header("Setups hookup")]
        public ScenarioSetup defaultSetup;
        public ScenarioSetup automationSetup;

        public LoadoutEnum Mode => LoadoutEnum.GetByValue(selectedScenario);

        // Methods

        public virtual ScenarioSetup GetStartingSetup()
        {
            if (selectedScenario == LoadoutEnum.DEFAULT.Value)
            {
                return defaultSetup;
            }
            else if (selectedScenario == LoadoutEnum.AUTOMATION.Value)
            {
                return automationSetup;
            }
            else
            {
                return defaultSetup;
            }
        }
    }
}