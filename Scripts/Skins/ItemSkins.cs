using System;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Skins
{
    [Serializable]
    public class ItemSkins
    {
        public string internalName;
        public ItemSkinGroup itemGroup;
        public List<SpriteToSwap> spritesToSwap;
    }

    [Serializable]
    public class SpriteToSwap
    {
        public SpriteRenderer spriteOnPrefab;
        public Sprite newSprite;
    }
}
