// Copyright Isto Inc.
using Isto.Core.Game;
using Isto.Core.Data;
using Isto.Core.Themes;
using UnityEngine;
using Zenject;

namespace Isto.Core.Skins
{
    public class SkinController : <PERSON>oBeh<PERSON>our, IDataLoadCompleteHandler
    {
        // Private Variables


        protected bool _registeredEvents = false;

        // Cache

        protected MasterSkinList _masterSkinList;
        protected GameState _gameState;
        protected ThemeManager _themeManager;

        // Lifecycle Events

        [Inject]
        public void Inject(MasterSkinList masterSkinList, GameState gameState, ThemeManager themeManager)
        {
            _masterSkinList = masterSkinList;
            _gameState = gameState;
            _themeManager = themeManager;
        }

        protected virtual void Start()
        {
            RegisterEvents();
        }

        private void OnEnable()
        {
            OnSkinChanged();
            RegisterEvents();
        }

        private void OnDisable()
        {
            UnregisterEvents();
        }

        private void OnDestroy()
        {
            UnregisterEvents();
        }

        protected virtual void RegisterEvents()
        {
            if (_registeredEvents)
                return;

            Events.Subscribe(Events.MINIDEER_SKIN_CHANGED, OnSkinChanged);
            Events.Subscribe(Events.PUSHBACK_SKIN_CHANGED, OnSkinChanged);

            _registeredEvents = true;
        }

        protected virtual void UnregisterEvents()
        {
            if (!_registeredEvents)
                return;

            Events.UnSubscribe(Events.MINIDEER_SKIN_CHANGED, OnSkinChanged);
            Events.UnSubscribe(Events.PUSHBACK_SKIN_CHANGED, OnSkinChanged);

            _registeredEvents = false;
        }

        protected virtual void OnSkinChanged()
        {
            if (_themeManager == null)
                return;  // Hasn't been injected yet so skip this

            CreatureSkinSet set = _themeManager.GetCurrentCreatureSkinSet(this.gameObject);
            if (set == null)
                return;

            string skinName = set.skinSetSpineName;

            if (string.IsNullOrEmpty(skinName))
                return;

            SetSkin(skinName);
        }

        public virtual void SetSkin(string skinName)
        {

        }

        public virtual string GetSkin()
        {
            return Constants.DEFAULT_SKIN_NAME;
        }

        public virtual void SetSkinNameFromData(string name)
        {
            // Note: we decided to have only the "current theme" matter for now but we'll probably have to have a way to pick
            // which skin to show if we start to use skins to differentiate creatures
        }

        public virtual void OnDataLoadComplete()
        {
            // We could apply the skin here in case we were looking to take into account the loaded data but for now we are
            // ignoring it
        }
    }
}