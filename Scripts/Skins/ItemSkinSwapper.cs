// Copyright Isto Inc.
using Isto.Core.Themes;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Skins
{
    public class ItemSkinSwapper : MonoBehaviour
    {
        [SerializeField] private int selectedSkin = 0;
        [SerializeField] private List<ItemSkins> itemSkins = default;

        // Injected References

        private ThemeManager _themeManager;

        [Inject]
        public void Inject(ThemeManager themeManager)
        {
            _themeManager = themeManager;
        }

        private void Start()
        {
            OnSkinChanged();

            Events.Subscribe(Events.ADVANCED_ITEMS_SKIN_CHANGED, OnSkinChanged);
        }

        private void OnDestroy()
        {
            Events.UnSubscribe(Events.ADVANCED_ITEMS_SKIN_CHANGED, OnSkinChanged);
        }

        private void OnSkinChanged()
        {
            ItemSkinGroup currentGroup = _themeManager.CurrentItemGroup;
            if (currentGroup == null)
                return;

            string targetGroupName = currentGroup.skinGroupInternalName;

            if (string.IsNullOrEmpty(targetGroupName))
                return;

            for (int i = 0; i < itemSkins.Count; i++)
            {
                if (itemSkins[i].itemGroup.skinGroupInternalName == targetGroupName)
                {
                    selectedSkin = i;
                    SkinSwap();
                    break;
                }
            }
        }

        [ContextMenu("Swap The Skins")]
        private void SkinSwap()
        {
            List<SpriteToSwap> spritesToSwap = itemSkins[selectedSkin].spritesToSwap;

            for (int i = 0; i < spritesToSwap.Count; i++)
            {
                spritesToSwap[i].spriteOnPrefab.sprite = spritesToSwap[i].newSprite;
            }
        }
    }
}
