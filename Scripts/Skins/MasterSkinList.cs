// Copyright Isto Inc.
using System.Linq;
using UnityEngine;

namespace Isto.Core.Skins
{
    /// <summary>
    /// Contains a reference to all the Skin definitions that need to be injected on scene load. [will be] Used by Zenject scene installers 
    /// to queue the cosiderations for injection.
    /// </summary>
    [CreateAssetMenu(fileName = "MasterSkinList", menuName = "Scriptables/Skins/Master Skin List")]
    public class MasterSkinList : ScriptableObject
    {
        public ThemeSkinSet[] themeSets;
        public PlayerSkinSet[] playerSets;
        public CreatureSkinSet[] creatureSets;
        public PlayerSkinPart[] playerParts;
        public ItemSkinGroup[] itemGroups;

        public PlayerSkinSet GetPlayerSkinSet(string internalName)
        {
            if (playerSets.Length <= 0)
            {
                return null;
            }
            
            return playerSets.First(x => x.skinSetInternalName == internalName);
        }

        public PlayerSkinPart GetPlayerHatSkin(string internalName)
        {
            if (playerParts.Length <= 0)
            {
                return null;
            }
            
            return playerParts.First(x => x.type == PlayerSkinPart.SkinPartType.Hat && x.skinPartInternalName == internalName);
        }

        public PlayerSkinPart GetPlayerBodySkin(string internalName)
        {
            if (playerParts.Length <= 0)
            {
                return null;
            }
            
            return playerParts.First(x => x.type == PlayerSkinPart.SkinPartType.Body && x.skinPartInternalName == internalName);
        }

        public CreatureSkinSet GetMiniDeerSkin(string internalName)
        {
            if (creatureSets.Length <= 0)
            {
                return null;
            }
            
            return creatureSets.First(x => x.type == Identifier.IdentifierFlags.MiniDeer && x.skinSetInternalName == internalName);
        }

        public CreatureSkinSet GetPushbackSkin(string internalName)
        {
            if (creatureSets.Length <= 0)
            {
                return null;
            }
            
            return creatureSets.First(x => Identifier.IdentifierFlags.AllPushbacks.HasFlag(x.type) && x.skinSetInternalName == internalName);
        }

        public ItemSkinGroup GetItemSkinGroup(string internalName)
        {
            if (itemGroups.Length <= 0)
            {
                return null;
            }
            
            return itemGroups.First(x => x.skinGroupInternalName == internalName);
        }

        /// <summary>
        /// Loads all skin scriptable objects from "Resources/Skins/" folders in the project.
        /// </summary>
        [ContextMenu("LoadAllSkinsFromAssets")]
        public void LoadAllSkinsFromAssets()
        {
            ThemeSkinSet[] loadedThemes = Resources.LoadAll<ThemeSkinSet>("Skins");
            themeSets = loadedThemes;


            PlayerSkinSet[] loadedPlayers = Resources.LoadAll<PlayerSkinSet>("Skins");
            playerSets = loadedPlayers;


            CreatureSkinSet[] loadedCreatures = Resources.LoadAll<CreatureSkinSet>("Skins");
            creatureSets = loadedCreatures;


            PlayerSkinPart[] loadedParts = Resources.LoadAll<PlayerSkinPart>("Skins");
            playerParts = loadedParts;

            ItemSkinGroup[] loadedGroups = Resources.LoadAll<ItemSkinGroup>("Skins");
            itemGroups = loadedGroups;

#if UNITY_EDITOR
            UnityEditor.EditorUtility.SetDirty(this);
#endif
        }
    }
}