// Copyright Isto Inc.
using System;

namespace Isto.Core.Enums
{
    /// <summary>
    /// Represents an enumeration who's values are stored as String.
    /// The generic definition means each StringEnum variation will be tracked separately where CoreEnum is concerned.
    /// (I don't want all CoreEnum of StringEnum to be aggregating their caches)
    /// </summary>
    /// <typeparam name="T">A type that derives from StringEnum</typeparam>
    public abstract class StringEnum<T> : ExpandableEnum<StringEnum<T>, String> where T : StringEnum<T>
    {
        /// <summary>
        /// Specific value constructor
        /// </summary>
        /// <param name="value">The internal string value of your Enum item</param>
        /// <param name="name">The name of your Enum item.</param>
        public StringEnum(string value, string name) : base(value, name)
        {
        }

        public static T GetByValue(string value)
        {
            return GetFromValue(value) as T;
        }

        public static T GetByName(string name)
        {
            return GetFromName(name) as T;
        }
    }
}