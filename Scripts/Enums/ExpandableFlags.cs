// Copyright Isto Inc.
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Enums
{
    /// <summary>
    /// This class is meant to add to our Enum implementation some operations that only make sense when working on Flag values
    /// </summary>
    /// <typeparam name="T">Your enum type</typeparam>
    /// <typeparam name="U">The underlying value type</typeparam>
    public abstract class ExpandableFlags<T, U> : ExpandableEnum<T, U>, IEnumerable
                                                where T : ExpandableFlags<T, U>
                                                 where U : new()
    {
        // Constructor for building flags at runtime (e.g. when combining flags)
        public ExpandableFlags() : base()
        {
        }

        // Constructor for defining flags only at initialization, "constant style"
        public ExpandableFlags(U value, string name) : base(value, name)
        {
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }

        /// <summary>
        /// Iterates over the flags that are raised in this set.
        /// </summary>
        public IEnumerator<ExpandableFlags<T, U>> GetEnumerator()
        {
            List<ExpandableFlags<T, U>> list = new List<ExpandableFlags<T, U>>();
            T flags = this as T;
            U defaultStruct = new U();

            foreach (ExpandableFlags<T, U> flagDefinition in GetValues())
            {
                // skip zero, we don't want to iterate on that
                if (flagDefinition.Value.Equals(defaultStruct))
                    continue;

                if (flags.HasFlag(flagDefinition as T))
                {
                    list.Add(flagDefinition);
                }
            }

            return list.GetEnumerator();
        }

        public abstract bool HasFlag(T flag);

        public static int LogAndCountFlags(T flags)
        {
            int flagCount = 0;
            foreach (T flag in flags)
            {
                flagCount++;
                string log = string.Format("Value:{0} Name:{1}", flag.Value, flag.Name);
                Debug.Log(log);
            }
            return flagCount;
        }
    }
}