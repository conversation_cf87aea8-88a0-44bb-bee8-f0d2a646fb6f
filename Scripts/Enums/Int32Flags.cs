// Copyright Isto Inc.
using System;

namespace Isto.Core.Enums
{
    public abstract class Int32Flags<T> : ExpandableFlags<Int32Flags<T>, UInt32>
                                where T : Int32Flags<T>, new()
    {
        public const UInt32 BIT_0 = 0;
        public const UInt32 BIT_1 = 1 << 0;
        public const UInt32 BIT_2 = 1 << 1;
        public const UInt32 BIT_3 = 1 << 2;
        public const UInt32 BIT_4 = 1 << 3;
        public const UInt32 BIT_5 = 1 << 4;
        public const UInt32 BIT_6 = 1 << 5;
        public const UInt32 BIT_7 = 1 << 6;
        public const UInt32 BIT_8 = 1 << 7;
        public const UInt32 BIT_9 = 1 << 8;
        public const UInt32 BIT_10 = 1 << 9;
        public const UInt32 BIT_11 = 1 << 10;
        public const UInt32 BIT_12 = 1 << 11;
        public const UInt32 BIT_13 = 1 << 12;
        public const UInt32 BIT_14 = 1 << 13;
        public const UInt32 BIT_15 = 1 << 14;
        public const UInt32 BIT_16 = 1 << 15;
        public const UInt32 BIT_17 = 1 << 16;
        public const UInt32 BIT_18 = 1 << 17;
        public const UInt32 BIT_19 = 1 << 18;
        public const UInt32 BIT_20 = 1 << 19;
        public const UInt32 BIT_21 = 1 << 20;
        public const UInt32 BIT_22 = 1 << 21;
        public const UInt32 BIT_23 = 1 << 22;
        public const UInt32 BIT_24 = 1 << 23;
        public const UInt32 BIT_25 = 1 << 24;
        public const UInt32 BIT_26 = 1 << 25;
        public const UInt32 BIT_27 = 1 << 26;
        public const UInt32 BIT_28 = 1 << 27;
        public const UInt32 BIT_29 = 1 << 28;
        public const UInt32 BIT_30 = 1 << 29;
        public const UInt32 BIT_31 = 1 << 30;
        public const UInt32 BIT_32 = (UInt32)1 << 31;
        public const UInt32 BIT_ALL = uint.MaxValue;

        public T AsDerivedType => this as T;

        // Constructor for building flags at runtime (e.g. when combining flags)
        public Int32Flags() : base()
        {
        }

        // Constructor for defining flags only at initialization, "constant style"
        public Int32Flags(UInt32 value, string name) : base(value, name)
        {
        }

        /// <summary>
        /// Gets the correct Enum symbol representation from its inner value representation.
        /// </summary>
        /// <param name="value">The inner value of the Enum.</param>
        /// <returns>The matching Enum entry, or null.</returns>
        public static new T GetFromValue(UInt32 value)
        {
            return (T) ExpandableFlags<Int32Flags<T>, UInt32>.GetFromValue(value);
        }

        /// <summary>
        /// Gets the correct Enum symbol representation from its string representation.
        /// </summary>
        /// <param name="enumValueName">The string representation of the Enum.</param>
        /// <returns>The matching Enum entry, or null.</returns>
        public static new T GetFromName(string enumValueName)
        {
            return (T) ExpandableFlags<Int32Flags<T>, UInt32>.GetFromName(enumValueName);
        }

        public static bool ContainsAny(T a, T b)
        {
            return a.HasAnyFlag(b);
        }

        public static bool ContainsAll(T a, T b)
        {
            return a.HasFlag(b);
        }

        /// <summary>
        /// Exclude flag set b from flag set a
        /// </summary>
        private static T ExcludeInternal(T a, T b)
        {
            UInt32 combinedValue = a.Value & ~b.Value;
            string combinedName = a.Name + " without " + b.Name;
            T ret = new T();
            ret.SetValue(combinedValue, combinedName);
            return ret;
        }

        /// <summary>
        /// Intersect flag set a and flag set b
        /// </summary>
        private static T IntersectInternal(T a, T b)
        {
            UInt32 combinedValue = a.Value & b.Value;
            string combinedName = a.Name + " & " + b.Name;
            T ret = new T();
            ret.SetValue(combinedValue, combinedName);
            return ret;
        }

        public static T Intersect(params T[] args)
        {
            T ret = args[0];
            for (int i = 1; i < args.Length; i++)
            {
                ret = IntersectInternal(ret, args[i]);
            }
            return ret;
        }

        /// <summary>
        /// Combine flags sets a and b
        /// </summary>
        private static T UnionInternal(T a, T b)
        {
            UInt32 combinedValue = a.Value | b.Value;
            string combinedName = a.Name + " | " + b.Name;
            T ret = new T();
            ret.SetValue(combinedValue, combinedName);
            return ret;
        }

        public static T Union(params T[] args)
        {
            T ret = args[0];
            for (int i = 1; i < args.Length; i++)
            {
                ret = UnionInternal(ret, args[i]);
            }
            return ret;
        }

        /// <summary>
        /// Exclusive OR on flags sets a and b
        /// XOR is (A | B) - (A & B)
        /// it is also (A - B) + (B - A)
        /// </summary>
        private static T ExclusiveUnion(T a, T b)
        {
            UInt32 combinedValue = a.Value ^ b.Value;
            string combinedName = a.Name + " ^ " + b.Name;
            T ret = new T();
            ret.SetValue(combinedValue, combinedName);
            return ret;
        }

        private static T Invert(T a)
        {
            UInt32 invertedValue = ~a.Value;

            string invertedName = "~" + a.Name;
            if (a.Name.StartsWith("~"))
                invertedName = a.Name.Substring(1);
            
            T ret = new T();
            ret.SetValue(invertedValue, invertedName);
            return ret;
        }

        public T Exclude(T flags)
        {
            return ExcludeInternal(this.AsDerivedType, flags);
        }

        public Int32Flags<T> Exclude(Int32Flags<T> flags)
        {
            return ExcludeInternal(this.AsDerivedType, flags.AsDerivedType);
        }

        public T CombineWith(T flags)
        {
            return UnionInternal(this.AsDerivedType, flags);
        }

        public Int32Flags<T> CombineWith(Int32Flags<T> flags)
        {
            return UnionInternal(this.AsDerivedType, flags.AsDerivedType);
        }

        public bool ContainsAny(T flags)
        {
            return ContainsAny(this.AsDerivedType, flags);
        }

        public bool ContainsAny(Int32Flags<T> flags)
        {
            return ContainsAny(this.AsDerivedType, flags.AsDerivedType);
        }

        public bool ContainsAll(T flags)
        {
            return ContainsAll(this.AsDerivedType, flags);
        }

        // same as HasFlags
        public bool ContainsAll(Int32Flags<T> flags)
        {
            return ContainsAll(this.AsDerivedType, flags.AsDerivedType);
        }

        public static T operator &(Int32Flags<T> a, Int32Flags<T> b)
        {
            return IntersectInternal(a.AsDerivedType, b.AsDerivedType);
        }

        public static T operator |(Int32Flags<T> a, Int32Flags<T> b)
        {
            return UnionInternal(a.AsDerivedType, b.AsDerivedType);
        }

        public static T operator ^(Int32Flags<T> a, Int32Flags<T> b)
        {
            return ExclusiveUnion(a.AsDerivedType, b.AsDerivedType);
        }

        public static T operator ~(Int32Flags<T> flags)
        {
            return Invert(flags.AsDerivedType);
        }

        public bool HasAnyFlag(Int32Flags<T> other)
        {
            return (this & other).Value > 0;
        }

        public override bool HasFlag(Int32Flags<T> other)
        {
            return (this & other) == other;
        }

        // To consider:
        /* The following operators can be used on values of enum types but these can't be defined at the base level
            <, >, <=, >= (12.12.6)
            ++, -- (12.8.15 and 12.9.6)
         */

        /*
            bool operator <(E x, E y);
            bool operator >(E x, E y);
            bool operator <=(E x, E y);
            bool operator >=(E x, E y);
         */
    }
}