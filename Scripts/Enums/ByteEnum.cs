// Copyright Isto Inc.
using System;
using System.Collections.ObjectModel;
using System.Linq;

namespace Isto.Core.Enums
{
    /// <summary>
    /// Represents an enumeration who's values are stored as Byte, so the possible values go from 0 to 255.
    /// The generic definition means each ByteEnum variation will be tracked separately where CoreEnum is concerned.
    /// (I don't want all CoreEnum of ByteEnum to be aggregating their caches)
    /// </summary>
    /// <typeparam name="T">A type that derives from ByteEnum</typeparam>
    public abstract class ByteEnum<T> : ExpandableEnum<ByteEnum<T>, Byte> where T : ByteEnum<T>
    {
        public ByteEnum() : base()
        {
        }

        // Generics are generated at compile time so this should mean that there is one of this static per T
        // When declaring SubEnum : BaseEnum, as long as a SubEnum is being picked up as a ByteEnum<BaseEnum>,
        // this static should work (tracking a counter per enum and all its subtypes).
        // Note: generics of reference types are optimized in a weird way to avoid generating too many declarations...
        // but we should be able to think of it in the same way as if this was not the case.
        private static Byte highestRatingValue = 0;

        /// <summary>
        /// Auto-value constructor. Default first value is 1.
        /// I am not 100% sure about this, for now I make sure the new value is simply bigger than any existing one.
        /// We could instead look for the smallest available value, but I think it doesn't matter, and this is easier.
        /// </summary>
        /// <param name="name">The name of your Enum item.</param>
        protected ByteEnum(String name) : base(++highestRatingValue, name)
        {
        }

        /// <summary>
        /// Specific value constructor
        /// </summary>
        /// <param name="value">The internal byte value of your Enum item</param>
        /// <param name="name">The name of your Enum item.</param>
        protected ByteEnum(Byte value, String name) : base(value, name)
        {
            if (value > highestRatingValue)
            {
                highestRatingValue = value;
            }
        }

        /// <summary>
        /// Finds all enumeration elements declared as type T.
        /// Ignores declatations from parent or derived types.
        /// </summary>
        /// <param name="t">The exact type who's values you want to see.</param>
        /// <returns>A collection of the possible values for this exact type.</returns>
        public static new ReadOnlyCollection<T> GetStrictValues(Type t)
        {
            ReadOnlyCollection<T> values = ExpandableEnum<ByteEnum<T>, Byte>.GetStrictValues(t).Cast<T>().ToList().AsReadOnly();
            return values;
        }

        public static T GetByValue(Byte value)
        {
            return GetFromValue(value) as T;
        }

        public static T GetByName(String name)
        {
            return GetFromName(name) as T;
        }

        // To consider:
        /* The following operators can be used on values of enum types but these can't be defined at the base level
            <, >, <=, >= (12.12.6)
            ++, -- (12.8.15 and 12.9.6)
         */

        /*
            bool operator <(E x, E y);
            bool operator >(E x, E y);
            bool operator <=(E x, E y);
            bool operator >=(E x, E y);
         */
    }
}