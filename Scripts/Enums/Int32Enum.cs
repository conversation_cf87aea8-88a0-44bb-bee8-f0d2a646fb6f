// Copyright Isto Inc.
using System;
using System.Collections.ObjectModel;
using System.Linq;

namespace Isto.Core.Enums
{
    /// <summary>
    /// Represents an enumeration who's values are stored as Int32.
    /// The generic definition means each Int32Enum variation will be tracked separately where CoreEnum is concerned.
    /// (I don't want all CoreEnum of Int32Enum to be aggregating their caches)
    /// </summary>
    /// <typeparam name="T">A type that derives from Int32Enum</typeparam>
    public abstract class Int32Enum<T> : ExpandableEnum<Int32Enum<T>, Int32> where T : Int32Enum<T>
    {
        public Int32Enum() : base()
        {
        }

        // Generics are generated at compile time so this should mean that there is one of this static per T
        // When declaring SubEnum : BaseEnum, as long as a SubEnum is being picked up as a Int32Enum<BaseEnum>,
        // this static should work (tracking a counter per enum and all its subtypes).
        // Note: generics of reference types are optimized in a weird way to avoid generating too many declarations...
        // but we should be able to think of it in the same way as if this was not the case.
        private static Int32 highestRatingValue = -1;

        /// <summary>
        /// Auto-value constructor. Default first value is 0.
        /// I am not 100% sure about this, for now I make sure the new value is simply bigger than any existing one.
        /// We could instead look for the smallest available value, but I think it doesn't matter, and this is easier.
        /// </summary>
        /// <param name="name">The name of your Enum item.</param>
        protected Int32Enum(string name) : base(++highestRatingValue, name)
        {
        }

        /// <summary>
        /// Specific value constructor
        /// </summary>
        /// <param name="value">The internal integer value of your Enum item</param>
        /// <param name="name">The name of your Enum item.</param>
        protected Int32Enum(int value, string name) : base(value, name)
        {
            if (value > highestRatingValue)
            {
                highestRatingValue = value;
            }
        }

        /// <summary>
        /// Finds all enumeration elements declared as type T.
        /// Ignores declatations from parent or derived types.
        /// </summary>
        /// <param name="t">The exact type who's values you want to see.</param>
        /// <returns>A collection of the possible values for this exact type.</returns>
        public static new ReadOnlyCollection<T> GetStrictValues(Type t)
        {
            ReadOnlyCollection<T> values = ExpandableEnum<Int32Enum<T>, Int32>.GetStrictValues(t).Cast<T>().ToList().AsReadOnly();
            return values;
        }

        public static T GetByValue(int value)
        {
            return GetFromValue(value) as T;
        }

        public static T GetByName(string name)
        {
            return GetFromName(name) as T;
        }

        // To consider:
        /* The following operators can be used on values of enum types but these can't be defined at the base level
            <, >, <=, >= (12.12.6)
            ++, -- (12.8.15 and 12.9.6)
         */

        /*
            bool operator <(E x, E y);
            bool operator >(E x, E y);
            bool operator <=(E x, E y);
            bool operator >=(E x, E y);
         */
    }
}