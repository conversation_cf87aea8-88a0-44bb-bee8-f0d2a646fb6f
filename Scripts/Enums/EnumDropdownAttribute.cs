// Copyright Isto Inc.
using System;
using UnityEngine;

namespace Isto.Core.Enums
{
    /// <summary>
    /// This attribute is intended to decorate a field of a backing type for an ExpandableEnum.
    /// As of this writing only string and int are implemented, you will need to add extra handling logic if you want
    /// to use enumerations backed by other types.
    /// Warning:
    /// Do not attempt to serialize the enum instances themselves in unity! There is some code in EnumDropdownDrawer
    /// that is intended to support it, but as of this writing, having those instances deserialized and generated by
    /// unity's hoodoo causes side effects of extra values in the enum registry that I'm not able to prevent.
    /// Instead of trying to clean that up we decided to avoid that technique for now.
    /// </summary>
    public class EnumDropdownAttribute : PropertyAttribute
    {
        public Type type;
        public EnumDropdownAttribute(Type t)
        {
            type = t;
        }
    }
}