// Copyright Isto Inc.
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using UnityEngine;

namespace Isto.Core.Enums
{
    /// <summary>
    /// This static class allows us to put some global ExpandableEnum functions to iterate on ExpandableEnum types
    /// or do other high level operations.
    ///
    /// We do this because generics are a bit of a hassle for this; we want some static utility methods to exist
    /// for dealing with our ExpandableEnum<,> types but we don't want all of these static methods to necessarily
    /// be generic or be copied into all those unpacked type declarations.
    /// </summary>
    public static class ExpandableEnum
    {
        // Root type so we can inherit Object -> ReferenceTypeEnum instead of Object -> ValueType -> Enum
        // Also used as a "tag" to be able to collect all types deriving from CoreEnum<T, U>
        public abstract class ReferenceTypeEnum
        {
        }

        // Only used as a way to point to some symbols despite the generic type, see ENUM_VALUE_PROPERTY_NAME
        private abstract class DummyEnum : ExpandableEnum<DummyEnum, int>
        {
        }


        // OTHER FIELDS

        public static readonly HashSet<Type> ALL_TYPES = new HashSet<Type>();
        public static readonly Dictionary<Type, List<object>> ALL_VALUES = new Dictionary<Type, List<object>>();

        private static readonly Type REFERENCE_ENUM_TYPE = typeof(ReferenceTypeEnum);
        // forced reference using DummyEnum so I don't have to rely on a string
        private static readonly string ENUM_VALUE_PROPERTY_NAME = nameof(DummyEnum.Value);
        private static readonly string ENUM_NAME_PROPERTY_NAME = nameof(DummyEnum.Name);


        // LIFECYCLE EVENTS

        /// <summary>
        /// This constructs ExpandableEnum's static values and in turn forces initialization of all declared
        /// ExpandableEnum<,> types since we rely on them to work with us.
        /// </summary>
        static ExpandableEnum()
        {
            Debug.Log("[ExpandableEnum] Initialization of all ReferenceTypeEnum");

            IEnumerable<Type> enumTypes = from assembly in AppDomain.CurrentDomain.GetAssemblies()
                                          from type in assembly.GetTypes()
                                          where REFERENCE_ENUM_TYPE.IsAssignableFrom(type) && !type.IsAbstract
                                          select type;

            foreach (Type t in enumTypes)
            {
                ALL_TYPES.Add(t);
                System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(t.TypeHandle);
            }
        }


        // ACCESSORS

        /// <summary>
        /// Finds all enumeration elements declared in Type as well as those of all parent enumerations
        /// so you can iterate on the full list. Notably this scope excludes any possible additional
        /// elements declared in subtypes or other child types of your inherited types.
        /// </summary>
        /// <param name="type">A ReferenceTypeEnum subtype.</param>
        /// <returns>A collection of the possible values for this type.</returns>
        public static ReadOnlyCollection<object> GetValuesAndDependencies(Type type)
        {
            List<object> values = new List<object>();

            Type currentType = type;
            while (currentType != REFERENCE_ENUM_TYPE)
            {
                values.AddRange(GetStrictValues(currentType));
                currentType = currentType.BaseType;
            }

            // We sort the values because this set of types is very controlled and each type is intended to complement
            // its parent type. Any values "in the middle" would likely be intended to be understood as such.
            // If this is wrong, feel free to delete this line and let the invoking code do the sorting itself where
            // required.
            values = values.OrderBy(x => GetBackingValue(x)).ToList();

            return values.AsReadOnly();
        }

        /// <summary>
        /// Finds all ReferenceTypeEnum declaration types that could be assigned to provided type or that provided type
        /// could be assigned to, and returns them; in other words it is a list of values an enum variable of that type
        /// could have, even if some of them might not be logical in your context.
        /// </summary>
        /// <param name="type">A ReferenceTypeEnum subtype.</param>
        /// <returns>The list of ReferenceTypeEnum possible values.</returns>
        public static ReadOnlyCollection<object> GetAllCompatibleValues(Type type)
        {
            List<object> values = new List<object>();

            IEnumerable<Type> compatibleTypes = from otherType in ALL_TYPES
                                                where type.IsAssignableFrom(otherType) || otherType.IsAssignableFrom(type)
                                                select otherType;

            foreach (Type t in compatibleTypes)
            {
                values.AddRange(GetStrictValues(t));
            }

            // Don't sort these values here. There could be types in the list that are not necessarily meant to work
            // together. Keeping the values grouped by type might make more sense for a user that looks through these
            // values. Otherwise the invoking code can easily sort them itself if needed.

            return values.AsReadOnly();
        }

        /// <summary>
        /// Finds all enumeration elements declared as type (which should all be inside the declaration of that type).
        /// Notably this scope exclude any possible additional elements declared in parent types or child types.
        /// </summary>
        /// <param name="type">A ReferenceTypeEnum subtype.</param>
        /// <returns>A collection of the possible values for this type.</returns>
        public static ReadOnlyCollection<object> GetStrictValues(Type type)
        {
            List<object> values = new List<object>();

            if (ALL_VALUES.ContainsKey(type))
            {
                foreach (object value in ALL_VALUES[type])
                {
                    values.Add(value);
                }
            }

            return values.AsReadOnly();
        }

        /// <summary>
        /// Finds the most appropriate default value for the provided enumType, which we deem to be the the first
        /// declaration in the file, which we should have as the first one in our list of values.
        /// </summary>
        /// <param name="enumType">A ReferenceTypeEnum subtype.</param>
        /// <returns>The default value of enumType, if it exists, null otherwise.</returns>
        public static object GetDefaultValue(Type enumType)
        {
            object value = null;

            if (ALL_VALUES.ContainsKey(enumType) && ALL_VALUES[enumType].Count > 0)
            {
                value = ALL_VALUES[enumType][0];
            }

            return value;
        }

        /// <summary>
        /// Gets the correct Enum symbol representation from its inner value representation.
        /// </summary>
        /// <param name="enumType">A ReferenceTypeEnum subtype.</param>
        /// <param name="value">The inner value of the Enum item.</param>
        /// <returns>The matching Enum entry, or null.</returns>
        public static object GetFromValueAndType(object value, Type enumType)
        {
            object enumValue = null;
            if (ALL_VALUES.ContainsKey(enumType))
            {
                ReadOnlyCollection<object> relevantValues = GetValuesAndDependencies(enumType);
                IEnumerator<object> enumerator = relevantValues.GetEnumerator();
                while (enumerator.MoveNext())
                {
                    object entry = enumerator.Current;
                    object entryValue = GetBackingValue(entry);
                    if (entryValue.Equals(value))
                    {
                        enumValue = entry;
                        break;
                    }
                }
            }
            return enumValue;
        }

        /// <summary>
        /// Gets the correct Enum symbol representation from its inner value representation.
        /// </summary>
        /// <param name="enumType">A ReferenceTypeEnum subtype.</param>
        /// <param name="value">The inner value of the Enum item.</param>
        /// <returns>The matching Enum entry, or null.</returns>
        public static object GetFromValueAndStrictType(object value, Type enumType)
        {
            object enumValue = null;
            if (ALL_VALUES.ContainsKey(enumType))
            {
                ReadOnlyCollection<object> relevantValues = GetStrictValues(enumType);
                IEnumerator<object> enumerator = relevantValues.GetEnumerator();
                while (enumerator.MoveNext())
                {
                    object entry = enumerator.Current;
                    object entryValue = GetBackingValue(entry);
                    if (entryValue.Equals(value))
                    {
                        enumValue = entry;
                        break;
                    }
                }
            }
            return enumValue;
        }

        /// <summary>
        /// Gets the correct Enum symbol representation from its inner value representation.
        /// </summary>
        /// <param name="enumType">A ReferenceTypeEnum subtype.</param>
        /// <param name="value">The inner value of the Enum item.</param>
        /// <returns>The matching Enum entry, or null.</returns>
        public static object GetFromValueAndCompatibleType(object value, Type enumType)
        {
            object enumValue = null;
            if (ALL_VALUES.ContainsKey(enumType))
            {
                ReadOnlyCollection<object> relevantValues = GetAllCompatibleValues(enumType);
                IEnumerator<object> enumerator = relevantValues.GetEnumerator();
                while (enumerator.MoveNext())
                {
                    object entry = enumerator.Current;
                    object entryValue = GetBackingValue(entry);
                    if (entryValue.Equals(value))
                    {
                        enumValue = entry;
                        break;
                    }
                }
            }
            return enumValue;
        }

        /// <summary>
        /// Gets the correct Enum symbol representation from its name representation.
        /// </summary>
        /// <param name="enumValueName">The name of the Enum item.</param>
        /// <param name="enumType">A ReferenceTypeEnum subtype.</param>
        /// <returns>The matching Enum entry, or null.</returns>
        public static object GetFromNameAndType(string enumValueName, Type enumType)
        {
            object enumValue = null;

            if (ALL_VALUES.ContainsKey(enumType))
            {
                ReadOnlyCollection<object> relevantValues = GetValuesAndDependencies(enumType);
                IEnumerator<object> enumerator = relevantValues.GetEnumerator();
                while (enumerator.MoveNext())
                {
                    object entry = enumerator.Current;
                    string entryName = GetName(entry);
                    if (entryName.Equals(enumValueName))
                    {
                        enumValue = entry;
                        break;
                    }
                }
            }

            return enumValue;
        }


        /// <summary>
        /// Gets the correct Enum symbol representation from its name representation.
        /// </summary>
        /// <param name="enumValueName">The name of the Enum item.</param>
        /// <param name="enumType">A ReferenceTypeEnum subtype.</param>
        /// <returns>The matching Enum entry, or null.</returns>
        public static object GetFromNameAndStrictType(string enumValueName, Type enumType)
        {
            object enumValue = null;

            if (ALL_VALUES.ContainsKey(enumType))
            {
                ReadOnlyCollection<object> relevantValues = GetStrictValues(enumType);
                IEnumerator<object> enumerator = relevantValues.GetEnumerator();
                while (enumerator.MoveNext())
                {
                    object entry = enumerator.Current;
                    string entryName = GetName(entry);
                    if (entryName.Equals(enumValueName))
                    {
                        enumValue = entry;
                        break;
                    }
                }
            }

            return enumValue;
        }


        /// <summary>
        /// Gets the correct Enum symbol representation from its name representation.
        /// </summary>
        /// <param name="enumValueName">The name of the Enum item.</param>
        /// <param name="enumType">A ReferenceTypeEnum subtype.</param>
        /// <returns>The matching Enum entry, or null.</returns>
        public static object GetFromNameAndCompatibleType(string enumValueName, Type enumType)
        {
            object enumValue = null;

            if (ALL_VALUES.ContainsKey(enumType))
            {
                ReadOnlyCollection<object> relevantValues = GetAllCompatibleValues(enumType);
                IEnumerator<object> enumerator = relevantValues.GetEnumerator();
                while (enumerator.MoveNext())
                {
                    object entry = enumerator.Current;
                    string entryName = GetName(entry);
                    if (entryName.Equals(enumValueName))
                    {
                        enumValue = entry;
                        break;
                    }
                }
            }

            return enumValue;
        }

        /// <summary>
        /// This could be anything
        /// </summary>
        /// <param name="enumValueName"></param>
        /// <returns></returns>
        public static object GetFromName(string enumValueName)
        {
            object enumValue = null;

            var result = from v in ALL_VALUES
                        where v.Value.Where(x => GetName(x).Equals(enumValueName)).Count() > 0
                        select v.Value.Where(x => GetName(x).Equals(enumValueName)).FirstOrDefault();

            enumValue = result.FirstOrDefault();

            return enumValue;
        }

        /// <summary>
        /// Looks if a declaration exists of type exactly enumType and considered equal to value.
        /// Note that the ExpandableEnum overrides equality comparison and only checks for equality on backing value.
        /// </summary>
        /// <param name="enumType">A ReferenceTypeEnum subtype.</param>
        /// <param name="value">An object that uniquely represents an enum declaration.</param>
        /// <returns>true if the declaration exists</returns>
        public static bool IsDefined(Type enumType, object value)
        {
            return ALL_VALUES.ContainsKey(enumType) && ALL_VALUES[enumType].Contains(value);
        }

        /// <summary>
        /// Looks if a known declaration exists of type exactly TEnum that is considered equal to value.
        /// Note that the ExpandableEnum overrides equality comparison and only checks for equality on backing value.
        /// </summary>
        /// <typeparam name="TEnum">A ReferenceTypeEnum subtype.</typeparam>
        /// <param name="value">an instance of an enum declaration</param>
        /// <returns>true if the declaration exists</returns>
        public static bool IsDefined<TEnum>(TEnum value) where TEnum : ReferenceTypeEnum
        {
            return IsDefined(typeof(TEnum), value);
        }

        /// <summary>
        /// Gets the name of the enum item that has the correct inner value representation.
        /// </summary>
        /// <param name="enumType">A ReferenceTypeEnum subtype.</param>
        /// <param name="value">The underlying backing type value of the enum item.</param>
        /// <returns>The enum item's name if it exists, null otherwise.</returns>
        public static string GetName(Type enumType, object value)
        {
            string name = null;

            if (ALL_VALUES.ContainsKey(enumType))
            {
                ReadOnlyCollection<object> relevantValues = GetValuesAndDependencies(enumType);
                IEnumerator<object> enumerator = relevantValues.GetEnumerator();
                while(enumerator.MoveNext())
                {
                    object entry = enumerator.Current;
                    object entryValue = GetBackingValue(entry);
                    if (entryValue.Equals(value))
                    {
                        name = GetName(entry);
                        break;
                    }
                }
            }

            return name;
        }

        /// <summary>
        /// Gets the name of the enum item that has the correct inner value representation.
        /// Ignores possible values from parent or derived types.
        /// </summary>
        /// <param name="enumType">A ReferenceTypeEnum subtype.</param>
        /// <param name="value">The underlying type value of the enum item.</param>
        /// <returns>The enum item's name if it exists, null otherwise.</returns>
        public static string GetNameStrictly(Type enumType, object value)
        {
            string name = null;

            if (ALL_VALUES.ContainsKey(enumType))
            {
                name = ALL_VALUES[enumType].Where(x => GetBackingValue(x).Equals(value)).Select(x => GetName(x)).FirstOrDefault();
            }

            return name;
        }

        /// <summary>
        /// Gets the name of the first enum item that has the correct inner value representation among those that could
        /// be assigned to a variable of enumType.
        /// </summary>
        /// <param name="enumType">A ReferenceTypeEnum subtype.</param>
        /// <param name="value">The underlying type value of the enum item.</param>
        /// <returns>The enum item's name if it exists, null otherwise.</returns>
        public static string GetNameFromAllCompatibleValues(Type enumType, object value)
        {
            string name = null;

            IEnumerable<Type> compatibleTypes = from otherType in ALL_TYPES
                                                where enumType.IsAssignableFrom(otherType)
                                                select otherType;


            foreach (Type type in compatibleTypes)
            {
                ReadOnlyCollection<object> compatibleValues = GetStrictValues(type);
                IEnumerator<object> enumerator = compatibleValues.GetEnumerator();
                while (enumerator.MoveNext())
                {
                    object entry = enumerator.Current;
                    object entryValue = GetBackingValue(entry);
                    if (entryValue.Equals(value))
                    {
                        name = GetName(entry);
                        break;
                    }
                }
            }

            return name;
        }

        /// <summary>
        /// Gets the name of the enum item that has the correct inner value representation.
        /// </summary>
        /// <typeparam name="TEnum">A ReferenceTypeEnum subtype.</typeparam>
        /// <param name="value">The underlying type value of the enum item.</param>
        /// <returns>The enum item's name if it exists, null otherwise.</returns>
        public static string GetName<TEnum>(TEnum value) where TEnum : ReferenceTypeEnum
        {
            return GetName(typeof(TEnum), GetBackingValue(value));
        }

        /// <summary>
        /// Gets the names of the values defined for this enum type.
        /// </summary>
        /// <param name="enumType">A ReferenceTypeEnum subtype.</param>
        /// <returns>The list of names (in order of value)</returns>
        public static string[] GetNames(Type enumType)
        {
            string[] names = GetValuesAndDependencies(enumType).Select(x => GetName(x)).ToArray();
            return names;
        }

        /// <summary>
        /// Gets the names of the values defined for this enum type.
        /// </summary>
        /// <typeparam name="TEnum">A ReferenceTypeEnum subtype.</typeparam>
        /// <returns>The list of names (in order of value)</returns>
        public static string[] GetNames<TEnum>() where TEnum : ReferenceTypeEnum
        {
            return GetNames(typeof(TEnum));
        }

        /// <param name="enumDefinition">A ReferenceTypeEnum item definition</param>
        /// <returns>The value from the definition</returns>
        public static object GetBackingValue(object enumDefinition)
        {
            Type enumType = enumDefinition.GetType();
            object definitionValue = enumType.GetProperty(ENUM_VALUE_PROPERTY_NAME).GetValue(enumDefinition);
            return definitionValue;
        }

        /// <param name="enumDefinition">A ReferenceTypeEnum item definition</param>
        /// <returns>The name of the definition</returns>
        private static string GetName(object enumDefinition)
        {
            Type enumType = enumDefinition.GetType();
            object definitionValue = enumType.GetProperty(ENUM_NAME_PROPERTY_NAME).GetValue(enumDefinition);
            return definitionValue as string;
        }

        // OTHER METHODS

        public static void LogAllTypes()
        {
            Debug.Log($"All known ExpandableEnum types:");
            foreach (Type type in ALL_TYPES)
            {
                Debug.Log($"{type}");
            }
        }

        public static void LogStrictValues(Type type)
        {
            Debug.Log($"All ExpandableEnum values where type is strictly {type}:");
            foreach (object value in GetStrictValues(type))
            {
                Debug.Log($"{value}");
            }
        }

        public static void LogValuesAndDependencies(Type type)
        {
            Debug.Log($"All ExpandableEnum values (including parent values) for type {type}:");
            foreach (object value in GetValuesAndDependencies(type))
            {
                Debug.Log($"{value}");
            }
        }
    }

    /// <summary>
    /// This class is meant to be used as a more powerful version of System.Enum
    /// Part of this code was based on https://www.codeproject.com/Articles/20805/Enhancing-C-Enums
    /// Drawbacks are that the definition is a little bit more clunky, and you cannot switch on these symbols
    /// Upsides are:
    /// we can control how they serialize themselves
    /// we can use inheritance to add values to an existing type
    /// we can use inheritance to pass a specialized enum to a class that doesn't need to care about the details
    /// we can use our enum types as better type constraints in generics
    /// we can add just about any feature we need
    /// TBD: we  might be able to develop a system to select one among many types of enum in the inspector
    /// </summary>
    /// <typeparam name="T">Your enum type</typeparam>
    /// <typeparam name="U">The underlying value type</typeparam>
    public abstract class ExpandableEnum<T, U> : ExpandableEnum.ReferenceTypeEnum, ISerializationCallbackReceiver
                                       where T : ExpandableEnum<T, U>
    {
        private static List<T> _values = new List<T>();

        protected static List<T> Values => _values;

        [SerializeField]
        private U _value;

        [SerializeField]
        private string _name;

        /// <summary>
        /// Value is the internal value of the enum item. This is how we cast or (eventually) serialize the enum.
        /// </summary>
        public U Value => _value;

        /// <summary>
        /// Name is the human readable name of the enum item.
        /// </summary>
        public string Name => _name;

        /// <summary>
        /// This constructs our specific type's static values and in turn forces initialization of the ExpandableEnum
        /// static class since we rely on it to work with us.
        /// </summary>
        static ExpandableEnum()
        {
            System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(ExpandableEnum).TypeHandle);
        }

        /// <summary>
        /// Constructor for creating enums at runtime when using operations.
        /// (e.g. when manipulating data, and then finding if it has a defined type)
        /// </summary>
        public ExpandableEnum()
        {
            // value intended to be set later
        }

        /// <summary>
        /// Constructor for defining the list of valid enum values in your enum class.
        /// </summary>
        /// <param name="value"></param>
        /// <param name="name"></param>
        protected ExpandableEnum(U value, string name)
        {
            SetValue(value, name);
            RegisterType(); // type first because value registration assumes that type already exists
            RegisterValue();
        }

        protected void RegisterType()
        {
            Type derivedType = this.GetType();
            ExpandableEnum.ALL_TYPES.Add(derivedType);
            if (!ExpandableEnum.ALL_VALUES.ContainsKey(derivedType))
            {
                ExpandableEnum.ALL_VALUES.Add(derivedType, new List<object>());
            }
        }

        protected void RegisterValue()
        {
            Values.Add((T)this);
            ExpandableEnum.ALL_VALUES[this.GetType()].Add(this);
        }

        public void SetValue(U value, string name)
        {
            //Debug.Log($"ExpandableEnum value set to {value} : {name}");
            this._value = value;
            this._name = name;
        }

        /// <summary>
        /// Finds all enumeration elements declared as type T (or any subtypes).
        /// Notably this will include any 'sibling' subclasses of T who are otherwise unrelated.
        /// Beware of using this to iterate on values, as it usually won't be what you would want to use.
        /// Loog for alternative in the ExpandableEnum static class rather than the generic ExpandableEnum<,> class.
        /// </summary>
        /// <returns>A collection of the possible values for this type.</returns>
        public static ReadOnlyCollection<T> GetValues()
        {
            return GetAllValues();
        }

        /// <summary>
        /// Finds all enumeration elements declared as type T (or any subtypes).
        /// Notably this will include any 'sibling' subclasses of T who are otherwise unrelated.
        /// </summary>
        /// <returns>A collection of the possible values for this type.</returns>
        public static ReadOnlyCollection<T> GetAllValues()
        {
            return Values.AsReadOnly();
        }

        public static ReadOnlyCollection<string> GetAllNames()
        {
            return Values.Select(x => x.Name).ToList().AsReadOnly();
        }

        /// <summary>
        /// Finds all enumeration elements declared as type T.
        /// Ignores declatations from parent or derived types.
        /// </summary>
        /// <param name="t">The exact type who's values you want to see.</param>
        /// <returns>A collection of the possible values for this exact type.</returns>
        public static ReadOnlyCollection<T> GetStrictValues(Type t)
        {
            ReadOnlyCollection<object> values = ExpandableEnum.GetStrictValues(t);
            ReadOnlyCollection<T> typedValues = values.Cast<T>().ToList().AsReadOnly();
            return typedValues;
        }

        public static void LogAllValues()
        {
            Type loggedType = typeof(T);
            Debug.Log($"All {loggedType} values (including extended values):");
            ReadOnlyCollection<T> list = GetAllValues();
            foreach (T item in list)
            {
                Debug.Log($"{item}");
            }
        }

        /// <summary>
        /// Gets the correct Enum symbol representation from its inner value representation.
        /// </summary>
        /// <param name="value">The inner value of the Enum.</param>
        /// <returns>The matching Enum entry, or null.</returns>
        public static T GetFromValue(U value)
        {
            foreach (T definition in Values)
            {
                if (definition.Value.Equals(value))
                    return definition;
            }
            return null;
        }

        /// <summary>
        /// Gets the correct Enum symbol representation from its string representation.
        /// </summary>
        /// <param name="enumValueName">The string representation of the Enum.</param>
        /// <returns>The matching Enum entry, or null.</returns>
        public static T GetFromName(string enumValueName, StringComparison comparisonType = StringComparison.Ordinal)
        {
            foreach (T definition in Values)
            {
                if (String.Equals(definition.Name, enumValueName, comparisonType))
                {
                    return definition;
                }
            }
            return null;
        }

        /// <summary>
        /// Tries to find an enum value declaration that matches the given value witin all known values of type T.
        /// Looks at the enum's name first, and if that fails, tries to check if the backing value is a string.
        /// Won't try to parse the string parameter as an integer (as opposed to System.Enum.TryParse! I think)
        /// </summary>
        /// <param name="value">The string to be parsed into an ExpandableEnum.</param>
        /// <param name="ignoreCase">True if you want the letter case to be ignored when comparing strings.</param>
        /// <param name="result">The Enum value found, null otherwise.</param>
        /// <returns>True if we managed to match the string value to an enum declaration.</returns>
        public static bool TryParse(string value, bool ignoreCase, out T result)
        {
            result = null;
            StringComparison comparisonSetting = ignoreCase ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal;

            ReadOnlyCollection <T> allValues = GetAllValues();

            foreach (T enumDeclaration in allValues)
            {
                if (string.Equals(enumDeclaration.Name, value, comparisonSetting))
                {
                    result = enumDeclaration;
                    break;
                }
            }

            if (result == null)
            {
                string declarationStringValue;
                foreach (T enumDeclaration in allValues)
                {
                    declarationStringValue = enumDeclaration.Value as string;
                    if (string.Equals(declarationStringValue, value, comparisonSetting))
                    {
                        result = enumDeclaration;
                        break;
                    }
                }
            }

            return result != null;
        }

        /// <summary>
        /// Converts the enum value to a human readable representation of it.
        /// This is not guaranteed to be unique.
        /// </summary>
        /// <returns>The name of the enum value.</returns>
        public override string ToString()
        {
            return Name;
        }

        /// <summary>
        /// Evaluates if two ExpandableEnum instances are equal.
        /// Relies on our custom implementation of the == operator, which in turn relies on backing value comparison.
        /// (we don't compare references)
        /// </summary>
        /// <param name="other"></param>
        /// <returns>True if the two values are equivalent</returns>
        public override bool Equals(object other)
        {
            return other is ExpandableEnum<T, U> otherEnum && this == otherEnum;
        }

        // Normally an enum is mostly a fancy representation of a value, and can be cast to/from a value type
        // without problems. I don't know if the hashcode from an enum also behaves like this, but it made sense to me.
        public override int GetHashCode()
        {
            return Value.GetHashCode();
        }

        /// <summary>
        /// Note that for serialization support you also need to decorate each intended type with [Serializable].
        /// Warning: don't use unity serialization for ExpandableEnum fields. It works but has side effects.
        /// </summary>
        public void OnBeforeSerialize()
        {
        }

        /// <summary>
        /// Note that for serialization support you also need to decorate each intended type with [Serializable].
        /// Warning: don't use unity serialization for ExpandableEnum fields. It works but has side effects.
        /// </summary>
        public void OnAfterDeserialize()
        {
            // The original idea was to serialize only the backing value but it might be worth putting the name in
            // there as well in order to have more info when we deserialize old values. Either way this makes sure the
            // name is up to date. If we want to report certain unexpected name changes for instance, it would have to
            // be done before this.
            this._name = ExpandableEnum.GetName(this.GetType(), Value);
        }

        /// <summary>
        /// Checks if the operands are equivalent by comparing their underlying backing types.
        /// Note: Traditionnaly enums behave like value types. Here we are using reference types, but I thought we
        /// should mostly disguise these implementation details and let people use them like they normally use enums.
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns>True if the value of both enums are equal</returns>
        public static bool operator ==(ExpandableEnum<T, U> a, ExpandableEnum<T, U> b)
        {
            object oa = a;
            object ob = b;

            if (null == oa && null == ob)
                return true;

            if (null == oa)
                return false;

            if (null == ob)
                return false;

            return a.Value.Equals(b.Value);
        }

        /// <summary>
        /// Checks if the operands are different by comparing their underlying backing types.
        /// Note: Traditionnaly enums behave like value types. Here we are using reference types, but I thought we
        /// should mostly disguise these implementation details and let people use them like they normally use enums.
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns>True if the value of both enums are different</returns>
        public static bool operator !=(ExpandableEnum<T, U> a, ExpandableEnum<T, U> b)
        {
            object oa = a;
            object ob = b;

            if (null == oa && null == ob)
                return false;

            if (null == oa)
                return true;

            if (null == ob)
                return true;

            return !a.Value.Equals(b.Value);
        }

        // To consider (For ExpandableEnum or ExpandableEnum<,>):
        //public int CompareTo (object? target); // -x if (this < target), 0 if equal, +x if (this > target or target is null)
        //public TypeCode GetTypeCode (); // from underlying type
        //public static Type GetUnderlyingType (Type enumType); // return U
        //public static Array GetValuesAsUnderlyingType (Type enumType); // returns List<object> ?
        //public static Array GetValuesAsUnderlyingType<TEnum> () where TEnum : struct; // returns List<U> ?
        //public static object ToObject (Type enumType, short value); // from U to T ?
        //public static object Parse(Type enumType, string value); // decode string as either name or value!
        //public static bool TryParse (Type enumType, string? value, out object? result);
        //public static bool TryParse (Type enumType, string? value, bool ignoreCase, out object? result);
        //public static bool TryParse<TEnum> (string? value, out TEnum result) where TEnum : struct;
        //public static bool TryParse<TEnum> (string? value, bool ignoreCase, out TEnum result) where TEnum : struct;
    }
}