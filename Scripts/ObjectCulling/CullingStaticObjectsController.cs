// Copyright Isto Inc.
using Isto.Core.Automation;
using Isto.Core.Items;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UPool;

namespace Isto.Core.ObjectCulling
{
    public class CullingStaticObjectsController
    {
        // Public Properties

        public CullingGroup CullingGroup { get { return _cullingGroup; } }
        public int CullingAreaGridDiameter { get; private set; }
        public BoundingSphere[] Spheres { get { return _spheres; } }

        // Private Variables

        private CullingGroup _cullingGroup;
        private BoundingSphere[] _spheres;
        private int _cullingSpheresRowLength;
        private Vector3 _bottomCorner;

        private int _cullingSpherePadding = 3;              // Extra radius given to culling sphere to trigger onscreen early enough to load items in for area

        private Dictionary<int, List<GameObject>> _itemsInArea = new Dictionary<int, List<GameObject>>();

        // Constructor

        /// <summary>
        /// Initializes the culling group based on the passed in constructor parameters and registers the callback for visibility changed events for BoundingSpheres
        /// </summary>
        /// <param name="gridSpaceDiameterPerCullingArea">How wide each area covered by one BoundingSphere is in grid spaces (Size of 1)</param>
        /// <param name="cullingSpheresPerRow">How many BoundingSpheres per row of the CullingGroup.  Determines total number of bounding spheres.</param>
        /// <param name="bottomRightCorner">Bottom Right Corner of where the BoundingSpheres start in world coordinates</param>
        /// <param name="camera">Camera that the CullingGroup uses to determine on screen visibility</param>
        /// <param name="onStateChangedCallback">Callback for when the BoundingSpheres change visibility</param>
        public CullingStaticObjectsController(int gridSpaceDiameterPerCullingArea, int cullingSpheresPerRow, Vector3 bottomRightCorner, Camera camera, CullingGroup.StateChanged onStateChangedCallback)
        {
            _cullingGroup = new CullingGroup();
            _cullingGroup.targetCamera = camera;

            _cullingSpheresRowLength = cullingSpheresPerRow;

            if (gridSpaceDiameterPerCullingArea % 2 != 0)
            {
                Debug.LogWarning("Culling Grid Space Diameter should be an even number for proper functionality.  Rounding value up");
                CullingAreaGridDiameter = gridSpaceDiameterPerCullingArea + 1;
            }
            else
            {
                CullingAreaGridDiameter = gridSpaceDiameterPerCullingArea;
            }

            _bottomCorner = bottomRightCorner;

            _spheres = new BoundingSphere[_cullingSpheresRowLength * _cullingSpheresRowLength];

            float sphereRadius = CullingAreaGridDiameter / 2 + _cullingSpherePadding;

            for (int i = 0; i < _cullingSpheresRowLength; i++)
            {
                for (int j = 0; j < _cullingSpheresRowLength; j++)
                {
                    Vector3 position = _bottomCorner + (Vector3.right * i + Vector3.forward * j) * CullingAreaGridDiameter;
                    int index = (i * _cullingSpheresRowLength) + j;

                    _spheres[index].position = position;
                    _spheres[index].radius = sphereRadius;
                }
            }

            _cullingGroup.SetBoundingSpheres(_spheres);
            _cullingGroup.SetBoundingSphereCount(_cullingSpheresRowLength * _cullingSpheresRowLength);

            _cullingGroup.onStateChanged = onStateChangedCallback;
        }

        public bool IsSpaceVisible(Vector3 worldPosition)
        {
            int index = GetIndexForNearestBoundingSphere(worldPosition);
            return _cullingGroup.IsVisible(index);
        }

        public int GetIndexForNearestBoundingSphere(Vector3 worldPosition)
        {
            // Snap world position to the nearest culling sphere position
            int x = Mathf.RoundToInt((worldPosition.x + 0.1f) / CullingAreaGridDiameter) * CullingAreaGridDiameter;
            int z = Mathf.RoundToInt((worldPosition.z + 0.1f) / CullingAreaGridDiameter) * CullingAreaGridDiameter;

            // Translate to sphere position which is the grid position based on the total number of spheres and the bottom corner
            float sphereX = (x - _bottomCorner.x) / CullingAreaGridDiameter;
            float sphereZ = (z - _bottomCorner.z) / CullingAreaGridDiameter;

            return (int)(sphereX * _cullingSpheresRowLength + sphereZ);
        }

        private Vector3 GetPosition(GameObject go)
        {
            Vector3 pos;
            if (go.GetComponent<ISpecialPositioning>() is ISpecialPositioning item)
            {
                pos = item.GetAutomationPosition();
            }
            else
            {
                pos = go.transform.position;
            }
            return pos;
        }

        public void AddStaticDisplayObject(GameObject targetObject)
        {
            int index = GetIndexForNearestBoundingSphere(GetPosition(targetObject));

            if (_itemsInArea.ContainsKey(index))
            {
                _itemsInArea[index].Add(targetObject);
            }
            else
            {
                _itemsInArea.Add(index, new List<GameObject>() { targetObject });
            }
        }

        public void ReleaseStaticDisplayObject(GameObject targetObject)
        {
            int index = GetIndexForNearestBoundingSphere(GetPosition(targetObject));

            if (_itemsInArea.ContainsKey(index))
            {
                if (_itemsInArea[index].Remove(targetObject))
                {
                    if (targetObject.GetComponent<PoolableObject>() != null)
                    {
                        targetObject.GetComponent<PooledInteractableItem>().DestroyGameObject();
                    }
                    else if (!Addressables.ReleaseInstance(targetObject))
                    {
                        Debug.LogError($"CullingStaticObjectsController.ReleaseStaticDisplayObject called onto item {targetObject.name} but item is not an Addressable");
                        GameObject.Destroy(targetObject);
                    }
                }
                else
                {
                    Debug.LogWarning($"CullingStaticObjectsController.ReleaseStaticDisplayObject called onto item {targetObject.name} but item is not registered in the list");
                }
            }
            else
            {
                // This happened once in a while when I dismantled stuff in Creative.
                // Also saw it during certain automated tests before, but I am not sure if it still happens, and in that case it seems normal (because of test environment)
                // I am still trying to figure out if we have edge cases that are mishandled with item culling and I have no good repro.

                InteractableItem interactable = targetObject.GetComponent<InteractableItem>();
                string item = "Item: " + (interactable != null && interactable.itemPile.item != null ? interactable.itemPile.item.itemID : "");

                Debug.LogWarning($"CullingStaticObjectsController.ReleaseStaticDisplayObject called onto item {targetObject.name} but item's sphere index does not exist. Position: {targetObject.transform.position}. {item}, Index:{index}");
            }
        }

        public Vector3 GetPositionForBoundingSphereAtIndex(int index)
        {
            return _spheres[index].position;
        }

        public int GetGridPositionsInBoundingArea(int boundingSphereIndex, ref Vector3[] results)
        {
            int radius = CullingAreaGridDiameter / 2;

            Vector3 bottomCorner = _spheres[boundingSphereIndex].position - Vector3.right * radius - Vector3.forward * radius;

            for (int i = 0; i < CullingAreaGridDiameter; i++)
            {
                for (int j = 0; j < CullingAreaGridDiameter; j++)
                {
                    Vector3 position = bottomCorner + (Vector3.right * i + Vector3.forward * j);

                    results[i * CullingAreaGridDiameter + j] = position;
                }
            }

            return CullingAreaGridDiameter * CullingAreaGridDiameter;
        }

        public void ReleaseObjectsInBoundingArea(int boudingSphereIndex, AutomationSystem _automationSystem)
        {
            if (_itemsInArea.ContainsKey(boudingSphereIndex))
            {
                List<GameObject> items = _itemsInArea[boudingSphereIndex];
                GameObject currentItem;
                for (int i = 0; i < items.Count; i++)
                {
                    currentItem = items[i];
                    if (currentItem != null)
                    {
                        if (_automationSystem.DoesSpaceExist(GetPosition(currentItem)))
                        {
                            AutomationGridSpace space = _automationSystem.GetOrCreateGridSpace(GetPosition(currentItem));
                            space.ProcessorRendered = false;
                            space.ResourceRendered = false;
                        }

                        PooledInteractableItem pooled = currentItem.GetComponent<PooledInteractableItem>();
                        if (pooled != null)
                        {
                            pooled.DestroyGameObject();
                        }
                        else if (!Addressables.ReleaseInstance(currentItem))
                        {
                            Debug.LogWarning($"CullingStaticObjectsController.ReleaseStaticDisplayObject called onto item {currentItem.name} but item is not an Addressable");
                            GameObject.Destroy(currentItem);
                        }
                    }
                }

                _itemsInArea[boudingSphereIndex].Clear();
                _itemsInArea.Remove(boudingSphereIndex);
            }
        }
    }
}