// Copyright Isto Inc.

using Isto.Core.Beings;
using System.IO;
using System.Xml.Serialization;
using Zenject;

namespace Isto.Core.Data
{
    public class PlayerDataManager : DataManager
    {
        private const string FILE_NAME = "coreplayer";
        private const string FILE_PREFIX = "/" + FILE_NAME + "_";

        public override string FilePrefix => FILE_PREFIX;
        public override string BlobName => FILE_NAME;


        //Injected

        private PlayerManager _playerManager;

        [Inject]
        public void Inject(PlayerManager playerManager)
        {
            _playerManager = playerManager;
        }


        public override bool Validate(in TextReader reader, in object previousDataObject)
        {
            bool valid = false;

            PlayerSaveData previousData = previousDataObject as PlayerSaveData;
            PlayerSaveData loadedData = LoadXMLFile<PlayerSaveData>(reader);

            if (loadedData != null)
            {
                valid = previousData.ContentEquals(loadedData);
            }

            return valid;
        }

        public override bool Load(in TextReader reader)
        {
            bool success = false;
            PlayerSaveData playerData = LoadXMLFile<PlayerSaveData>(reader);

            if (playerData != null)
            {
                _playerManager.Player.SetPlayerPosition(playerData.position);

                success = true;
            }

            return success;
        }

        public override void Save(out object saveData)
        {
            // WARNING -  Not in use in GTW

            PlayerSaveData playerSave = new PlayerSaveData()
            {
                position = _playerManager.Player.GetPlayerPosition()
            };

            saveData = playerSave;
        }


        public override object GetSampleSaveData()
        {
            PlayerSaveData playerSave = new PlayerSaveData();
            return playerSave;
        }

        public override System.Type[] GetSaveExtraTypes()
        {
            return null;
        }

        public override void UpgradeSave(string targetVersion, GameStateSaveData metaData, TextReader reader, out XmlSerializer serializer,
            out object saveData)
        {
            serializer = null;
            saveData = null;
        }
    }
}