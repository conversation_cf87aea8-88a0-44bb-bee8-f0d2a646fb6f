// Copyright Isto Inc.
using System;
using System.Collections.Generic;
using System.Xml.Serialization;

namespace Isto.Core.Data
{
    [Serializable]
    [XmlRoot("GameStateData")]
    public class GameStateSaveData
    {
        public bool isAutoSave;
        public string saveSlotName;
        public string gameModeName; //InternalName! Need to map to actual game mode definition to find loc key if you want to display this
        public string gameVersion;
        public string internalBuildVersion;
        public float totalGameTimeSeconds;
        public DateTime saveDate;
        public List<DateTime> autoSaveDates; //Not in use as we're treating auto saves as separate normal slots at the moment
        public ThemeManagerData themeData;

        public override bool Equals(object obj)
        {
            GameStateSaveData other = obj as GameStateSaveData;

            if (other == null)
                return false;
            if (this.isAutoSave != other.isAutoSave)
                return false;
            if (!this.saveSlotName.IsEqualWithNullsAsEmpty(other.saveSlotName))
                return false;
            if (!this.gameModeName.IsEqualWithNullsAsEmpty(other.gameModeName))
                return false;
            if (!this.gameVersion.IsEqualWithNullsAsEmpty(other.gameVersion))
                return false;
            // Game time could get very high and 1s of epsilon should be very overkill but remains relatively
            // meaningless in the context of a saved game.
            if (!this.totalGameTimeSeconds.Approx(other.totalGameTimeSeconds, epsilon: 1f))
                return false;
            if (this.saveDate != other.saveDate)
                return false;

            // Not sure internalBuildVersion is used and/or means anything so I'm skipping it for safety
            // Also ignoring autoSaveDates because we're not using it and lists are a hassle

            return this.themeData.Equals(other.themeData);
        }

        public override int GetHashCode()
        {
            // ignore internalBuildVersion and autoSaveDates to be consistent with Equals
            return (isAutoSave, saveSlotName, gameModeName, gameVersion, totalGameTimeSeconds, saveDate, themeData).GetHashCode();
        }
    }
}
