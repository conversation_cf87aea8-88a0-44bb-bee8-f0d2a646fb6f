// Copyright Isto Inc.
using System.IO;
using System.Xml.Serialization;

namespace Isto.Core.Data
{
    /// <summary>
    /// Interface for any data manager that will save and load data for a particular data set.
    /// </summary>
	public interface IDataManager
    {
        /// <summary>
        /// Grabs all progress data this manager is responsible for tracking and prepares a serializer for it.
        /// </summary>
        void Save(out object saveData);

        /// <summary>
        /// Grabs the serialized progress data this manager is responsible for, and applies it to the game. Returns true
        /// if the process happened without issue (note that the data could still be partially loaded even if there is an issue).
        /// </summary>
        bool Load(in TextReader reader);

        /// <summary>
        /// Grabs the serialized progress data this manager is responsible for, and compares it to the current progress data.
        /// Returns true if the data is equal.
        /// </summary>
        bool Validate(in TextReader reader, in object previousDataObject);

        /// <summary>
        /// Upgrades the save data without loading the game. This means we won't have injection references when this method is called.
        /// </summary>        
        void UpgradeSave(string targetVersion, GameStateSaveData metaData, TextReader reader, out XmlSerializer serializer, out object saveData);
    }
}