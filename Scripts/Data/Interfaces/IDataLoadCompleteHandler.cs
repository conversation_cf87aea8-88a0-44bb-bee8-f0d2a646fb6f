// Copyright Isto Inc.

namespace Isto.Core.Data
{
    public interface IDataLoadCompleteHandler
    {
        /// <summary>
        /// This event will be sent when loading the game is complete, whether it's from a saved game or not.
        /// (Use the flag GameState.LoadingFromSave to differentiate with saved games)
        /// 
        /// This guarantees that:
        /// - the essentials scene is loaded and set as active scene
        /// - the main level scene is loaded
        /// - the pools are initialized
        /// - your project specific FinalizeLoading() is done
        /// - the loading scene is 90% unloaded minimum
        /// </summary>
        void OnDataLoadComplete();
    }
}