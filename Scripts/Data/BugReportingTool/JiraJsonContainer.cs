// Copyright Isto Inc.
using System;

namespace Isto.Core.Data
{
    /// <summary>
    /// This container is set up to follow the outline for Jira's rest API. The property names are lower case as
    /// the rest api on Jira's side is all lower case.
    /// </summary>
    public class JiraJsonContainer
    {
        public JiraFields fields;

        [Serializable]
        public class <PERSON><PERSON><PERSON>ields
        {
            public JiraProject project;
            public string summary;
            public string description;
            public string environment;
            public JiraIssueType issuetype;
        }

        [Serializable]
        public class <PERSON>raProject
        {
            public string key;
        }

        [Serializable]
        public class JiraIssueType
        {
            public string name;
        }
    }
}