// Copyright Isto Inc.
using System;
using System.Globalization;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace Isto.Core.Data
{
    /// <summary>
    /// This class contains all of the information that is needed for a bug that we want to submit to Jira. This is
    ///  not determined by the Jira API but is determined by what we at Isto want to be included in the Bug Reports.
    /// </summary>
    [Serializable]
    public class JiraBugData
    {
        // Must be provided
        public string ReportedBy;
        public string Title;
        public string Description;
        public string ReproductionSteps;
        public string UsefulInformation;
        public string ImageFilePath;

        //Provided procedurally
        public string RuntimeScenario;
        public string CurrentScene;
        public string CurrentVersion;
        public string Date;

        public JiraBugData()
        {
            RuntimeScenario = Application.isEditor ? "Editor" : Application.platform.ToString();
            CurrentScene = SceneManager.GetActiveScene().name;
            CurrentVersion = Application.version;
            Date = DateTime.Now.ToString(CultureInfo.InvariantCulture);
        }

    }
}