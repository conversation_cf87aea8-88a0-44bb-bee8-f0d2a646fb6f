// Copyright Isto Inc.
using System;
using System.Xml.Serialization;

namespace Isto.Core.Data
{
    [Serializable, XmlInclude(typeof(TorchItemData))]
    public abstract class EquippableItemData
    {
        [XmlAttribute("GUID")]
        public string guid;

        [XmlAttribute("Name")]
        public string itemName;

        public EquippableItemData()
        {

        }

        public EquippableItemData(string guid)
        {
            this.guid = guid;
        }

        public override bool Equals(object obj)
        {
            EquippableItemData other = obj as EquippableItemData;

            if (other == null)
                return false;

            // itemName doesn't seem in use, just check guid

            return this.guid.IsEqualWithNullsAsEmpty(other.guid);
        }

        public override int GetHashCode()
        {
            return (guid, itemName).GetHashCode();
        }
    }
}