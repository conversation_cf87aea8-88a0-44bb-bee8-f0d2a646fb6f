// Copyright Isto Inc.
using Isto.Core.Automation;
using Isto.Core.Items;
using System;
using System.Collections.Generic;
using System.Xml.Serialization;
using UnityEngine;

namespace Isto.Core.Data
{
    /// <summary>
    /// Class for tracking ItemPile based on the GUID instead of using CoreItem.  Used for saving Item lists, etc.
    /// </summary>
    [Serializable]
    public class ItemData
    {
        // Public Variables

        [XmlAttribute("ID")]
        public string id;
        [XmlAttribute("Name")]
        public string itemName;
        public int count;

        public ItemData()
        {
            this.id = "";
            this.itemName = "";
            this.count = 0;
        }

        public ItemData(ItemPile itemPile)
        {
            if (itemPile != null && itemPile.HasItems())
            {
                this.id = itemPile.item.itemID;
                this.itemName = itemPile.item.itemName;
                this.count = itemPile.count;
            }
        }

        public ItemData(string guid, string itemName, int count)
        {
            this.id = guid;
            this.itemName = itemName;
            this.count = count;
        }

        /// <summary>
        /// Helper method to simplify conversion of ItemPile lists to ItemData lists
        /// </summary>
        /// <param name="items"></param>
        /// <returns></returns>
        public static List<ItemData> ConvertItemPileList(List<ItemPile> items)
        {
            List<ItemData> data = new List<ItemData>();

            foreach (ItemPile itemPile in items)
            {
                data.Add(new ItemData(itemPile));
            }

            return data;
        }

        public static void LoadItemsIntoInventory(ItemData[] items, IInventory inventory, MasterItemList masterItemList)
        {
            for (int i = 0; i < items.Length; i++)
            {
                CoreItem item = masterItemList.GetItemByID<CoreItem>(items[i].id);

                if (item != null)
                {
                    int addedAmt = inventory.Add(item, items[i].count);

                    if (addedAmt != items[i].count)
                        Debug.LogWarning("Cannot put ItemData into inventory, should be only used for loading data and this shouldn't happen.");
                }
            }
        }

        public override bool Equals(object obj)
        {
            ItemData other = obj as ItemData;

            if (other == null)
                return false;
            if (!this.id.IsEqualWithNullsAsEmpty(other.id)) // empty equip slots have an id of "" but after deserialization it becomes null so watch out for that
                return false;

            // Note: ignore itemName - it's localized and I think it's there for debugging purposes only.
            // This field probably should not be serialized.

            return this.count == other.count;
        }

        public override int GetHashCode()
        {
            return (id, itemName, count).GetHashCode();
        }
    }
}