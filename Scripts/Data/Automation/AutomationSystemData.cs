// Copyright Isto Inc.
using Isto.Core.Items;
using System;
using System.Collections.Generic;
using System.Xml.Serialization;
using UnityEngine;

namespace Isto.Core.Data
{
    [Serializable, XmlRoot("Automation System")] // Note for future: having a space in here is not great for how it looks serialized
    public class AutomationSystemData
    {
        public AutomationSystemData()
        {
            gridSpaces = new List<AutomationGridSpaceData>();
            lightBulbPositions = new List<Vector3>();
            superBulbPositions = new List<SuperBulbData>();

#if UNITY_EDITOR
            itemSpawners = new List<ItemSpawnerData>();
#endif
        }

        public List<AutomationGridSpaceData> gridSpaces;
        public List<Vector3> lightBulbPositions;
        public List<SuperBulbData> superBulbPositions;

#if UNITY_EDITOR
        public List<ItemSpawnerData> itemSpawners;
#endif

        // Not implementing actual Equals to avoid the burden of doing a GetHashCode that we're not really going to use.
        // And I don't want to write a GetHashCode that's non-compliant since that could be misleading for the future.
        public bool ContentEquals(AutomationSystemData other)
        {
            if (this.gridSpaces != null && other.gridSpaces == null)
                return false;
            if (this.gridSpaces == null && other.gridSpaces != null)
                return false;
            if (this.gridSpaces != null && this.gridSpaces.Count != other.gridSpaces.Count)
                return false;
            if (!this.superBulbPositions.SequenceEqualOrNull(other.superBulbPositions))
                return false;
            if (!this.lightBulbPositions.SequenceEqualOrNull(other.lightBulbPositions))
                return false;

#if UNITY_EDITOR
            if (!this.itemSpawners.SequenceEqualOrNull(other.itemSpawners))
                return false;
#endif

            // Equals is not overriden on AutomationGridSpaceData either, same reason as us. So write out the comparison.
            bool spacesEqual = true;
            if (this.gridSpaces != null && other.gridSpaces != null)
            {
                for (int i = 0; i < gridSpaces.Count; i++)
                {
                    AutomationGridSpaceData ours = this.gridSpaces[i];
                    AutomationGridSpaceData theirs = other.gridSpaces[i];

                    if (ours == null || theirs == null || !ours.ContentEquals(theirs))
                    {
                        spacesEqual = false;
                        Debug.LogError($"Automation grid space mismatch in automation data post serialization");
                        break;
                    }
                }
            }
            return spacesEqual;
        }

        [Serializable]
        public class SuperBulbData
        {
            public Vector3 position;
            public string bulbID;

            public SuperBulbData()
            {
            }

            public SuperBulbData(Vector3 position, string bulbID)
            {
                this.position = position;
                this.bulbID = bulbID;
            }

            public override bool Equals(object obj)
            {
                SuperBulbData other = obj as SuperBulbData;

                if (other == null)
                    return false;
                if (this.position != other.position)
                    return false;

                return this.bulbID == other.bulbID;
            }

            public override int GetHashCode()
            {
                return (position, bulbID).GetHashCode();
            }
        }
    }
}