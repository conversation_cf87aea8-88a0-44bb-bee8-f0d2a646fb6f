// Copyright Isto Inc.
using Isto.Core.Automation;
using Isto.Core.Items;
using System.Collections.Generic;

namespace Isto.Core.Data
{
    public class AutomationPlanterData : AutomationProcessorData, IInventoryData
    {
        public string recipeID;
        public List<ItemData> inventory;

        public AutomationPlanterData() : base() { }

        public static AutomationPlanterParams GetParamsFromData(MasterItemList masterItemList, AutomationPlanterData data)
        {
            if (masterItemList.TryGetItemByID(data.recipeID, out CoreItem item))
            {
                return new AutomationPlanterParams(data.typeId, item.GetRecipe(), data.position, data.direction);
            }

            return null;
        }

        public List<ItemData> GetInventoryData()
        {
            return inventory;
        }
    }
}