// Copyright Isto Inc.
using Isto.Core.Automation;
using System.Collections.Generic;

namespace Isto.Core.Data
{
    public class AutomationResearchBoosterData : AutomationProcessorData, IInventoryData
    {
        public float cookingPercent;
        public List<ItemData> inventory;
        public ResearchBoosts[] boosts;

        public List<ItemData> GetInventoryData()
        {
            return inventory;
        }
    }
}