// Copyright Isto Inc.
using System.Collections.Generic;
using System.Linq;

namespace Isto.Core.Data
{
    public class AutomationItemContainerData : AutomationProcessorData, IInventoryData
    {
        public int inventoryPileSize;
        public int inventoryPileCount;
        public ItemData[] inventory;
        public bool isRigidInventory = false;
        public bool pullsItemsFromNeighbors = false;

        public List<ItemData> GetInventoryData()
        {
            return inventory.ToList();
        }
    }
}