// Copyright Isto Inc.
using Isto.Core.Automation;
using System;
using System.Xml.Serialization;
using UnityEngine;

namespace Isto.Core.Data
{
    [Serializable]
    [XmlInclude(typeof(AutomationHarvesterData))]
    [XmlInclude(typeof(AutomationItemMoverData))]
    [XmlInclude(typeof(AutomationItemSinglePullerData))]
    [XmlInclude(typeof(AutomationItemPusherData))]
    [XmlInclude(typeof(AutomationFactoryData))]
    [XmlInclude(typeof(AutomationResearchBoosterData))]
    [XmlInclude(typeof(ResearchBoosts))]
    [XmlInclude(typeof(AutomationItemContainerData))]
    [XmlInclude(typeof(AutomationPlanterData))]
    [XmlInclude(typeof(AutomationItemPullerData))]
    [XmlInclude(typeof(AutomationCreatureHiveData))]
    [XmlInclude(typeof(AutomationItemDispenserData))]
    public class AutomationProcessorData
    {
        public string typeId;
        public Vector3 direction;
        public Vector3 position;
        public bool visible = true;

        public override bool Equals(object obj)
        {
            AutomationProcessorData other = obj as AutomationProcessorData;

            if (other == null)
                return false;
            if (!this.typeId.IsEqualWithNullsAsEmpty(other.typeId))
                return false;
            if (this.direction != other.direction)
                return false;
            if (this.position != other.position)
                return false;

            return this.visible == other.visible;
        }

        public override int GetHashCode()
        {
            return (typeId, direction, position, visible).GetHashCode();
        }
    }
}