// Copyright Isto Inc.
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Isto.Core.Data
{
    [Serializable]
    public class AutomationGridSpaceData
    {
        public AutomationGridSpaceData()
        {
            items = new List<ItemData>();
        }

        // Public Variables
        public Vector3 position;

        // Resource Info
        public string resourceID;
        public float resourceHealth;
        public float growthPercent;

        // Core Items Info
        public List<ItemData> items;

        // Item Processor Info
        public AutomationProcessorData processorData;

        public bool IsEmpty()
        {
            return string.IsNullOrEmpty(resourceID) && items.Count == 0 && processorData == null;
        }

        public override string ToString()
        {
            string itemsString = (items != null && items.Count > 0) ? (items.Count == 1 ? $"item={items[0].id}" : $"items.Count={items.Count}") : "no items";
            string processorString = processorData == null ? "no processor" : processorData.typeId;
            return $"Space Data: pos={position}, r={resourceID}, h={resourceHealth}, g={growthPercent}, {itemsString}, p={processorString}";
        }

        // Not implementing actual Equals to avoid the burden of doing a GetHashCode that we're not really going to use.
        // And I don't want to write a GetHashCode that's non-compliant since that could be misleading for the future.
        public bool ContentEquals(AutomationGridSpaceData other)
        {
            if (other == null)
                return false;
            if (this.position != other.position)
                return false;
            if (!this.resourceID.IsEqualWithNullsAsEmpty(other.resourceID))
                return false;
            if (!this.resourceHealth.Approx(other.resourceHealth))
                return false;
            if (!this.growthPercent.Approx(other.growthPercent))
                return false;
            if (this.processorData == null && other.processorData != null)
                return false;
            if (this.processorData != null && !this.processorData.Equals(other.processorData))
                return false;
            if (this.items == null && other.items != null)
                return false;
            if (this.items != null && other.items == null)
                return false;
            if (this.items.Count != other.items.Count)
                return false;

            return this.items.SequenceEqual(other.items);
        }
    }
}