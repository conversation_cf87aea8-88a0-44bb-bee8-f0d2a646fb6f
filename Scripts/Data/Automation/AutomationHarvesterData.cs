// Copyright Isto Inc.
using System.Collections.Generic;

namespace Isto.Core.Data
{
    public class AutomationHarvesterData : AutomationProcessorData, IInventoryData
    {
        public AutomationHarvesterData() : base()
        {
            itemsQueue = new List<ItemData>();
        }

        public float harvesterDPS;
        public bool destroyOnHarvestComplete;
        public List<ItemData> itemsQueue;

        public List<ItemData> GetInventoryData()
        {
            return itemsQueue;
        }
    }
}