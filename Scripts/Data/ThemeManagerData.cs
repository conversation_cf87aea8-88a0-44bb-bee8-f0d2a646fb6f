// Copyright Isto Inc.
using System;

namespace Isto.Core.Data
{
    [Serializable]
    public class ThemeManagerData
    {
        public string themeInternalName;
        public string playerSetInternalName;
        public string playerHatInternalName;
        public string playerBodyInternalName;
        public string miniDeerInternalName;
        public string pushbackInternalName;
        public string itemGroupInternalName;

        public override bool Equals(object obj)
        {
            ThemeManagerData other = obj as ThemeManagerData;

            if (other == null)
                return false;
            if (!this.themeInternalName.IsEqualWithNullsAsEmpty(other.themeInternalName))
                return false;
            if (!this.playerSetInternalName.IsEqualWithNullsAsEmpty(other.playerSetInternalName))
                return false;
            if (!this.playerHatInternalName.IsEqualWithNullsAsEmpty(other.playerHatInternalName))
                return false;
            if (!this.playerBodyInternalName.IsEqualWithNullsAsEmpty(other.playerBodyInternalName))
                return false;
            if (!this.miniDeerInternalName.IsEqualWithNullsAsEmpty(other.miniDeerInternalName))
                return false;
            if (!this.pushbackInternalName.IsEqualWithNullsAsEmpty(other.pushbackInternalName))
                return false;

            return this.itemGroupInternalName.IsEqualWithNullsAsEmpty(other.itemGroupInternalName);
        }

        public override int GetHashCode()
        {
            return (themeInternalName, playerSetInternalName, playerHatInternalName, playerBodyInternalName,
                    miniDeerInternalName, pushbackInternalName, itemGroupInternalName).GetHashCode();
        }
    }
}