// Copyright Isto Inc.
using Isto.Core.Inputs;
using Rewired;
using Rewired.Data;
using System;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Data
{
    public class UserKeybindingDataStore : UserDataStore
    {
        [SerializeField] private bool _loadDataOnStart = false;

        [NonSerialized] private bool allowImpreciseJoystickAssignmentMatching = true;

        [Inject] private IGameData _gameData;


        protected override void OnInitialize()
        {
            // Disallow imprecise joystick assignment matching on some platforms when
            // system id/player Rewired Player alignment needs to stay fixed.
#if !UNITY_EDITOR && (UNITY_XBOXONE || UNITY_PS4 || UNITY_SWITCH)
            allowImpreciseJoystickAssignmentMatching = false;
#endif

            if (_loadDataOnStart)
            {
                Load();
            }
        }

        public override void Load()
        {
            Player player = ReInput.players.GetPlayer(Controls.DEFAULT_REWIRED_PLAYER_ID);

            _gameData.HasCustomControlsData((result) =>
            {
                if (result)
                {
                    _gameData.LoadCustomControlMappings((saveData) =>
                    {
                        if (saveData != null)
                            LoadPlayerDataNow(player, saveData);
                        else
                            Debug.LogError("Couldn't load data but some exists");
                    });
                }
                else
                {
                    Save();
                }
            });
        }

        public override void LoadControllerData(int playerId, ControllerType controllerType, int controllerId)
        {
            throw new System.NotImplementedException();
        }

        public override void LoadControllerData(ControllerType controllerType, int controllerId)
        {
            throw new System.NotImplementedException();
        }

        public override void LoadInputBehavior(int playerId, int behaviorId)
        {
            throw new System.NotImplementedException();
        }

        public override void LoadPlayerData(int playerId)
        {
            throw new System.NotImplementedException();
        }

        public override void Save()
        {
            Player player = ReInput.players.GetPlayer(Controls.DEFAULT_REWIRED_PLAYER_ID);

            SavePlayerDataNow(player);

#if UNITY_EDITOR
            Debug.Log("Saved all user data to XML.");
#endif
        }

        public override void SaveControllerData(int playerId, ControllerType controllerType, int controllerId)
        {
            throw new System.NotImplementedException();
        }

        public override void SaveControllerData(ControllerType controllerType, int controllerId)
        {
            throw new System.NotImplementedException();
        }

        public override void SaveInputBehavior(int playerId, int behaviorId)
        {
            throw new System.NotImplementedException();
        }

        public override void SavePlayerData(int playerId)
        {
            throw new System.NotImplementedException();
        }

        protected override void OnControllerConnected(ControllerStatusChangedEventArgs args)
        {
            // Load data when joystick is connected
            if (args.controllerType == ControllerType.Joystick)
            {
                LoadJoystickData(args.controllerId);
            }
        }

        protected override void OnControllerDisconnected(ControllerStatusChangedEventArgs args)
        {
            // Currently doing nothing
        }

        // Save Methods

        private void SavePlayerDataNow(Player player)
        {
            if (player == null)
                return;

            // Get all savable data from player
            Rewired.PlayerSaveData playerData = player.GetSaveData(true);

            PlayerCustomControlsSaveData saveData = new PlayerCustomControlsSaveData();

            // Save controller maps
            foreach (ControllerMapSaveData mapSaveData in playerData.AllControllerMapSaveData)
            {
                saveData.mapDatas.Add(new ControllerMapData()
                {
                    categoryID = mapSaveData.map.categoryId,
                    type = mapSaveData.controllerType,
                    xmlString = mapSaveData.map.ToXmlString()
                });
            }

            _gameData.SaveCustomControlMappings(saveData);
        }

        // Load Methods

        private void LoadPlayerDataNow(Player player, PlayerCustomControlsSaveData saveData)
        {
            // Load Keyboard Maps
            LoadControllerMaps(player.id, ControllerType.Keyboard, 0, saveData);

            // Load Mouse Maps
            LoadControllerMaps(player.id, ControllerType.Mouse, 0, saveData);

            // Load Joystick Maps for each joystick
            foreach (Joystick joystick in player.controllers.Joysticks)
            {
                LoadControllerMaps(player.id, ControllerType.Joystick, joystick.id, saveData);
            }

            // Trigger Layout Manager refresh after load
            RefreshLayoutManager(player.id);
        }

        private void LoadControllerMaps(int playerId, ControllerType controllerType, int controllerId, PlayerCustomControlsSaveData saveData)
        {
            Player player = ReInput.players.GetPlayer(playerId);

            if (player == null)
                return;

            Controller controller = ReInput.controllers.GetController(controllerType, controllerId);

            if (controller == null)
                return;

            IList<InputMapCategory> categories = ReInput.mapping.MapCategories;

            for (int i = 0; i < categories.Count; i++)
            {
                InputMapCategory category = categories[i];
                if (!category.userAssignable)
                    continue; // skip map because not user-assignable

                IList<InputLayout> layouts = ReInput.mapping.MapLayouts(controller.type);
                for (int j = 0; j < layouts.Count; j++)
                {
                    InputLayout layout = layouts[j];

                    // Load the Controller Map
                    ControllerMap controllerMap = LoadControllerMap(player, controller.identifier, category.id, layout.id, saveData);
                    if (controllerMap == null)
                        continue;

                    // Add the map to the Player
                    player.controllers.maps.AddMap(controller, controllerMap);
                }
            }
        }

        private ControllerMap LoadControllerMap(Player player, ControllerIdentifier controllerIdentifier, int categoryId, int layoutId, PlayerCustomControlsSaveData saveData)
        {
            if (player == null)
                return null;

            // Get the XML for the Controller Map
            string xml = GetControllerMapXml(controllerIdentifier, categoryId, saveData);

            if (string.IsNullOrEmpty(xml))
                return null;

            ControllerMap controllerMap = ControllerMap.CreateFromXml(controllerIdentifier.controllerType, xml);
            if (controllerMap == null)
                return null;

            // Load default mappings for new Actions
            //List<int> knownActionIds = GetControllerMapKnownActionIds(player, controllerIdentifier, categoryId, layoutId);
            //AddDefaultMappingsForNewActions(controllerIdentifier, controllerMap, knownActionIds);

            return controllerMap;
        }

        private string GetControllerMapXml(ControllerIdentifier controllerIdentifier, int categoryId, PlayerCustomControlsSaveData saveData)
        {
            for (int i = 0; i < saveData.mapDatas.Count; i++)
            {
                ControllerMapData nextMap = saveData.mapDatas[i];

                if (nextMap.type == controllerIdentifier.controllerType && nextMap.categoryID == categoryId)
                    return nextMap.xmlString;
            }

            return null;
        }

        private void LoadJoystickData(int joystickId)
        {
            _gameData.HasCustomControlsData((result) =>
            {
                if (result)
                {
                    _gameData.LoadCustomControlMappings((saveData) =>
                    {
                        if (saveData != null)
                        {
                            Player player = ReInput.players.GetPlayer(Controls.DEFAULT_REWIRED_PLAYER_ID);

                            if (!player.controllers.ContainsController(ControllerType.Joystick, joystickId))
                                return; // player does not have the joystick

                            LoadControllerMaps(player.id, ControllerType.Joystick, joystickId, saveData); // load the maps
                        }
                    });
                }
            });
        }

        private void RefreshLayoutManager(int playerId)
        {
            Player player = ReInput.players.GetPlayer(playerId);
            if (player == null)
                return;
            player.controllers.maps.layoutManager.Apply();
        }
    }
}