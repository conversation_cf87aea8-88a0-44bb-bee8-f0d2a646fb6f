// Copyright Isto Inc.
using System;
using System.Collections.Generic;

namespace Isto.Core.Data
{
    [Serializable]
    public class PlayerCustomControlsSaveData
    {
        public List<ControllerMapData> mapDatas;

        public PlayerCustomControlsSaveData() { mapDatas = new List<ControllerMapData>(); }
    }

    [Serializable]
    public class ControllerMapData
    {
        public Rewired.ControllerType type;
        public int categoryID;
        public string xmlString;

        public ControllerMapData() { }
    }
}
