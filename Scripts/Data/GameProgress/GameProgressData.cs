// Copyright Isto Inc.
using System.Xml.Serialization;

namespace Isto.Core.Data
{
    public class GameProgressData
    {
        [XmlAttribute("ActionID")]
        public string progressActionID;

        [XmlAttribute("Completed")]
        public bool completed;

        [XmlAttribute("Started")]
        public bool started;

        public GameProgressData()
        {

        }

        public GameProgressData(string actionID)
        {
            this.progressActionID = actionID;
        }

        public override bool Equals(object obj)
        {
            GameProgressData other = obj as GameProgressData;

            if (other == null)
                return false;
            if (!this.progressActionID.IsEqualWithNullsAsEmpty(other.progressActionID))
                return false;
            if (this.completed != other.completed)
                return false;

            return this.started == other.started;
        }

        public override int GetHashCode()
        {
            return (progressActionID, completed, started).GetHashCode();
        }
    }
}
