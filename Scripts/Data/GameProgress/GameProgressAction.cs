// Copyright Isto Inc.
using Isto.Core.Configuration;
using Isto.Core.Game;
using System.Linq;
using UnityEngine;
using Zenject;

namespace Isto.Core.Data
{
    /// <summary>
    /// Abstract class for actions that occur as the player progresses through the game or hits certain milestones.
    /// Concrete classes need to implement registering for events that are relevant to the progress being tracked.
    /// </summary>
	public abstract class GameProgressAction : MonoBehaviour, IDataLoadCompleteHandler
    {
        // Public Variables

        [Tooltip("Does the action repeat everytime the requirements are met or is it a one shot?")]
        public bool repeating;

        public string actionID;

        [Tooltip("The Action will only start if running the gane in a compabitle Game Mode")]
        public GameModeDefinition[] compatibleGameModes;

        public bool ActionCompleted => _actionCompleted;

        protected bool _actionStarted;
        protected bool _actionCompleted;

        private GameState _gameState;

        [Inject]
        public void Inject(GameState gameState)
        {
            _gameState = gameState;
        }

        /// <summary>
        /// This is called after all save data is loaded.  If the action has already been completed the flag will
        /// be set in the LoadData method before this is called.  If action has not been completed, the RegisterForEvents
        /// method is called which should listen to the appropriate events to trigger the action
        /// </summary>
        public void OnDataLoadComplete()
        {
            if (enabled && !_actionCompleted && IsCompatibleWithCurrentGameMode())
            {
                RegisterForEvents();
            }
        }

        public void OnDisable()
        {
            UnregisterForEvents();
        }

        // Methods		

        public abstract void RegisterForEvents();
        public abstract void UnregisterForEvents();
        public abstract GameProgressData GetSaveData();
        public abstract void LoadData(GameProgressData data);

        protected bool IsCompatibleWithCurrentGameMode()
        {
            if (_gameState.CurrentGameMode != null && compatibleGameModes.Length == 0)
                Debug.LogWarning($"Gameplay Action {actionID} has no compatible Game Modes asigned to it in inspector. Will not start.");

            return _gameState.CurrentGameMode != null && compatibleGameModes.Length > 0 && compatibleGameModes.Contains(_gameState.CurrentGameMode);
        }
    }
}