// Copyright Isto Inc.
using Isto.Core.UI;
using System;
using System.IO;
using System.Xml.Serialization;
using UnityEngine;
using Zenject;

namespace Isto.Core.Data
{
    /// <summary>
    /// Abstract class for saving and loading data for various game objects.  Handles the serialization of the passed in JSON data
    /// into a file and loading from that file.  The concrete DataManager classes will know what to do with that JSON and how to encode their
    /// data into JSON
    /// </summary>
	public abstract class DataManager : MonoBehaviour, IDataManager
    {
        public abstract string FilePrefix { get; }
        public abstract string BlobName { get; }

        private static UIModalPopup _errorPopup;

        [Inject]
        public void Inject([InjectOptional] UIModalPopup errorPopup)
        {
            _errorPopup = errorPopup;
        }

        // Abstract Methods

        public abstract bool Load(in TextReader reader);
        public abstract void Save(out object saveData);
        public abstract bool Validate(in TextReader reader, in object previousData);

        /// <summary>
        /// Upgrades the save data without loading the game. This means we won't have injection references when this method is called.
        /// </summary>
        public abstract void UpgradeSave(string targetVersion, GameStateSaveData metaData, TextReader reader, out XmlSerializer serializer, out object saveData);

        // Static Methods

        public static string SaveBackupFolderName => "/Backup";


        public abstract object GetSampleSaveData();
        public abstract System.Type[] GetSaveExtraTypes();

        public static XmlSerializer GetXMLSerializer<T>(T saveData, System.Type[] extraTypes = null)
        {
            // I changed typeof(T) to saveData.GetType() because this one should still work if we pass in a subclass of T.
            // It's not something we're going to use right now but it seems to be the more future proof solution. -Frank
            return new XmlSerializer(saveData.GetType(), extraTypes);
        }

        /// <summary>
        /// Responsible for closing the reader
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="reader"></param>
        /// <param name="extraTypes"></param>
        /// <returns></returns>
        public static T LoadXMLFile<T>(TextReader reader, System.Type[] extraTypes = null)
        {
            if (reader == null)
            {
                Debug.LogError($"LoadXMLFile for type {typeof(T)} received a null reader and as such cannot read the file");
                _errorPopup?.DisplayError(UIModalPopup.ErrorPopupType.UnknownError, new NullReferenceException($"LoadXMLFile cannot provide {typeof(T)} because reader was invalid. Save file might be corrupted."));
                return default;
            }

            try
            {
                XmlSerializer serializer = new XmlSerializer(typeof(T), extraTypes);

                T data = (T)serializer.Deserialize(reader); // throws InvalidOperationException

                return data;
            }
            catch (System.Exception e)
            {
                Debug.Log($"Unable to load data file for {typeof(T)}, Error: {e.Message}, Inner: {e.InnerException?.Message}");

                // Hack the exception message to give a bit more context about which file is bad
                e = new Exception($"[{typeof(T)}] " + e.Message, e.InnerException);
                _errorPopup?.DisplayError(UIModalPopup.ErrorPopupType.UnknownError, e);

                return default;
            }
            finally
            {
                reader?.Close();
            }
        }
    }
}