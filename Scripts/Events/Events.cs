// Copyright Isto Inc.
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Isto.Core
{
    /// <summary>
    /// Static class for components to subscribe to events
    /// </summary>
    public static class Events
    {
        // Non-parameterized events
        public const string GAME_END = "EndOfGame";

        /// <summary>
        /// this event happens when a heart state gets entered, except the very first one that's entered as part of scene setup
        /// since that one is not really a new state being entered, it is just initialization
        /// </summary>
        public const string HEART_BOX_ENTER_STATE = "OnHeartEnterState";

        public const string HEART_BOX_TRANSITION_COMPLETE = "OnHeartTransitionComplete";

        public const string PLAYER_INVENTORY_CHANGED_EVENT = "PlayerInventoryChanged";
        public const string PLAYER_INVENTORY_FULL_EVENT = "PlayerInventoryFull";
        public const string RESEARCH_COMPLETE_EVENT = "ResearchComplete";
        public const string SETTINGS_CHANGED = "SettingsChanged";
        public const string SETTINGS_SAVED = "SettingsSaved";
        //public const string DEMO_GOAL_REACHED = "GoalReached";
        //public const string PLAYER_ENTER_DARK = "PlayerEnterDark";
        //public const string PLAYER_EXIT_DARK = "PlayerExitDark";
        public const string GAME_AUTOSAVING = "GameAutoSaving";
        public const string GAME_SAVED = "GameSaved";
        public const string PLAYER_DEATH_CUTSCENE_START = "PlayerDeathCutsceneStart";
        public const string PLAYER_DEATH_CUTSCENE_END = "PlayerDeathCutsceneEnd";
        public const string FAST_TRAVEL_START = "FastTravelStart";
        public const string FAST_TRAVEL_END = "FastTravelEnd";
        public const string GAME_START_FADE_IN = "GameStartFadeIn"; // when setup is finished, just before we fade in/show the elevator

        /*public const string RAIN_START = "RainStart";
        public const string RAIN_END = "RainEnd";
        public const string DAY_START = "DayStart";
        public const string NIGHT_START = "NightStart";
        public const string CRAFTED_PATCHEDPICK = "CraftedPatchedPick";

        public const string PUNK_MONK_CONNECTED = "PunkMonkConnected";
        public const string IRIS_STATION_CONNECTED = "IrisStationConnected";
        public const string BULB_MAN_CONNECTED = "BulbManConnected";
        public const string DEER_STATION_CONNECTED = "DeerStationConnected";
        public const string SUPERBULBS_CONNECTED = "SuperbulbsConnected";*/
        public const string GIANT_CUTSCENE_START = "GiantCutsceneStart";
        public const string GIANT_CUTSCENE_END = "GiantCutsceneEnd";
        /*public const string HEART_CUTSCENE_START = "HeartCutsceneStart"; // For now this is fired only at the interaction after the return from employee orientation
        public const string HEART_CUTSCENE_END = "HeartCutsceneEnd"; // For now this is fired only after the conversation after the return from employee orientation
        */
        public const string THEME_CHANGED = "ThemeChanged";
        public const string PLAYER_SKIN_SET_CHANGED = "PlayerSkinSetChanged";
        public const string PLAYER_SKIN_HAT_CHANGED = "PlayerHatChanged";
        public const string PLAYER_SKIN_BODY_CHANGED = "PlayerBodyChanged";
        public const string MINIDEER_SKIN_CHANGED = "MiniDeerSkinChanged";
        public const string PUSHBACK_SKIN_CHANGED = "PushbackSkinChanged";
        public const string ADVANCED_ITEMS_SKIN_CHANGED = "AdvancedItemsSkinChanged";

        public const string CONTROLLER_INPUT = "ChangeInputToControllerMode";
        public const string MOUSEKEYBOARD_INPUT = "ChangeInputToMouseKeyboardMode";
        public const string INPUT_MODE_CHANGED = "InputChanged";
        public const string LANGUAGE_CHANGED = "LanguageChanged";
        public const string CONTROLS_CHANGED = "ControlsChanged";

        public static readonly string NETWORK_ROOM_JOINED = "NetworkRoomJoined";

        // Xbox only?
        public const string USER_CHANGED = "UserChanged";
        public const string XBOX_INITIALIZED = "XboxInitialized";

        // Events that send parameters
        //public const string INITIALIZED_EMPLOYEE_NUMBER = "EmployeeNumberInitialized";
        public const string INITIALIZED_HEARTBOX_CONTROLLER = "HeartboxControllerInitialized";
        public const string PLAYER_ANIMATION_SET = "PlayerAnimationTrigger";
        public const string DEVELOPER_COMMAND = "DeveloperCommand";
        /*public const string LIGHTS_ON = "LightsOn";
        public const string LIGHTS_OFF = "LightsOff";
        public const string NOTIFICATION_UPDATE = "NotificationUpdate";
        public const string DISPLAY_NEW_ITEM_COMPLETE = "DisplayNewItemComplete";
        public const string CRAFTING_UPDATE_TUTORIAL = "CraftingUpdateTutorialArrow"; // bool showing, CoreItem target, string instructions

        public const string MENU_OPEN = "MenuOpen";*/

        public static readonly string PLAYER_ITEM_ACQUIRED = "PlayerItemAcquired"; // always triggers, for handlers that should not be silenced

        public const string SAVE_SPEEDRUN_BUTTON_CLICKED = "SaveSpeedrunButtonClicked";

        public static readonly string GAMEOBJECT_SPAWNED_FROM_NETWORK = "GameObjectSpawnedFromNetwork"; // passes the game object
        

        // Fields

        private static Dictionary<string, List<OrderedParamEvent>> _paramListeners = new Dictionary<string, List<OrderedParamEvent>>();

        private static Dictionary<string, List<OrderedEvent>> _listeners = new Dictionary<string, List<OrderedEvent>>();

        // Methods

        /// <summary>
        /// Subscribes to the event type specified so the delegate method is called when ever the event is raised.
        /// Priority can be set to determine calling order of delegate method.
        /// </summary>
        /// <param name="eventName"></param>
        /// <param name="del">Delegate method</param>
        /// <param name="priority">1 = Do first, 100 = Default Order, 200 = Do last</param>
        public static void Subscribe(string eventName, OrderedEvent.GameEvent del, int priority = 100)
        {
            List<OrderedEvent> listenerList;

            if (!_listeners.ContainsKey(eventName))
            {
                listenerList = new List<OrderedEvent>();
                _listeners.Add(eventName, listenerList);
            }
            else
                listenerList = _listeners[eventName];

            listenerList.Add(new OrderedEvent(del, priority));

            _listeners[eventName] = listenerList.OrderBy(x => x.priority).ToList();
        }

        public static void SubscribeWithParams(string eventName, OrderedParamEvent.ParamGameEvent del, int priority = 100)
        {
            List<OrderedParamEvent> listenerList;

            if (!_paramListeners.ContainsKey(eventName))
            {
                listenerList = new List<OrderedParamEvent>();
                _paramListeners.Add(eventName, listenerList);
            }
            else
                listenerList = _paramListeners[eventName];

            listenerList.Add(new OrderedParamEvent(del, priority));

            _paramListeners[eventName] = listenerList.OrderBy(x => x.priority).ToList();
        }

        public static void UnSubscribe(string eventName, OrderedEvent.GameEvent del)
        {
            if (_listeners.ContainsKey(eventName))
            {
                List<OrderedEvent> eventListeners = _listeners[eventName];

                OrderedEvent eventListener = eventListeners.Find(x => x.callback == del);

                if (eventListener != null)
                    eventListeners.Remove(eventListener);

                _listeners[eventName] = eventListeners;
            }
        }

        public static void UnSubscribeWithParams(string eventName, OrderedParamEvent.ParamGameEvent del)
        {
            if (_paramListeners.ContainsKey(eventName) && _paramListeners[eventName].Count > 0)
            {
                List<OrderedParamEvent> eventListeners = _paramListeners[eventName];

                OrderedParamEvent eventListener = eventListeners.Find(x => x.callback == del);

                if (eventListener != null)
                    eventListeners.Remove(eventListener);
                else
                    Debug.LogWarningFormat("Trying to remove event listener {0} that is not registered to event {1}", del.ToString(), eventName);

                _paramListeners[eventName] = eventListeners;
            }
            else
            {
                Debug.LogWarningFormat("Trying to remove event listener {0} that is not registered to event {1}", del.ToString(), eventName);
            }
        }

        public static void RaiseEvent(string eventName)
        {
            //Debug.Log("Event raised: " + eventName);

            if (_listeners.ContainsKey(eventName) && _listeners[eventName].Count > 0)
            {
                List<OrderedEvent> eventListeners = _listeners[eventName];

                for (int i = 0; i < eventListeners.Count; i++)
                {
                    eventListeners[i].callback.Invoke();
                }
            }
        }

        public static void RaiseEvent(string eventName, params object[] args)
        {
            if (_paramListeners.ContainsKey(eventName) && _paramListeners[eventName].Count > 0)
            {
                List<OrderedParamEvent> eventListeners = _paramListeners[eventName];

                for (int i = 0; i < eventListeners.Count; i++)
                {
                    eventListeners[i].callback.Invoke(args);
                }
            }
            else
            {
                //Debug.LogWarningFormat("Raising event {0} with no listeners", eventName);
            }
        }

        public static void RaiseEvent<T>(string eventName, T args)
        {
            if (_paramListeners.ContainsKey(eventName) && _paramListeners[eventName].Count > 0)
            {
                List<OrderedParamEvent> eventListeners = _paramListeners[eventName];

                for (int i = 0; i < eventListeners.Count; i++)
                {
                    eventListeners[i].callback.Invoke(args);
                }
            }
            else
            {
                //Debug.LogWarningFormat("Raising event {0} with no listeners", eventName);
            }
        }
    }

    public class OrderedEvent
    {
        public delegate void GameEvent();

        public int priority;
        public GameEvent callback;

        public OrderedEvent(GameEvent del, int order)
        {
            callback = del;
            priority = order;
        }
    }

    public class OrderedParamEvent
    {
        public delegate void ParamGameEvent(params object[] args);

        public int priority;
        public ParamGameEvent callback;

        public OrderedParamEvent(ParamGameEvent del, int order)
        {
            callback = del;
            priority = order;
        }
    }

    public class MessageFunctions
    {
        // Same method name as in IInventoryListener interface
        public static string INVENTORY_UPDATE = "InventoryChanged";
        public static string INVENTORY_FULL = "InventoryFull";
    }

    //public class OrderedParamEventTypeSafe<T>
    //{
    //    public delegate void ParamGameEvent(T arg);

    //    public int priority;

    //    public ParamGameEvent callback;

    //    public OrderedParamEventTypeSafe(ParamGameEvent del, int order)
    //    {
    //        callback = del;
    //        priority = order;
    //    }
    //}
}