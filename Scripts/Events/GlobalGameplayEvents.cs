// Copyright Isto Inc.
using Isto.Core.Beings;
using Isto.Core.Items;
using Isto.Core.UI;
using System;

namespace Isto.Core
{
    /// <summary>
    /// (Deprecated)
    /// A place to organise events into
    /// Example of event raising: GlobalGameplayEvents.OnItemAddedToPlayerInventory(this, item, count);
    /// 
    /// Note that putting a bunch of events to be fired from a global scope is not ideal, and most of the events that
    /// are in here should in theory be refactored into a more specific location
    ///    For instance: ItemCrafted and OnItemCrafted should just be in UICraftingMenuState since it is the only
    ///                  caller; and since that class is in Atrio, maybe the event should not even be here.
    ///                  --> it makes things clearer when the receiver classes register to the sender itself.
    ///                  --> if decoupling is absolutely needed we will prefer an interface over events when possible
    ///                  --> right now PlayerProgress is the only Core class that listens to the event but nothing in
    ///                      core ever invokes the event so from outside of Atrio this looks a bit weird
    ///                  --> UICraftingMenuState probably should be brought into Core at some point
    /// 
    /// Real "global" events are only justified when they need to be fired from all over the place, like in the case
    /// of MenuInteraction and OnMenuInteraction;
    /// Even then, it is not really needed. This project relies on injection. Static classes circumvent the dependency
    /// system. The event handler could probably have been a singleton instance being injected around.
    /// 
    /// The events here should be refactored out of this class if possible.
    /// With that in mind: DON'T ADD MORE EVENTS HERE!
    /// </summary>
    public static class GlobalGameplayEvents
    {
        // Item PickedUp Event

        /// <summary> Deprecated </summary>
        public static event EventHandler<ItemEventArgs> ItemCrafted;

        /// <summary> Deprecated </summary>
        public static void OnItemCrafted(object sender, CoreItem item, ItemContainer container)
        {
            ItemCrafted?.Invoke(sender, new ItemEventArgs(container, item, 1));
        }

        /// <summary> Deprecated </summary>
        public static event EventHandler<ItemEventArgs> ItemEquipped;

        /// <summary> Deprecated </summary>
        public static void OnItemEquipped(object sender, CoreItem item)
        {
            ItemEquipped?.Invoke(sender, new ItemEventArgs(null, item, 1));
        }

        // Player Inventory Events

        /// <summary> Deprecated </summary>
        public static event EventHandler<ItemEventArgs> ItemAddedToPlayerInventory;

        /// <summary> Deprecated </summary>
        public static void OnItemAddedToPlayerInventory(object sender, CoreItem item, int count, PlayerInventory playerInventory)
        {
            ItemAddedToPlayerInventory?.Invoke(sender, new ItemEventArgs(playerInventory, item, count));
        }

        // Menu related Event

        public static event EventHandler<MenuInteractionEventArgs> MenuInteraction;

        public static void OnMenuInteraction(object sender, GameMenusEnum menu, MenuInteractionEventArgs.Action actionType)
        {
            MenuInteraction?.Invoke(sender, new MenuInteractionEventArgs(menu, actionType));
        }
    }
}