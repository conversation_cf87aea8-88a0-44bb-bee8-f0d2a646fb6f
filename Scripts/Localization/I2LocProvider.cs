// Copyright Isto Inc.

using I2.Loc;
using Isto.Core.Installers;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Zenject;

namespace Isto.Core.Localization
{
    public class I2LocProvider : ILocalizationProvider
    {
        // injection

        private LanguageList _supportedLanguages;

        [Inject]
        public void Inject([Inject(Id = InjectId.SUPPORTED_LANGUAGES)] LanguageList supportedLanguages)
        {
            _supportedLanguages = supportedLanguages;
        }


        // other methods

        public string GetLocalizedText(string locKey)
        {
            string localizedText = LocalizationManager.GetTranslation(locKey);

            if (localizedText == null)
            {
                Debug.LogWarning("Unable to find term using key: " + locKey);
                localizedText = Constants.LOCALIZATION_KEY_NOT_FOUND;
            }

            return localizedText;
        }

        public void SetLanguage(LanguageChanger.LanguageEnum language)
        {
            Debug.Log("Setting language to " + language.Name);
            LocalizationManager.CurrentLanguage = language.Name;
            Events.RaiseEvent(Events.LANGUAGE_CHANGED);
        }

        public LanguageChanger.LanguageEnum GetCurrentLanguage()
        {
            string languageName = GetCurrentLanguageName();
            var currentLanguage = LanguageChanger.LanguageEnum.GetFromName(languageName) as LanguageChanger.LanguageEnum;
            return currentLanguage;
        }

        /// <summary>
        /// Gets the current language name according to I2 localization system's internal tracking.
        /// </summary>
        /// <returns>The name of the current language.</returns>
        public string GetCurrentLanguageName()
        {
            // Not sure what the language defaults to when undefined. Hopefully it's same as GetCurrentDeviceLanguage.
            // Otherwise we should detect if CurrentLanguage was not a pre-established value and use
            // GetCurrentDeviceLanguage instead. But, during LocalizationManager's initialization,
            // GetCurrentDeviceLanguage does get called, so, while I'm not sure what their logic does, it seems likely
            // that it would be the same value as GetCurrentDeviceLanguage.
            string currentLanguageName = LocalizationManager.CurrentLanguage;
            List<string> supportedLanguageNames = _supportedLanguages.GetLanguageNames();
            if (!supportedLanguageNames.Contains(currentLanguageName))
            {
                currentLanguageName = GetCurrentDeviceLanguage();
            }
            return currentLanguageName;
        }

        /// <summary>
        /// Relies on I2 to detect device language for us.
        /// Overrides the result with our project default if it is not part of our supported languages list.
        /// </summary>
        /// <returns>The device language</returns>
        public string GetCurrentDeviceLanguage()
        {
            string currentLanguage = LocalizationManager.GetCurrentDeviceLanguage();
            List<string> supportedLanguageNames = _supportedLanguages.GetLanguageNames();
            if(!supportedLanguageNames.Contains(currentLanguage))
            {
                currentLanguage = _supportedLanguages.defaultLanguage;
            }
            return currentLanguage;
        }
    }
}