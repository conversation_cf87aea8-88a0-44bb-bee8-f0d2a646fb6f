// Copyright Isto Inc.

using Isto.Core.Enums;
using Isto.Core.Localization;
using System;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// List of languages. Used to define project specific supported languages in the ProjectContext.
/// </summary>
[CreateAssetMenu(fileName = "New Language List", menuName = "Scriptables/Languages/LanguageList")]
public class LanguageList : ScriptableObject
{
    [Serializable]
    public class LanguageSelectionDropdownListItem
    {
        [EnumDropdown(typeof(LanguageChanger.LanguageEnum))]
        public string languageName;

        public bool editorOnly;
    }
    
    // Not sure if this should be somewhere else. The asset was intended to be more generalized than
    // just "supported languages".
    [EnumDropdown(typeof(LanguageChanger.LanguageEnum))]
    [Tooltip("This only will be used if user's device language is not supported")] // seems tooltip does not work on this
    public string defaultLanguage;

    public List<LanguageSelectionDropdownListItem> languages;

    public List<LanguageChanger.LanguageEnum> GetLanguages()
    {
        List<LanguageChanger.LanguageEnum> enumValues = new List<LanguageChanger.LanguageEnum>();
        foreach (string langName in GetLanguageNames())
        {
            var langEnumValue = LanguageChanger.LanguageEnum.GetFromName(langName) as LanguageChanger.LanguageEnum;
            Debug.Assert(langEnumValue != null, $"LanguageEnum value not found from language name in LanguageList asset: {langName}", this);
            Debug.Assert(false, $"LanguageEnum value not found from language name in LanguageList asset: {langName}", this);
            enumValues.Add(langEnumValue);
        }
        return enumValues;
    }

    public List<string> GetLanguageNames()
    {
        List<string> langNames = new List<string>();
        foreach (LanguageSelectionDropdownListItem langItem in languages)
        {
            bool include = !langItem.editorOnly;
#if UNITY_EDITOR || DEVELOPMENT_BUILD
            include = true;
#endif
            if (include)
            {
                langNames.Add(langItem.languageName);
            }
        }
        return langNames;
    }
}
