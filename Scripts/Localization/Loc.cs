// Copyright Isto Inc.
using I2.Loc;
using TMPro;
using UnityEngine;

namespace Isto.Core.Localization
{
    /// <summary>
    /// The Localization manager that goes ontop of I2's localization
    /// </summary>
    public static class Loc
    {
        // Very basic version of localized string utilization, with universal default value.
        public static string Get(LocalizedString source)
        {
            return source != null ? source.ToString() : Constants.LOCALIZATION_KEY_NOT_FOUND;
        }

        public static string GetString(string key)
        {
            return GetTranslation(key);
        }

        public static string GetItemName(CoreItem item)
        {
            // items/bloodrock
            // continuebutton
            return GetTranslation("items/" + item.itemName.ToString());
        }

        public static void SetTMPro(TextMeshProUGUI tmpro, string key)
        {
            //Look for a style
            //If there is one, strip it out and save it
            // Ex string openingDef, string closing Def

            //Set a new text -> openingDef +  LocalizationManager.GetTranslation(str); + closingDef


            SetTMProLocalized(tmpro, GetString(key));
        }

        public static void SetTMPro(TextMeshProUGUI tmpro, LocalizedString source)
        {
            SetTMProLocalized(tmpro, Get(source));
        }

        /// <summary>
        /// Assigns the already translated text to the TextMeshPro asset, and updates the asset's font if needed.
        /// </summary>
        /// <param name="tmpro">The asset to assign the text to</param>
        /// <param name="text">The already localized text to assign</param>
        public static void SetTMProLocalized(TextMeshProUGUI tmpro, string text)
        {
            TMP_FontAsset currentFont = LocalizationManager.GetTranslatedObjectByTermName<TMP_FontAsset>(Constants.MAIN_FONT);
            if (tmpro.font != currentFont)
            {
                tmpro.font = currentFont;
            }

            tmpro.text = text;
        }

        public static void SetTMProLocalized(TextMeshPro tmpro, string text)
        {
            TMP_FontAsset currentFont = LocalizationManager.GetTranslatedObjectByTermName<TMP_FontAsset>(Constants.MAIN_FONT);
            if (tmpro.font != currentFont)
            {
                tmpro.font = currentFont;
            }

            tmpro.text = text;
        }

        // Localize Component should be configured to get the proper font already (in its secondary key), only need to update main term
        public static void SetLocalize(Localize localizer, LocalizedString source)
        {
            localizer.Term = source.mTerm;
            localizer.OnLocalize(true);
        }

        public static void SetTMProFont(TMP_InputField tmpro)
        {
            TMP_FontAsset currentFont = LocalizationManager.GetTranslatedObjectByTermName<TMP_FontAsset>(Constants.MAIN_FONT);
            if (tmpro.textComponent.font != currentFont)
            {
                tmpro.textComponent.font = currentFont;
            }
        }

        public static string GetTimeLocalizedAndFormated(float totalGameTimeSeconds)
        {
            int minutes = Mathf.FloorToInt(totalGameTimeSeconds / 60);
            int seconds = (int)totalGameTimeSeconds % 60;
            return $"{Loc.GetString(Constants.MINUTES_PREFIX)}{minutes}{Loc.GetString(Constants.MINUTES_SUFFIX)} {Loc.GetString(Constants.SECONDS_PREFIX)}{seconds}{Loc.GetString(Constants.SECONDS_SUFFIX)}";
        }

        // https://www.somacon.com/p576.php
        // Returns the human-readable file size for an arbitrary, 64-bit file size 
        // The default format is "0.### XB", e.g. "4.2 KB" or "1.434 GB"
        public static string GetFileSizeLocalizedAndFormated(long totalBytes)
        {
            // Get absolute value
            long absolute_total = (totalBytes < 0 ? -totalBytes : totalBytes);
            // Determine the suffix and readable value
            string suffix;
            double readable;
            if (absolute_total >= 0x1000000000000000) // Exabyte
            {
                suffix = "EB"; // TODO: localize these units? I'm sure everyone knows english units but they have alternatives in other alphabets
                readable = (totalBytes >> 50);
            }
            else if (absolute_total >= 0x4000000000000) // Petabyte
            {
                suffix = "PB";
                readable = (totalBytes >> 40);
            }
            else if (absolute_total >= 0x10000000000) // Terabyte
            {
                suffix = "TB";
                readable = (totalBytes >> 30);
            }
            else if (absolute_total >= 0x40000000) // Gigabyte
            {
                suffix = "GB";
                readable = (totalBytes >> 20);
            }
            else if (absolute_total >= 0x100000) // Megabyte
            {
                suffix = "MB";
                readable = (totalBytes >> 10);
            }
            else if (absolute_total >= 0x400) // Kilobyte
            {
                suffix = "KB";
                readable = totalBytes;
            }
            else
            {
                return totalBytes.ToString("0 B"); // Byte
            }

            // Divide by 1024 to get fractional value
            readable = (readable / 1024);

            // Return formatted number with suffix
            return readable.ToString("0.### ") + suffix;
        }

        private static string GetTranslation(string key)
        {
            string localizedText = LocalizationManager.GetTranslation(key);

            if (localizedText == null)
            {
                Debug.LogWarning("Unable to find term using key: " + key);
                localizedText = Constants.LOCALIZATION_KEY_NOT_FOUND;
            }

            return localizedText;
        }
    }
}
