// Copyright Isto Inc.

namespace Isto.Core.Localization
{
    /// <summary>
    /// Represents a non-localized string that uses the key as it's main value.
    /// </summary>
    public class NonLocalizedString : LocTerm
    {
        public NonLocalizedString(string locKey) : base(locKey)
        {
        }

        public override string Localize()
        {
            return _key;
        }

        /// <summary>
        /// Retrieves the key as a non-localized string. The intent of this is to allow for better readability for
        /// the user.
        /// </summary>
        /// <returns>The non-localized string value of the key.</returns>
        public string GetNonLocalizedString()
        {
            return _key;
        }
    }
}