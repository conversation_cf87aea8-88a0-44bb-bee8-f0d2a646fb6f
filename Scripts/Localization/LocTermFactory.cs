// Copyright Isto Inc.

using UnityEngine;
using Zenject;

namespace Isto.Core.Localization
{
    /// <summary>
    /// Factory class for creating LocTerm instances based on localization type and key. To bind this custom factory,
    /// ensure that the FromFactory method is used. Using the Create from the factory will return a LocTerm of the
    /// corresponding child class. Following this pattern ensures that there is a single factory that returns the
    /// correct LocTerm needed.
    /// Refer to the LocalizationTests for more examples of it's usages.
    /// </summary>
    /// <typeparam name="T">Type that inherits from LocTerm.</typeparam>
    public class LocTermFactory : IFactory<LocTerm.LocalizationType, string, LocTerm>
    {
        private DiContainer _container;

        [Inject]
        public void Inject(DiContainer container)
        {
            _container = container;
        }

        public LocTerm Create(LocTerm.LocalizationType locType, string key)
        {
            LocTerm locTerm = null;

            if (locType == LocTerm.LocalizationType.NonLocalized)
            {
                locTerm = new NonLocalizedString(key);
            }
            else if (locType == LocTerm.LocalizationType.Localized)
            {
                locTerm = new IstoLocalizedString(key);
            }
            else
            {
                Debug.LogError($"Unsupported type for {locType} within the CustomLocTermFactory.");
            }

            _container.Inject(locTerm);

            return locTerm;
        }
    }
}