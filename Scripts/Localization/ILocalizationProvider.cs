// Copyright Isto Inc.

namespace Isto.Core.Localization
{
    public interface ILocalizationProvider
    {
        public string GetLocalizedText(string locKey);
        public void SetLanguage(LanguageChanger.LanguageEnum language);
        public LanguageChanger.LanguageEnum GetCurrentLanguage();
        public string GetCurrentLanguageName();
        public string GetCurrentDeviceLanguage();
    }
}