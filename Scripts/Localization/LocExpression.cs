// Copyright Isto Inc.

using System.Collections.Generic;
using System.Linq;
using System.Text;
using TMPro;

namespace Isto.Core.Localization
{
    /// <summary>
    /// Abstract base class for localized expressions. It provides a mechanism to localize expressions by combining
    /// multiple localized terms.
    /// </summary>
    public abstract class LocExpression
    {
        protected List<LocTerm> _locTerms;

        public string Localize()
        {
            StringBuilder stringBuilder = new StringBuilder();
            foreach (LocTerm locTerm in _locTerms)
            {
                stringBuilder.Append(locTerm.Localize());
            }
            return stringBuilder.ToString();
        }
        
        public void LocalizeInto(TextMeshProUGUI tmpUGUI)
        {
            if (tmpUGUI != null)
            {
                tmpUGUI.text = Localize();
            }
        }
        
        public override bool Equals(object otherObject)
        {
            if (otherObject == null || GetType() != otherObject.GetType())
            {
                return false;
            }
    
            LocExpression other = (LocExpression) otherObject;
            
            List<LocTerm> thisLocTerms = _locTerms ?? new List<LocTerm>();
            List<LocTerm> otherLocTerms = other._locTerms ?? new List<LocTerm>();
            
            return thisLocTerms.SequenceEqual(otherLocTerms);
        }

        /// <summary>
        /// Gets the hashcode for the current <see cref="LocExpression"/> object.
        /// Getting a hashcode from a list of LocTerms. See the Wiki for more information on hashcodes.
        /// </summary>
        /// <returns>An integer hash code.</returns>
        public override int GetHashCode()
        {
            unchecked
            {
                if (_locTerms == null)
                    return 0;

                int hash = 17; // Using a prime number for the start of the hash
                foreach (var locTerm in _locTerms)
                {
                    hash *= 31 + (locTerm?.GetHashCode() ?? 0); // Using another prime number to combine hashes
                }
                return hash;
            }
        }
    }
}