using Isto.Core.Configuration;
using Isto.Core.Inputs;
using Isto.Core.StateMachine;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    public class CoreUISetGameModeSubState : MonoState
    {
        // UNITY HOOKUP

        public CanvasGroup canvasGroup = null;
        public Transform modeButtonsParent = null;

        // OTHER FIELDS

        private MonoPushdownStateMachine _mainMenu;
        private List<Selectable> _gameModeButtons = null;
        private Selectable _firstSelection = null;


        // PROPERTIES

        public enum MenuExitMode { Confirm, Cancel }
        public MenuExitMode ExitMode { get; private set; }
        public GameModeDefinition SelectedGameMode { get; private set; }


        // INJECTION

        protected IControls _controls;

        [Inject]
        public void Inject(IControls controls)
        {
            _controls = controls;
        }


        // LIFECYCLE METHODS

        private void Awake()
        {
            InitializeButtonsForNavigation();
        }

        private void InitializeButtonsForNavigation()
        {
            _gameModeButtons = new List<Selectable>(modeButtonsParent.GetComponentsInChildren<Selectable>());
            _firstSelection = _gameModeButtons.FirstOrDefault();
            UIUtils.SetupVerticalButtonsNavigation(_gameModeButtons);
        }

        public override void Enter(MonoStateMachine controller)
        {
            _mainMenu = controller as MonoPushdownStateMachine;



            Selectable firstSelection = _controls.UsingJoystick() ? _firstSelection : null;

            StartCoroutine(UIUtils.SetCanvasAlpha(canvasGroup, 1, Constants.CANVAS_FADE_TIME, true, firstSelection));

            ExitMode = MenuExitMode.Cancel;
        }

        public override void Exit(MonoStateMachine controller)
        {
            if (ExitMode == MenuExitMode.Cancel)
            {
                StartCoroutine(UIUtils.SetCanvasAlpha(canvasGroup, 0, Constants.CANVAS_FADE_TIME, false, null));
            }
            else
            {
                canvasGroup.interactable = false;
                canvasGroup.blocksRaycasts = false;
                canvasGroup.alpha = 0f;
            }

            _controls.DisableControls(Constants.BUTTON_SPAM_DELAY);
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
            Debug.LogWarning("UISetGameModeSubState not expected to have running substates. Cascading the Return.");
            // If somehow several menu states have been stacked, the menu setup is not currently designed for this. Try to recover by closing it all.
            (controller as MonoPushdownStateMachine)?.ExitSubState();
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            if (_controls.GetButton(UserActions.UICANCEL))
            {
                _mainMenu.ExitSubState();
            }

            return this;
        }


        // EVENT HANDLERS

        public void ModeButtonClicked(GameModeDefinition currentGameMode)
        {
            ExitMode = MenuExitMode.Confirm;
            SelectedGameMode = currentGameMode;
            _mainMenu.ExitSubState();
        }

        public void ExitAreaClicked()
        {
            _mainMenu.ExitSubState();
        }
    }
}