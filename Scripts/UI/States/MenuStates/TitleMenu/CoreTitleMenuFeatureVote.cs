using Isto.Core.UI;
using UnityEngine;

namespace Isto.Atrio
{
    public class CoreTitleMenuFeatureVote : MonoBehaviour
    {
        // UNITY HOOKUP
        
        [SerializeField] private string url = default;
        [SerializeField] private UISimpleConfirmModalState _confirmDialogue;
        [SerializeField] private SimpleTitleMenuStateMachine _titleMenu;

        public void OpenURL()
        {
#if PLATFORM_GAMECORE
            _confirmDialogue.SetCallbacks(OpenConfirmed, null);

            _titleMenu.EnterSubState(_confirmDialogue);
#else
            OpenConfirmed();
#endif
        }

        private void OpenConfirmed()
        {
            Application.OpenURL(url);
        }
    }
}
