//Copyrite Isto Inc. 2018
using Isto.Core.Inputs;
using Isto.Core.StateMachine;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    public class CoreUICreditsMenuState : MonoState
    {
        // UNITY HOOKUP

        [SerializeField] private Selectable _firstSelection = null;


        // OTHER FIELDS

        private MonoPushdownStateMachine _controller;


        // INJECTION
        
        private IControls _controls;

        [Inject]
        public void Inject(IControls controls)
        {
            _controls = controls;
        }


        // LIFECYCLE EVENTS

        public override void Enter(MonoStateMachine controller)
        {
            _controller = controller as MonoPushdownStateMachine;

            gameObject.SetActive(true);

            if (_firstSelection != null && Controls.UsingController)
                _firstSelection.Select();

            RegisterEvents();
        }

        public override void Exit(MonoStateMachine controller)
        {
            UnregisterEvents();

            gameObject.SetActive(false);
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
            Debug.LogError("Shouldn't happen");
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            if (_controls.GetButtonDown(UserActions.UICANCEL))
            {
                _controller.ExitSubState();
            }

            return this;
        }

        public void ExitSubState()
        {
            _controller.ExitSubState();
        }


        // EVENT HANDLING

        private void RegisterEvents()
        {

            Events.Subscribe(Events.INPUT_MODE_CHANGED, Events_OnInputModeChanged);
        }

        private void UnregisterEvents()
        {

            Events.UnSubscribe(Events.INPUT_MODE_CHANGED, Events_OnInputModeChanged);
        }

        private void Events_OnInputModeChanged()
        {
            if (Controls.UsingController)
            {
                _firstSelection.Select();
            }
            else
            {
                if (EventSystem.current.currentSelectedGameObject != null)
                {
                    EventSystem.current.SetSelectedGameObject(null);
                }
            }
        }
    }
}