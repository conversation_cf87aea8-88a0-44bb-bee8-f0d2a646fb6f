// Copyright Isto Inc.
using Isto.Core.Analytics;
using Isto.Core.Audio;
using Isto.Core.Cheats;
using Isto.Core.Configuration;
using Isto.Core.Data;
using Isto.Core.Game;
using Isto.Core.Inputs;
using Isto.Core.Localization;
using Isto.Core.StateMachine;
using Isto.Core.Themes;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    public class UISimpleTitleMenuState : MonoState
    {
        // UNITY HOOKUP

        [Header("Menu Parts")]
        public CanvasGroup canvas_Title;
        public CanvasGroup canvas_FadeOut;
        public float fadeTime = 1f;
        public Selectable startFocusObject;
        public GameObject quickPatchNotesRoot;
        public GameObject discordQRCode;

        [Header("Menu Navigation")]
        public Button newGameButton;
        public Button loadButton;
        public Button settingsButton;
        public Button creditsButton;
        public Button quitButton;
        public Button patchNotesButton;
        public Button latestVlogButton;
        public Button discordButton;

        [Header("Sub Menus")]
        public CoreUISetGameModeSubState gameModeSubstate;
        public CoreUISetSaveSlotSubState saveSlotSubstate;
        public UISettingsMenuState settingsSubState;
        public CoreUICreditsMenuState creditsSubState;
        public UISimpleConfirmModalState confirmOpenBrowserState;

        [Header("Feedback")]
        public string feedbackUrl;


        // OTHER FIELDS

        protected MonoPushdownStateMachine _controller;
        protected Selectable _previousSelection = null;

        private List<Selectable> _controllerNavigation = new List<Selectable>();


        // INJECTION

        protected IControls _controls;
        protected IGameData _gameData;
        protected IAnalyticsHandler _analytics;
        protected GameState _gameState;
        protected UISounds _uiSounds;
        protected ThemeManager _themeManager;
        protected UserKeybindingDataStore _controlsDataStore;

        [Inject]
        private void Inject(IControls controls, IGameData gameData, IAnalyticsHandler analytics,
            GameState gameState, UISounds uISounds, ThemeManager themeManager,
            UserKeybindingDataStore dataStore)
        {
            _controls = controls;
            _gameData = gameData;
            _analytics = analytics;

            _gameState = gameState;
            _uiSounds = uISounds;
            _themeManager = themeManager;
            _controlsDataStore = dataStore;
        }


        // LIFECYCLE EVENTS

        private void Awake()
        {
            InitializeButtonsForNavigation();
            UIUtils.SetupVerticalButtonsNavigation(_controllerNavigation);
        }

        private void OnDestroy()
        {
            UnregisterEvents();
        }

        public override void Enter(MonoStateMachine controller)
        {
            _controller = controller as MonoPushdownStateMachine;

            _previousSelection = newGameButton;

            SetupLoadButton();
            UpdateTheme();

            RegisterEvents();

            if (_controls.UsingJoystick())
            {
                startFocusObject.Select();
            }

            SetupControls();

            _controls.SetControlMode(Controls.Mode.UI);

            // this value doesn't really matter to the title menu but we're clearing the cache in case we're coming from in-game
            // so managers can check this to know if we're in the game or not
            _gameState.CurrentGameMode = null;
        }

        public override void Exit(MonoStateMachine controller)
        {
            // Warning: we don't go through a clean exit flow when exiting title screen to load game!
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            return this;
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
            _uiSounds.PlayButtonClickSound();

            SetupLoadButton();

            if (previousState == gameModeSubstate && gameModeSubstate.ExitMode == CoreUISetGameModeSubState.MenuExitMode.Confirm)
            {
                StartGameButtonClicked(gameModeSubstate.SelectedGameMode);
            }
            else
            {
                if (Controls.UsingController)
                {
                    EventSystem.current.SetSelectedGameObject(_previousSelection.gameObject);
                }

                StartCoroutine(UIUtils.SetCanvasAlpha(canvas_Title, targetAlpha: 1f, Constants.CANVAS_FADE_TIME, interactable: true));
            }
        }


        // EVENT HANDLERS

        protected virtual void RegisterEvents()
        {
            Events.Subscribe(Events.THEME_CHANGED, Events_OnThemeChanged);
            Events.Subscribe(Events.INPUT_MODE_CHANGED, Events_OnInputModeChanged);
        }

        protected virtual void UnregisterEvents()
        {
            Events.UnSubscribe(Events.THEME_CHANGED, Events_OnThemeChanged);
            Events.UnSubscribe(Events.INPUT_MODE_CHANGED, Events_OnInputModeChanged);
        }

        protected void Events_OnThemeChanged()
        {
            UpdateTheme();
        }

        private void Events_OnInputModeChanged()
        {
            if (_controller.GetCurrentState() != this)
                return;

            if (_controls.UsingJoystick())
            {
                // Ensure that a gameObject is always selected (in case the user clicks off the buttons and then tries to use controller)
                // TODO: cleanup hovered object state. doesn't seem to be stored in available info from EventSystem. might have to use the
                // controller connected event in UIButton & others.
                if (EventSystem.current.currentSelectedGameObject == null)
                {
                    startFocusObject.Select();
                }
            }
            else
            {
                // Conversely, clean up if controller is gone, this should have the selection graphics disappear
                if (EventSystem.current.currentSelectedGameObject != null)
                {
                    EventSystem.current.SetSelectedGameObject(null);
                }
            }
        }

        /// <summary>
        /// Might not be hooked up anywhere for now, or that code might just be disabled.
        /// User is important for association with cloud data. On console it is the only way we get save files.
        /// </summary>
        protected void Events_OnUserChanged()
        {
            SetupLoadButton();
        }

        public void NewGameButtonClicked()
        {
            _previousSelection = newGameButton;

            CloseAllStatesAndEnterNewState(gameModeSubstate);
        }

        public void StartGameButtonClicked(GameModeDefinition gameMode)
        {
            StartCoroutine(UIUtils.SetCanvasAlpha(canvas_Title, targetAlpha: 0f, fadeTime, interactable: false));
            StartCoroutine(UIUtils.SetCanvasAlpha(canvas_FadeOut, targetAlpha: 1f, fadeTime, interactable: false));
            StartCoroutine(WaitToExitTitleScreen(fadeTime, gameMode));
        }

        public void RunTestsButtonClicked()
        {
            Debug.LogError($"No default test to run right now.");
        }

        public void GCCollectButtonClicked()
        {
            GC.Collect();
        }

        public void DowngradeSaveButtonClicked()
        {
            _gameData.LoadGameMetaData(0, (data) =>
            {
                data.gameVersion = "0.0.1";
                _gameData.SaveGameMetaData(0, data, (success) =>
                {
                    Debug.LogError("Made save slot #0 into a fake 0.0.1 save file!");
                });
            });
        }

        public void GetRemainingDataButtonClicked()
        {
            Debug.LogError($"Sending GetRemainingSaveFileSpace request...");
            _gameData.GetRemainingSaveFileSpace((long size) =>
            {
                Debug.Log($"got callback for remaining file space size = {Loc.GetFileSizeLocalizedAndFormated(size)}");
            });
        }

        public void QuitButtonClicked()
        {

#if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
#else
            Application.Quit();
#endif
        }

        public void LoadButtonClicked()
        {
            // Button shouldn't even be interactable if there's no save data but this is just in case
            //if (!_gameData.HasSaveData()) return;

            MonoState current = _controller.GetCurrentState();

            if (current != saveSlotSubstate)
            {
                while (current != this && current != null)
                {
                    _controller.ExitSubState();

                    current = _controller.GetCurrentState();
                }

                _previousSelection = loadButton;
                saveSlotSubstate.SetMenuMode(CoreUISetSaveSlotSubState.MenuMode.Load);
                _controller.EnterSubState(saveSlotSubstate);
            }

            _analytics.RegisterMenuPageVisited("Load Menu");
        }

        public void ClearSaveButtonClicked()
        {
            _gameData.ClearAllSaveData((success) => { loadButton.interactable = !success; });
        }

        public void ClearPlayerPrefsButtonClicked()
        {
            PlayerPrefs.DeleteAll();
        }

        public void FeedbackButtonClicked()
        {
            Application.OpenURL(feedbackUrl);
            _analytics.RegisterMenuPageVisited("Feedback");
        }

        public void SettingsButtonClicked()
        {
            _previousSelection = settingsButton;

            CloseAllStatesAndEnterNewState(settingsSubState);

            _analytics.RegisterMenuPageVisited("Settings");
        }

        public void CreditsButtonClicked()
        {
            _previousSelection = creditsButton;

            CloseAllStatesAndEnterNewState(creditsSubState);

            _analytics.RegisterMenuPageVisited("Credits");
        }

        public virtual void PatchNotesButtonClicked()
        {
#if PLATFORM_GAMECORE
            confirmOpenBrowserState.SetCallbacks(OpenPatchNotesURL, null);

            _controller.EnterSubState(confirmOpenBrowserState);
#else
            OpenPatchNotesURL();
#endif
        }

        public void VlogButtonClicked()
        {
            _analytics.RegisterMenuPageVisited("Latest Vlog");
        }

        public void ClearNotificationsButtonClicked()
        {
            _analytics.RegisterMenuPageVisited("Clear Notifications");
        }


        // OTHER METHODS

        private void InitializeButtonsForNavigation()
        {
            _controllerNavigation.Clear();

            if (newGameButton.gameObject.activeSelf)
            {
                _controllerNavigation.Add(newGameButton);
            }
            if (loadButton.gameObject.activeSelf)
            {
                _controllerNavigation.Add(loadButton);
            }
            if (settingsButton.gameObject.activeSelf)
            {
                _controllerNavigation.Add(settingsButton);
            }
            if (creditsButton.gameObject.activeSelf)
            {
                _controllerNavigation.Add(creditsButton);
            }
            if (quitButton.gameObject.activeSelf)
            {
                _controllerNavigation.Add(quitButton);
            }
            if (patchNotesButton.gameObject.activeSelf)
            {
                _controllerNavigation.Add(patchNotesButton);
            }
            if (latestVlogButton.gameObject.activeSelf)
            {
                _controllerNavigation.Add(latestVlogButton);
            }
            if (discordButton.gameObject.activeSelf)
            {
                _controllerNavigation.Add(discordButton);
            }
        }

        private IEnumerator WaitToExitTitleScreen(float time, GameModeDefinition gameMode)
        {
            CheatCodeDetector.CheatsBlocked = true;
            yield return new WaitForSeconds(time);

            _gameState.StartGameMode(gameMode);
            CheatCodeDetector.CheatsBlocked = false;
        }

        protected void OpenPatchNotesURL()
        {
            Application.OpenURL("https://store.steampowered.com/news/app/1125390");

            _analytics.RegisterMenuPageVisited("Patch Notes");
        }

        protected void SetupLoadButton()
        {
            SetLoadButton(interactable: false);
            _gameData.HasAnySaveData((dataExists) =>
            {
                SetLoadButton(interactable: dataExists);
            });
        }

        /// <summary>
        /// Normally, simply setting the button to be interactable or not should suffice to have its visual state adjust
        /// properly. However in Atrio the setup was not ideal, so this method needs to be overridable to provide with
        /// some extra logic when the button interactability changes.
        /// </summary>
        protected virtual void SetLoadButton(bool interactable)
        {
            loadButton.interactable = interactable;
        }

        private void SetupControls()
        {
            _controlsDataStore.Load();
        }

        protected void CloseAllStatesAndEnterNewState(MonoState nextState)
        {
            _controls.DisableControls(0.25f);

            MonoState current = _controller.GetCurrentState();

            if (current != nextState)
            {
                while (current != this && current != null)
                {
                    _controller.ExitSubState();

                    current = _controller.GetCurrentState();
                }

                _controller.EnterSubState(nextState);
            }
        }

        protected virtual void UpdateTheme()
        {

        }
    }
}