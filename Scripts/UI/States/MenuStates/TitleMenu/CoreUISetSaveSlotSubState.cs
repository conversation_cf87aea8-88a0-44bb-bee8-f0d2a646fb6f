// Copyright Isto Inc.
using I2.Loc;
using Isto.Core.Data;
using Isto.Core.Game;
using Isto.Core.Inputs;
using Isto.Core.Localization;
using Isto.Core.StateMachine;
using System;
using System.Collections;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    public class CoreUISetSaveSlotSubState : MonoState, IStateExitResult
    {
        // UNITY HOOKUP

        [SerializeField] private LocalizedString _gameSavedMessageKey;
        [SerializeField] private LocalizedString _loadGameTitleKey;
        [SerializeField] private LocalizedString _saveGameTitleKey;
        [SerializeField] private LocalizedString _slotCountKey;

        [SerializeField] private TextMeshProUGUI _titleLabel = null;
        [SerializeField] private CanvasGroup _canvasGroup = null;
        [SerializeField] private TextMeshProUGUI _spaceRemainingLabel = null;
        [SerializeField] private CoreUISaveSlotList _saveSlotsList;
        [SerializeField] private TMP_Text _saveSlotCountLabel = null;
        [SerializeField] private CoreUISaveSlotDisplay _saveSlotDisplay;
        [SerializeField] private Button _exitAreaButton;

        [Header("File Renaming")]
        [SerializeField] private TextMeshProUGUI _renameMessageText;
        [SerializeField] private float _timeToShowRenameMessage = 3f;


        // OTHER FIELDS

        private MonoPushdownStateMachine _mainMenu;
        private MenuMode _currentMode;
        private float _spamTimer;
        private int _currentSelectedSaveSlotNumber = -1;
        private bool _open = false;


        // PROPERTIES

        public enum MenuMode { Save, Load }
        public IStateExitResult.ResultType Result { get; private set; }


        // INJECTION
        private IControls _controls;
        private IUIMessages _uiMessages;
        private IGameData _gameData;
        private GameState _gameState;

        [Inject]
        public void Inject(IControls controls, [Inject(Optional = true, Id = UIMessageHandlerType.Message)] IUIMessages uIMessages, IGameData gameData, GameState gameState)
        {
            _controls = controls;
            _uiMessages = uIMessages;
            _gameData = gameData;
            _gameState = gameState;
        }

        public void Awake()
        {
            _exitAreaButton.interactable = false;
        }

        public override void Enter(MonoStateMachine controller)
        {
            _mainMenu = controller as MonoPushdownStateMachine;

            _spamTimer = Constants.CANVAS_FADE_TIME;
            _open = true;

            //We won't select the slot button until the slot list is finished loading, but in the meantime,
            //we can't let you keep the selection in the title menu buttons!
            EventSystem.current.SetSelectedGameObject(null);
            RefreshSaveList();

            StartCoroutine(UIUtils.SetCanvasAlpha(_canvasGroup, targetAlpha: 1f, Constants.CANVAS_FADE_TIME, interactable: true));

            StartCoroutine(UpdateFileSpaceLabel());

            _controls.DisableControls(Constants.BUTTON_SPAM_DELAY);
        }

        public override void Exit(MonoStateMachine controller)
        {
            _open = false;
            _exitAreaButton.interactable = false;

            StopAllCoroutines();
            _saveSlotsList.ClearListItems();

            if (Controls.UsingController)
            {
                EventSystem.current.SetSelectedGameObject(null);
            }

            StartCoroutine(UIUtils.SetCanvasAlpha(_canvasGroup, targetAlpha: 0f, Constants.CANVAS_FADE_TIME, interactable: false));

            _controls.DisableControls(Constants.BUTTON_SPAM_DELAY);

            _renameMessageText.enabled = false;
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
            if (previousState is IStateExitResult stateResult)
            {
                Debug.Log("Coming back from Upgrade Menu, choice: " + stateResult.Result);

                switch (stateResult.Result)
                {
                    // In case of submit, we need to show the new backup we created in the list
                    case IStateExitResult.ResultType.Submit:
                    // In case of error, the backup could still exist, and the meta data could be out of date
                    // I'm working to prevent any weird interrim status/garbage from persisting, but at least this ensures that
                    // if something does happen, we see it
                    case IStateExitResult.ResultType.Error:
                        RefreshSaveList(_currentSelectedSaveSlotNumber);
                        break;
                    case IStateExitResult.ResultType.Cancelled:
                        // TODO: re-enable the load button if the upgrade is cancelled
                        // right now it's grayed out and disabled, so if you cancel and want to upgrade again, you have to select
                        // a different slot first and then come back to this one


                        break;
                    default:
                        Debug.LogError($"UISetSaveSlotSubState.ReturnFromSubState: Unhandled IStateExitResult.ResultType \"{stateResult.Result}\" from previousState of type {previousState.GetType()} on object {previousState.name}");
                        break;
                }

                _saveSlotDisplay.RestorePreviousSelection();

            }
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            if (_controls.GetButtonDown(UserActions.UICANCEL) && !_saveSlotDisplay.IsBlockingForPopup)
            {
                Result = IStateExitResult.ResultType.Cancelled;
                _mainMenu.ExitSubState();
            }

            _spamTimer -= Time.unscaledDeltaTime;

            if (_spamTimer < 0f)
                _exitAreaButton.interactable = true;

            return this;
        }

        private IEnumerator UpdateFileSpaceLabel()
        {
            long freeBytes = 0, maxBytes = 0;
            short blocking = 2;
            _gameData.GetRemainingSaveFileSpace((bytes) => { freeBytes = bytes; blocking--; });
            _gameData.GetMaximumSaveFileSpace((bytes) => { maxBytes = bytes; blocking--; });
            while (blocking > 0)
                yield return null;
            _spaceRemainingLabel.text = $"{Loc.GetFileSizeLocalizedAndFormated(freeBytes)} free of {Loc.GetFileSizeLocalizedAndFormated(maxBytes)}";
        }

        public void SetMenuMode(MenuMode mode)
        {
            _currentMode = mode;
            if (_titleLabel != null && string.IsNullOrEmpty(_loadGameTitleKey.mTerm) && string.IsNullOrEmpty(_saveGameTitleKey.mTerm))
                Loc.SetTMPro(_titleLabel, mode == MenuMode.Load ? _loadGameTitleKey : _saveGameTitleKey);
        }

        /// <summary>
        /// Saves or loads the game according to the chosen slot number.
        /// </summary>
        public void SaveOrLoadButtonClicked(int slotNumber)
        {
            _controls.DisableControls(Constants.BUTTON_SPAM_DELAY);

            Result = IStateExitResult.ResultType.Submit;

            switch (_currentMode)
            {
                case MenuMode.Save:
                    _gameData.SaveGameData(slotNumber, createBackup: true, (success) =>
                    {
                        if (!success)
                        {
                            // The internal cause of the error should be causing an error dialog popup to happen
                            Debug.LogError($"SaveGameData for slot#{slotNumber} has failed ");
                            return;
                        }

                        Events.RaiseEvent(Events.GAME_SAVED);

                        ShowGameSavedConfirmationMessage();

                        // Assume user is done and auto-exit
                        _mainMenu.ExitSubState();

                        // Update auto-save flag if needed
                        _gameData.LoadGameMetaData(slotNumber, (metaData) =>
                        {
                            if (metaData == null)
                            {
                                Debug.LogError($"LoadSaveSlotMetaData for slot#{slotNumber}, right after saving it, has failed ");
                                return;
                            }

                            if (metaData.isAutoSave)
                            {
                                metaData.isAutoSave = false;

                                _gameData.SaveGameMetaData(slotNumber, metaData, (saveSuccess) =>
                                //_gameState.UpdateSaveSlotMetaData(slotNumber, metaData, (saveSuccess) =>
                                {
                                    if (!saveSuccess)
                                        Debug.LogWarning($"SaveGameMetaData for slot#{slotNumber} has failed to save metadata after turning the autosave flag OFF ");
                                });
                            }
                        });
                    });
                    break;
                case MenuMode.Load:
                    _gameData.HasSaveData(slotNumber, (dataExists) =>
                    {
                        if (dataExists)
                        {
                            _mainMenu.ExitSubState();
                            _gameState.LoadSaveGame(slotNumber);
                        }
                    });
                    break;
                default:
                    break;
            }
        }

        public void ShowGameSavedConfirmationMessage()
        {
            if (_uiMessages != null)
            {
                _uiMessages.CreateMessage(Loc.Get(_gameSavedMessageKey));
            }
        }

        public void ExitAreaClicked()
        {
            Result = IStateExitResult.ResultType.Cancelled;

            _mainMenu.ExitSubState();
        }

        public void RefreshSaveList(int selectedSlot = -1, Action callback = null)
        {
            if (_saveSlotsList == null)
                return;

            _saveSlotsList.SetSlotMenuMode(_currentMode);
            _saveSlotsList.ClearListItems();

            _gameData.GetAllExistingSaveSlots((slots) =>
            {
                if (!_open)
                    return;

                int readySlots = 0;

                if (slots.Count == 0)
                    _saveSlotsList.NewSaveFileButton.Select();

                foreach (int num in slots)
                {
                    _saveSlotsList.SetupSaveSlot(num, selected: num == selectedSlot,
                        () =>
                        {
                            if (!_open)
                                return;

                            // We don't know in what order the slots will be ready so we must wait for all of them to be ready
                            // Eventually we could switch to a more intelligent way to insert them as they load for earlier feedback
                            // but that would be messing with a lot of bits.
                            // Might be good enough to just load the menu page instantly and put a loading sign while the slots are loading?
                            // This can be pretty slow on console though as you are constrained by internet and could have 50 slots to download
                            readySlots++;
                            if (readySlots >= slots.Count)
                            {
                                StartCoroutine(DelayedRebuildNavigation(selectedSlot == -1));
                            }
                        });
                }

                _saveSlotCountLabel.text = $"({Loc.Get(_slotCountKey)} {slots.Count}/{_gameData.MaximumSaveSlots})";
            });

            callback?.Invoke();
        }

        private IEnumerator DelayedRebuildNavigation(bool resetSelection)
        {
            yield return null;

            if (!_open)
                yield break;

            _saveSlotsList.RebuildNavigation();

            if (resetSelection)
            {
                _saveSlotsList.SelectFirstSlot();
                Vector2 anchoredPos = _saveSlotsList.RectTransform.anchoredPosition;
                anchoredPos.y = 0f;
                _saveSlotsList.RectTransform.anchoredPosition = anchoredPos;
            }
        }

        public void SetCurrentSelectedSaveSlot(int slotNumber)
        {
            _currentSelectedSaveSlotNumber = slotNumber;
        }

        public void ShowRenameTextMessage()
        {
            StartCoroutine(ShowRenameRoutine());
        }

        private IEnumerator ShowRenameRoutine()
        {
            _renameMessageText.enabled = true;

            yield return new WaitForSecondsRealtime(_timeToShowRenameMessage);

            _renameMessageText.enabled = false;
        }

        public void OnTextFieldSubmit()
        {
            if (_currentSelectedSaveSlotNumber != -1 && _currentMode == MenuMode.Save)
            {
                // This method is named from the old version of the save menu where you would click on a slot to save to it.
                // It is not to select a save slot; it directly causes a save to happen.
                // I don't think we want to actually save the game just from pressing enter in the name text box.
                // I don't know why it was hooked up here, but I've disabled it and behavior seems good now.

                //SaveSlotClicked(_currentSelectedSaveSlotNumber, false);
            }
        }

        private void EnableExitZoneButton()
        {
            _exitAreaButton.interactable = true;
        }
    }
}