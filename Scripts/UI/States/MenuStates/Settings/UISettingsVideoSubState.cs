// Copyright Isto Inc.

using I2.Loc;
using Isto.Core.Localization;
using Isto.Core.StateMachine;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    /// <summary>
    /// Sub-state class for managing the games video settings.
    /// </summary
    public class UISettingsVideoSubState : UISettingsBaseSubState
    {
        // UNITY HOOKUP

        [Header("Display Category")]
        [SerializeField] private UISettingsOptionCarousel _windowModeCarousel;
        [SerializeField] private UISettingsOptionLabel _windowModeLabel;
        [SerializeField] private UISettingsOptionCarousel _resolutionCarousel;
        [SerializeField] private UISettingsOptionLabel _resolutionLabel;
        [SerializeField] private UISettingsOptionCarousel _refreshRateCarousel;
        [SerializeField] private UISettingsOptionToggle _vSyncToggle;

        [Header("Window Mode Localization")]
        [SerializeField] private LocalizedString _displayModeWindowed;
        [SerializeField] private LocalizedString _displayModeMaximizedWindow;
        [SerializeField] private LocalizedString _displayModeFullScreenWindow;
        [SerializeField] private LocalizedString _displayModeExclusiveFullScreen;

        [Header("Measurement Units Localization")]
        [SerializeField] private LocalizedString _unitHertz;


        // OTHER FIELDS

        public static readonly string REFRESH_RATE_PLAYER_PREFS_KEY = "RefreshRateSetting";
        public static readonly string RESOLUTION_HEIGHT_PLAYER_PREFS_KEY = "ResolutionHeightSetting";
        public static readonly string RESOLUTION_WIDTH_PLAYER_PREFS_KEY = "ResolutionWidthSetting";
        public static readonly string VSYNC_PLAYER_PREFS_KEY = "VSyncSetting";
        private static readonly float REFRESH_RATE_COMPARISON_TOLERANCE = 0.001f;
        private static readonly bool VSYNC_DEFAULT_VALUE = true;

        private List<RefreshRate> _availableRefreshRates = new List<RefreshRate>();
        private List<Resolution> _availableResolutions = new List<Resolution>();

        // Incoming settings - This is used for the settings that will be applied AFTER the Apply button has been pressed
        private FullScreenMode _incomingDisplayModeValue;
        private RefreshRate _incomingRefreshRate;
        private Resolution _incomingResolutionValue;
        private bool _incomingVSyncValue;


        // Previous settings - These are used in case the user decides to cancel their changes.
        private FullScreenMode _previousDisplayModeValue;
        private RefreshRate _previousRefreshRate;
        private Resolution _previousResolutionValue;
        private bool _previousVSyncValue;

        private List<FullScreenMode> _windowModes = new List<FullScreenMode>();
        private Dictionary<FullScreenMode, LocExpression> _screenModeDictionary = new Dictionary<FullScreenMode, LocExpression>();


        // PROPERTIES

        public override bool HasChanges => IsVideoSettingsChanged();
        public override Selectable FirstSelectable => DefaultSelectable;
        public override Selectable DefaultSelectable => GetDefaultSelectable();
        public override SettingsSubMenusEnum SettingsSubMenu => SettingsSubMenusEnum.Video;

        public override bool RequiresUserValidation => true; // Consider not requiring it for vsync changes?


        // INJECTION

        protected LocTerm.Factory _locTermFactory;

        [Inject]
        public virtual void Inject(LocTerm.Factory locTermFactory)
        {
            _locTermFactory = locTermFactory;
        }


        // LIFECYCLE EVENTS

        private void Awake()
        {
            InitializeScreenModeDictionary();
        }

        private void Start()
        {
            SetupSelectableNavigation(transform);
        }

        public override void Enter(MonoStateMachine controller)
        {
            base.Enter(controller);

#if PLATFORM_GAMECORE
            SetupXboxDisplay();
#else
            SetupPCDisplay();
#endif
            InitializeCarousels();
        }


        // EVENT HANDLING

        protected override void RegisterEvents()
        {
            base.RegisterEvents();
            _windowModeCarousel.OnValueChanged += WindowModeCarousel_OnValueChanged;
            _resolutionCarousel.OnValueChanged += ResolutionCarousel_OnValueChanged;
            _refreshRateCarousel.OnValueChanged += RefreshRate_OnValueChanged;
            _vSyncToggle.OnValueChanged += VSyncToggle_OnValueChanged;
        }

        protected override void UnregisterEvents()
        {
            base.UnregisterEvents();
            _windowModeCarousel.OnValueChanged -= WindowModeCarousel_OnValueChanged;
            _resolutionCarousel.OnValueChanged -= ResolutionCarousel_OnValueChanged;
            _refreshRateCarousel.OnValueChanged -= RefreshRate_OnValueChanged;
            _vSyncToggle.OnValueChanged -= VSyncToggle_OnValueChanged;
        }

        protected override void ApplySettings_OnSubmit()
        {
            ApplySettingsForUserValidation();
            SetupConfirmationSubState();
        }

        private void WindowModeCarousel_OnValueChanged(LocExpression windowMode)
        {
            FullScreenMode fullScreenMode = _screenModeDictionary.FirstOrDefault(keyValuePair => Equals(keyValuePair.Value, windowMode)).Key;
            _incomingDisplayModeValue = fullScreenMode;

            _windowModeCarousel.SetDirtyFlag(_previousDisplayModeValue != _incomingDisplayModeValue);
        }

        private void ResolutionCarousel_OnValueChanged(LocExpression resolutionLocExpression)
        {
            _incomingResolutionValue = ParseLocExpressionToResolution((MultiTermLocExpression)resolutionLocExpression);
            _resolutionCarousel.SetDirtyFlag(IsResolutionChanged());
        }

        private void RefreshRate_OnValueChanged(LocExpression refreshRateLocExpression)
        {
            _incomingRefreshRate = ParseLocExpressionToRefreshRate((MultiTermLocExpression)refreshRateLocExpression);
            _refreshRateCarousel.SetDirtyFlag(IsRefreshRateChanged());
        }

        private void VSyncToggle_OnValueChanged(bool isVsyncEnabled)
        {
            _incomingVSyncValue = isVsyncEnabled;
            _vSyncToggle.SetDirtyFlag(_previousVSyncValue != _incomingVSyncValue);
        }


        // ACCESSORS

        protected virtual bool IsVideoSettingsChanged()
        {
            return _previousDisplayModeValue != _incomingDisplayModeValue ||
                   IsResolutionChanged() ||
                   IsRefreshRateChanged() ||
                   _previousVSyncValue != _incomingVSyncValue;
        }

        private void SetVSyncUI(bool isVsyncEnabled)
        {
            PlayerPrefs.SetInt(VSYNC_PLAYER_PREFS_KEY, isVsyncEnabled ? 1 : 0);
            _vSyncToggle.SetToggleValueNoNotify(isVsyncEnabled);
        }

        private void SetResolution(Resolution newResolution, RefreshRate newRefreshRate)
        {
            Screen.SetResolution(newResolution.width, newResolution.height, Screen.fullScreenMode, newRefreshRate);
            PlayerPrefs.SetInt(RESOLUTION_WIDTH_PLAYER_PREFS_KEY, newResolution.width);
            PlayerPrefs.SetInt(RESOLUTION_HEIGHT_PLAYER_PREFS_KEY, newResolution.height);
            PlayerPrefs.SetFloat(REFRESH_RATE_PLAYER_PREFS_KEY, (float)newRefreshRate.value);

            _resolutionCarousel.SetCarouselSelectionNoNotify(FormatResolutionToLocExpression(newResolution));
            _refreshRateCarousel.SetCarouselSelectionNoNotify(FormatRefreshRateToLocExpression(newRefreshRate));
        }

        private bool IsResolutionChanged()
        {
            return _incomingResolutionValue.width != _previousResolutionValue.width || _incomingResolutionValue.height != _previousResolutionValue.height;
        }

        private bool IsRefreshRateChanged()
        {
            bool isRefreshRateChanged = false;
            if (Screen.fullScreenMode == FullScreenMode.ExclusiveFullScreen)
            {
                isRefreshRateChanged = !_previousRefreshRate.value.Approx(_incomingRefreshRate.value, REFRESH_RATE_COMPARISON_TOLERANCE);
            }
            return isRefreshRateChanged;
        }

        private void SetDisplayModeUI(FullScreenMode displayMode)
        {
            Screen.fullScreenMode = displayMode;
            _windowModeCarousel.SetCarouselSelectionNoNotify(GetWindowModeText(displayMode));
        }

        private LocExpression GetWindowModeText(FullScreenMode mode)
        {
#if UNITY_GAMECORE_XBOXONE
            mode = FullScreenMode.ExclusiveFullScreen; // Value is -1 on xbox one
#endif
            string value = "";
            switch (mode)
            {
                case FullScreenMode.ExclusiveFullScreen:
                    value = _displayModeExclusiveFullScreen.mTerm;
                    break;
                case FullScreenMode.FullScreenWindow:
                    value = _displayModeFullScreenWindow.mTerm;
                    break;
                case FullScreenMode.MaximizedWindow:
                    value = _displayModeMaximizedWindow.mTerm;
                    break;
                case FullScreenMode.Windowed:
                    value = _displayModeWindowed.mTerm;
                    break;
                default:
                    Debug.LogError($"Cannot localize dropdown text for unexpected value of type FullScreenMode ({mode})");
                    break;
            }

            LocTerm screenMode = _locTermFactory.Create(LocTerm.LocalizationType.Localized, value);

            return new SingleTermLocExpression(screenMode);
        }

        private Resolution GetCurrentResolution()
        {
            return Screen.currentResolution;
        }

        private RefreshRate GetCurrentRefreshRate()
        {

#if PLATFORM_GAMECORE
            // For xbox, we appear to not have any reliable way to get the current refresh rate, so we track it from settings
            RefreshRate currentRefreshRate = new RefreshRate
            {
                numerator = (uint)PlayerPrefs.GetFloat(REFRESH_RATE_PLAYER_PREFS_KEY, defaultValue: 60f),
                denominator = 1
            };
#else
            Resolution currenResolution = GetCurrentResolution();
            RefreshRate currentRefreshRate = currenResolution.refreshRateRatio;
#endif

            return GetRoundedRefreshRate(currentRefreshRate);
        }

        private RefreshRate GetRoundedRefreshRate(float refreshRateValue)
        {
            return new RefreshRate
            {
                numerator = (uint)Math.Round(refreshRateValue),
                denominator = 1
            };
        }

        private RefreshRate GetRoundedRefreshRate(RefreshRate refreshRate)
        {
            return GetRoundedRefreshRate((float)refreshRate.value);
        }

        private UISettingsSelectable GetDefaultSelectable()
        {
            UISettingsSelectable currentDefaultSelectable = _windowModeCarousel;
#if PLATFORM_GAMECORE
            currentDefaultSelectable = _windowModeLabel;
#endif
            return currentDefaultSelectable;
        }


        // OTHER METHODS

        public override void ApplySettingsForUserValidation()
        {
            ApplyAllVideoSettings();
        }

        private void SetupPCDisplay()
        {
            _resolutionLabel.gameObject.SetActive(false);
            _windowModeLabel.gameObject.SetActive(false);

            _resolutionCarousel.gameObject.SetActive(true);
            _windowModeCarousel.gameObject.SetActive(true);
            UIUtils.SetNavigation(_windowModeCarousel, UIUtils.NavigationDirection.Down, _resolutionCarousel);
            UIUtils.SetNavigation(_resolutionCarousel, UIUtils.NavigationDirection.Up, _windowModeCarousel);
        }

        private void SetupXboxDisplay()
        {
            _resolutionLabel.gameObject.SetActive(true);
            _windowModeLabel.gameObject.SetActive(true);

            _resolutionCarousel.gameObject.SetActive(false);
            _windowModeCarousel.gameObject.SetActive(false);
            UIUtils.SetNavigation(_windowModeCarousel, UIUtils.NavigationDirection.Down, _vSyncToggle);
            UIUtils.SetNavigation(_vSyncToggle, UIUtils.NavigationDirection.Up, _windowModeCarousel);
        }

        /// <summary>
        /// In the case of the Video settings, in this part of the flow, we already have applied the settings so they
        /// can be previewed. Now it's time to lock them in.
        /// </summary>
        public override void ApplySettingsChanges()
        {
            base.ApplySettingsChanges();

            UpdateSettingsValues();
            InitializeResolutionCarousel();
            InitializeRefreshRateCarousel();
            ApplyAllVideoSettings();
        }

        public override void DiscardSettingsChanges()
        {
            base.DiscardSettingsChanges();

            _previousResolutionValue.refreshRateRatio = _previousRefreshRate;

            _incomingResolutionValue = _previousResolutionValue;
            _incomingRefreshRate = _previousRefreshRate;
            _incomingDisplayModeValue = _previousDisplayModeValue;
            _incomingVSyncValue = _previousVSyncValue;

            SetDisplayModeUI(_incomingDisplayModeValue);
            SetResolution(_incomingResolutionValue, _incomingRefreshRate);
            SetVSyncUI(_incomingVSyncValue);

            UpdateSettingsValues();
            DiscardRefreshRateCarousel();
            ApplyAllVideoSettings();
        }

        public override void ResetToDefaults()
        {
#if UNITY_GAMECORE
            // Note: on xbox we hide this page so don't mess with the settings.
            return;
#else
            // Note: Ignore resolution and display mode when restoring defaults. Changing them is risky.

            // Since we won't be assigning all the values, first make sure we don't keep any dirty fields.
            DiscardSettingsChanges();

            // Then reset all settings that we want to allow being reset.
            _incomingVSyncValue = VSYNC_DEFAULT_VALUE;
            SetVSyncUI(_incomingVSyncValue);

            // And save them.
            ApplySettingsChanges();
#endif
        }

        private void SetupConfirmationSubState()
        {
            _confirmationSubState.SetTimer(10);
            _confirmationSubState.SetCallbacks(ApplySettingsChanges, DiscardSettingsChanges);
            _confirmationSubState.SetMainText(Loc.GetString(Constants.UI_SETTINGS_MESSAGE_KEEP_SETTINGS));
            _menuController.EnterSubState(_confirmationSubState);
        }

        private void ApplyAllVideoSettings()
        {
            ApplyResolutionSettings();
            ApplyDisplayModeSettings();
            ApplyVsyncSettings();
            PlayerPrefs.Save();
        }

        private void ApplyResolutionSettings()
        {
            Resolution currentResolution = GetCurrentResolution();
            RefreshRate currentRefreshRate = GetCurrentRefreshRate();

            if (!_incomingResolutionValue.IsEquivalentTo(currentResolution) || !_incomingRefreshRate.value.Approx(currentRefreshRate.value, REFRESH_RATE_COMPARISON_TOLERANCE))
            {
                SetResolution(_incomingResolutionValue, _incomingRefreshRate);
            }
        }

        private void ApplyDisplayModeSettings()
        {
            if (_incomingDisplayModeValue != Screen.fullScreenMode)
            {
                Screen.fullScreenMode = _incomingDisplayModeValue;
            }
        }

        private void ApplyVsyncSettings()
        {
            bool isVsyncEnabled = PlayerPrefs.GetInt(VSYNC_PLAYER_PREFS_KEY) == 1;
            if (_incomingVSyncValue != isVsyncEnabled)
            {
                QualitySettings.vSyncCount = _incomingVSyncValue ? 1 : 0;
                PlayerPrefs.SetInt(VSYNC_PLAYER_PREFS_KEY, _incomingVSyncValue ? 1 : 0);
            }
        }

        private void UpdateSettingsValues()
        {
            _previousResolutionValue = _incomingResolutionValue;
            _previousRefreshRate = _incomingRefreshRate;
            _previousDisplayModeValue = _incomingDisplayModeValue;
            _previousVSyncValue = _incomingVSyncValue;
        }

        private void InitializeCarousels()
        {
            InitializeWindowModeCarousel();
            InitializeResolutionCarousel();
            InitializeRefreshRateCarousel();
            InitializeVsyncToggle();
        }

        private void InitializeWindowModeCarousel()
        {
            _windowModes = new List<FullScreenMode>((FullScreenMode[])Enum.GetValues(typeof(FullScreenMode)));

            _previousDisplayModeValue = Screen.fullScreenMode;
            _incomingDisplayModeValue = _previousDisplayModeValue;

            if (_windowModeLabel.gameObject.activeSelf)
            {
                _windowModeLabel.SetLabel(GetWindowModeText(_incomingDisplayModeValue));
            }
            else
            {
                List<LocExpression> windowModesLocTerms = new List<LocExpression>();
                foreach (FullScreenMode mode in _windowModes)
                {
                    windowModesLocTerms.Add(GetWindowModeText(mode));
                }
                _windowModeCarousel.SetCarouselList(windowModesLocTerms, GetWindowModeText(_incomingDisplayModeValue));
                _windowModeCarousel.SetDirtyFlag(false);
            }
        }

        private void InitializeResolutionCarousel()
        {
            // Go through the list of resolutions and filter out duplicate screen width and height to ensure we only
            // have one of each. This is because the list of resolutions include the refresh rate.
            List<Resolution> resolutions = Screen.resolutions.Select(resolution =>
                new Resolution { width = resolution.width, height = resolution.height }).Distinct().ToList();

            Resolution currentWindowResolution = new Resolution
            {
                width = Screen.width,
                height = Screen.height,
                refreshRateRatio = GetCurrentRefreshRate()
            };

            if (!resolutions.Any(currentRes =>
                    currentRes.width == currentWindowResolution.width &&
                    currentRes.height == currentWindowResolution.height))
            {
                resolutions.Add(currentWindowResolution);
            }

            // Ordering the available resolutions by height
            _availableResolutions = resolutions.OrderBy(resolution => resolution.height).ToList();

            _previousResolutionValue = currentWindowResolution;
            _incomingResolutionValue = _previousResolutionValue;
            MultiTermLocExpression currentResolutionExpression = FormatResolutionToLocExpression(_previousResolutionValue);

            if (_resolutionLabel.gameObject.activeSelf)
            {
                _resolutionLabel.SetLabel(currentResolutionExpression);
            }
            else
            {
                List<MultiTermLocExpression> currentLocExpressions = new List<MultiTermLocExpression>();
                foreach (Resolution resolution in _availableResolutions)
                {
                    currentLocExpressions.Add(FormatResolutionToLocExpression(resolution));
                }
                _resolutionCarousel.SetCarouselList(currentLocExpressions, currentResolutionExpression);
                _resolutionCarousel.SetDirtyFlag(false);
            }
        }

        private void InitializeRefreshRateCarousel()
        {
            SetupRefreshRateCarousel(GetCurrentRefreshRate());
        }

        private void DiscardRefreshRateCarousel()
        {
            RefreshRate currentRefreshRate = _incomingRefreshRate;
            if (double.IsNaN(currentRefreshRate.value))
            {
                currentRefreshRate = GetCurrentRefreshRate();
            }

            SetupRefreshRateCarousel(currentRefreshRate);
        }

        private void SetupRefreshRateCarousel(RefreshRate initialRefreshRate)
        {
            _refreshRateCarousel.gameObject.SetActive(Screen.fullScreenMode == FullScreenMode.ExclusiveFullScreen);

            if (Screen.fullScreenMode == FullScreenMode.ExclusiveFullScreen)
            {
                RefreshRate currentRefreshRate = initialRefreshRate;
                Resolution currentResolution = _incomingResolutionValue;

                if (currentResolution.IsNullOrDestroyed())
                {
                    currentResolution = GetCurrentResolution();
                }

                List<RefreshRate> refreshRates = new List<RefreshRate>();
                foreach (Resolution resolution in Screen.resolutions)
                {
                    if (resolution.width == currentResolution.width && resolution.height == currentResolution.height)
                    {
                        RefreshRate refreshRate = GetRoundedRefreshRate(resolution.refreshRateRatio);
                        if (!refreshRates.Contains(refreshRate))
                        {
                            refreshRates.Add(refreshRate);
                        }
                    }
                }

                if (!refreshRates.Contains(currentRefreshRate))
                {
                    refreshRates.Add(currentRefreshRate);
                }

                _availableRefreshRates = refreshRates.OrderBy(refreshRate => refreshRate.value).ToList();
                _previousRefreshRate = currentRefreshRate;
                _incomingRefreshRate = _previousRefreshRate;
                MultiTermLocExpression refreshRateLoc = FormatRefreshRateToLocExpression(_incomingRefreshRate);

                List<MultiTermLocExpression> refreshRatesLoc = new List<MultiTermLocExpression>();
                foreach (RefreshRate rate in _availableRefreshRates)
                {
                    refreshRatesLoc.Add(FormatRefreshRateToLocExpression(rate));
                }

                _refreshRateCarousel.SetCarouselList(refreshRatesLoc, refreshRateLoc);
                _refreshRateCarousel.SetDirtyFlag(false);
            }
        }

        private void InitializeVsyncToggle()
        {
            bool isVsyncEnabled = PlayerPrefs.GetInt(VSYNC_PLAYER_PREFS_KEY, 1) == 1;

            _previousVSyncValue = isVsyncEnabled;
            _incomingVSyncValue = _previousVSyncValue;
            _vSyncToggle.SetToggleValue(_incomingVSyncValue);
            _vSyncToggle.SetDirtyFlag(false);
        }

        private MultiTermLocExpression FormatResolutionToLocExpression(Resolution resolution)
        {
            List<LocTerm> terms = new List<LocTerm>
            {
                _locTermFactory.Create(LocTerm.LocalizationType.NonLocalized, resolution.width.ToString()),
                _locTermFactory.Create(LocTerm.LocalizationType.NonLocalized, " x "),
                _locTermFactory.Create(LocTerm.LocalizationType.NonLocalized, resolution.height.ToString())
            };

            return new MultiTermLocExpression(terms);
        }

        private MultiTermLocExpression FormatRefreshRateToLocExpression(RefreshRate refreshRate)
        {
            List<LocTerm> terms = new List<LocTerm>
            {
                _locTermFactory.Create(LocTerm.LocalizationType.NonLocalized, refreshRate.value.ToString()),
                _locTermFactory.Create(LocTerm.LocalizationType.NonLocalized, " "),
                _locTermFactory.Create(LocTerm.LocalizationType.Localized, _unitHertz.mTerm)
            };

            return new MultiTermLocExpression(terms);
        }

        private Resolution ParseLocExpressionToResolution(MultiTermLocExpression resolution)
        {
            List<LocTerm> terms = resolution.GetTerms();
            int width = int.Parse(terms[0].GetKey());
            int height = int.Parse(terms[2].GetKey());

            return new Resolution
            {
                width = width,
                height = height,
                refreshRateRatio = GetCurrentRefreshRate()
            };
        }

        private RefreshRate ParseLocExpressionToRefreshRate(MultiTermLocExpression refreshRateExpression)
        {
            List<LocTerm> terms = refreshRateExpression.GetTerms();
            float refreshRate = float.Parse(terms[0].GetKey());

            return GetRoundedRefreshRate(refreshRate);
        }

        private void InitializeScreenModeDictionary()
        {
            _screenModeDictionary = new Dictionary<FullScreenMode, LocExpression>
            {
                { FullScreenMode.ExclusiveFullScreen, GetWindowModeText(FullScreenMode.ExclusiveFullScreen) },
                { FullScreenMode.FullScreenWindow, GetWindowModeText(FullScreenMode.FullScreenWindow) },
                { FullScreenMode.MaximizedWindow, GetWindowModeText(FullScreenMode.MaximizedWindow) },
                { FullScreenMode.Windowed, GetWindowModeText(FullScreenMode.Windowed) }
            };
        }
    }
}