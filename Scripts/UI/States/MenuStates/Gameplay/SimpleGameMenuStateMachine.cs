// Copyright Isto Inc.

using Isto.Core.Inputs;
using Isto.Core.StateMachine;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.UI
{
    public class SimpleGameMenuStateMachine : MonoPushdownStateMachine, IUIGameMenu
    {
        // OTHER FIELDS

        protected MonoState _previousState;
        protected MenusEnum _currentMenu;

        // Canvas Groups that are part of the HUD need to be added here
        protected List<CanvasGroup> _hudGroups;

        // PROPERTIES

        public MenusEnum CurrentMenu => _currentMenu;
        public GameMenusEnum CurrentGameMenu => _currentMenu as GameMenusEnum;
        public MonoState CurrentState => _stateStack.Peek();


        //INJECTED

        protected IControls _controls;
        protected UISimpleMenusClosedState _closedMenuState;
        private UISimpleGameMenuState _coreUIGameMenuState;
        private CheatMenuState _cheatMenu;

        [Inject]
        private void Inject(IControls controls, UISimpleMenusClosedState closedMenuSTate, UIAccessManager uiAccessor,
                            UISimpleGameMenuState gameMenuState, CheatMenuState cheatMenu)
        {
            _controls = controls;

            _closedMenuState = closedMenuSTate;
            _coreUIGameMenuState = gameMenuState;
            _cheatMenu = cheatMenu;

            // this should be using ExpandableEnum instead of this old regular enum
            // and since this is the new UIAccessManager from core, also note that it does nothing for now, but correct
            // setup in the project would eventually mean that this will work
            // (the UIAccessManager is setup a certain way in Atrio and the UIMainMenuStateMachine fetches those
            // canvases itself to put in here)
            _hudGroups = uiAccessor.GetCanvasGroups(UIAccessManager.CanvasGroupId.HUDGroups);
        }

        public virtual void OpenMenu(MenusEnum menu, OpenMenuArgs args = null)
        {
            _currentMenu = menu;

            if (menu == GameMenusEnum.GAME)
            {
                EnterSubState(_coreUIGameMenuState);
            }
            else if (menu == GameMenusEnum.CHEATER)
            {
                EnterSubState(_cheatMenu);
            }
            else if (menu == GameMenusEnum.SCENE_START)
            {
                if (_currentState != startState)
                {
                    PushState(startState);
                }
            }
            else
            {
                Debug.LogError("Logic not implemented for this menu.  " + menu.ToString());
            }
        }

        public virtual void CloseMenu()
        {
            // If not already in closed menu state, exit out of any substates
            if (_currentState != _closedMenuState)
            {
                while (_stateStack.Count > 1)
                {
                    ExitSubState();
                }

                // If at bottom menu state and it is not the closed menu state, change to that state.
                if (_currentState != _closedMenuState)
                {
                    ChangeState(_closedMenuState);
                }
            }

            _currentMenu = MenusEnum.CLOSED;
        }

        public override void ExitSubState()
        {
            // Check to make sure we haven't already exited back to the closed menu state.  Happens when tutorial / cinematic actions override menus
            if (_stateStack.Count > 1)
            {
                _previousState = PopState();

                _currentState.ReturnFromSubState(this, _previousState);

                if (_currentState == _closedMenuState)
                {
                    _controls.SetControlMode(Controls.Mode.Gameplay);
                    _currentMenu = GameMenusEnum.CLOSED;
                }
            }
        }

        // HideHUD and ShowHUD:
        // These can't do anything for now in Core, because _hudGroups is going to be empty (I expect)
        // Right now Atrio.UIMainMenuStateMachine assumes that the base class is injected first and just overrides it
        // TODO: check if there are actually canvas groups to pick up in Core and make sure code plays nice.
        public void HideHUD(float fadeTime)
        {
            StopAllCoroutines();

            for (int i = 0; i < _hudGroups.Count; i++)
            {
                StartCoroutine(UIUtils.SetCanvasAlpha(_hudGroups[i], 0f, fadeTime, false));
            }
        }

        public void ShowHUD(float fadeTime)
        {
            StopAllCoroutines();

            for (int i = 0; i < _hudGroups.Count; i++)
            {
                StartCoroutine(UIUtils.SetCanvasAlpha(_hudGroups[i], 1f, fadeTime, true));
            }
        }

        // EnterCutSceneState and ExitCutSceneState:
        // These can't do anything for now in Core, consider moving their definition in Atrio.UIMainMenuStateMachine
        // or moving the UICutSceneState into Core.
        public virtual void EnterCutSceneState()
        {
        }

        public virtual void ExitCutSceneState()
        {
        }
    }
}