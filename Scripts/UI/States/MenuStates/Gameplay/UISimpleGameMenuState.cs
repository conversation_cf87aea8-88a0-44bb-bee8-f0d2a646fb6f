// Copyright Isto Inc.

using Isto.Core.Data;
using Isto.Core.Game;
using Isto.Core.Inputs;
using Isto.Core.Scenes;
using Isto.Core.StateMachine;
using System;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    public class UISimpleGameMenuState : MonoState
    {
        // UNITY HOOKUP

        public CanvasGroup canvasGroup;
        public Transform buttonsParent;
        public Button loadButton;
        public Button saveButton;
        public Button settingsButton;
        public Button restartButton;

        [Header("Confirm Dialogue")]
        [SerializeField] private UISimpleConfirmModalState _confirmationSubState;

        [Header("Sub State")]
        public MonoState settingsSubMenu;
        public CoreUISetSaveSlotSubState setSaveSlotState;
        [Space(20)]
        [Range(0, 1)] public float mainCanvasFadeOutPercent = 0.3f;

        [SerializeField] private ScrollRect _saveListScrollRect;


        // OTHER FIELDS

        protected SimpleGameMenuStateMachine _mainMenu;
        private bool _exiting;
        private float _spamTimer;
        private bool _inSubState;
        private Selectable _previousSelection; // Used when coming back from sub states to return selection to last button used


        // PROPERTIES

        public UISimpleConfirmModalState QuitConfirmationPopUp => _confirmationSubState;


        // INJECTION

        protected IGameData _gameData;
        protected GameState _gameState;
        protected IControls _controls;
        protected GameScenesReference _coreGameScenesReference;

        [Inject]
        private void Inject(IGameData gameData, IControls controls,
            GameState gameState, GameScenesReference coreGameScenesReference)
        {
            _gameState = gameState;
            _gameData = gameData;
            _controls = controls;
            _coreGameScenesReference = coreGameScenesReference;
        }


        // LIFECYCLE METHODS

        protected virtual void OnDestroy()
        {
            // Unregister event on destroy in case the game is stopped in the editor while the menu is opened.
            UnregisterEvents();
        }

        public override void Enter(MonoStateMachine controller)
        {
            //Some speedrunners use custom scripts to read our debug.log and update their LiveSplit programs
            Debug.Log("SpeedrunMode - Game paused");

            _controls.SetControlMode(Controls.Mode.UI);

            _mainMenu = controller as SimpleGameMenuStateMachine;

            if (_saveListScrollRect)
            {
                _saveListScrollRect.enabled = true;
            }

            UpdateActiveButtons();
            UpdateButtonNavigation();

            _previousSelection = null;  // Clearing this so save button is selected by default when entering the menu

            StartCoroutine(UIUtils.SetCanvasAlpha(canvasGroup, 1, Constants.CANVAS_FADE_TIME, true, GetCurrentSelectable()));

            Time.timeScale = 0.00f;

            _exiting = false;

            _spamTimer = -Constants.BUTTON_SPAM_DELAY;

            GlobalGameplayEvents.OnMenuInteraction(this, GameMenusEnum.GAME, MenuInteractionEventArgs.Action.Open);

            RegisterEvents();
        }

        public override void Exit(MonoStateMachine controller)
        {
            //Some speedrunners use custom scripts to read our debug.log and update their LiveSplit programs
            Debug.Log("SpeedrunMode - Game unpaused");

            UnregisterEvents();

            StartCoroutine(UIUtils.SetCanvasAlpha(canvasGroup, 0, Constants.CANVAS_FADE_TIME, false));

            Time.timeScale = 1f;

            GlobalGameplayEvents.OnMenuInteraction(this, GameMenusEnum.GAME, MenuInteractionEventArgs.Action.Close);

            if (_saveListScrollRect)
            {
                _saveListScrollRect.enabled = false;
            }
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            _spamTimer += Time.unscaledDeltaTime;

            if (_spamTimer < Constants.BUTTON_SPAM_DELAY)
                return this;

            if (_controls.GetButtonDown(UserActions.UICANCEL) ||
                _controls.GetButtonDown(UserActions.PAUSE) ||
                _controls.GetButtonDown(UserActions.TOGGLEGAMEMENU))
            {
                _mainMenu.CloseMenu();
                _exiting = true;
            }

            return this;
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
            _inSubState = false;

            // in case any config changed while in the submenus
            UpdateActiveButtons();
            UpdateButtonNavigation();

            // When returning using mouse or keyboard we need to clean up the cached selection
            if (_previousSelection != null && !_controls.UsingJoystick())
            {
                _previousSelection = null;
            }

            // If coming back from settings menu, don't exit pause menu
            if (previousState == settingsSubMenu)
            {
                StartCoroutine(UIUtils.SetCanvasAlpha(canvasGroup, 1, Constants.CANVAS_FADE_TIME, true, GetCurrentSelectable()));
                return;
            }

            if (previousState is IStateExitResult exitResult && exitResult.Result == IStateExitResult.ResultType.Cancelled)
            {
                StartCoroutine(UIUtils.SetCanvasAlpha(canvasGroup, 1, Constants.CANVAS_FADE_TIME, true, GetCurrentSelectable()));
                return;
            }

            ExitingMenuCleanup();

            _mainMenu.ExitSubState();
            _exiting = true;
        }


        // EVENT HANDLERS

        private bool _subscribed = false;

        protected virtual void RegisterEvents()
        {
            if (_subscribed)
                return;

            Events.Subscribe(Events.INPUT_MODE_CHANGED, Events_OnInputModeChanged);

            _subscribed = true;
        }

        protected virtual void UnregisterEvents()
        {
            if (!_subscribed)
                return;

            Events.UnSubscribe(Events.INPUT_MODE_CHANGED, Events_OnInputModeChanged);

            _subscribed = false;
        }

        private void Events_OnInputModeChanged()
        {
            if (_exiting || _inSubState)
                return;

            if(_mainMenu.CurrentState != this)
                return;

            if (_controls.UsingJoystick())
            {
                // Ensure that a gameObject is always selected (in case the user clicks off the buttons and then tries to use controller)
                GetCurrentSelectable().Select();
            }
            else
            {
                // Conversely, clean up if controller is gone, this should have the selection graphics disappear
                if (EventSystem.current.currentSelectedGameObject != null)
                {
                    EventSystem.current.SetSelectedGameObject(null);
                }
            }
        }

        public void HandleSaveClicked()
        {
            if (!_inSubState)
            {
                _inSubState = true;

                StartCoroutine(UIUtils.SetCanvasAlpha(canvasGroup, mainCanvasFadeOutPercent, Constants.CANVAS_FADE_TIME, false));

                setSaveSlotState.SetMenuMode(CoreUISetSaveSlotSubState.MenuMode.Save);
                _mainMenu.EnterSubState(setSaveSlotState);

                _controls.DisableControls(Constants.CANVAS_FADE_TIME);

                _previousSelection = saveButton;
            }
        }

        public void HandleLoadClicked()
        {
            if (!_inSubState)
            {
                _inSubState = true;

                StartCoroutine(UIUtils.SetCanvasAlpha(canvasGroup, mainCanvasFadeOutPercent, Constants.CANVAS_FADE_TIME, false));

                setSaveSlotState.SetMenuMode(CoreUISetSaveSlotSubState.MenuMode.Load);
                _mainMenu.EnterSubState(setSaveSlotState);

                _controls.DisableControls(Constants.BUTTON_SPAM_DELAY);

                _previousSelection = loadButton;
            }
        }

        public void HandleRestartClicked()
        {
            if (_exiting)
                return;

            ExitingMenuCleanup();

            _gameState.StartGameMode(_gameState.CurrentGameMode);
        }

        public void HandleSettingsClicked()
        {
            if (_mainMenu.CurrentState == _confirmationSubState)
            {
                _confirmationSubState.CancelClicked();
            }

            if (_mainMenu.CurrentState != settingsSubMenu)
            {
                _mainMenu.EnterSubState(settingsSubMenu);
                _controls.DisableControls(Constants.CANVAS_FADE_TIME);

                _previousSelection = settingsButton;
            }
        }

        public void QuitGame()
        {
            if (_exiting)
                return;

            if (_mainMenu.CurrentState == _confirmationSubState)
            {
                return;
            }

            _confirmationSubState.SetCallbacks(QuitConfirmed, null); // callbacks happen after popup substate pops itself
            _mainMenu.EnterSubState(_confirmationSubState);
        }


        // OTHER METHODS

        protected virtual void UpdateActiveButtons()
        {
            loadButton.interactable = false;
            saveButton.interactable = (_gameState.CurrentGameMode != null ? _gameState.CurrentGameMode.CanSaveGame : true);

            bool restartAllowed = IsRestartButtonAllowed();
            restartButton.interactable = restartAllowed;
            restartButton.gameObject.SetActive(restartAllowed);

            // a bit of a special case because of the asynchronous nature of this check
            // however it's not like this can change often normally - eventually we should cache this info to reduce data calls
            _gameData.HasAnySaveData((dataExists) =>
            {
                bool allowLoad = dataExists && (_gameState.CurrentGameMode != null ? _gameState.CurrentGameMode.CanLoadGame : true);
                if(loadButton.interactable != allowLoad)
                {
                    loadButton.interactable = allowLoad;
                    // this is called after UpdateActiveButtons but we need to call it again if we made a change
                    UpdateButtonNavigation();
                }
            });
        }

        protected virtual bool IsRestartButtonAllowed()
        {
            return _gameState.CurrentGameMode != null ? _gameState.CurrentGameMode.ShowRestartButton : false;
        }

        private void UpdateButtonNavigation()
        {
            Button previousButton = null;

            for (int i = 0; i < buttonsParent.childCount; i++)
            {
                Button b = buttonsParent.GetChild(i).GetComponent<Button>();

                if (b != null && b.interactable)
                {
                    UIUtils.SetNavigation(b, UIUtils.NavigationDirection.Up, previousButton);

                    for (int j = i + 1; j < buttonsParent.childCount; j++)
                    {
                        Button nextBut = buttonsParent.GetChild(j).GetComponent<Button>();

                        if (nextBut != null && nextBut.interactable)
                        {
                            UIUtils.SetNavigation(b, UIUtils.NavigationDirection.Down, nextBut);
                            break;
                        }
                    }

                    previousButton = b;
                }
            }
        }

        private Selectable GetCurrentSelectable()
        {
            if (_previousSelection != null)
                return _previousSelection;

            Selectable currentSelectable = null;

            bool allowSave = (_gameState.CurrentGameMode != null ? _gameState.CurrentGameMode.CanSaveGame : true);
            if (allowSave)
            {
                currentSelectable = saveButton;
            }
            else
            {
                bool allowLoad = (_gameState.CurrentGameMode != null ? _gameState.CurrentGameMode.CanLoadGame : true);
                allowLoad &= loadButton.interactable;
                if (allowLoad)
                {
                    currentSelectable = loadButton;
                }
                else
                {
                    currentSelectable = settingsButton;
                }
            }

            return currentSelectable;
        }

        protected virtual void QuitConfirmed()
        {
            ExitingMenuCleanup();
            _gameState.LoadScene(_coreGameScenesReference.TitleScreen);
        }

        protected virtual void ExitingMenuCleanup()
        {
            Time.timeScale = 1f;
            _exiting = true;
        }
    }
}