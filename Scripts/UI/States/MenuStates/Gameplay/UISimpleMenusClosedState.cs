// Copyright Isto Inc.

using Isto.Core.Inputs;
using Isto.Core.StateMachine;
using UnityEngine.EventSystems;
using Zenject;

namespace Isto.Core.UI
{
    /// <summary>
    /// When theres no menus open, and regular gameplay is just happening.
    /// The default/start state.
    /// </summary>
    public class UISimpleMenusClosedState : MonoState
    {
        // OTHER FIELDS

        protected SimpleGameMenuStateMachine _coreMainMenu;


        // INJECTED

        protected IControls _controls;

        [Inject]
        private void Inject(IControls controls)
        {
            _controls = controls;
        }


        // LIFECYCLE METHODS

        public override void Enter(MonoStateMachine controller)
        {
            _coreMainMenu = controller as SimpleGameMenuStateMachine;
        }

        public override void Exit(MonoStateMachine controller)
        {
            // Not supposed to happen during gameplay.
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            if (_controls.GetButtonDown(UserActions.TOGGLEGAMEMENU))
            {
                _coreMainMenu.OpenMenu(GameMenusEnum.GAME);
            }

            return this;
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
            // Clear any UI selection
            EventSystem.current.SetSelectedGameObject(null);
            _controls.DisableControls(Constants.BUTTON_SPAM_DELAY);
        }
    }
}