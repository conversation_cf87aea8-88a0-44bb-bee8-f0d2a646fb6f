// Copyright Isto Inc.

using Isto.Core.Analytics;
using Isto.Core.Cheats;
using Isto.Core.Inputs;
using Isto.Core.StateMachine;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.Serialization;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    /// <summary>
    /// Developer console controller which reads user input in the dev console and handles perfoming associated actions
    /// </summary>
    public class CheatMenuState : MonoState
    {
        // Public Variables

        [Header("Dashboard Display")]
        public CanvasGroup dashboardContainerGroup;
        public Selectable controllerDefaultSelection;
        public List<Selectable> nonInteractableForController;

        [Header("Skin Picker Display")]
        public CanvasGroup skinPickerContainerGroup;

        [Header("Terminal Display")]
        [FormerlySerializedAs("terminal")]
        public UIConsole console;

        [Header("Garbage Collect Inducer")]
        public UIButtonHoldDisplay forceGarbageCollectButton;


        // Private Variables

        private List<CheatAction> _activeActions;

        private GameObject _selectedBeforeConsoleOpened = null;

        private readonly List<UIConsole.DeveloperConsoleCommandEntry> _currentCommandStates = new List<UIConsole.DeveloperConsoleCommandEntry>();

        private List<UIConsole.ConsoleCommandDefinition> _commands = new List<UIConsole.ConsoleCommandDefinition>();

        MonoStateMachine _menuStateMachine;

        // PROPERTIES

        public bool IsOpen { get; private set; }

        public List<UIConsole.DeveloperConsoleCommandEntry> CurrentCommandStates => _currentCommandStates;

        private List<CheatAction> startUpActions => _cheatSettings.startUpActions;
        private List<CheatAction> cheatCommands => _cheatSettings.playerCommands;
        private List<CheatAction> devCommands => _cheatSettings.debugCommands;
        private List<CheatAction> keyCodeCommands => _cheatSettings.keyCodeCommands;


        // INJECTION

        private IControls _controls;
        private IAnalyticsHandler _analytics;
        private IUIGameMenu _gameMenu;
        private CheatSettings _cheatSettings;

        [Inject]
        public void Inject(IControls controls, IAnalyticsHandler analytics, IUIGameMenu gameMenu,
            CheatSettings cheatSettings)
        {
            _controls = controls;
            _analytics = analytics;
            _gameMenu = gameMenu;
            _cheatSettings = cheatSettings;
        }


        // LIFECYCLE EVENTS

        protected void Awake()
        {
            _activeActions = new List<CheatAction>();

            forceGarbageCollectButton?.gameObject.SetActive(_cheatSettings.showForceGCTool && Debug.isDebugBuild);
        }

        protected void Start()
        {
#if UNITY_EDITOR
            cheatCommands.AddRange(devCommands);
#else
            if (Debug.isDebugBuild)
            {
                cheatCommands.AddRange(devCommands);
            }
#endif
            for (int i = 0; i < cheatCommands.Count; i++)
            {
                CheatAction action = cheatCommands[i];
                for (int j = 0; j < action.aliases.Count; j++)
                {
                    string alias = action.aliases[j];
                    // TODO: cleanup the command definitions, see if we can't do without it
                    var cmd = new UIConsole.ConsoleCommandDefinition()
                    {
                        commandStr = alias,
                        closeConsoleWhenUsed = action.closeMenuAfterActivation,
                        actions = new List<CheatAction> { action }
                    };
                    _commands.Add(cmd);
                }
            }

            _commands.Sort((x, y) => { return x.commandStr.CompareTo(y.commandStr); });
            console.RegisterCommands(_commands);
            RunStartUpActions();

            //_analytics = transform.parent.gameObject.GetComponentInChildren<IAnalyticsHandler>();

            RegisterEvents();
        }

        protected void OnDestroy()
        {
            // Deactivate any actions that are running when ever we destroy this object
            for (int i = 0; i < _activeActions.Count; i++)
            {
                try
                {
                    _activeActions[i].Deactivate();
                }
                catch (Exception) { }
                ; // DevAction exceptions have to do with user error messages; ignore them
            }

            UnregisterEvents();
        }

        private void OnEnable()
        {
            console.CloseConsole();
            dashboardContainerGroup.SetCanvasValues(0, false, false);
            skinPickerContainerGroup.SetCanvasValues(0, false, false);
        }

        protected void Update()
        {
            // These are expected to work when the console is closed.

            // Update any continuous running actions
            for (int i = 0; i < _activeActions.Count; i++)
            {
                _activeActions[i].OnUpdate();
            }

            // Check for any key press actions
            if (!_cheatSettings.disableAllKeyCodeCommands)
            {
                for (int i = 0; i < keyCodeCommands.Count; i++)
                {
                    if (Input.GetKeyDown(keyCodeCommands[i].hotkey))
                    {
                        string[] fakeInput = new string[] { keyCodeCommands[i].aliases[0] };
                        // TODO: cleanup the command definitions, see if we can't do without it
                        var cmd = new UIConsole.ConsoleCommandDefinition()
                        {
                            commandStr = keyCodeCommands[i].aliases[0],
                            closeConsoleWhenUsed = keyCodeCommands[i].closeMenuAfterActivation,
                            actions = new List<CheatAction> { keyCodeCommands[i] }
                        };
                        ActivateCommand(cmd, fakeInput);
                    }
                }
            }
        }

        public override void Enter(MonoStateMachine controller)
        {
            _menuStateMachine = controller;
            OpenConsole();
        }

        public override void Exit(MonoStateMachine controller)
        {
            CloseConsole();
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            // Check for console navigation
#if PLATFORM_GAMECORE && !UNITY_EDITOR

#else
            if (Input.anyKeyDown && !Controls.UsingController)
            {
                console.SelectConsole();
            }
#endif

            return this;
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
            // For now this should not happen unless there is an exception causing an error popup while we're open
            // But while the dashboard and console are sort of available while you're perusing the cheat menu some
            // other features require decicated windows such as the skin selector. This could be a substate.
        }

        // EVENT HANDLING

        private void RegisterEvents()
        {
            console.OnCommandActivated += Terminal_OnCommandActivated;
            Events.Subscribe(Events.INPUT_MODE_CHANGED, Events_OnSettingsChanged);
        }

        private void UnregisterEvents()
        {
            console.OnCommandActivated -= Terminal_OnCommandActivated;
            Events.UnSubscribe(Events.INPUT_MODE_CHANGED, Events_OnSettingsChanged);
        }

        private void Terminal_OnCommandActivated(UIConsole.ConsoleCommandDefinition definition, string[] args)
        {
            ProcessCommand(definition, args);
        }

        private void Events_OnSettingsChanged()
        {
            if (IsOpen)
            {
                UpdateControllerDependentObjects();

                EventSystem.current.SetSelectedGameObject(Controls.UsingController ? controllerDefaultSelection?.gameObject : null);
            }
        }

        // other Methods

        public void OpenConsole()
        {
            IsOpen = true;
            dashboardContainerGroup.SetCanvasValues(alpha: 1f, interactable: true, blockRaycast: true);
            _controls.SetControlMode(Controls.Mode.DeveloperConsole);

            // This block, be careful and test - TODO: cleanup
            {
                _selectedBeforeConsoleOpened = EventSystem.current.currentSelectedGameObject;
                console.OpenConsole(); // defaults selection to console
                UpdateControllerDependentObjects(); // may be redundant
                if (Controls.UsingController && controllerDefaultSelection != null)
                {
                    controllerDefaultSelection.Select();
                }
            }

            _analytics?.RegisterDeveloperConsoleOpened();
        }

        public void CloseConsole()
        {
            IsOpen = false;

            console.CloseConsole();
            dashboardContainerGroup.SetCanvasValues(0, false, false);

            _controls.SetControlMode(Controls.Mode.Gameplay);

            EventSystem.current.SetSelectedGameObject(Controls.UsingController ? _selectedBeforeConsoleOpened : null);
        }

        public void Run(List<UIConsole.DeveloperConsoleCommandEntry> sequence)
        {
            if (sequence == null)
            {
                Debug.LogWarning("DeveloperConsole trying to run a null command list");
                return;
            }

            for (int i = 0; i < sequence.Count; i++)
            {
                UpdateCurrentCommand(sequence[i]);
                /*
                string line = sequence[i].command;
                for (int j = 0; j < sequence[i].arguments.Length; j++)
                {
                    line += " " + sequence[i].arguments[j];
                }
                ProcessInput(line);*/
            }

            console.RunCommands(sequence);

            Events.RaiseEvent(Events.DEVELOPER_COMMAND, sequence);
        }

        /// <summary>
        /// This method expects that we've first broken down our commands and parameters by whitespaces, and does a
        /// second pass to put back together any parameters that were surrounded by quotation marks while also trimming
        /// those quotation marks so the parameter logic doesn't have to know about them.
        /// </summary>
        /// <param name="splitInput">The inputs list broken down by white spaces</param>
        /// <returns>The new version of the inputs list with quotation marks applied</returns>
        private string[] GroupTokensByQuotationMarks(string[] splitInput)
        {
            List<string> fixedList = new List<string>();
            List<string> buffer = new List<string>();
            List<string> currentGroup = fixedList;
            for (int i = 0; i < splitInput.Length; i++)
            {
                string token = splitInput[i];

                if (splitInput[i].StartsWith("\"") && !splitInput[i].EndsWith("\""))
                {
                    currentGroup = buffer;
                }

                if (token.StartsWith("\""))
                {
                    token = token.Substring(1);
                }

                if (token.EndsWith("\""))
                {
                    token = token.Substring(0, token.Length - 1);
                }

                currentGroup.Add(token);

                if (splitInput[i].EndsWith("\"") && !splitInput[i].StartsWith("\""))
                {
                    fixedList.Add(string.Join(" ", buffer));
                    buffer.Clear();
                    currentGroup = fixedList;
                }
            }

            return fixedList.ToArray();
        }

        public void ProcessCommand(UIConsole.ConsoleCommandDefinition devCommand, string[] splitInput)
        {
            bool success = ActivateCommand(devCommand, splitInput);

            if (success && devCommand.closeConsoleWhenUsed)
            {
                _gameMenu.CloseMenu();
            }
        }

        public bool ActivateCommand(UIConsole.ConsoleCommandDefinition devCommand, string[] splitInput)
        {
            ReportCheatAnalyticsData(devCommand);

            if (splitInput.Any(x => x.StartsWith("\"")))
            {
                splitInput = GroupTokensByQuotationMarks(splitInput);
            }

            bool commandSuccess = true;
            for (int i = 0; i < devCommand.actions.Count; i++)
            {
                CheatAction currentAction = devCommand.actions[i];

                commandSuccess &= ActivateAction(currentAction, splitInput);
            }
            return commandSuccess;
        }

        private void ReportCheatAnalyticsData(UIConsole.ConsoleCommandDefinition cmd)
        {
            _analytics?.RegisterCheatActivated(cmd.commandStr);
        }

        public bool ActivateAction(CheatAction currentAction, string[] splitInput)
        {
            bool actionSuccess;
            if (!_activeActions.Contains(currentAction))
            {
                try
                {
                    // Check argument length matches expected argument length
                    if (splitInput.Length - 1 != currentAction.numberOfParameters)
                    {
                        console.AddResponseText(string.Format("Invalid number of arguments passed to action.  Expecting {0} args.", currentAction.numberOfParameters));
                        actionSuccess = false;
                    }
                    else
                    {
                        // If the command has arguments, grab those from the splitInput and pass them to the activate method
                        if (currentAction.numberOfParameters > 0)
                        {
                            string[] args = new string[splitInput.Length - 1];

                            Array.ConstrainedCopy(splitInput, 1, args, 0, splitInput.Length - 1);

                            currentAction.Activate(args);
                        }
                        else
                        {
                            currentAction.Activate();
                        }

                        console.AddResponseText(string.Format("{0} {1}", currentAction.continuous ? "Starting" : "Triggering", currentAction.name));

                        if (currentAction.continuous)
                            _activeActions.Add(currentAction);

                        if (!String.IsNullOrEmpty(currentAction.ResultMessage))
                            console.AddResponseText(currentAction.ResultMessage);

                        actionSuccess = true;
                    }
                }
                catch (Exception e)
                {
                    console.AddResponseText(string.Format("Error performing action {0}.  Error: {1}", currentAction.name, e.Message));
                    actionSuccess = false;
                }
            }
            else
            {
                try
                {
                    currentAction.Deactivate();

                    console.AddResponseText(string.Format("Deactivating {0}", currentAction.name));

                    _activeActions.Remove(currentAction);
                    actionSuccess = true;
                }
                catch (Exception e)
                {
                    console.AddResponseText(string.Format("Error deactivating action {0}.  Error: {1}", currentAction.name, e.Message));
                    actionSuccess = false;
                }
            }
            return actionSuccess;
        }

        public void ShowAllActiveCommands()
        {
            console.AddResponseText("Active dev mode actions:");

            if (_activeActions.Count == 0)
                console.AddResponseText("None");

            for (int i = 0; i < _activeActions.Count; i++)
            {
                console.AddResponseText(_activeActions[i].name);
            }
        }

        public void ShowSkinSelector()
        {
            skinPickerContainerGroup.gameObject.SetActive(true);
            skinPickerContainerGroup.SetCanvasValues(alpha: 1f, interactable: true, blockRaycast: true);
        }

        public void HideSkinSelector()
        {
            skinPickerContainerGroup.SetCanvasValues(alpha: 0f, interactable: false, blockRaycast: false);
            skinPickerContainerGroup.gameObject.SetActive(false);
        }

        [ContextMenu("ForceGCCollect")]
        public void ForceGCCollect()
        {
            Debug.Log("Inducing garbage collection.");
            GC.Collect();
        }

        /// <summary>
        /// Gets the command that goes with the commandStr passed in.  Returns null if none is found
        /// </summary>
        /// <param name="commandStr"></param>
        /// <returns>Command that matches the string, null otherwise</returns>
        private UIConsole.ConsoleCommandDefinition GetCommand(string commandStr)
        {
            for (int i = 0; i < _commands.Count; i++)
            {
                if (_commands[i].commandStr.Equals(commandStr, System.StringComparison.OrdinalIgnoreCase))
                {
                    return _commands[i];
                }
            }

            return null;
        }

        private void RunStartUpActions()
        {
            for (int i = 0; i < startUpActions.Count; i++)
            {
                try
                {
                    startUpActions[i].Activate();

                    if (startUpActions[i].continuous)
                        _activeActions.Add(startUpActions[i]);
                }
                catch (Exception e)
                {
                    Debug.LogWarning(string.Format("Error performing startup DeveloperConsoleAction {0}.  Error: {1}", startUpActions[i].name, e.Message));
                }
            }
        }

        /// <summary>
        /// Updates things that need to hide or become inactive when the controller is in use.
        /// </summary>
        private void UpdateControllerDependentObjects()
        {
            bool activeAndInteractable = !Controls.UsingController;

            foreach (Selectable s in nonInteractableForController)
            {
                s.interactable = activeAndInteractable;
            }

            // as part of this we also make sure the console is hidden because it does nothing on console at the moment
            //float alpha = activeAndInteractable ? 1f : 0f;
            //terminalContainerGroup.SetCanvasValues(alpha, activeAndInteractable, blockRaycast: activeAndInteractable);

            if (activeAndInteractable)
            {
                if (!console.IsOpen)
                {
                    console.OpenConsole();
                }
            }
            else
            {
                console.CloseConsole();
            }
        }

        // TODO: not clear what this does, but I think we can refactor this out and use a simpler system
        // DeveloperConsoleCommandEntry in itself is something that I wish to remove
        private void UpdateCurrentCommand(UIConsole.DeveloperConsoleCommandEntry newEntry)
        {
            UIConsole.DeveloperConsoleCommandEntry existingEntry = _currentCommandStates.FirstOrDefault(commandEntry =>
                commandEntry.command.Equals(newEntry.command, StringComparison.OrdinalIgnoreCase));

            if (existingEntry != null)
            {
                if (!existingEntry.arguments.SequenceEqual(newEntry.arguments))
                {
                    // Update the current command with the latest arguments
                    int index = _currentCommandStates.IndexOf(existingEntry);
                    _currentCommandStates[index] = newEntry;
                }
            }
            else
            {
                // Add new command to current command states
                _currentCommandStates.Add(newEntry);
            }
        }
    }
}