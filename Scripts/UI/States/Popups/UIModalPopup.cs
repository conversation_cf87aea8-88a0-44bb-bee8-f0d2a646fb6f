// Copyright Isto Inc.
using I2.Loc;
using Isto.Core.Audio;
using Isto.Core.Inputs;
using Isto.Core.Localization;
using Isto.Core.StateMachine;
using System;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    // Named very abstractly but basically only used for errors? Consider renaming.
    /// <summary>
    /// This is a popup that lives in the ProjectContext and handles error messages.
    /// </summary>
    public class UIModalPopup : MonoState
    {
        public enum ErrorPopupType
        {
            UnknownError = 0,
            PathTooLongException,
            DirectoryNotFoundException,
            IOException,
            UnauthorizedAccessException,
            SecurityException
        }

        [Header("Loc")]
        [SerializeField] private LocalizedString _pathlengthErrorKey;
        [SerializeField] private LocalizedString _notFoundErrorKey;
        [SerializeField] private LocalizedString _ioErrorKey;
        [SerializeField] private LocalizedString _noAccessErrorKey;
        [SerializeField] private LocalizedString _securityErrorKey;
        [SerializeField] private LocalizedString _unknownErrorKey;

        [Header("UI pieces")]
        [SerializeField] private CanvasGroup _canvasGroup;
        [SerializeField] private TextMeshProUGUI _errorMessageTextArea;
        [SerializeField] private TMP_Text _exceptionField;
        [SerializeField] private Button _closeButton;

        private IUIMenu _menuController;
        private GameObject _previousSelection;
        private Controls.Mode _previousMode;
        private float _previousTimeScale;
        private int _errorsOnDisplay = 0;
        private bool _isOpen = false;

        // Injected

        private IControls _controls;
        protected UISounds _uiSounds;

        [Inject]
        public void Inject(IControls controls, UISounds uiSounds)
        {
            _controls = controls;
            _uiSounds = uiSounds;
        }

        private void Awake()
        {
            ClosePopup();
        }

        public void DisplayError(ErrorPopupType type)
        {
            Debug.LogWarning($"Displaying error popup of {type}");
            LocalizedString errorMsgKey;
            switch (type)
            {
                case ErrorPopupType.PathTooLongException:
                    errorMsgKey = _pathlengthErrorKey;
                    break;
                case ErrorPopupType.DirectoryNotFoundException:
                    errorMsgKey = _notFoundErrorKey;
                    break;
                case ErrorPopupType.IOException:
                    errorMsgKey = _ioErrorKey;
                    break;
                case ErrorPopupType.UnauthorizedAccessException:
                    errorMsgKey = _noAccessErrorKey;
                    break;
                case ErrorPopupType.SecurityException:
                    errorMsgKey = _securityErrorKey;
                    break;
                case ErrorPopupType.UnknownError:
                default:
                    errorMsgKey = _unknownErrorKey;
                    break;
            }

            Loc.SetTMPro(_errorMessageTextArea, errorMsgKey);

            if (_errorsOnDisplay < 1)
            {
                OpenPopup();

                StealUIContext();
            }

            _errorsOnDisplay++;
        }

        public void DisplayError(ErrorPopupType type, Exception e)
        {
            if (_exceptionField != null)
            {
                if (e != null)
                {
                    if (_errorsOnDisplay > 0)
                    {
                        _exceptionField.text += $"\n\n";
                    }
                    else
                    {
                        _exceptionField.text = "";
                    }

                    _exceptionField.text += $"Exception:{e.Message}\n{e.InnerException?.Message}";
                }
                else
                {
                    _exceptionField.text = "";
                }
            }

            DisplayError(type);
        }

        public void OnConfirmButtonPressed()
        {
            _errorsOnDisplay = 0;
            ClosePopup();
            RestoreUIContext();
        }

        private void OpenPopup()
        {
            _isOpen = true;
            _canvasGroup.alpha = 1f;
            _canvasGroup.interactable = true;
            _canvasGroup.blocksRaycasts = true;
        }

        private void ClosePopup()
        {
            _isOpen = false;
            _canvasGroup.alpha = 0f;
            _canvasGroup.interactable = false;
            _canvasGroup.blocksRaycasts = false;
        }

        [ContextMenu(nameof(ShowExampleError))]
        private void ShowExampleError()
        {
            DisplayError(ErrorPopupType.UnknownError,
                new Exception("Here's the main exception message. It should be fairly long to take up as much realestate on the pop-up as possible since these things often are very verbose and full of useless crap\n"
                , new Exception("Inner exception message.  This also should be really long just so we can test the functionality of the scroll rect if it's needed and make sure it actually works. " +
                " That would be great, something that actually works.  OMG that wasn't long enough to trigger the scroll rect.  Hopefully this does it because I'm getting bored of typing nonsense" +
                "Nope, still wasn't long enough because I made the text smaller, which means I have to type more crap to get the scrolling to be needed.  Ugh" +
                "\n\nSecond Verse\nApparently it still was not enough because I found out that the scroll rect was not working.\n\n-Frank\n\nP.S. Line breaks are neat")));
        }

        [ContextMenu(nameof(ShowExampleSmallError))]
        private void ShowExampleSmallError()
        {
            DisplayError(ErrorPopupType.UnknownError, new Exception("Sometimes errors could be simple"));
        }

        private void StealUIContext()
        {
            _previousMode = _controls.GetControlMode();
            _controls.SetControlMode(Controls.Mode.UI);

            _previousTimeScale = Time.timeScale;
            Time.timeScale = 0.00f;

            // Not sure about using Zenject to get these state machines.
            // Our container is at the project installer level.
            // Same conundrum in UIModalChoicePopup.
            // For now I'll just look for the behaviors I need in the scenes.
            MonoPushdownStateMachine[] allStateMachines = GameObject.FindObjectsOfType<MonoPushdownStateMachine>();
            foreach (MonoPushdownStateMachine stateMachine in allStateMachines)
            {
                if (stateMachine is IUIMenu menuFound) // there should be only one
                {
                    _menuController = menuFound;
                    break;
                }
            }

            if (_menuController != null)
            {
                _menuController.EnterSubState(this);
            }

            if (Controls.UsingController)
            {
                _previousSelection = EventSystem.current.currentSelectedGameObject;
                _closeButton.Select();
            }
            else
            {
                _previousSelection = null;
            }
        }

        private void RestoreUIContext()
        {
            Time.timeScale = _previousTimeScale;
            _controls.SetControlMode(_previousMode);

            if (_menuController != null)
            {
                if (_menuController.GetCurrentState() == this)
                    _menuController.ExitSubState();
            }

            if (Controls.UsingController && _previousSelection != null)
                EventSystem.current.SetSelectedGameObject(_previousSelection);
        }

        // We inherit the state abstract class mostly just so we can throw ourselves on the state machine and "pause" it

        public override void Enter(MonoStateMachine controller)
        {
            Events.Subscribe(Events.INPUT_MODE_CHANGED, OnInputModeChanged);
        }

        public override void Exit(MonoStateMachine controller)
        {
            Events.UnSubscribe(Events.INPUT_MODE_CHANGED, OnInputModeChanged);
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            if (Controls.UsingController && _controls.GetButtonDown(UserActions.UICANCEL))
            {
                _uiSounds.PlayButtonClickSound();
                OnConfirmButtonPressed();
            }
            return this;
        }

        private void OnInputModeChanged()
        {
            if (_isOpen)
            {
                EventSystem.current.SetSelectedGameObject(Controls.UsingController ? _closeButton.gameObject : null);
            }
        }
    }
}