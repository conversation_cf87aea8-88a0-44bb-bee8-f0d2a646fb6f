// Copyright Isto Inc.
using Isto.Core.Audio;
using Isto.Core.Inputs;
using Isto.Core.StateMachine;
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    /// <summary>
    /// Despite the name be aware that this is a simple "confirm or cancel" popup and not just an "OK" popup
    /// </summary>
    public class UISimpleModalStateBase : MonoState, IStateExitResult
    {
        public IStateExitResult.ResultType Result { get; private set; }

        public bool CancelAllowed = false;

        protected MonoPushdownStateMachine _controller;

        private Action _submitCallback;
        private Action _cancelCallback;

        private float _cooldownAtSateEnter = 0f;
        protected List<Selectable> _containedSelectablesCache = new List<Selectable>();

        // Injected variables
        protected IControls _controls;
        protected UISounds _uiSounds;

        [Inject]
        public void Inject(IControls controls, UISounds uISounds)
        {
            _controls = controls;
            _uiSounds = uISounds;
        }

        private void Awake()
        {
            CacheNavigationElements();
        }

        public override void Enter(MonoStateMachine controller)
        {
            _controller = controller as MonoPushdownStateMachine;
            _cooldownAtSateEnter = Constants.BUTTON_SPAM_DELAY;
            gameObject.SetActive(true);
        }

        protected virtual void CacheNavigationElements()
        {
            _containedSelectablesCache.Clear();
            // Right now this changes nothing but I'm setting includeInactive in the call because some items could be
            // activated/deactivated as part of the popup's interaction with the user or which mode you are using it in
            _containedSelectablesCache.AddRange(this.gameObject.GetComponentsInChildren<Selectable>(includeInactive: true));
        }

        public override void Exit(MonoStateMachine controller)
        {
            gameObject.SetActive(false);
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
            // not supposed to happen here I think, let base code throw its warning
            base.ReturnFromSubState(controller, previousState);
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            // Need to block our Run logic completely here because even disabling the controls doesn't prevent this
            // GetButtonDown check from falsely triggering if we enter while the button press is happening from somewhere else
            _cooldownAtSateEnter -= Time.unscaledDeltaTime;
            if (_cooldownAtSateEnter > 0f)
                return this;

            EnforceFocus();

            if (CancelAllowed && _controls.GetButtonDown(UserActions.UICANCEL))
            {
                _uiSounds.PlayButtonClickSound();
                CancelClicked();
            }

            return this;
        }

        private void EnforceFocus()
        {
            GameObject currentSelection = EventSystem.current.currentSelectedGameObject;
            bool focused = false;

            if (_containedSelectablesCache.Count == 0 && currentSelection != null)
            {
                EventSystem.current.SetSelectedGameObject(null);
            }

            for (int i = 0; i < _containedSelectablesCache.Count; i++)
            {
                if (_containedSelectablesCache[i].gameObject == currentSelection)
                {
                    focused = true;
                    break;
                }
            }

            if (!focused)
            {
                SetDefaultSelection();
            }
        }

        public void Show(bool cancelAllowed = false)
        {
            CancelAllowed = cancelAllowed;
        }

        public void Hide()
        {
            // This method doesn't really represent a cancel, but in case Hide is called while this is the current state,
            // it seems better to indicate that nothing was confirmed/accepted. Ideally this result would be "default" or "unassigned".
            Result = IStateExitResult.ResultType.Cancelled;

            Close();
        }

        private void Close()
        {
            if (_controller != null && _controller.GetCurrentState() == this)
                _controller.ExitSubState();

            // In case we need to be hidden while we're not the current state
            gameObject.SetActive(false);
        }

        public void SetCallbacks(Action submit, Action cancel)
        {
            _submitCallback = submit;
            _cancelCallback = cancel;
        }

        public void CancelClicked()
        {
            Result = IStateExitResult.ResultType.Cancelled;

            // Important to exit state before callback
            Close();

            if (_cancelCallback != null)
                _cancelCallback();
        }

        public void SubmitClicked()
        {
            Result = IStateExitResult.ResultType.Submit;

            // Important to exit state before callback
            Close();

            if (_submitCallback != null)
                _submitCallback();
        }

        public virtual void SetDefaultSelection()
        {
            // There might not be a selection to apply in certain popups
        }
    }
}