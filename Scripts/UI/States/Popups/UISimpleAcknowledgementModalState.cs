// Copyright Isto Inc.
using Isto.Core.Inputs;
using Isto.Core.StateMachine;
using TMPro;
using UnityEngine;

namespace Isto.Core.UI
{
    /// <summary>
    /// This is a popup with a single button and closing the popup, if possible, counts as a OK and not a Cancel
    /// </summary>
    public class UISimpleAcknowledgementModalState : UISimpleModalStateBase
    {
        [SerializeField] private TextMeshProUGUI _mainTextbox;
        [SerializeField] private TextMeshProUGUI _detailsTextbox;

        public override void Enter(MonoStateMachine controller)
        {
            base.Enter(controller);

            CancelAllowed = false;

            //Invoke(nameof(SetDefaultSelection), 0f);
            //SetDefaultSelection();
        }

        public override void Exit(MonoStateMachine controller)
        {
            base.Exit(controller);

            //_uiSounds.PlayButtonClickSound();
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
            base.ReturnFromSubState(controller, previousState);
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            return base.Run(controller);
        }

        public override void SetDefaultSelection()
        {
            if (!Controls.UsingController)
                return;

            CoreButton mainButton = GetComponentInChildren<CoreButton>();
            if (mainButton != null)
                mainButton.Select();
        }

        public void SetMainText(string text)
        {
            _mainTextbox.text = text;
        }

        public void SetDetailsText(string text)
        {
            _detailsTextbox.text = text;
        }
    }
}