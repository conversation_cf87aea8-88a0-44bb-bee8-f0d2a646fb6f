// Copyright Isto Inc.
using I2.Loc;
using Isto.Core.Audio;
using Isto.Core.Inputs;
using Isto.Core.Localization;
using Isto.Core.StateMachine;
using System;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    /// <summary>
    /// This is a popup that lives in the ProjectContext and handles simple dialog configurations.
    /// It is used for save slot upgrade confirmation, among other things.
    /// </summary>
    public class UIModalChoicePopup : MonoState
    {
        public enum ChoicePopupType
        {
            NoChoice = 0, // Will enable only one button - not sure if I should create a completely new class for this
            YesNoChoice,
            OkCancelChoice
        }

        [SerializeField] private LocalizedString _defaultTitleKey; // UI/ChoicePopup/Header
        [SerializeField] private LocalizedString _yesKey; // UI/ChoicePopup/Accept
        [SerializeField] private LocalizedString _noKey; // UI/ChoicePopup/Refuse
        [SerializeField] private LocalizedString _okKey; // UI/ErrorDialog/ConfirmButton
        [SerializeField] private LocalizedString _cancelKey; // UI/CancelButton/Description

        [SerializeField] private CanvasGroup _canvasGroup;
        [SerializeField] private UIPanelHeader _headerPanel;
        [SerializeField] private TextMeshProUGUI _informationTextArea;
        [SerializeField] private TextMeshProUGUI _questionTextArea;
        [SerializeField] private CoreButtonController _confirmButton;
        [SerializeField] private CoreButtonController _cancelButton;

        private Action _onConfirm;
        private Action _onCancel;

        private Controls.Mode _previousMode;
        private float _previousTimeScale;
        private IUIMenu _gameMenu;
        private GameObject _previousSelection;

        // Injected

        private IControls _controls;
        protected UISounds _uiSounds;

        [Inject]
        public void Inject(IControls controls, UISounds uiSounds)
        {
            _controls = controls;
            _uiSounds = uiSounds;
        }

        private void Awake()
        {
            ClosePopup();
        }

        public void DisplayChoice(ChoicePopupType type, LocalizedString questionText, Action onConfirm, Action onCancel = null, LocalizedString? optionalExtraDescription = null,
                                  LocalizedString? titleOverride = null, LocalizedString? confirmButtonOverride = null, LocalizedString? cancelButtonOverride = null)
        {
            DisplayChoice(type,
                          Loc.Get(questionText),
                          onConfirm,
                          onCancel,
                          optionalExtraDescription != null ? Loc.Get(optionalExtraDescription.Value) : "",
                          titleOverride != null ? Loc.Get(titleOverride.Value) : "",
                          confirmButtonOverride != null ? Loc.Get(confirmButtonOverride.Value) : "",
                          cancelButtonOverride != null ? Loc.Get(cancelButtonOverride.Value) : "");
        }

        public void DisplayChoice(ChoicePopupType type, string questionText, Action onConfirm, Action onCancel = null, string optionalExtraDescription = "",
                                  string titleOverride = "", string confirmButtonOverride = "", string cancelButtonOverride = "")
        {
            Debug.Log($"DisplayChoice popup of type {type} asking: {questionText}");
            string title, information, question, confirmButton, cancelButton;
            Selectable defaultSelection = null;

            switch (type)
            {
                case ChoicePopupType.NoChoice:
                    _cancelButton.gameObject.SetActive(false);
                    cancelButton = "";
                    confirmButton = Loc.Get(_okKey);
                    defaultSelection = _confirmButton.GetSelectable();
                    break;
                case ChoicePopupType.YesNoChoice:
                    _cancelButton.gameObject.SetActive(true);
                    cancelButton = Loc.Get(_noKey);
                    confirmButton = Loc.Get(_yesKey);
                    defaultSelection = _cancelButton.GetSelectable();
                    break;
                case ChoicePopupType.OkCancelChoice:
                    _cancelButton.gameObject.SetActive(true);
                    cancelButton = Loc.Get(_cancelKey);
                    confirmButton = Loc.Get(_okKey);
                    defaultSelection = _cancelButton.GetSelectable();
                    break;
                default:
                    Debug.LogWarning($"Unsuported ChoicePopupType {type} in DisplayChoice");
                    return;
            }

            title = (string.IsNullOrEmpty(titleOverride)) ? Loc.Get(_defaultTitleKey) : titleOverride;
            information = optionalExtraDescription;
            question = questionText;

            if (!string.IsNullOrEmpty(confirmButtonOverride))
                confirmButton = confirmButtonOverride;
            if (!string.IsNullOrEmpty(cancelButtonOverride))
                cancelButton = cancelButtonOverride;

            _headerPanel.SetTitle(title);

            _questionTextArea.text = question;
            _informationTextArea.text = information;

            _cancelButton.SetText(cancelButton);
            _confirmButton.SetText(confirmButton);

            _onConfirm = onConfirm;
            _onCancel = onCancel;

            OpenPopup();
            StealUIContext();

            // Order is important, keep this at the bottom.
            if (Controls.UsingController && defaultSelection != null)
                defaultSelection.Select();
        }

        public void OnConfirmButtonPressed()
        {
            ClosePopup();
            RestoreUIContext();
            _onConfirm?.Invoke();
        }

        public void OnCancelButtonPressed()
        {
            ClosePopup();
            RestoreUIContext();
            _onCancel?.Invoke();
        }

        private void OpenPopup()
        {
            _canvasGroup.alpha = 1f;
            _canvasGroup.interactable = true;
            _canvasGroup.blocksRaycasts = true;
        }

        private void ClosePopup()
        {
            _canvasGroup.alpha = 0f;
            _canvasGroup.interactable = false;
            _canvasGroup.blocksRaycasts = false;
        }

        [ContextMenu(nameof(ShowExampleChoice))]
        private void ShowExampleChoice()
        {
            DisplayChoice(ChoicePopupType.YesNoChoice, "Do you accept?", () => { Debug.Log("Example choice accepted."); },
                          () => { Debug.Log("Example choice refused."); }, "Here is an example choice.", "Test", "Um, yeah.", "Ugh, no.");
        }

        private void StealUIContext()
        {
            _previousMode = _controls.GetControlMode();
            _controls.SetControlMode(Controls.Mode.UI);

            _previousTimeScale = Time.timeScale;
            Time.timeScale = 0.00f;

            // Not sure about using Zenject to get these state machines.
            // Our container is at the project installer level.
            // Same conundrum in UIModalPopup.
            // For now I'll just look for the behaviors I need in the scenes.
            MonoPushdownStateMachine[] allStateMachines = GameObject.FindObjectsOfType<MonoPushdownStateMachine>();
            foreach (MonoPushdownStateMachine stateMachine in allStateMachines)
            {
                if (stateMachine is IUIMenu mainMenuFound)
                    _gameMenu = mainMenuFound;
            }

            if (_gameMenu != null)
            {
                _gameMenu.EnterSubState(this);
            }

            _previousSelection = Controls.UsingController ? EventSystem.current.currentSelectedGameObject : null;
        }

        private void RestoreUIContext()
        {
            Time.timeScale = _previousTimeScale;
            _controls.SetControlMode(_previousMode);

            if (_gameMenu != null)
            {
                if (_gameMenu.GetCurrentState() == this)
                    _gameMenu.ExitSubState();
            }

            if (Controls.UsingController && _previousSelection != null)
                EventSystem.current.SetSelectedGameObject(_previousSelection);
        }

        // We inherit the state abstract class mostly just so we can throw ourselves on the state machine and "pause" it

        public override void Enter(MonoStateMachine controller)
        {
            // TODO: Eventually we'll have to make sure we react properly if controllers plug in/out
            //ReInput.ControllerConnectedEvent += OnControllerConnected;
            //ReInput.ControllerDisconnectedEvent += OnControllerDisconnected;
            //Events.Subscribe(Events.INPUT_MODE_CHANGED, () => { });
        }

        public override void Exit(MonoStateMachine controller)
        {
            //ReInput.ControllerConnectedEvent -= OnControllerConnected;
            //ReInput.ControllerDisconnectedEvent -= OnControllerDisconnected;
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            if (Controls.UsingController && _controls.GetButtonDown(UserActions.UICANCEL))
            {
                _uiSounds.PlayButtonClickSound();

                // We used to have the cancel button always send a OnConfirm, which is weird, not sure why we had it
                // like that. Hope this new logic is good wherever we need it. Only testing this in GTW at the moment.                
                if(_cancelButton.gameObject.activeSelf)
                {
                    OnCancelButtonPressed();
                }
                else
                {
                    OnConfirmButtonPressed();
                }
            }
            return this;
        }
    }
}