// Copyright Isto Inc.
using I2.Loc;
using Isto.Core.Beings;
using Isto.Core.Configuration;
using Isto.Core.Enums;
using Isto.Core.Inputs;
using Isto.Core.Localization;
using Isto.Core.StateMachine;
using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    // Confusing between popup dialog boxes and conversation dialogue, but somewhat standard.
    // Consider renaming to some type of Popup variant or something else similar?
    // Otherwise at least try to unify the language for all our popup class names.
    /// <summary>
    /// This is a message popup that lives in the essentials scene to show the user game information at key moments
    /// during gameplay.
    /// </summary>
    public class UIDialogueMenuState : MonoState
    {
        // TODO: move to atrio
        public enum MessageType
        {
            GameStart,
            FuelDepot,
            TutorialComplete,
            DemoComplete,
            CircuitJuiceWarning,
            FirstTimeCircuitJuice,
            CircuitJuiceRefillUnlocked,
            GameCompleted,
            EquippingTutorial,
            PlacingLightbulbTutorial,
            ResearchBackpackTutorial,
            ResearchLightbulbTutorial,
            EarlyAccessMainGameCompleted,
            AttackWaveIncomingTutorial,
            TornatoadTutorial,
            BeeBoxTutorial,
            RechargeableTorchTutorial,
            FreePlayStart,
            FreePlayComplete,
            EntangledChestTutorial
        }


        // Extend this enumeration in your project.
        // Is it safe to edit this list?
        public class MessageTypeEnum : Int32Enum<MessageTypeEnum>
        {
            public MessageTypeEnum(int value, string name) : base(value, name)
            {
            }
        }

        public class AtrioMessageTypeEnum : MessageTypeEnum
        {
            public static readonly AtrioMessageTypeEnum GAMESTART = new AtrioMessageTypeEnum((int)MessageType.GameStart, nameof(GAMESTART));
            public static readonly AtrioMessageTypeEnum FUELDEPOT = new AtrioMessageTypeEnum((int)MessageType.FuelDepot, nameof(FUELDEPOT));
            public static readonly AtrioMessageTypeEnum TUTORIALCOMPLETE = new AtrioMessageTypeEnum((int)MessageType.TutorialComplete, nameof(TUTORIALCOMPLETE));
            public static readonly AtrioMessageTypeEnum DEMOCOMPLETE = new AtrioMessageTypeEnum((int)MessageType.DemoComplete, nameof(DEMOCOMPLETE));
            public static readonly AtrioMessageTypeEnum CIRCUITJUICEWARNING = new AtrioMessageTypeEnum((int)MessageType.CircuitJuiceWarning, nameof(CIRCUITJUICEWARNING));
            public static readonly AtrioMessageTypeEnum FIRSTTIMECIRCUITJUICE = new AtrioMessageTypeEnum((int)MessageType.FirstTimeCircuitJuice, nameof(FIRSTTIMECIRCUITJUICE));
            public static readonly AtrioMessageTypeEnum CIRCUITJUICEREFILLUNLOCKED = new AtrioMessageTypeEnum((int)MessageType.CircuitJuiceRefillUnlocked, nameof(CIRCUITJUICEREFILLUNLOCKED));
            public static readonly AtrioMessageTypeEnum GAMECOMPLETED = new AtrioMessageTypeEnum((int)MessageType.GameCompleted, nameof(GAMECOMPLETED));
            public static readonly AtrioMessageTypeEnum EQUIPPINGTUTORIAL = new AtrioMessageTypeEnum((int)MessageType.EquippingTutorial, nameof(EQUIPPINGTUTORIAL));
            public static readonly AtrioMessageTypeEnum PLACINGLIGHTBULBTUTORIAL = new AtrioMessageTypeEnum((int)MessageType.PlacingLightbulbTutorial, nameof(PLACINGLIGHTBULBTUTORIAL));
            public static readonly AtrioMessageTypeEnum RESEARCHBACKPACKTUTORIAL = new AtrioMessageTypeEnum((int)MessageType.ResearchBackpackTutorial, nameof(RESEARCHBACKPACKTUTORIAL));
            public static readonly AtrioMessageTypeEnum RESEARCHLIGHTBULBTUTORIAL = new AtrioMessageTypeEnum((int)MessageType.ResearchLightbulbTutorial, nameof(RESEARCHLIGHTBULBTUTORIAL));
            public static readonly AtrioMessageTypeEnum EARLYACCESSMAINGAMECOMPLETED = new AtrioMessageTypeEnum((int)MessageType.EarlyAccessMainGameCompleted, nameof(EARLYACCESSMAINGAMECOMPLETED));
            public static readonly AtrioMessageTypeEnum ATTACKWAVEINCOMINGTUTORIAL = new AtrioMessageTypeEnum((int)MessageType.AttackWaveIncomingTutorial, nameof(ATTACKWAVEINCOMINGTUTORIAL));
            public static readonly AtrioMessageTypeEnum TORNATOADTUTORIAL = new AtrioMessageTypeEnum((int)MessageType.TornatoadTutorial, nameof(TORNATOADTUTORIAL));
            public static readonly AtrioMessageTypeEnum BEEBOXTUTORIAL = new AtrioMessageTypeEnum((int)MessageType.BeeBoxTutorial, nameof(BEEBOXTUTORIAL));
            public static readonly AtrioMessageTypeEnum RECHARGEABLETORCHTUTORIAL = new AtrioMessageTypeEnum((int)MessageType.RechargeableTorchTutorial, nameof(RECHARGEABLETORCHTUTORIAL));
            public static readonly AtrioMessageTypeEnum FREEPLAYSTART = new AtrioMessageTypeEnum((int)MessageType.FreePlayStart, nameof(FREEPLAYSTART));
            public static readonly AtrioMessageTypeEnum FREEPLAYCOMPLETE = new AtrioMessageTypeEnum((int)MessageType.FreePlayComplete, nameof(FREEPLAYCOMPLETE));
            public static readonly AtrioMessageTypeEnum ENTANGLEDCHESTTUTORIAL = new AtrioMessageTypeEnum((int)MessageType.EntangledChestTutorial, nameof(ENTANGLEDCHESTTUTORIAL));

            public AtrioMessageTypeEnum(int value, string name) : base(value, name)
            {
            }
        }

        public class DialogueMessageArgs : EventArgs
        {
            public MessageType type;

            public DialogueMessageArgs(MessageType type)
            {
                this.type = type;
            }
        }

        public event EventHandler<DialogueMessageArgs> DialogueOpen;

        [Header("Regular Message Box")]
        [SerializeField] private CanvasGroup _canvasGroup = default;
        [SerializeField] private TextMeshProUGUI _titleText = default;
        [SerializeField] private AnimationClip _taskComplete = null;
        [SerializeField] private Animator _anim = default;
        [SerializeField] private Button _confirmButton = default;
        [SerializeField] private Button _feedbackButton = default;

        [Header("Large Message Box")]
        [SerializeField] private CanvasGroup _largeCanvasGroup = default;
        [SerializeField] private TextMeshProUGUI _largeTitleText = default;
        [SerializeField] private Animator _largeAnim = default;
        [SerializeField] private CoreButtonController _largeConfirmButton = default;

        [Header("Loc refs")]
        [SerializeField] private LocalizedString _defaultTitleKey;
        [SerializeField] private LocalizedString _defaultConfirmButtonKey;

        [Header("Message Types")]
        [SerializeField] private GameObject _textOnlyParent;
        [SerializeField] private TextMeshProUGUI _textOnlyMessageTxt = default;

        [SerializeField] private GameObject _textWithImageParent;
        [SerializeField] private TextMeshProUGUI _textWithImageMessageTxt = default;
        [SerializeField] private Image _infoImage;

        [SerializeField] private TextMeshProUGUI _largeBoxPreImageMessageTxt = default;
        [SerializeField] private Image _largeBoxInfoImage;
        [SerializeField] private TextMeshProUGUI _largeBoxPostImageMessageTxt = default;

        private Dictionary<MessageType, string> _messages = new Dictionary<MessageType, string>()
        {
            { MessageType.GameStart, "MainTasks/GameStart/Content" },
            { MessageType.FuelDepot, "MainTasks/FuelDepot/Content" },
            { MessageType.TutorialComplete, "MainTasks/TutorialComplete/Content" },
            { MessageType.DemoComplete, "MainTasks/DemoComplete/Content" },
            { MessageType.CircuitJuiceWarning, "MainTasks/CircuitJuiceWarning/Content" },
            { MessageType.FirstTimeCircuitJuice, "MainTasks/FirstTimeCircuitJuice/Content" },
            { MessageType.CircuitJuiceRefillUnlocked, "MainTasks/CircuitJuiceRefillUnlocked/Content" },
            { MessageType.GameCompleted, "MainTasks/GameCompleted/Content" },
            { MessageType.EquippingTutorial, "MainTasks/EquippingTutorial/Content" },
            { MessageType.PlacingLightbulbTutorial, "MainTasks/PlacingLightbulbTutorial/Content" },
            { MessageType.ResearchBackpackTutorial, "MainTasks/ResearchBackpackTutorial/Content" },
            { MessageType.ResearchLightbulbTutorial, "MainTasks/ResearchLightbulbTutorial/Content" },
            { MessageType.EarlyAccessMainGameCompleted, "MainTasks/EarlyAccessMainGameCompleted/Content" },
            { MessageType.AttackWaveIncomingTutorial, "MainTasks/AttackWaveIncomingTutorial/Content" },
            { MessageType.TornatoadTutorial, "MainTasks/TornatoadTutorial/Content" },
            { MessageType.BeeBoxTutorial, "MainTasks/BeeBoxTutorial/Content" },
            { MessageType.RechargeableTorchTutorial, "MainTasks/RechargeTorchTutorial/Content" },
            { MessageType.FreePlayStart, "MainTasks/FreePlayStart/Content" },
            { MessageType.FreePlayComplete, "MainTasks/FreePlayComplete/Content" },
            { MessageType.EntangledChestTutorial, "MainTasks/EntangledChest/Content" },
        };

        private Action _callbackOnClose;
        private bool _usingLargePopup = false;
        private MessageType _currentMessage;
        private Controls.Mode _previousMode;
        private Selectable _currentSelection = null;
        private Canvas _mainCanvas;

        // Injected
        private IControls _controls;
        private IUIGameMenu _mainMenu;
        private GameplaySettings _settings;
        private AutomationPlayerController _playerController;

        [Inject]
        public void Inject(IControls controls, IUIGameMenu mainMenu, GameplaySettings gameplaySettings, AutomationPlayerController playerController)
        {
            _controls = controls;
            _mainMenu = mainMenu;
            _settings = gameplaySettings;
            _playerController = playerController;
        }

        private void Awake()
        {
            TryGetComponent(out _mainCanvas);

            if (_mainCanvas)
                _mainCanvas.enabled = false;
        }

        public override void Enter(MonoStateMachine controller)
        {
            //Debug.Log("Entering UIDialogueMenuState");

            if (_mainCanvas)
                _mainCanvas.enabled = true;

            _previousMode = _controls.GetControlMode();

            _playerController.PlaceAdvItemState.ForceCancelDrop();
            _controls.SetControlMode(Controls.Mode.UI);
            _controls.DisableControls(1f);     // Adding extra delay to prevent closing quickly without having a chance to see pop up

            _currentSelection = _usingLargePopup ? _largeConfirmButton.GetSelectable() : _confirmButton;
            CanvasGroup canvas = _usingLargePopup ? _largeCanvasGroup : _canvasGroup;
            StartCoroutine(UIUtils.SetCanvasAlpha(canvas, 1, Constants.CANVAS_FADE_TIME, interactable: true, _currentSelection));

            Events.Subscribe(Events.INPUT_MODE_CHANGED, OnInputModeChanged);
        }

        public override void Exit(MonoStateMachine controller)
        {
            Events.UnSubscribe(Events.INPUT_MODE_CHANGED, OnInputModeChanged);
            _currentSelection = null;

            if (_usingLargePopup)
                StartCoroutine(UIUtils.SetCanvasAlpha(_largeCanvasGroup, 0, Constants.CANVAS_FADE_TIME, interactable: false));
            else
                StartCoroutine(UIUtils.SetCanvasAlpha(_canvasGroup, 0, Constants.CANVAS_FADE_TIME, interactable: false));

            _controls.SetControlMode(_previousMode);

            if (_mainCanvas)
                _mainCanvas.enabled = false;
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
            Debug.LogError($"Shouldn't be coming back from sub state in dialogue menu.  Previous state: {previousState.name}. Forcing all menus closed");

            if (controller is IUIGameMenu mainMenu)
                mainMenu.CloseMenu();
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            if (_controls.GetButtonUp(UserActions.PAUSE) && _controls.GetButtonUp(UserActions.UICANCEL))
            {
                ExitMenu();
            }

            return this;
        }

        /// <summary>
        /// Looks up the message from the internal dictionary and then opens the pop up
        /// </summary>
        /// <param name="type"></param>
        /// <param name="callbackOnClose"></param>
        public void OpenPopUp(MessageType type, Action callbackOnClose = null)
        {
            if (_messages.ContainsKey(type))
            {
                _currentMessage = type;
                OpenPopUp(Loc.Get(_defaultTitleKey), Loc.GetString(_messages[type]), callbackOnClose);

                // If is game complete message, also show feedback button, else hide it
                _feedbackButton.gameObject.SetActive(type == MessageType.GameCompleted);
            }
            else
            {
                Debug.LogWarning("No message for type: " + type);
            }
        }

        public void OpenLargePopUp(MessageType type, Action callbackOnClose = null)
        {
            if (_messages.ContainsKey(type))
            {
                _currentMessage = type;
                OpenLargePopUp(Loc.Get(_defaultTitleKey), Loc.GetString(_messages[type]), centerImage: null, localizedSecondMessage: null, Loc.Get(_defaultConfirmButtonKey), callbackOnClose);

                // If is game complete message, also show feedback button, else hide it
                _feedbackButton.gameObject.SetActive(type == MessageType.GameCompleted);
            }
            else
            {
                Debug.LogWarning("No message for type: " + type);
            }
        }

        public void OpenPopUp(MessageType type, Sprite image, Action callbackOnClose = null)
        {
            if (_messages.ContainsKey(type))
            {
                _currentMessage = type;
                if (image != null)
                    OpenPopUpWithImage(Loc.Get(_defaultTitleKey), Loc.GetString(_messages[type]), image, callbackOnClose);
                else
                    OpenPopUp(Loc.Get(_defaultTitleKey), Loc.GetString(_messages[type]), callbackOnClose);

                // If is game complete message, also show feedback button, else hide it
                _feedbackButton.gameObject.SetActive(type == MessageType.GameCompleted);
            }
            else
            {
                Debug.LogWarning("No message for type: " + type);
            }
        }

        public void OpenPopUp(MessageType type, LocalizedString title, LocalizedString extraMessage, LocalizedString buttonText, Sprite image = null, Action callbackOnClose = null)
        {
            if (_messages.ContainsKey(type))
            {
                _currentMessage = type;

                string titleText = Loc.Get(title); // not optional
                string mainText = Loc.Get(_messages[type]); // not optional... I think
                string extraText = extraMessage != null ? Loc.Get(extraMessage) : null;
                string button = Loc.Get(buttonText); // not optional

                OpenLargePopUp(titleText, mainText, image, extraText, button, callbackOnClose);

                _feedbackButton.gameObject.SetActive(false);
            }
            else
            {
                Debug.LogWarning("No message for type: " + type);
            }
        }

        public void OpenPopUp(string localizedTitle, string localizedMessage, Action callbackOnClose = null)
        {
            _usingLargePopup = false;
            Loc.SetTMProLocalized(_titleText, localizedTitle);
            Loc.SetTMProLocalized(_textOnlyMessageTxt, localizedMessage);
            _callbackOnClose = callbackOnClose;

            _textOnlyParent.SetActive(true);
            _textWithImageParent.SetActive(false);

            _anim.SetTrigger("showMainTask");
            _mainMenu.EnterSubState(this);
        }

        private void OpenPopUpWithImage(string localizedTitle, string localizedMessage, Sprite image, Action callbackOnClose = null)
        {
            _usingLargePopup = false;
            Loc.SetTMProLocalized(_titleText, localizedTitle);
            Loc.SetTMProLocalized(_textWithImageMessageTxt, localizedMessage);

            _callbackOnClose = callbackOnClose;

            _infoImage.sprite = image;
            _textOnlyParent.SetActive(false);
            _textWithImageParent.SetActive(true);

            _anim.SetTrigger("showMainTask");
            _mainMenu.EnterSubState(this);
        }

        private void OpenLargePopUp(string localizedTitle, string localizedFirstMessage, Sprite centerImage, string localizedSecondMessage, string localizedButtonText, Action callbackOnClose = null)
        {
            _usingLargePopup = true;
            Loc.SetTMProLocalized(_largeTitleText, localizedTitle);
            Loc.SetTMProLocalized(_largeBoxPreImageMessageTxt, localizedFirstMessage);
            Loc.SetTMProLocalized(_largeBoxPostImageMessageTxt, localizedSecondMessage);
            _largeConfirmButton.SetText(localizedButtonText);
            _callbackOnClose = callbackOnClose;
            _largeBoxInfoImage.sprite = centerImage;
            _largeBoxInfoImage.gameObject.SetActive(centerImage != null);

            _largeAnim.SetTrigger("showMainTask");
            _mainMenu.EnterSubState(this);
        }

        public void OnCloseButtonClicked()
        {
            ExitMenu();
        }

        public void OnFeedbackButtonClicked()
        {
            Application.OpenURL(_settings.feedbackUrl);
        }

        public IEnumerator RunTaskComplete()
        {
            StartCoroutine(UIUtils.SetCanvasAlpha(_canvasGroup, 1f, Constants.CANVAS_FADE_TIME, interactable: true));
            _anim.SetTrigger("taskComplete");

            yield return new WaitForSeconds(_taskComplete.length);

            DialogueOpen?.Invoke(this, new DialogueMessageArgs(_currentMessage));
        }

        /*public bool IsPopupOpen()
        {
            UIMainMenuStateMachine stateMachine = _mainMenu as UIMainMenuStateMachine;
            return stateMachine != null && stateMachine.GetCurrentState() == this;
        }*/

        private void ExitMenu()
        {
            _callbackOnClose?.Invoke();
            _mainMenu.ExitSubState();
        }

        private void OnInputModeChanged()
        {
            EventSystem.current.SetSelectedGameObject(Controls.UsingController ? _currentSelection.gameObject : null);
        }

        [ContextMenu("OpenTestPopup")]
        private void OpenTestPopup()
        {
            OpenPopUp(MessageType.FreePlayComplete, _defaultTitleKey, extraMessage: null, _defaultTitleKey, image: null);
        }
    }
}