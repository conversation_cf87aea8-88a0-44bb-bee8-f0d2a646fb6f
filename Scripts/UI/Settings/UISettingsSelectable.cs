// Copyright Isto Inc.

using Isto.Core.Audio;
using Isto.Core.Inputs;
using System;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    /// <summary>
    /// Base class that handles the navigation and input events for the Selectable items within the Settings menu.
    /// </summary>
    public abstract class UISettingsSelectable : Selectable
    {
        // UNITY HOOKUP

        [Header("UI Component Hookups")]
        [SerializeField] protected TextMeshProUGUI _titleText;
        [SerializeField] protected GameObject _optionDirtyFlag;

        [Header("Selection Highlighting")]
        [SerializeField] protected ColorDataObject _selectedColorDataObject;
        [SerializeField] protected ColorDataObject _normalColorDataObject;

        [Header("Enable Hover Sound")]
        [SerializeField] private bool _playSoundOnHover = true;

        [Header("Xbox Specific")]
        [SerializeField] private bool _removeFromXboxBuild = false;


        //OTHER FIELDS

        protected bool _isSelected;
        private bool _isDirty;
        private bool _isHorizontalAxisInUse;
        private bool _isPointerOnUI;


        // PROPERTIES

        /// <summary>
        /// Determines if this setting should be removed from the xbox build
        /// </summary>
        public bool RemoveFromXboxBuild => _removeFromXboxBuild;
        public bool IsDirty => _isDirty;


        // EVENTS

        protected Action OnDirectionLeft;
        protected Action OnDirectionRight;


        // INJECTION

        protected IGameSounds _gameSounds;

        [Inject]
        public void Inject(IGameSounds gameSounds)
        {
            _gameSounds = gameSounds;
        }


        // EVENT HANDLING

        public override void OnSelect(BaseEventData eventData)
        {
#if BUTTON_LOGGING
            Debug.Log($"{gameObject.name} UISettingsSelectable.OnSelect", this.gameObject);
#endif

            base.OnSelect(eventData);
            SetSelection(true);
            if (_playSoundOnHover)
            {
                _gameSounds.PlayOneShot(UISoundEnum.BUTTON_HOVER, transform.position);
            }
        }

        public override void OnDeselect(BaseEventData eventData)
        {
#if BUTTON_LOGGING
            Debug.Log($"{gameObject.name} UISettingsSelectable.OnDeselect", this.gameObject);
#endif

            // For mouse navigation, if selection moves to a selectable displayed on top of us, we want it to look like
            // it is a part of us, and so we ignore the deselection event. We'll deselect properly when pointer leaves.
            if (_isPointerOnUI && !Controls.UsingController)
            {
                return;
            }

            base.OnDeselect(eventData);
            SetSelection(false);
        }

        public override void OnPointerEnter(PointerEventData eventData)
        {
#if BUTTON_LOGGING
            Debug.Log($"{gameObject.name} UISettingsSelectable.OnPointerEnter", this.gameObject);
#endif
            base.OnPointerEnter(eventData);
            Select();
            _isPointerOnUI = true;
        }

        public override void OnPointerExit(PointerEventData eventData)
        {
#if BUTTON_LOGGING
            Debug.Log($"{gameObject.name} UISettingsSelectable.OnPointerExit", this.gameObject);
#endif
            base.OnPointerExit(eventData);
            _isPointerOnUI = false;
            Deselect();
        }

        private void Deselect()
        {
            // Make sure we only delete current selection if we own it
            if (EventSystem.current.currentSelectedGameObject == this.gameObject)
            {
                EventSystem.current.SetSelectedGameObject(null);
            }
            else
            {
                // In OnDeselect we abort the deselection flow when we're entering a selectable that is on top of us,
                // which means we still look selected, though we're not. But, we won't know when the pointer comes back.
                // So, when that flow happens, we need to force late deselection here, restoring balance to the button.
                if (_isSelected)
                {
                    base.OnDeselect(eventData: null); // Data not needed
                    SetSelection(false);
                }
            }
        }

        public void HandleRightInput()
        {
            OnDirectionRight?.Invoke();
        }

        public void HandleLeftInput()
        {
            OnDirectionLeft?.Invoke();
        }


        // ACCESSORS

        public void SetDirtyFlag(bool isDirty)
        {
            _isDirty = isDirty;
            _optionDirtyFlag.SetActive(_isDirty);
        }

        public void SetSelection(bool isSelected)
        {
            IData data = isSelected ? _selectedColorDataObject.GetData() : _normalColorDataObject.GetData();
            _titleText.color = ((ColorData)data).Evaluate(0);
            _isSelected = isSelected;
        }
    }
}