// Copyright Isto Inc.

using Isto.Core.Audio;
using Isto.Core.Enums;
using System;
using UnityEngine;

namespace Isto.Core.UI
{
    /// <summary>
    /// Slider-based setting option, allowing the user to select a value via a UI Slider.
    /// </summary>
    public class UISettingsOptionSlider : UISettingsSelectable
    {
        // UNITY HOOKUP

        [Header("Settings Hookup")]
        [SerializeField] private UISlider _uiSlider;

        [Header("Slider Configuration")]
        [SerializeField] private bool _playSliderSound;

        [EnumDropdown(typeof(SettingsSoundEnum))]
        [SerializeField] private int _settingsSoundEnum;

        // EVENTS

        public event Action<float> OnValueChangedPercentage;


        // LIFECYCLE EVENTS

        protected override void OnEnable()
        {
            RegisterEvents();
        }

        protected override void OnDisable()
        {
            UnregisterEvents();
        }


        // EVENT HANDLING

        private void RegisterEvents()
        {
            _uiSlider.Slider.onValueChanged.AddListener(UISlider_OnValueChanged);
            _uiSlider.OnPointerUp += PlaySound;
            OnDirectionRight += SettingsSelectable_OnDirectionRight;
            OnDirectionLeft += SettingsSelectable_OnDirectionLeft;
        }

        private void UnregisterEvents()
        {
            _uiSlider.Slider.onValueChanged.RemoveListener(UISlider_OnValueChanged);
            _uiSlider.OnPointerUp -= PlaySound;
            OnDirectionRight -= SettingsSelectable_OnDirectionRight;
            OnDirectionLeft -= SettingsSelectable_OnDirectionLeft;
        }

        private void SettingsSelectable_OnDirectionRight()
        {
            ChangeSliderValue(_uiSlider.IncreaseValue);
        }

        private void SettingsSelectable_OnDirectionLeft()
        {
            ChangeSliderValue(_uiSlider.DecreaseValue);
        }

        private void UISlider_OnValueChanged(float sliderValue)
        {
            OnValueChangedPercentage?.Invoke(sliderValue / _uiSlider.Slider.maxValue);
        }


        // ACCESSORS

        public void SetSliderAmountPercentage(float sliderPercentage)
        {
            _uiSlider.Slider.value = sliderPercentage * _uiSlider.Slider.maxValue;
        }

        // OTHER METHODS

        private void PlaySound()
        {
            if (!_playSliderSound)
                return;

            var temp = SettingsSoundEnum.GetByValue(_settingsSoundEnum);

            if (temp != null)
            {
                _gameSounds.PlayOneShot(temp, transform.position);
            }
            else
            {
                Debug.LogError($"You are trying to play a sound for slider {name} which is currently null. " +
                               $"Make sure that the sound is not null or disable the PlaySound boolean in the Editor.");
            }
        }

        private void ChangeSliderValue(Action sliderAction)
        {
            sliderAction();
            PlaySound();
        }
    }
}