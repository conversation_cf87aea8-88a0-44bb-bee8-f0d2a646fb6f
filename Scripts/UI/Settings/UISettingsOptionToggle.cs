// Copyright Isto Inc.

using Isto.Core.UI;
using System;
using UnityEngine;
using UnityEngine.Serialization;

/// <summary>
/// Toggle-based setting option, allowing the user to enable or disable a settings value.
/// </summary>
public class UISettingsOptionToggle : UISettingsSelectable
{
    // UNITY HOOKUP

    [FormerlySerializedAs("_coreCarousel")]
    [Header("Settings Hookup")]
    [SerializeField] protected UICarousel _uiCarousel;


    // EVENTS

    public event Action<bool> OnValueChanged;


    // LIFECYCLE EVENTS

    protected override void OnEnable()
    {
        base.OnEnable();
        RegisterEvents();
    }

    protected override void OnDisable()
    {
        base.OnDisable();
        UnregisterEvents();
    }


    // EVENT HANDLING

    private void RegisterEvents()
    {
        _uiCarousel.OnSelectionChanged += UICarousel_OnSelectionChanged;
        OnDirectionRight += UISettingsSelectable_MoveRight;
        OnDirectionLeft += UISettingsSelectable_MoveLeft;
    }

    private void UnregisterEvents()
    {
        _uiCarousel.OnSelectionChanged -= UICarousel_OnSelectionChanged;
        OnDirectionRight -= UISettingsSelectable_MoveRight;
        OnDirectionLeft -= UISettingsSelectable_MoveLeft;
    }

    private void UISettingsSelectable_MoveLeft()
    {
        _uiCarousel.MoveSelectionLeft();
    }

    private void UISettingsSelectable_MoveRight()
    {
        _uiCarousel.MoveSelectionRight();
    }

    private void UICarousel_OnSelectionChanged(int currentSelection)
    {
        // This check should not be needed, but it is here just in case.
        if (currentSelection > 1 || currentSelection < 0)
        {
            throw new IndexOutOfRangeException();
        }

        bool isEnabled = currentSelection == 1;
        OnValueChanged?.Invoke(isEnabled);
    }


    // ACCESSORS

    public void SetToggleValue(bool isEnabled)
    {
        int selectionValue = isEnabled ? 1 : 0;
        _uiCarousel.SetCarouselSelection(selectionValue);
    }

    public void SetToggleValueNoNotify(bool isEnabled)
    {
        int selectionValue = isEnabled ? 1 : 0;
        _uiCarousel.SetCarouselSelection(selectionValue, false);
    }
}