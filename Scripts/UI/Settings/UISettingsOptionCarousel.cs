// Copyright Isto Inc.

using Isto.Core.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Serialization;

namespace Isto.Core.UI
{
    /// <summary>
    /// Carousel settings option that allows the user to select from a list of items.
    /// </summary>
    public class UISettingsOptionCarousel : UISettingsSelectable
    {
        // UNITY HOOKUP

        [FormerlySerializedAs("_coreCarousel")] 
        [Header("Settings Hookup")] 
        [SerializeField] protected UICarousel _uiCarousel;


        // EVENTS

        public event Action<LocExpression> OnValueChanged;


        // LIFECYCLE EVENTS

        protected override void OnEnable()
        {
            RegisterEvents();
        }

        protected override void OnDisable()
        {
            UnregisterEvents();
        }


        // EVENT HANDLING

        private void RegisterEvents()
        {
            _uiCarousel.OnSelectionChanged += UICarousel_OnSelectionChanged;
            OnDirectionRight += SettingsOptionCarousel_OnDirectionRightMethod;
            OnDirectionLeft += SettingsOptionCarousel_OnDirectionLeftMethod;
        }

        private void UnregisterEvents()
        {
            _uiCarousel.OnSelectionChanged -= UICarousel_OnSelectionChanged;
            OnDirectionRight -= SettingsOptionCarousel_OnDirectionRightMethod;
            OnDirectionLeft -= SettingsOptionCarousel_OnDirectionLeftMethod;
        }

        private void UICarousel_OnSelectionChanged(int index)
        {
            LocExpression coreCarouselCarouselValue = _uiCarousel.CarouselValues[index];
            OnValueChanged?.Invoke(coreCarouselCarouselValue);
        }

        private void SettingsOptionCarousel_OnDirectionRightMethod()
        {
            _uiCarousel.MoveSelectionRight();
        }

        private void SettingsOptionCarousel_OnDirectionLeftMethod()
        {
            _uiCarousel.MoveSelectionLeft();
        }


        // ACCESSORS
        
        public void SetCarouselList(List<LocExpression> optionsList, int currentSelection = 0)
        {
            _uiCarousel.SetCarouselList(optionsList, currentSelection);
        }
        
        public void SetCarouselList(List<LocExpression> optionsList, LocExpression currentSelection)
        {
            int selectionIndex = optionsList.IndexOf(currentSelection);
            if (selectionIndex < 0)
            {
                Debug.LogError($"carousel selection {currentSelection.ToString()} not found among carousel values on {this.gameObject.name}", this.gameObject);
            }
            _uiCarousel.SetCarouselList(optionsList, selectionIndex);
        }
        
        public void SetCarouselList(List<RepositionableLocExpression> optionsListRepositionable, RepositionableLocExpression currentSelectionRepositionable)
        {
            List<LocExpression> optionsList = optionsListRepositionable.Cast<LocExpression>().ToList();
            SetCarouselList(optionsList, currentSelectionRepositionable);
        }
        
        public void SetCarouselList(List<MultiTermLocExpression> optionsListMultiTerm, MultiTermLocExpression currentSelectionMultiTerm)
        {
            List<LocExpression> optionsList = optionsListMultiTerm.Cast<LocExpression>().ToList();
            SetCarouselList(optionsList, currentSelectionMultiTerm);
        }

        public void SetCarouselSelectionNoNotify(LocExpression carouselSelection)
        {
            int selectionIndex = _uiCarousel.CarouselValues.IndexOf(carouselSelection);
            if(selectionIndex < 0)
            {
                Debug.LogError($"carousel selection {carouselSelection.ToString()} not found among carousel values on {this.gameObject.name}", this.gameObject);
            }
            _uiCarousel.SetCarouselSelection(selectionIndex, false);
        }

        public void SetCarouselSelectionNoNotify(int selectionIndex)
        {
            _uiCarousel.SetCarouselSelection(selectionIndex, false);
        }
    }
}