// Copyright Isto Inc.

using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace Isto.Core.UI
{
    /// <summary>
    /// Remap settings option that allows the user to change their controls for Keyboard and Mouse or Gamepad.
    /// </summary>
    public class UISettingsOptionRemap : UISettingsSelectable, ISubmitHandler
    {

        private enum RemapStyleEnum
        {
            Primary,
            Secondary,
            Locked
        }


        // UNITY HOOKUP

        [Header("Settings Hookup")]
        [SerializeField] private UIControlRebindingBase _controlRebinding;
        [SerializeField] private Image _backgroundImage;

        [Header("Locked Settings")]
        [SerializeField] private TextMeshProUGUI _lockedText;
        [SerializeField] private ColorDataObject _lockedTextColor;

        [Header("New Settings")]
        [SerializeField] private RemapStyleEnum _remapStyleEnum;
        [SerializeField] private ColorDataObject _backgroundPrimaryColor;
        [SerializeField] private ColorDataObject _backgroundSecondaryColor;
        [SerializeField] private ColorDataObject _backgroundLockedColor;


        // OTHER FIELDS

        private static readonly float LOCKED_TEXT_SPACING = 42f;
        private Vector2 _currentLockedLocation = Vector2.zero;
        private RemapStyleEnum _previousRemapStyle;


        // LIFECYCLE EVENTS

        protected override void Awake()
        {
            base.Awake();
            UpdateLockedVisuals();
            if (_remapStyleEnum == RemapStyleEnum.Locked)
            {
                _normalColorDataObject = _lockedTextColor;
            }
        }

        private void Update()
        {
            if (_titleText.rectTransform.hasChanged)
            {
                UpdateLockedTextRectTransform();
                _titleText.rectTransform.hasChanged = false;
            }
        }

        public void OnSubmit(BaseEventData eventData)
        {
            RemapSelected();
        }

        public override void OnPointerDown(PointerEventData eventData)
        {
            if (eventData.button == PointerEventData.InputButton.Left)
            {
                RemapSelected();
            }
        }

#if UNITY_EDITOR
        /// <summary>
        /// When we lock the settings option within the Editor, this will update the visuals in the Unity Editor
        /// so that we know what the controls UI will look like.
        /// </summary>
        private void OnGUI()
        {
            if (_remapStyleEnum == _previousRemapStyle || _isSelected)
            {
                return;
            }

            UpdateLockedTextRectTransform();
            UpdateLockedVisuals();
            _previousRemapStyle = _remapStyleEnum;
        }
#endif

        /// <summary>
        /// To avoid using the Horizontal Layout group to align the "(Locked)" text for our controller remaps
        /// we instead get the settings title and calculate where the rect transform for the text ends and then add
        /// a buffer.
        /// </summary>
        private void UpdateLockedTextRectTransform()
        {
            Rect rect = _titleText.rectTransform.rect;
            Vector2 vectorLocation = new Vector2(rect.x + rect.width + LOCKED_TEXT_SPACING, _lockedText.rectTransform.anchoredPosition.y);

            if (vectorLocation != _currentLockedLocation)
            {
                _lockedText.rectTransform.anchoredPosition = vectorLocation;
                _currentLockedLocation = vectorLocation;
            }
        }

        // OTHER METHODS

        private void UpdateLockedVisuals()
        {
            IData data = _remapStyleEnum == RemapStyleEnum.Locked ? _lockedTextColor.GetData() : _normalColorDataObject.GetData();
            bool active = _remapStyleEnum == RemapStyleEnum.Locked;
            ToggleVisuals(data, active);
        }

        private void ToggleVisuals(IData data, bool active)
        {
            UpdateBackgroundImageColor();
            _backgroundImage.enabled = true;

            ColorData colorData = (ColorData)data;
            _titleText.color = colorData.Evaluate(0);
            _lockedText.gameObject.SetActive(active);
            interactable = !active;
        }

        private void UpdateBackgroundImageColor()
        {
            if (_remapStyleEnum == RemapStyleEnum.Primary)
            {
                _backgroundImage.color = _backgroundPrimaryColor.data.Evaluate(0);
            } else if (_remapStyleEnum == RemapStyleEnum.Secondary)
            {
                _backgroundImage.color = _backgroundSecondaryColor.data.Evaluate(0);
            } else if(_remapStyleEnum == RemapStyleEnum.Locked)
            {
                _backgroundImage.color = _backgroundLockedColor.data.Evaluate(0);
            }
        }

        private void RemapSelected()
        {
            if (!_isSelected || _remapStyleEnum == RemapStyleEnum.Locked)
            {
                return;
            }

            _controlRebinding.BindingButtonClicked();
        }
    }
}