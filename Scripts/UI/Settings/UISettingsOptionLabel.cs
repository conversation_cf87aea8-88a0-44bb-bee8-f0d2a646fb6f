// Copyright Isto Inc.

using Isto.Core.Localization;
using TMPro;
using UnityEngine;

namespace Isto.Core.UI
{
    public class UISettingsOptionLabel : UISettingsSelectable
    {
        // UNITY HOOKUP

        [Header("Settings Hookup")] 
        [SerializeField] private TextMeshProUGUI _label;


        // ACCESSORS

        public void SetLabel(string newLabel)
        {
            _label.text = newLabel;
        }

        public void SetLabel(LocExpression locTermString)
        {
            locTermString.LocalizeInto(_label);
        }
    }
}
