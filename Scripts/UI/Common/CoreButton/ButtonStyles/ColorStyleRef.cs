using UnityEngine;

namespace Isto.Core.UI.ButtonStyles
{
    [CreateAssetMenu(fileName = "ColorStyleRef", menuName = "Scriptables/Core Button/Color Style Ref")]
    [System.Serializable]
    public class ColorStyleRef : ColorStyle
    {
        public ColorStateDataRef stateData = new ColorStateDataRef();
        
        public override IStateData GetStateData()
        {
            return stateData;
        }
    }
}