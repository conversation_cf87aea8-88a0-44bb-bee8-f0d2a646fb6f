using UnityEngine;

namespace Isto.Core.UI.ButtonStyles
{
    [CreateAssetMenu(fileName = "ColorStyleValue", menuName = "Scriptables/Core Button/Color Style Value")]
    [System.Serializable]
    public class ColorStyleValue : ColorStyle
    {
        public ColorStateDataValue stateData = new ColorStateDataValue();
        
        public override IStateData GetStateData()
        {
            return stateData;
        }
    }
}