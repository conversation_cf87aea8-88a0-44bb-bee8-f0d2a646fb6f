// unset
using UnityEngine;

namespace Isto.Core.UI.ButtonStyles
{
    [CreateAssetMenu(fileName = "MaterialStyleValue", menuName = "Scriptables/Core Button/Material Style Value")]
    [System.Serializable]
    public class MaterialStyleValue : MaterialStyle
    {
        public MaterialStateDataValue stateData = new MaterialStateDataValue();
        
        public override IStateData GetStateData()
        {
            return stateData;
        }
    }
}