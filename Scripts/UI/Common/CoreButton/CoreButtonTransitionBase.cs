using UnityEngine;

namespace Isto.Core.UI
{
    public abstract class CoreButtonTransitionBase : MonoBehaviour, ICoreButtonTransition
    {
        private CoreButton _coreButton = null;

        public CoreButton CoreButton
        {
            get
            {
                if (_coreButton == null && this != null)
                {
                    _coreButton = GetComponent<CoreButton>();
                }

                return _coreButton;
            }
        }
        
        protected virtual void Reset()
        {
            CoreButton.AddCoreButtonTransition(this);
        }

        protected virtual void OnDestroy()
        {
            CoreButton button = CoreButton;
            if (button != null)
            {
                button.RemoveCoreButtonTransition(this);
            }
        }

        public abstract void DoTransition(CoreButton.CoreButtonSelectionState state, bool instant = true);
    }
}