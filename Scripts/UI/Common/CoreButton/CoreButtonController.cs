// Copyright Isto Inc.
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace Isto.Core.UI
{
    /// <summary>
    /// CoreButtonController act as an interface with the CoreButton features and related components.
    /// </summary>
    public class CoreButtonController : MonoBehaviour
    {
        // UNITY HOOKUP

        [SerializeField] private bool _autoReassignNavigation = true;

        public virtual bool Active => Button.Active;
        public virtual bool Interactable => Button.Interactable;
        public virtual bool IsSelected => Button.IsSelected;


        // OTHER FIELDS

        private CoreButton _coreButton = null;
        private Animator _coreButtonAnimator = null;
        private TextMeshProUGUI _mainText = null;
        private CoreButtonStyleController _styleController = null;
        private Navigation _defaultNavigation;

        protected IDynamicSelectableProvider _upwardNavigationTargetProvider = null;
        protected IDynamicSelectableProvider _downwardNavigationTargetProvider = null;
        protected IDynamicSelectableProvider _leftwardNavigationTargetProvider = null;
        protected IDynamicSelectableProvider _rightwardNavigationTargetProvider = null;


        // PROPERTIES

        private CoreButton Button
        {
            get
            {
                if (_coreButton == null)
                {
                    _coreButton = GetComponent<CoreButton>();
                }

                return _coreButton;
            }
        }

        // LIFECYCLE EVENTS

        protected virtual void Awake()
        {
            _coreButtonAnimator = GetComponent<Animator>();
            _mainText = GetComponentInChildren<TextMeshProUGUI>();
            _styleController = GetComponent<CoreButtonStyleController>();

            if (Button != null)
            {
                _defaultNavigation = Button.navigation;
                Button.OnSelectEvent += CoreButton_OnSelectedEvent;
            }
        }

        protected virtual void OnDestroy()
        {
            if (_coreButton == null)
                return;

            _coreButton.OnSelectEvent -= CoreButton_OnSelectedEvent;
        }


        // EVENT HANDLERS

        private void CoreButton_OnSelectedEvent(BaseEventData eventData)
        {
            if (_autoReassignNavigation)
            {
                UpdateNavigation();
            }
        }


        // OTHER METHODS

        public virtual Selectable GetSelectable()
        {
            return Button;
        }

        public CoreButton GetButton()
        {
            return Button;
        }

        public virtual void Select()
        {
            Button.Select();
        }

        public virtual void Selected()
        {
            //Handled by the UnityEngine.UI.Button when using the CoreButton
            //Use the CoreButton.OnSelectedEvent
        }

        public virtual void DeSelected()
        {
            //Handled by the UnityEngine.UI.Button when using the CoreButton
            //Use the CoreButton.OnDeselectedEvent
        }

        public virtual void SetEnabled()
        {
            if (Button != null && !Button.interactable)
            {
                Button.interactable = true;
            }
        }

        public virtual void SetDisabled()
        {
            if (Button != null && Button.interactable)
            {
                Button.interactable = false;
            }
        }

        public virtual void SetText(string localizedText)
        {
            if (_mainText != null)
            {
                _mainText.text = localizedText;
            }
        }

        public virtual void SetButtonStyle(int id)
        {
            if (_styleController != null)
            {
                _styleController.SetButtonStyle(id);
            }
        }

        public virtual void SetButtonStyle(string name)
        {
            if (_styleController != null)
            {
                _styleController.SetButtonStyle(name);
            }
        }

        public virtual void ResetAnimatorState()
        {
            //Handled by the UnityEngine.UI.Button when using the CoreButton
        }

        protected virtual void UpdateNavigation()
        {
            if (_coreButton == null)
                return;

            Navigation nav = _coreButton.navigation;
            if (nav.mode != Navigation.Mode.Explicit)
                return;

            // restore navigation references if not dynamically overriden
            nav.Copy(_defaultNavigation, true);

            // update navigation cache
            _defaultNavigation = nav;

            // remove destinations that are not interactable
            nav.RemoveUninteractableDestinations();

            nav.SetUp(_upwardNavigationTargetProvider);
            nav.SetDown(_downwardNavigationTargetProvider);
            nav.SetLeft(_leftwardNavigationTargetProvider);
            nav.SetRight(_rightwardNavigationTargetProvider);

            _coreButton.navigation = nav;
        }

        public virtual void SetUpwardNavigationTargetProvider(IDynamicSelectableProvider provider)
        {
            _upwardNavigationTargetProvider = provider;
        }

        public virtual void SetDownwardNavigationTargetProvider(IDynamicSelectableProvider provider)
        {
            _downwardNavigationTargetProvider = provider;
        }

        public virtual void SetLeftwardNavigationTargetProvider(IDynamicSelectableProvider provider)
        {
            _leftwardNavigationTargetProvider = provider;
        }

        public virtual void SetRightwardNavigationTargetProvider(IDynamicSelectableProvider provider)
        {
            _rightwardNavigationTargetProvider = provider;
        }
    }
}