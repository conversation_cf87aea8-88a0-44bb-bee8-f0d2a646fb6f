using Isto.Core.UI.ButtonStyles;

namespace Isto.Core.UI
{
    public abstract class CoreButtonVector1AnimTransition : CoreButtonAnimTransitionBase<Vector1Style, Vector1StateData, Vector1StateDataValue>
    {
        protected override bool Evaluate(CoreButton.CoreButtonSelectionState state, float time)
        {
            Vector1StateData stateData = GetStateData();
            if (stateData != null)
            {
                Vector1Data animCurve = stateData.GetState(state) as Vector1Data;
                if (animCurve != null)
                {
                    SetObjectProperties(animCurve.Evaluate(time));
                    return time >= animCurve.AnimationTimeLength;
                }
            }

            return true;
        }

        protected abstract void SetObjectProperties(float xValue);
    }
}