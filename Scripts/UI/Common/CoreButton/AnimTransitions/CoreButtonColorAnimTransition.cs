using Isto.Core.UI.ButtonStyles;
using UnityEngine;

namespace Isto.Core.UI
{
    public abstract class CoreButtonColorAnimTransition : CoreButtonAnimTransitionBase<ColorStyle, ColorStateData, ColorStateDataValue>
    {
        protected override bool Evaluate(CoreButton.CoreButtonSelectionState state, float time)
        {
            IStateData stateData = GetStateData();
            if (stateData != null)
            {
                IColorData gradientAnim = stateData.GetState(state) as IColorData;
                if (gradientAnim != null)
                {
                    Color color = gradientAnim.Evaluate(time);
                    SetObjectProperties(color);
                    return time >= gradientAnim.AnimationTimeLength;
                }
            }

            return true;
        }
        
        protected abstract void SetObjectProperties(Color color);
    }
}