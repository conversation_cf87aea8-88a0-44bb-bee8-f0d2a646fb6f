using Isto.Core.UI.ButtonStyles;

namespace Isto.Core.UI
{
    public abstract class CoreButtonVector2AnimTransition : CoreButtonAnimTransitionBase<Vector2Style, Vector2StateData, Vector2StateDataValue>
    {
        protected override bool Evaluate(CoreButton.CoreButtonSelectionState state, float time)
        {
            IStateData stateData = GetStateData();
            if (stateData != null)
            {
                Vector2Data animCurve = stateData.GetState(state) as Vector2Data;
                if (animCurve != null)
                {
                    SetObjectProperties(animCurve.Evaluate(time));
                    return time >= animCurve.AnimationTimeLength;
                }
            }

            return true;
        }
        
        protected abstract void SetObjectProperties(UnityEngine.Vector2 value);
    }
}