using UnityEngine;

namespace Isto.Core.UI
{
    [System.Serializable]
    public class ColorData : IData, IColorData
    {
        public bool animated = false;
        public Gradient gradient = new Gradient();
        public float animLength = 0.1f;
        public Color instantColor = Color.white;

        public float AnimationTimeLength => animated ? animLength : 0f;

        public Color Evaluate(float time)
        {
            if (animated)
            {
                time = Mathf.Clamp(time, 0f, animLength);
                float t = time > 0f ? time / animLength : 0f;
                return gradient.Evaluate(t);
            }
            else
            {
                return instantColor;
            }
        }
    }
    
    public class ColorDataAlphaOverride : IData, IColorData
    {
        public ColorData colorData = null;
        public float alpha = 1f;
        public float AnimationTimeLength => colorData.AnimationTimeLength;

        public Color Evaluate(float time)
        {
            Color colorResult = colorData.Evaluate(time);
            colorResult.a = alpha;
            return colorResult;
        }
    }
    
    [System.Serializable]
    public class ColorStateData : IStateData, IAnimatedStateData
    {
        public virtual IData GetState(CoreButton.CoreButtonSelectionState state)
        {
            return null;
        }

        public virtual void UpdateAnimLength()
        {
            
        }
    }
    
    [System.Serializable]
    public class ColorStateDataValue : ColorStateData
    {
        public ColorData normal      = new ColorData();
        public ColorData selected    = new ColorData();
        public ColorData pressed     = new ColorData();
        public ColorData disabled    = new ColorData();
        
        public override IData GetState(CoreButton.CoreButtonSelectionState state)
        {
            switch (state)
            {
                case CoreButton.CoreButtonSelectionState.Normal:
                    return normal;
                case CoreButton.CoreButtonSelectionState.Pressed:
                    return pressed;
                case CoreButton.CoreButtonSelectionState.Highlighted:
                case CoreButton.CoreButtonSelectionState.Selected:
                    return selected;
                case CoreButton.CoreButtonSelectionState.Disabled:
                    return disabled;
            }

            return null;
        }

        public override void UpdateAnimLength()
        {
            //Animation length is user defined, no need to update.
        }
    }
    
    [System.Serializable]
    public class ColorStateDataRef : ColorStateData
    {
        public ColorDataObjectBase normal      = null;
        public ColorDataObjectBase selected    = null;
        public ColorDataObjectBase pressed     = null;
        public ColorDataObjectBase disabled    = null;
        
        public override IData GetState(CoreButton.CoreButtonSelectionState state)
        {
            switch (state)
            {
                case CoreButton.CoreButtonSelectionState.Normal:
                    return normal != null ? normal.GetData() : null;
                case CoreButton.CoreButtonSelectionState.Pressed:
                    return pressed != null ? pressed.GetData() : null;
                case CoreButton.CoreButtonSelectionState.Highlighted:
                case CoreButton.CoreButtonSelectionState.Selected:
                    return selected != null ? selected.GetData() : null;
                case CoreButton.CoreButtonSelectionState.Disabled:
                    return disabled != null ? disabled.GetData() : null;
            }

            return null;
        }

        public override void UpdateAnimLength()
        {
            //Animation length is user defined, no need to update.
        }
    }

    [CreateAssetMenu(fileName = "ColorData", menuName = "Scriptables/Core Button/State Data/Color Data")]
    [System.Serializable]
    public class ColorDataObject : ColorDataObjectBase
    {
        public ColorData data = new ColorData();
        
        public override IData GetData()
        {
            return data;
        }
    }
}