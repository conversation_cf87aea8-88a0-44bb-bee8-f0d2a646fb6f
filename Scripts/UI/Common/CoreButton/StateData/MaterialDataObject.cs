// unset
using UnityEngine;

namespace Isto.Core.UI
{
    [System.Serializable]
    public class MaterialData : IData
    {
        public Material material = null;
    }
    
    [System.Serializable]
    public class MaterialStateData : IStateData
    {
        public virtual IData GetState(CoreButton.CoreButtonSelectionState state)
        {
            return null;
        }
    }
    
    [System.Serializable]
    public class MaterialStateDataValue : MaterialStateData
    {
        public MaterialData normal      = new MaterialData();
        public MaterialData selected    = new MaterialData();
        public MaterialData pressed     = new MaterialData();
        public MaterialData disabled    = new MaterialData();

        public override IData GetState(CoreButton.CoreButtonSelectionState state)
        {
            switch (state)
            {
                case CoreButton.CoreButtonSelectionState.Normal:
                    return normal;
                case CoreButton.CoreButtonSelectionState.Pressed:
                    return pressed;
                case CoreButton.CoreButtonSelectionState.Highlighted:
                case CoreButton.CoreButtonSelectionState.Selected:
                    return selected;
                case CoreButton.CoreButtonSelectionState.Disabled:
                    return disabled;
                default:
                    return null;
            }
        }
    }
    
    [System.Serializable]
    public class MaterialStateDataRef : MaterialStateData
    {
        public MaterialDataObject normal      = null;
        public MaterialDataObject selected    = null;
        public MaterialDataObject pressed     = null;
        public MaterialDataObject disabled    = null;

        public override IData GetState(CoreButton.CoreButtonSelectionState state)
        {
            switch (state)
            {
                case CoreButton.CoreButtonSelectionState.Normal:
                    return normal != null ? normal.data : null;
                case CoreButton.CoreButtonSelectionState.Pressed:
                    return pressed != null ? pressed.data : null;
                case CoreButton.CoreButtonSelectionState.Highlighted:
                case CoreButton.CoreButtonSelectionState.Selected:
                    return selected != null ? selected.data : null;
                case CoreButton.CoreButtonSelectionState.Disabled:
                    return disabled != null ? disabled.data : null;
                default:
                    return null;
            }
        }
    }
    
    [CreateAssetMenu(fileName = "MaterialData", menuName = "Scriptables/Core Button/State Data/Material Data")]
    [System.Serializable]
    public class MaterialDataObject : ScriptableObject
    {
        public MaterialData data = new MaterialData();
    }
}