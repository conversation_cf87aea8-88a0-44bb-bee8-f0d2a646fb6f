using UnityEngine;

namespace Isto.Core.UI
{
    [System.Serializable]
    public class Vector3Data : IData
    {
        public bool animated = false;
        public AnimationCurve x = AnimationCurve.Constant(0f, 0.1f, 0f);
        public AnimationCurve y = AnimationCurve.Constant(0f, 0.1f, 0f);
        public AnimationCurve z = AnimationCurve.Constant(0f, 0.1f, 0f);
        public float instantX = 0f;
        public float instantY = 0f;
        public float instantZ = 0f;

        public Vector3 InstantValue
        {
            get { return new Vector3(instantX, instantY, instantZ); }
            set
            {
                instantX = value.x;
                instantY = value.y;
                instantZ = value.z;
            }
        }
        
        [SerializeField] private float animationTimeLength = -1f;
        public float AnimationTimeLength => animated ? animationTimeLength : 0f;

        public void UpdateAnimLength()
        {
            float[] animTimes = new float[3];
            animTimes[0] = x.keys[x.length - 1].time;
            animTimes[1] = y.keys[y.length - 1].time;
            animTimes[2] = z.keys[z.length - 1].time;
            animationTimeLength = Mathf.Max(animTimes);
        }
        
        public Vector3 Evaluate(float time)
        {
            if (animated)
            {
                return new Vector3(x.Evaluate(time), y.Evaluate(time), z.Evaluate(time));
                
            }
            else
            {
                return new Vector3(instantX, instantY, instantZ);
            }
        }
    }
    
    [System.Serializable]
    public class Vector3StateData : IStateData, IAnimatedStateData
    {
        public virtual IData GetState(CoreButton.CoreButtonSelectionState state)
        {
            return null;
        }

        public virtual void UpdateAnimLength()
        {
            
        }
    }
    
    [System.Serializable]
    public class Vector3StateDataValue : Vector3StateData
    {
        public Vector3Data normal      = new Vector3Data();
        public Vector3Data selected    = new Vector3Data();
        public Vector3Data pressed     = new Vector3Data();
        public Vector3Data disabled    = new Vector3Data();
        
        public override IData GetState(CoreButton.CoreButtonSelectionState state)
        {
            switch (state)
            {
                case CoreButton.CoreButtonSelectionState.Normal:
                    return normal;
                case CoreButton.CoreButtonSelectionState.Pressed:
                    return pressed;
                case CoreButton.CoreButtonSelectionState.Highlighted:
                case CoreButton.CoreButtonSelectionState.Selected:
                    return selected;
                case CoreButton.CoreButtonSelectionState.Disabled:
                    return disabled;
            }

            return null;
        }
        
        public override void UpdateAnimLength()
        {
            normal.UpdateAnimLength();
            pressed.UpdateAnimLength();
            selected.UpdateAnimLength();
            disabled.UpdateAnimLength();
        }
    }
    
    [System.Serializable]
    public class Vector3StateDataRef : Vector3StateData
    {
        public Vector3DataObject normal      = null;
        public Vector3DataObject selected    = null;
        public Vector3DataObject pressed     = null;
        public Vector3DataObject disabled    = null;
        
        public override IData GetState(CoreButton.CoreButtonSelectionState state)
        {
            switch (state)
            {
                case CoreButton.CoreButtonSelectionState.Normal:
                    return normal != null ? normal.data : null;
                case CoreButton.CoreButtonSelectionState.Pressed:
                    return pressed != null ? pressed.data : null;
                case CoreButton.CoreButtonSelectionState.Highlighted:
                case CoreButton.CoreButtonSelectionState.Selected:
                    return selected != null ? selected.data : null;
                case CoreButton.CoreButtonSelectionState.Disabled:
                    return disabled != null ? disabled.data : null;
            }

            return null;
        }
        
        public override void UpdateAnimLength()
        {
            normal?.data.UpdateAnimLength();
            pressed?.data.UpdateAnimLength();
            selected?.data.UpdateAnimLength();
            disabled?.data.UpdateAnimLength();
        }
    }
    
    [CreateAssetMenu(fileName = "Vector3Data", menuName = "Scriptables/Core Button/State Data/Vector 3 Data")]
    [System.Serializable]
    public class Vector3DataObject : ScriptableObject
    {
        public Vector3Data data = new Vector3Data();

        private void OnValidate()
        {
            data.UpdateAnimLength();
        }
    }
}