// unset
using UnityEngine;

namespace Isto.Core.UI
{
    [CreateAssetMenu(fileName = "ColorDataAlphaOverride", menuName = "Scriptables/Core Button/State Data/Color Data Alpha Override")]
    [System.Serializable]
    public class ColorDataAlphaOverrideObject : ColorDataObjectBase
    {
        public ColorDataObject colorData = null;
        public float alpha = 1f;

        private ColorDataAlphaOverride _colorAlphaOverride = null;
        
        public override IData GetData()
        {
            if (_colorAlphaOverride == null || _colorAlphaOverride.colorData == null)
            {
                _colorAlphaOverride = new ColorDataAlphaOverride() {colorData = colorData.data};
            }

            _colorAlphaOverride.alpha = alpha;
            
            return _colorAlphaOverride;
        }
    }
}