using UnityEngine;

namespace Isto.Core.UI
{
    [System.Serializable]
    public class Vector1Data : IData
    {
        public bool animated = false;
        public AnimationCurve x = AnimationCurve.Constant(0f, 0.1f, 0f);
        public float instantX = 0f;

        [SerializeField] private float animationTimeLength = -1f;
        public float AnimationTimeLength => animated ? animationTimeLength : 0f;

        public void UpdateAnimLength()
        {
            animationTimeLength = x.keys[x.length - 1].time;
        }

        public float Evaluate(float time)
        {
            if (animated)
            {
                return x.Evaluate(time);
            }
            else
            {
                return instantX;
            }
        }
    }
    
    [System.Serializable]
    public class Vector1StateData : IStateData, IAnimatedStateData
    {
        public virtual IData GetState(CoreButton.CoreButtonSelectionState state)
        {
            return null;
        }

        public virtual void UpdateAnimLength()
        {
            
        }
    }

    [System.Serializable]
    public class Vector1StateDataValue : Vector1StateData
    {
        public Vector1Data normal      = new Vector1Data();
        public Vector1Data selected    = new Vector1Data();
        public Vector1Data pressed     = new Vector1Data();
        public Vector1Data disabled    = new Vector1Data();

        public override IData GetState(CoreButton.CoreButtonSelectionState state)
        {
            switch (state)
            {
                case CoreButton.CoreButtonSelectionState.Normal:
                    return normal;
                case CoreButton.CoreButtonSelectionState.Pressed:
                    return pressed;
                case CoreButton.CoreButtonSelectionState.Highlighted:
                case CoreButton.CoreButtonSelectionState.Selected:
                    return selected;
                case CoreButton.CoreButtonSelectionState.Disabled:
                    return disabled;
                default:
                    return null;
            }
        }

        public override void UpdateAnimLength()
        {
            normal.UpdateAnimLength();
            pressed.UpdateAnimLength();
            selected.UpdateAnimLength();
            disabled.UpdateAnimLength();
        }
    }
    
    [System.Serializable]
    public class Vector1StateDataRef : Vector1StateData
    {
        public Vector1DataObject normal      = null;
        public Vector1DataObject selected    = null;
        public Vector1DataObject pressed     = null;
        public Vector1DataObject disabled    = null;

        public override IData GetState(CoreButton.CoreButtonSelectionState state)
        {
            switch (state)
            {
                case CoreButton.CoreButtonSelectionState.Normal:
                    return normal != null ? normal.data : null;
                case CoreButton.CoreButtonSelectionState.Pressed:
                    return pressed != null ? pressed.data : null;
                case CoreButton.CoreButtonSelectionState.Highlighted:
                case CoreButton.CoreButtonSelectionState.Selected:
                    return selected != null ? selected.data : null;
                case CoreButton.CoreButtonSelectionState.Disabled:
                    return disabled != null ? disabled.data : null;
                default:
                    return null;
            }
        }

        public override void UpdateAnimLength()
        {
            normal?.data.UpdateAnimLength();
            pressed?.data.UpdateAnimLength();
            selected?.data.UpdateAnimLength();
            disabled?.data.UpdateAnimLength();
        }
    }
    
    [CreateAssetMenu(fileName = "Vector1Data", menuName = "Scriptables/Core Button/State Data/Vector 1 Data")]
    [System.Serializable]
    public class Vector1DataObject : ScriptableObject
    {
        public Vector1Data data = new Vector1Data();

        private void OnValidate()
        {
            data.UpdateAnimLength();
        }
    }
}