using UnityEngine;

namespace Isto.Core.UI
{
    [System.Serializable]
    public class Vector2Data : IData
    {
        public bool animated = false;
        public AnimationCurve x = AnimationCurve.Constant(0f, 0.1f, 0f);
        public AnimationCurve y = AnimationCurve.Constant(0f, 0.1f, 0f);
        public float instantX = 0f;
        public float instantY = 0f;
        
        public Vector3 InstantValue
        {
            get { return new Vector3(instantX, instantY); }
            set
            {
                instantX = value.x;
                instantY = value.y;
            }
        }

        [SerializeField] private float animationTimeLength = -1f;
        public float AnimationTimeLength => animated ? animationTimeLength : 0f;

        public void UpdateAnimLength()
        {
            float[] animTimes = new float[2];
            animTimes[0] = x.keys[x.length - 1].time;
            animTimes[1] = y.keys[y.length - 1].time;
            animationTimeLength = Mathf.Max(animTimes);
        }
        
        public Vector2 Evaluate(float time)
        {
            if (animated)
            {
                return new Vector2(x.Evaluate(time), y.Evaluate(time));
            }
            else
            {
                return new Vector2(instantX, instantY);
            }
        }
    }
    
    [System.Serializable]
    public class Vector2StateData : IStateData, IAnimatedStateData
    {
        public virtual IData GetState(CoreButton.CoreButtonSelectionState state)
        {
            return null;
        }

        public virtual void UpdateAnimLength()
        {
            
        }
    }
    
    [System.Serializable]
    public class Vector2StateDataValue : Vector2StateData
    {
        public Vector2Data normal      = new Vector2Data();
        public Vector2Data selected    = new Vector2Data();
        public Vector2Data pressed     = new Vector2Data();
        public Vector2Data disabled    = new Vector2Data();
        
        public override IData GetState(CoreButton.CoreButtonSelectionState state)
        {
            switch (state)
            {
                case CoreButton.CoreButtonSelectionState.Normal:
                    return normal;
                case CoreButton.CoreButtonSelectionState.Pressed:
                    return pressed;
                case CoreButton.CoreButtonSelectionState.Highlighted:
                case CoreButton.CoreButtonSelectionState.Selected:
                    return selected;
                case CoreButton.CoreButtonSelectionState.Disabled:
                    return disabled;
            }

            return null;
        }

        public override void UpdateAnimLength()
        {
            normal.UpdateAnimLength();
            pressed.UpdateAnimLength();
            selected.UpdateAnimLength();
            disabled.UpdateAnimLength();
        }
    }
    
    [System.Serializable]
    public class Vector2StateDataRef : Vector2StateData
    {
        public Vector2DataObject normal      = null;
        public Vector2DataObject selected    = null;
        public Vector2DataObject pressed     = null;
        public Vector2DataObject disabled    = null;
        
        public override IData GetState(CoreButton.CoreButtonSelectionState state)
        {
            switch (state)
            {
                case CoreButton.CoreButtonSelectionState.Normal:
                    return normal != null ? normal.data : null;
                case CoreButton.CoreButtonSelectionState.Pressed:
                    return pressed != null ? pressed.data : null;
                case CoreButton.CoreButtonSelectionState.Highlighted:
                case CoreButton.CoreButtonSelectionState.Selected:
                    return selected != null ? selected.data : null;
                case CoreButton.CoreButtonSelectionState.Disabled:
                    return disabled != null ? disabled.data : null;
            }

            return null;
        }

        public override void UpdateAnimLength()
        {
            normal?.data.UpdateAnimLength();
            pressed?.data.UpdateAnimLength();
            selected?.data.UpdateAnimLength();
            disabled?.data.UpdateAnimLength();
        }
    }
    
    [CreateAssetMenu(fileName = "Vector2Data", menuName = "Scriptables/Core Button/State Data/Vector 2 Data")]
    [System.Serializable]
    public class Vector2DataObject : ScriptableObject
    {
        public Vector2Data data = new Vector2Data();

        private void OnValidate()
        {
            data.UpdateAnimLength();
        }
    }
}