using System;
using System.Collections;
using Isto.Core.UI.ButtonStyles;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Isto.Core.UI
{
    /// <summary>
    /// An animation transition is a transition allowing for animation without the use of an animator.
    /// To create a new animated transition type, two classes need to be extended:
    ///     CoreButtonAnimTransitionBase and CoreButtonAnimTransitionBaseEditor
    /// Please take a look at CoreButtonGameObjectAnimTransition and CoreButtonGameObjectAnimTransitionEditor
    /// for a simple example using 1 axis animation.
    /// </summary>
    public abstract class CoreButtonAnimTransitionBase<TStyle, TStateData, TValueStateData> : CoreButtonTransitionBase, ICoreButtonAnimTransition, ICoreButtonStyled
        where TStyle : class, IStyle
        where TStateData : class, IStateData, IAnimatedStateData, new()
        where TValueStateData : class, TStateData, new()
    {
        public TStyle style = null;
        public TValueStateData stateData = new TValueStateData();
        
        protected CoreButton.CoreButtonSelectionState _currentButtonState = CoreButton.CoreButtonSelectionState.Normal;
        protected bool _isAnimating = false;
        protected Coroutine _currentAnimationCoroutine = null;

        protected TStateData GetStateData()
        {
            if (style != null)
            {
                return style.GetStateData() as TStateData;
            }

            return stateData;
        }

        protected virtual void Awake()
        {
            (stateData).UpdateAnimLength();
        }

        public override void DoTransition(CoreButton.CoreButtonSelectionState state, bool instant = true)
        {
#if UNITY_EDITOR
            //TODO: show preview
            if (!Application.isPlaying)
            {
                instant = true;
            }
#endif

            if (!gameObject.activeInHierarchy)
            {
                instant = true;
            }
            
            if (_isAnimating && state != _currentButtonState)
            {
                // Debug.Log("End of anim");
                //Play the last animation frame if the current state was interrupted
                Evaluate(_currentButtonState, float.MaxValue);
            }

            _currentButtonState = state;

            if (instant)
            {
                Evaluate(_currentButtonState, float.MaxValue);
            }
            else
            {
                _currentAnimationCoroutine = StartCoroutine(EvaluateAnimation());
            }
        }

        public IEnumerator EvaluateAnimation(CoreButton.CoreButtonSelectionState state)
        {
            _currentButtonState = state;
            return EvaluateAnimation();
        }

        public IEnumerator EvaluateAnimation()
        {
            if (_currentAnimationCoroutine != null)
            {
                StopCoroutine(_currentAnimationCoroutine);
                _currentAnimationCoroutine = null;
            }

            _isAnimating = true;
            float currentTime = 0f;
            bool animFinished = false;

            while (!animFinished)
            {
                animFinished = Evaluate(_currentButtonState, currentTime);
                if (Application.isPlaying)
                {
                    currentTime += Time.deltaTime;
                }
                else
                {
                    currentTime += Time.fixedUnscaledDeltaTime;
                    
#if UNITY_EDITOR
                    EditorUtility.SetDirty(gameObject); //SetDirty to force update in Scene view when previewing animation
#endif
                }

                yield return null;
            }

            _isAnimating = false;
            _currentAnimationCoroutine = null;
        }

        protected abstract bool Evaluate(CoreButton.CoreButtonSelectionState state, float time);
        public virtual GameObject GetTargetObject()
        {
            return null;
        }

        public Type GetStyleType()
        {
            return typeof(TStyle);
        }

        public void SetStyle(IStyle style)
        {
            this.style = style as TStyle;
            DoTransition(_currentButtonState);
        }
    }
}