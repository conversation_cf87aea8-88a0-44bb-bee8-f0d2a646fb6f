// Copyright Isto Inc.
using Isto.Core.Audio;
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Zenject;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Isto.Core.UI
{
    [ExecuteAlways]
    public class CoreButton : Button, IAnyInputButtonDown, IAnyInputButtonUp
    {
        /// <summary>
        /// This enum mirrors UnityEngine.UI.Selectable.SelectionState
        /// </summary>
        public enum CoreButtonSelectionState
        {
            Normal = SelectionState.Normal,
            Highlighted = SelectionState.Highlighted,
            Pressed = SelectionState.Pressed,
            Selected = SelectionState.Selected,
            Disabled = SelectionState.Disabled,
        }


        // UNITY HOOKUP

        [Tooltip("Time to double click.\nSet higher than zero and leave IsDoubleClickRequired unchecked for double click protection.")]
        public float doubleClickTime = 0.25f;

        [Tooltip("Set to true and set DoubleClickTime to higher than zero to require a double click to press the button.")]
        public bool isDoubleClickRequired = false;

        [Tooltip("Play sound effect on click. Error sound when inert.")]
        public bool playSound = true;

        //Note: Transitions are additional objects/component changes that happens instantly on a button state change.
        //      See CoreButtonGOTransition and CoreButtonGOTransitionEditor for a simple example of how to create a new Transition.
        //-------------------------------------------------------------------------------------------------------------
        //      Anim Transitions are transition with an animation without the use of an animator.
        //      See CoreButtonGameObjectAnimTransition and CoreButtonGameObjectAnimTransitionEditor for a simple example.
        [SerializeField] private List<CoreButtonTransitionBase> _coreButtonTransitions = new List<CoreButtonTransitionBase>();


        // OTHER FIELDS

        protected float _lastClickTime = -1f;
        protected float _lastDoubleClickStartTime = -1f;

        private CoreButtonSelectionState _previewSelectionState = CoreButtonSelectionState.Normal;


        // PROPERTIES

        public List<CoreButtonTransitionBase> CoreButtonTransitions => _coreButtonTransitions;
        public CoreButtonSelectionState PreviewSelectionState => _previewSelectionState;
        public bool Active => gameObject.activeInHierarchy && enabled && interactable;
        public bool Interactable => interactable;
        public bool IsSelected => EventSystem.current.currentSelectedGameObject == gameObject;
        private bool CanClick => IsActive() && IsInteractable() && !IsSpammed();


        // EVENTS

        /// <summary>
        /// Redirection of UnityEngine.UI.Button OnClick event. Is fired on Mouse Up and Controller Button Down.
        /// Prioritize this for button pressed events.
        /// </summary>
        public event Action OnSubmitEvent;

        /// <summary>
        /// Matches unity OnSelect.
        /// </summary>
        public event Action<BaseEventData> OnSelectEvent;

        /// <summary>
        /// Matches unity OnDeselect.
        /// </summary>
        public event Action<BaseEventData> OnDeselectEvent;

        // We may or may not want to support this event long term, but right now I need custom behavior to patch a
        // problem with Atrio buttons until we have time to investigate further.
        public event Action<BaseEventData> OnSubmitPressEvent;

        /// <summary>
        /// Matches unity OnPointerDown.
        /// </summary>
        public event Action<PointerEventData> OnPointerDownEvent;

        /// <summary>
        /// Matches unity OnPointerUp.
        /// </summary>
        public event Action<PointerEventData> OnPointerUpEvent;

        /// <summary>
        /// Fired on any Rewired input button pressed.
        /// </summary>
        public event Action OnAdditionalInputButtonDown;

        /// <summary>
        /// Fired on any Rewired input button released.
        /// </summary>
        public event Action OnAdditionalInputButtonUp;


        // INJECTION

        protected UISounds _uiSounds;

        [Inject]
        public void Inject(UISounds uISounds)
        {
            _uiSounds = uISounds;
        }


        // LIFECYCLE METHODS

        protected override void Awake()
        {
            base.Awake();

            onClick.AddListener(Button_OnClick);

            UpdateTransitionList();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();

            onClick.RemoveListener(Button_OnClick);
        }


        // EVENT HANDLERS

        public override void OnSelect(BaseEventData eventData)
        {
#if BUTTON_LOGGING
            Debug.Log($"{gameObject.name} OnSelect", this.gameObject);
#endif
            base.OnSelect(eventData);
            OnSelectEvent?.Invoke(eventData);
        }

        public override void OnDeselect(BaseEventData eventData)
        {
#if BUTTON_LOGGING
            Debug.Log($"{gameObject.name} OnDeselect", this.gameObject);
#endif
            base.OnDeselect(eventData);
            OnDeselectEvent?.Invoke(eventData);
        }

        public override void OnPointerEnter(PointerEventData eventData)
        {
            base.OnPointerEnter(eventData);

            if (EventSystem.current != null && EventSystem.current.currentSelectedGameObject != gameObject)
            {
                //Select the button when using a mouse so that the onSelectedEvent is fired when highlighting it.
                Select();
            }
        }

        public override void OnPointerExit(PointerEventData eventData)
        {
            base.OnPointerExit(eventData);

            if (EventSystem.current != null)
            {
                //Deselect the button when using a mouse so that it doesn't stay highlighted.
                if (EventSystem.current.currentSelectedGameObject == gameObject)
                {
                    EventSystem.current.SetSelectedGameObject(null);
                }
                else
                {
                    // This flow probably is not supposed to happen if everything else is working normally
                    // But in case a problem flow happens we're not supposed to force other objects to deselect either.
                }
            }
        }

        // Handles mouse clicks only (after OnPointerUp)
        public override void OnPointerClick(PointerEventData eventData)
        {
#if BUTTON_LOGGING
            Debug.Log($"{gameObject.name} OnPointerClick", this.gameObject);
#endif

            if (!CanClick)
            {
                return;
            }

            PlaySound();

            if (TryConfirmClick())
            {
                base.OnPointerClick(eventData);
            }
        }

        // Fires on both mouse click (after OnSelect and before OnPointerDown)
        // and button press (as the button goes down).
        public override void OnSubmit(BaseEventData eventData)
        {
#if BUTTON_LOGGING
            Debug.Log($"{gameObject.name} OnSubmit", this.gameObject);
#endif

            if (!CanClick)
            {
                return;
            }

            PlaySound();

            if (TryConfirmClick())
            {
                base.OnSubmit(eventData);

                OnSubmitPressEvent?.Invoke(eventData);
            }
        }

        private void Button_OnClick()
        {
#if BUTTON_LOGGING
            Debug.Log($"{gameObject.name} Button_OnClick", this.gameObject);
#endif

            OnSubmitEvent?.Invoke();
        }

        public override void OnPointerDown(PointerEventData eventData)
        {
#if BUTTON_LOGGING
            Debug.Log($"{gameObject.name} OnPointerDown", this.gameObject);
#endif

            base.OnPointerDown(eventData);

            OnPointerDownEvent?.Invoke(eventData);
        }

        public override void OnPointerUp(PointerEventData eventData)
        {
#if BUTTON_LOGGING
            Debug.Log($"{gameObject.name} OnPointerUp", this.gameObject);
#endif

            base.OnPointerUp(eventData);

            OnPointerUpEvent?.Invoke(eventData);
        }

        public void OnAnyInputButtonDown()
        {
            OnAdditionalInputButtonDown?.Invoke();
        }

        public void OnAnyInputButtonUp()
        {
            OnAdditionalInputButtonUp?.Invoke();
        }


        // OTHER METHODS

        protected void SetDirtyIfNotPlaying()
        {
#if UNITY_EDITOR
            if (!Application.isPlaying)
            {
                EditorUtility.SetDirty(this.gameObject);
            }
#endif
        }

        public void AddCoreButtonTransition(ICoreButtonTransition transition)
        {
            if (!_coreButtonTransitions.Contains(transition as CoreButtonTransitionBase))
            {
                _coreButtonTransitions.Add(transition as CoreButtonTransitionBase);
            }

            SetDirtyIfNotPlaying();
        }

        public void RemoveCoreButtonTransition(ICoreButtonTransition transition)
        {
            _coreButtonTransitions.Remove(transition as CoreButtonTransitionBase);
            _coreButtonTransitions.RemoveAll(item => item == null);

            SetDirtyIfNotPlaying();
        }

        [ContextMenu("Update transition list")]
        public void UpdateTransitionListContextMenu()
        {
            UpdateTransitionList();
            Debug.Log($"{gameObject.name} Transition count: " + _coreButtonTransitions.Count, gameObject);

            SetDirtyIfNotPlaying();
        }

        public void UpdateTransitionList()
        {
            _coreButtonTransitions.Clear();
            _coreButtonTransitions.AddRange(GetComponents<CoreButtonTransitionBase>());
        }

        protected void PlaySound()
        {
            if (!playSound)
                return;

            //TODO: external sound event controller
            _uiSounds.PlayButtonClickSound();
        }

        /// <summary>
        /// Cooldown check for accidental double click protection
        /// </summary>
        private bool IsSpammed()
        {
            // Can be checked as many times as needed without consequence.
            // Note: This is meant to protect from user spam clicking, not allow us to spray and pray with events.
            if (Time.unscaledTime < _lastClickTime + doubleClickTime)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Checks for double clicks 
        /// </summary>
        private bool TryConfirmClick()
        {
            if (IsDoubleClickGated())
            {
                _lastDoubleClickStartTime = Time.unscaledTime;
                return false;
            }
            else
            {
                _lastClickTime = Time.unscaledTime;
                return true;
            }
        }

        private bool IsDoubleClickGated()
        {
            if (!isDoubleClickRequired)
                return false;

            bool gated = false;

            // Cooldown check when a double click is required for button activation
            // Refuse first click, accept next one if before doubleClickTime
            // This has to be tested last (after IsSpammed).
            if (Time.unscaledTime > _lastDoubleClickStartTime + doubleClickTime)
            {
                gated = true;
            }

            return gated;
        }

        protected override void DoStateTransition(SelectionState state, bool instant)
        {
            CoreButtonSelectionState coreButtonState = (CoreButtonSelectionState)state;

            base.DoStateTransition(state, instant);

            foreach (ICoreButtonTransition transition in _coreButtonTransitions)
            {
                if (transition != null)
                {
                    transition.DoTransition(coreButtonState, instant);
                }
            }
        }

        public void DoPreviewStateTransition(CoreButtonSelectionState state)
        {
            _previewSelectionState = state;
            DoStateTransition((SelectionState)state, true);

            SetDirtyIfNotPlaying();
        }

        [ContextMenu("SetSelected")]
        private void SetSelected()
        {
            Select();
        }
    }
}