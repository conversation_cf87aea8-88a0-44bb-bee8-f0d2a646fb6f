using Isto.Core.UI.ButtonStyles;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Isto.Core.UI
{
    [RequireComponent(typeof(CoreButton))]
    public class CoreButtonStyleController : MonoBehaviour
    {
        [System.Serializable]
        public class CoreButtonStyleElement
        {
            [System.Serializable]
            public class TransitionPair
            {
                public CoreButtonTransitionBase transition = null;
                public CoreButtonStyle style = null;

                public void SetStyle()
                {
                    if (transition != null)
                    {
                        (transition as ICoreButtonStyled)?.SetStyle(style);
                    }
                }
            }

            public string name = "Style";
            public List<TransitionPair> transitionPairs = new List<TransitionPair>();

            public void AddTransitions(CoreButtonTransitionBase[] transitions, bool doubleCheck = true)
            {
                for (int i = 0; i < transitions.Length; i++)
                {
                    if (doubleCheck && transitionPairs.Any(x => x.transition != null && x.transition.GetType() == transitions[i].GetType()))
                    {
                        continue;
                    }

                    transitionPairs.Add(new TransitionPair()
                    {
                        transition = transitions[i]
                    });
                }
            }

            public void SetStyle()
            {
                foreach (TransitionPair transitionPair in transitionPairs)
                {
                    transitionPair.SetStyle();
                }
            }
        }

        [SerializeField][CoreButtonStyleElementShowName] public List<CoreButtonStyleElement> buttonStyles = new List<CoreButtonStyleElement>();

        public void SetButtonStyle(int styleId)
        {
            if (styleId >= 0 && styleId < buttonStyles.Count)
            {
                buttonStyles[styleId].SetStyle();
            }
        }

        public void SetButtonStyle(string name)
        {
            int styleId = buttonStyles.FindIndex(x => x.name == name);
            SetButtonStyle(styleId);
        }

        [ContextMenu("Auto Complete Missing Transitions")]
        public void AutoCompleteMissingTransitions()
        {
            CoreButtonTransitionBase[] transitions = this.GetComponents<CoreButtonTransitionBase>();

            foreach (CoreButtonStyleElement buttonStyle in buttonStyles)
            {
                buttonStyle.AddTransitions(transitions, true);
            }

#if UNITY_EDITOR
            EditorUtility.SetDirty(this);
#endif
        }
    }
}