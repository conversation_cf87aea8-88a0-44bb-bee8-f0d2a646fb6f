using UnityEngine;
using Sisus.ComponentNames;

namespace Isto.Core.UI
{
    public class CoreButtonCanvasAlpha : CoreButtonVector1AnimTransition
    {
        [SerializeField] private CanvasGroup _canvasGroup = null;
        
        private void OnValidate()
        {
            this.SetName(_canvasGroup.gameObject.name);
        }
        
        public override GameObject GetTargetObject()
        {
            return _canvasGroup.gameObject;
        }
        
        protected override void SetObjectProperties(float xValue)
        {
            if (_canvasGroup != null)
            {
                _canvasGroup.alpha = xValue;
            }
        }
    }
}