using UnityEngine;
using Sisus.ComponentNames;

namespace Isto.Core.UI
{
    public class CoreButtonRectScale : CoreButtonVector3AnimTransition
    {
        [SerializeField] private RectTransform _rectTransform = null;
        
        private void OnValidate()
        {
            this.SetName(_rectTransform.gameObject.name);
            
            DoTransition(CoreButton.PreviewSelectionState);
        }
        
        public override GameObject GetTargetObject()
        {
            return _rectTransform.gameObject;
        }
        
        protected override void SetObjectProperties(Vector3 value)
        {
            if (_rectTransform != null)
            {
                _rectTransform.localScale = value;
            }
        }
        
        protected override void Reset()
        {
            base.Reset();

            Vector3StateDataValue colorStateDataValue = stateData as Vector3StateDataValue;
            colorStateDataValue.normal.InstantValue = new Vector3(1f, 1f, 1f);
            colorStateDataValue.selected.InstantValue = new Vector3(1f, 1f, 1f);
            colorStateDataValue.pressed.InstantValue = new Vector3(1f, 1f, 1f);
            colorStateDataValue.disabled.InstantValue = new Vector3(1f, 1f, 1f);
        }
    }
}