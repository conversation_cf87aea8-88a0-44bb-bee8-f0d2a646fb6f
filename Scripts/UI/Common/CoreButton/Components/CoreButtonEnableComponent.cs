using UnityEngine;
using Sisus.ComponentNames;

namespace Isto.Core.UI
{
    public class CoreButtonEnableComponent : CoreButtonVector1AnimTransition
    {
        [SerializeField] private Behaviour _component = null;
        
        private void OnValidate()
        {
            this.SetName(_component.gameObject.name);
            
            DoTransition(CoreButton.PreviewSelectionState);
        }
        
        public override GameObject GetTargetObject()
        {
            return _component.gameObject;
        }
        
        protected override void SetObjectProperties(float xValue)
        {
            bool shouldBeEnabled = xValue > 0.9f;
            
            if (_component != null && _component.enabled != shouldBeEnabled)
            {
                _component.enabled = shouldBeEnabled;
            }
        }
    }
}