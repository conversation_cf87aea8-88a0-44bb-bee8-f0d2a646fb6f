using TMPro;
using UnityEngine;
using Sisus.ComponentNames;

namespace Isto.Core.UI
{
    public class CoreButtonTMPColor : CoreButtonColorAnimTransition
    {
        [SerializeField] private TextMeshProUGUI _tmp = null;
        
        private void OnValidate()
        {
            this.SetName(_tmp.gameObject.name);
            
            DoTransition(CoreButton.PreviewSelectionState);
        }
        
        public override GameObject GetTargetObject()
        {
            return _tmp.gameObject;
        }
        
        protected override void SetObjectProperties(Color color)
        {
            if (_tmp != null)
            {
                _tmp.color = color;
            }
        }
    }
}