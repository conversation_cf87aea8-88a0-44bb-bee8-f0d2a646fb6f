using UnityEngine;
using UnityEngine.UI;
using Sisus.ComponentNames;

namespace Isto.Core.UI
{
    public class CoreButtonImageColor : CoreButtonColorAnimTransition
    {
        [SerializeField] private Image _image = null;
        
        private void OnValidate()
        {
            this.SetName(_image.gameObject.name);
            
            DoTransition(CoreButton.PreviewSelectionState);
        }
        
        public override GameObject GetTargetObject()
        {
            return _image.gameObject;
        }
        
        protected override void SetObjectProperties(Color color)
        {
            if (_image != null)
            {
                _image.color = color;
            }
        }
    }
}