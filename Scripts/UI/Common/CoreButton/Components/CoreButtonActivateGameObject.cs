using UnityEngine;
using Sisus.ComponentNames;

namespace Isto.Core.UI
{
    public class CoreButtonActivateGameObject : CoreButtonVector1AnimTransition
    {
        [SerializeField] private GameObject _go = null;
        
        private void OnValidate()
        {
            this.SetName(_go.gameObject.name);
            
            DoTransition(CoreButton.PreviewSelectionState);
        }

        public override GameObject GetTargetObject()
        {
            return _go;
        }

        protected override void SetObjectProperties(float xValue)
        {
            bool shouldBeActive = xValue > 0.9f;
            
            if (_go != null && _go.activeSelf != shouldBeActive)
            {
                _go.SetActive(shouldBeActive);
            }
        }
    }
}