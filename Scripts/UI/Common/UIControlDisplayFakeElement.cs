// Copyright Isto Inc.
using Isto.Core.Inputs;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    /// <summary>
    /// A simpler version of what UIControlDisplayElement does, meant for special cases that aren't represented as an action directly.
    /// Will only handle showing/hiding it according to current control scheme.
    /// </summary>
    public class UIControlDisplayFakeElement : MonoBehaviour
    {
        // Public Variables

        public bool shownForMouseInput;
        public bool shownForControllerInput;
        public Image actionImage;
        public LayoutElement layoutElement;

        // Private Variables

        private IControls _controls;
        private TextMeshProUGUI _descriptionText;

        // Lifecycle Events

        [Inject]
        public void Inject(IControls controls)
        {
            _controls = controls;
        }

        private void Awake()
        {
            _descriptionText = GetComponentInChildren<TextMeshProUGUI>();
        }

        private void OnEnable()
        {
            OnSettingsChanged();

            Events.Subscribe(Events.SETTINGS_CHANGED, OnSettingsChanged);
            Events.Subscribe(Events.INPUT_MODE_CHANGED, OnSettingsChanged);
        }

        private void OnDisable()
        {
            Events.UnSubscribe(Events.SETTINGS_CHANGED, OnSettingsChanged);
            Events.UnSubscribe(Events.INPUT_MODE_CHANGED, OnSettingsChanged);
        }

        private void Start()
        {
            OnSettingsChanged();
        }

        private void OnSettingsChanged()
        {
            UpdateControlDisplay(_controls);
        }

        public void UpdateControlDisplay(IControls controls)
        {
            if (Controls.UsingController)
            {
                if (shownForControllerInput)
                {
                    EnableDisplay();
                }
                else
                {
                    DisableDisplay();
                }
            }
            else
            {
                if (shownForMouseInput)
                {
                    EnableDisplay();
                }
                else
                {
                    DisableDisplay();
                }
            }
        }

        private void EnableDisplay()
        {
            if (actionImage)
                actionImage.enabled = true;

            if (_descriptionText)
                _descriptionText.enabled = true;

            if (layoutElement)
                layoutElement.enabled = true;
        }

        private void DisableDisplay()
        {
            if (actionImage)
                actionImage.enabled = false;

            if (_descriptionText)
                _descriptionText.enabled = false;

            if (layoutElement)
                layoutElement.enabled = false;
        }
    }
}