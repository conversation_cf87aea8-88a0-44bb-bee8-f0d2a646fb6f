// Copyright Isto Inc.

using Isto.Core.Inputs;
using UnityEngine;
using UnityEngine.UI;

namespace Isto.Core.UI
{
    public class UIInputModeDependentGlyph : MonoBehaviour
    {
        // UNITY HOOKUP

        [Header("Control Image")]
        [SerializeField] private Image _controlImage;

        [Header("Rect Transform")]
        [SerializeField] private RectTransform _rectTransform;

        [<PERSON><PERSON>("Input Glyphs")]
        [SerializeField] private Sprite _gamepadGlyph;
        [SerializeField] private Sprite _keyboardMouseGlyph;

        [Header("Image Sizes")]
        [SerializeField] private Vector2 _keyboardMouseSize;
        [SerializeField] private Vector2 _gamepadSize;



        // LIFECYCLE EVENTS

        private void OnEnable()
        {
            UpdateMenuGlyphs();
            Events.Subscribe(Events.INPUT_MODE_CHANGED, UpdateMenuGlyphs);
        }

        private void OnDisable()
        {
            Events.UnSubscribe(Events.INPUT_MODE_CHANGED, UpdateMenuGlyphs);
        }


        // OTHER METHODS

        private void UpdateMenuGlyphs()
        {
            _controlImage.sprite = Controls.UsingController ? _gamepadGlyph : _keyboardMouseGlyph;
            AdjustImageSize();
        }

        public void AdjustImageSize()
        {
            if (_controlImage.sprite != null)
            {
                _rectTransform.sizeDelta = Controls.UsingController ? _gamepadSize : _keyboardMouseSize;
            }
        }
    }
}