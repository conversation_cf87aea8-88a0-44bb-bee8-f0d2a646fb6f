// Copyright Isto Inc.

using UnityEngine;
using UnityEngine.UI;

namespace Isto.Core.UI
{
    /// <summary>
    /// A carousel tick is a small ui piece within the carousel that shows the user where there current selection is.
    /// It is expected that the UICarousel that uses this will contain multiple UICarouselTicks and only one of them
    /// will be highlighted.
    /// </summary>
    public class UICarouselTick : MonoBehaviour
    {
        // UNITY HOOKUP
        
        [Header("UI Component Hookups")] 
        [SerializeField] private Image _carouselTickImage;
        [SerializeField] private Color _highlightedColor;
        
        
        // OTHER FIELDS
        
        private Color _defaultColor;
        
        
        // LIFECYCLE EVENTS
        
        private void Awake()
        {
            _defaultColor = _carouselTickImage.color;
        }
        
        
        // ACCESSORS

        public void SetHighlight(bool isHighlighted)
        {
            if (isHighlighted)
            {
                _carouselTickImage.color = _highlightedColor;
            }
            else
            {
                _carouselTickImage.color = _defaultColor;
            }
        }
    }
}