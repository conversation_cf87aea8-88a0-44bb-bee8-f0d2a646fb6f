// Copyright Isto Inc.

using I2.Loc;
using Isto.Core.Audio;
using Isto.Core.Localization;
using ModestTree;
using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    public class UICarousel : MonoBehaviour
    {
        // UNITY HOOKUP

        [Header("UI Component Hookups")]
        [SerializeField]
        private TextMeshProUGUI _carouselValueLabel;

        [SerializeField] private CoreButton _leftButton;
        [SerializeField] private CoreButton _rightButton;
        [SerializeField] private Transform _carouselTickContainer;

        [Header("Prefabs")] [SerializeField] private UICarouselTick _carouselTick;
        [SerializeField] private int _currentSelection;

        [Header("Carousel List")]
        // This allows for the user to make a list of Localized Strings that will be the options for the Carousel List
        // via the Editor.
        [Tooltip("A list of Localized Strings that define the default values for the Carousel List via the Editor. NOTE:" +
                 " These can be overridden in the code.")]
        [SerializeField]
        // TODO: JP.2024-04-30 [MTM-279] - Replace LocalizedString with string (or a isto localized string data class)
        private List<LocalizedString> _defaultValues = new List<LocalizedString>();


        // OTHER FIELDS

        private readonly List<UICarouselTick> _carouselTicks = new List<UICarouselTick>();

        // List that contains all the values for the current carousel as well as any prefixes or suffixes it
        // may or may not use.
        private List<LocExpression> _internalCarouselValues = new List<LocExpression>();
        private LocExpression _currentValueExpression;
        private HorizontalLayoutGroup _horizontalLayoutGroup;


        // PROPERTIES

        public List<LocExpression> CarouselValues => _internalCarouselValues;


        // EVENTS

        public event Action<int> OnSelectionChanged;


        // INJECTION

        private UISounds _uiSounds;
        private LocTerm.Factory _localizedStringFactory;

        [Inject]
        public void Inject(UISounds uISounds, LocTerm.Factory localizedStringFactory)
        {
            _uiSounds = uISounds;
            _localizedStringFactory = localizedStringFactory;
        }


        // LIFECYCLE EVENTS

        private void Awake()
        {
            _horizontalLayoutGroup = _carouselTickContainer.GetComponent<HorizontalLayoutGroup>();

            if (!_defaultValues.IsEmpty())
            {
                _internalCarouselValues = new List<LocExpression>();
                foreach (LocalizedString localizedString in _defaultValues)
                {
                    LocTerm currentLoc =
                        _localizedStringFactory.Create(LocTerm.LocalizationType.Localized, localizedString.mTerm);
                    _internalCarouselValues.Add(new SingleTermLocExpression(currentLoc));
                }
            }

            StartCoroutine(DisableHorizontalLayout());
        }

        private void OnEnable()
        {
            RegisterEvents();
            UpdateCarouselValue();
        }

        private void OnDisable()
        {
            UnregisterEvents();
        }


        // EVENT HANDLING

        private void RegisterEvents()
        {
            Events.Subscribe(Events.LANGUAGE_CHANGED, LocalizationManager_OnLocalize);
            _leftButton.OnPointerDownEvent += LeftButton_OnPointerDown;
            _rightButton.OnPointerDownEvent += RightButton_OnPointerDown;
        }

        private void UnregisterEvents()
        {
            Events.UnSubscribe(Events.LANGUAGE_CHANGED, LocalizationManager_OnLocalize);
            _leftButton.OnPointerDownEvent -= LeftButton_OnPointerDown;
            _rightButton.OnPointerDownEvent -= RightButton_OnPointerDown;
        }

        private void LocalizationManager_OnLocalize()
        {
            UpdateCarouselValue();
        }

        private void LeftButton_OnPointerDown(BaseEventData baseEventData)
        {
            MoveSelectionLeft();
        }

        private void RightButton_OnPointerDown(BaseEventData baseEventData)
        {
            MoveSelectionRight();
        }


        // ACCESSORS

        public void SetCarouselList(List<LocExpression> optionsList, int currentSelection = 0)
        {
            _internalCarouselValues = optionsList;

            SetCarouselSelection(currentSelection, false);
        }

        public void SetCarouselSelection(int selectedIndex, bool sendEventMessage = true)
        {

            SetSelectedValueFromCarousel(selectedIndex);
            _currentSelection = selectedIndex;

            if (sendEventMessage)
            {
                OnSelectionChanged?.Invoke(_currentSelection);
            }

            UpdateTickVisuals();
        }

        private void SetSelectedValueFromCarousel(int selectedIndex)
        {
            if (_internalCarouselValues == null
                || _internalCarouselValues.Count <= selectedIndex
                || selectedIndex < 0)
                return;

            _currentValueExpression = _internalCarouselValues[selectedIndex];
            UpdateCarouselValue();
        }

        private void UpdateCarouselValue()
        {
            _currentValueExpression?.LocalizeInto(_carouselValueLabel);
        }


        // OTHER METHODS

        public void MoveSelectionLeft()
        {
            int newSelection = _currentSelection - 1;

            if (newSelection < 0)
            {
                newSelection = CarouselValues.Count - 1;
            }

            _uiSounds.PlayButtonClickSound();
            SetCarouselSelection(newSelection);
        }

        public void MoveSelectionRight()
        {
            int newSelection = _currentSelection + 1;

            if (newSelection > CarouselValues.Count - 1)
            {
                newSelection = 0;
            }

            _uiSounds.PlayButtonClickSound();
            SetCarouselSelection(newSelection);
        }

        /// <summary>
        /// Disables the Horizontal Layout group component of the carousel tick container. It is done via a coroutine as
        /// doing this in the Start method did not give enough time for the layout to visually update correctly. This ensures
        /// that the visuals are updated before they layout is disabled.
        /// </summary>
        private IEnumerator DisableHorizontalLayout()
        {
            yield return new WaitForEndOfFrame();

            if (_horizontalLayoutGroup)
            {
                _horizontalLayoutGroup.enabled = false;
            }
        }

        private void UpdateTickVisuals()
        {
            if (_carouselTicks.Count != CarouselValues.Count)
            {
                if (_horizontalLayoutGroup)
                {
                    _horizontalLayoutGroup.enabled = true;
                }

                foreach (var tick in _carouselTicks)
                {
                    DestroyImmediate(tick.gameObject);
                }

                _carouselTicks.Clear();

                for (int i = 0; i < CarouselValues.Count; i++)
                {
                    _carouselTicks.Add(Instantiate(_carouselTick, _carouselTickContainer));
                }

                if (gameObject.activeInHierarchy)
                {
                    StartCoroutine(DisableHorizontalLayout());
                }
            }

            // Set selected tick here
            for (int i = 0; i < _carouselTicks.Count; i++)
            {
                _carouselTicks[i].SetHighlight(i == _currentSelection);
            }
        }
    }
}