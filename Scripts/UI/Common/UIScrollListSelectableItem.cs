// Copyright Isto Inc.
using Isto.Core.Inputs;
using UnityEngine;
using UnityEngine.EventSystems;

namespace Isto.Core.UI
{
    public class UIScrollListSelectableItem : MonoBehaviour, ISelectHandler
    {
        public enum ScrollListAdjustmentDriver
        {
            SiblingIndexDriven,
            PositionDriven
        }

        [SerializeField] private ScrollListAdjustmentDriver _adjustmentDriver;
        [SerializeField] private bool _horizontal;
        [SerializeField] private bool _vertical;
        [SerializeField] private bool _fixDropdownOffset;
        /*[SerializeField]*/
        private bool _fixDisabledSiblings; // DON'T USE FOR NOW

        private RectTransform _parent;

        private void Awake()
        {
            _parent = this.transform.parent as RectTransform;

            Debug.Assert(_horizontal ^ _vertical, "UIScrollListSelectableItem expects to be vertical or horizontal, anything else will fail");
        }

        void ISelectHandler.OnSelect(BaseEventData eventData)
        {
            // Didn't expect mouse hover to select anything but it does in our dropdowns.
            // Ideally though we'd still want the dropdown to at least align to the selection when opened so you can see it.
            if (!Controls.UsingController)
                return;

            if (_fixDisabledSiblings)
            {
                // The dropdowns have a disabled template child which messes with the EnsureChildIsVisible logic so we
                // remove it, which improves the situation (the selected item tends to be visible when you go up). There
                // are still issues to fix for correct dropdown scroll alignment but at least it's usable for now. So I'll
                // investigate later.
                // Follow-up: turns out this breaks the option index for the dropdown. Need a different way to fix it.
                Transform parent = this.transform.parent;
                for (int i = parent.childCount - 1; i >= 0; i--)
                {
                    GameObject currentSibling = parent.GetChild(i).gameObject;
                    if (!currentSibling.activeSelf)
                    {
                        GameObject.Destroy(currentSibling);
                    }
                }
            }

            if (_horizontal)
                (this.transform.parent as RectTransform).EnsureChildIsVisibleHorizontally(this.transform as RectTransform, _adjustmentDriver, _fixDropdownOffset);

            if (_vertical)
                (this.transform.parent as RectTransform).EnsureChildIsVisibleVertically(this.transform as RectTransform, _adjustmentDriver, _fixDropdownOffset);
        }
    }
}