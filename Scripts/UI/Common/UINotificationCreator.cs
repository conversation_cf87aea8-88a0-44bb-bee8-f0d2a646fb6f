// Copyright Isto Inc.
using UnityEngine;

namespace Isto.Core.UI
{
    public class UINotificationCreator : MonoBeh<PERSON>our
    {
        public UINotification Notification { get; set; }

        [SerializeField] private GameObject notificationPrefab;
        [SerializeField] private GameObject notificationParent;

        public void InstantiateNotification()
        {
            if (Notification == null)
            {
                GameObject notificationObj = Instantiate(notificationPrefab, notificationParent.transform);
                Notification = notificationObj.GetComponent<UINotification>();
            }
        }

        public void DestroyNotification()
        {
            if (Notification != null)
                Destroy(Notification.gameObject);
        }

        public void ShowNotification(bool show)
        {
            if (Notification != null)
                Notification.gameObject.SetActive(show);
        }
    }
}