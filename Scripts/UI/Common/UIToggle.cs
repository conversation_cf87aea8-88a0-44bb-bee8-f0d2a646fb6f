// Copyright Isto Inc.
using Isto.Core.Audio;
using Isto.Core.Localization;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    /// <summary>
    /// Provides some help with event handling and updating the UI colors for toggles (intended for items from a dropdown).
    /// The color scheme is basically defined throughout this class. Consider refactoring that info to be available at the top
    /// of the class or from outside the code. (I would prefer defining these colors in a scriptable rather than in Constants.cs)
    /// </summary>
    [RequireComponent(typeof(Toggle))]
    public class UIToggle : MonoBehaviour, IPointerDownHandler, IPointerUpHandler, IPointerEnterHandler, IPointerExitHandler, ISelectHandler, IDeselectHandler, ISubmitHandler
    {
        public enum UIToggleColorScheme
        {
            Normal, // Ready and standing by
            Highlighted, // Hovered or Selected
            Engaged, // Clicked or Pressed
            Disabled // Not navigatable or interactable
        }

        public RectTransform RectTransform => this.transform as RectTransform;

        private Toggle Toggle
        {
            get
            {
                if (_toggle == null)
                {
                    _toggle = this.GetComponent<Toggle>();
                }
                return _toggle;
            }
        }

        [Header("UI Hookup")]
        [SerializeField] protected Image background;
        [SerializeField] protected Image checkmark;
        [SerializeField] protected TextMeshProUGUI label;
        [SerializeField] protected bool disabled = false;
        [SerializeField] protected bool setToggleColors = true;
        [SerializeField] protected bool fixPositionForDropdown = false;

        [Header("Animation")]
        [SerializeField] private bool _animate = true;
        [SerializeField] private string _selectedTrigger = "selected";
        [SerializeField] private string _deselectedTrigger = "deselected";
        [SerializeField] private string _pressedFlag = "down";

        private bool _fixPositionForDropdownApplied = false;
        private bool _lastChildCorrectionApplied = false;

        // Cache
        protected Toggle _toggle;
        protected Animator _anim; // Not certain we'll need this
        private Navigation _nav;

        // Injected
        protected UISounds _uiSounds;

        [Inject]
        public void Inject(UISounds uiSounds)
        {
            _uiSounds = uiSounds;
        }

        private void OnEnable()
        {
            _anim = this.GetComponent<Animator>();
            _nav = Toggle.navigation;

            if (fixPositionForDropdown && !_fixPositionForDropdownApplied)
            {
                // The content root in the dropdown has its size and position managed by the dropdown and the scroller.
                // Something funky is going on at items configuration but this magic number mostly fixes the end result.
                Vector2 uiPos = this.RectTransform.anchoredPosition;
                uiPos.y -= 12f;
                this.RectTransform.anchoredPosition = uiPos;

                _fixPositionForDropdownApplied = true;
            }

            if (disabled)
                SetDisabled();
            else
                SetEnabled();
        }

        private void Start()
        {
            // The content root in the dropdown has its size and position managed by the dropdown and the scroller and it looks bad.
            // I have a general fix in OnEnable, but it leaves the last item offset a bit short from the end of the panel.
            if (fixPositionForDropdown && !_lastChildCorrectionApplied && transform.GetSiblingIndex() == transform.parent.childCount - 1)
            {
                // Move the item down and enlarge it to fill the leftover space
                Vector2 uiPos = this.RectTransform.anchoredPosition;
                uiPos.y -= 6f;
                this.RectTransform.anchoredPosition = uiPos;
                Vector2 uiSize = this.RectTransform.sizeDelta;
                uiSize.y += 10f;
                this.RectTransform.sizeDelta = uiSize;

                // Theoretically this serves to put the text back at the same height as previously, but this looks better if I underadjust it a little
                Vector3 textPos = label.transform.position;
                textPos.y += 4f;
                label.transform.position = textPos;

                _lastChildCorrectionApplied = true;
            }
        }

        private void OnDisable()
        {
        }

        public void SetLabelText(string localizedText)
        {
            if (localizedText != "")
                Loc.SetTMProLocalized(label, localizedText);
        }

        public virtual void Selected()
        {
            if (disabled)
                return;

            if (_animate && _anim != null)
                _anim.SetTrigger(_selectedTrigger);

            if (setToggleColors)
                SetColor(UIToggleColorScheme.Highlighted);
        }

        public virtual void DeSelected()
        {
            if (_animate && _anim != null)
                _anim.SetTrigger(_deselectedTrigger);

            if (setToggleColors)
                SetColor(UIToggleColorScheme.Normal);
        }

        public virtual void SetDisabled()
        {
            if (setToggleColors)
                SetColor(UIToggleColorScheme.Disabled);

            disabled = true;
            Toggle.interactable = false;
        }

        public virtual void SetEnabled()
        {
            if (setToggleColors)
                SetColor(UIToggleColorScheme.Normal);

            disabled = false;
            Toggle.interactable = true;
        }

        // Using mouse the control will get hovered before press
        void IPointerEnterHandler.OnPointerEnter(PointerEventData eventData)
        {
            if (disabled)
                return;

            Selected();
        }

        void IPointerExitHandler.OnPointerExit(PointerEventData eventData)
        {
            if (disabled)
                return;

            DeSelected();
        }

        void IPointerDownHandler.OnPointerDown(PointerEventData eventData)
        {
            if (disabled)
                return;

            if (_animate && _anim != null)
                _anim.SetBool(_pressedFlag, true);
        }

        void IPointerUpHandler.OnPointerUp(PointerEventData eventData)
        {
            if (disabled)
                return;

            OnInteracted();
        }

        // Using controller the element will get selected before submit
        void ISelectHandler.OnSelect(BaseEventData eventData)
        {
            if (disabled)
                return;

            Selected();
        }

        void IDeselectHandler.OnDeselect(BaseEventData eventData)
        {
            if (disabled)
                return;

            DeSelected();
        }

        void ISubmitHandler.OnSubmit(BaseEventData eventData)
        {
            if (disabled)
                return;

            OnInteracted();
        }

        private void OnInteracted()
        {
            if (_animate && _anim != null)
                _anim.SetBool(_pressedFlag, false);

            // Injection fails us here, not sure why... might be the way the duplicated gameobject is created is not supported by zenject
            // TODO: fix this if we want sound on the toggles?
            //_uiSounds.PlayButtonClickSound();
        }

        protected void SetColor(UIToggleColorScheme colorScheme)
        {
            switch (colorScheme)
            {
                case UIToggleColorScheme.Normal:
                    SetBackgroundColor(Constants.ATRIO_UI_BACKGROUND_GRAY);
                    SetLabelColor(Constants.ATRIO_LIGHT_GREY_TEXT);
                    break;
                case UIToggleColorScheme.Highlighted:
                    SetBackgroundColor(Constants.ATRIO_TITLESCREEN_YELLOW);
                    SetLabelColor(Constants.ATRIO_UI_BACKGROUND_GRAY);
                    break;
                case UIToggleColorScheme.Engaged:
                    SetBackgroundColor(Constants.ATRIO_TITLESCREEN_YELLOW);
                    SetLabelColor(Constants.ATRIO_UI_BACKGROUND_GRAY);
                    break;
                case UIToggleColorScheme.Disabled:
                default:
                    SetBackgroundColor(Constants.ATRIO_DISABLED_GREY2.WithAlpha(Constants.BACKGROUND_ALPHA_NORMAL));
                    SetLabelColor(Constants.ATRIO_GREY_TEXT);
                    break;
            }
        }

        private void SetBackgroundColor(Color c)
        {
            if (background != null)
                background.color = c;
        }

        private void SetCheckmarkColor(Color c)
        {
            if (checkmark != null)
                checkmark.color = c;
        }

        private void SetLabelColor(Color c)
        {
            if (label != null)
                label.color = c;
        }

        [ContextMenu("Select this")]
        public void Select()
        {
            Toggle.Select();
        }
    }
}