// Copyright Isto Inc.
using System;
using TMPro;
using UnityEngine;

namespace Isto.Core.UI
{
    public class UINotification : MonoBehaviour
    {
        public event Action<NotificationEventArgs> OnNotificationChanged;

        public bool BottomNode => _bottomNode;

        // Public Variables
        public GameObject notificationContainer;
        public GameObject wideCircle; //For notifications with numbers
        public GameObject regularCircle; //for a single notification (no numbers)
        public TextMeshProUGUI notificationCount;
        public bool hasCount; //If it's high level, it'll show you how many notifications you have
        public bool active;

        private int _notificationCount;
        private bool _active;
        private bool _bottomNode; // You should only be informing the parent if you're the bottom node.

        // Lifecycle Events

        private void Start()
        {
            SetCountVisibility(hasCount);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="depth">//1 for top level, 2 for one below, </param>
        /// <param name="parentMenu">The top level menu</param>
        /// <param name="notificationCount"></param>
        public void Setup(int notificationCount, bool bottomNode, bool hasCount = false)
        {
            _bottomNode = bottomNode;
            _active = true;
            _notificationCount = notificationCount;

            //Set Up Visuals
            if (_active)
            {
                SetCountVisibility(hasCount);
            }
            else
            {
                ClearNotification();
            }

            UpdateUI();

            if (_bottomNode)
                InformParent(_notificationCount);
        }

        /// <summary>
        /// Used when you've switched categories, and you need to update the bottom node, but don't inform the parents
        /// </summary>
        /// <param name="notificationCount"></param>
        /// <param name="bottomNode"></param>
        public void ReInitialize(int count, bool bottomNode)
        {
            _notificationCount = count;
            _bottomNode = bottomNode;

            UpdateUI();
        }

        public void UpdateUI()
        {
            if (!_active)
                return;

            if (_notificationCount <= 0)
            {
                ClearNotification();
            }
            else
            {
                notificationContainer.SetActive(true);
                notificationCount.text = _notificationCount.ToString();
            }
        }

        public void UpdateNotificationCount(int count)
        {
            _notificationCount = count;

            UpdateUI();

            if (_notificationCount >= 0)
            {
                if (_bottomNode)
                    InformParent(_notificationCount);
            }
        }

        public void InformParent(int amount)
        {
            OnNotificationChanged?.Invoke(new NotificationEventArgs(this, amount));
        }

        public void ClearNotification()
        {
            int amount = 0;
            notificationCount.text = amount.ToString();
            notificationContainer.SetActive(false);
            Destroy(this.gameObject);
        }

        private void SetCountVisibility(bool isVisible)
        {
            if (isVisible)
            {
                wideCircle.SetActive(true);
                regularCircle.SetActive(false);
            }
            else
            {
                wideCircle.SetActive(false);
                regularCircle.SetActive(true);
            }
        }
    }
}