// Copyright Isto Inc.
using UnityEngine;
using Zenject;

namespace Isto.Core.UI
{
    public class UICloseButton : MonoBehaviour
    {
        // Private Variables

        private IUIMenu _mainMenu;

        // Lifecycle Events

        [Inject]
        public void Inject(IUIMenu uIMainMenu)
        {
            _mainMenu = uIMainMenu;
        }

        // Methods		

        public void CloseMenu()
        {
            _mainMenu.CloseMenu();
        }
    }
}