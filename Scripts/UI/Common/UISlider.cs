// Copyright Isto Inc.

using System;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace Isto.Core.UI
{
    /// <summary>
    /// Component to be attached to slider UI elements to add helper methods for interaction
    /// </summary>
    [RequireComponent(typeof(Slider))]
    public class UISlider : Mono<PERSON><PERSON><PERSON>our, IPointerDownHandler, IPointerUpHandler, IPointerEnterHandler, IPointerExitHandler, ISelectHandler, IDeselectHandler, ISubmitHandler
    {
        public enum UISliderColorScheme
        {
            Normal, // Ready and standing by
            Highlighted, // Hovered or Selected
            Engaged // Clicked or Pressed
        }

        public enum UISliderColorSet
        {
            TitleMenu,
            Game
        }


        // UNITY HOOKUP

        [SerializeField] private Image _handle;
        [SerializeField] private Image _handleOutline;
        [SerializeField] private Image _fill;
        [SerializeField] private Image _background;
        [SerializeField] private TextMeshProUGUI _quantityLabel;

        [SerializeField] private Slider _slider;
        [SerializeField] private bool _setSliderColors;
        [SerializeField] private UISliderColorSet _sliderColorSet;
        [SerializeField] private bool _toggleHandleOutline;
        public static float backgroundAlphaSliderHandle = 0.15f;

        
        // OTHER FIELDS

        private bool _engaged;
        private bool _hovered;

        private bool _selected;
        private bool _snapButtonBack;
        private float _timer;
        
        
        // PROPERTIES

        public Slider Slider => _slider;


        // EVENTS

        public event Action OnPointerExit;
        public event Action OnPointerUp;


        // LIFECYCLE EVENTS

        private void Update()
        {
            if (_snapButtonBack && Time.unscaledTime - _timer > 0.1f)
            {
                if (!_engaged)
                {
                    if (_hovered || _selected)
                    {
                        SetHighlighted();
                    }
                    else
                    {
                        SetNormal();
                    }
                }

                _snapButtonBack = false;
            }
        }

        private void OnEnable()
        {
            SetNormal();
            RefreshLabel();
            RegisterEvents();
        }

        private void OnDisable()
        {
            UnregisterEvents();
        }


        // EVENT HANDLING

        private void RegisterEvents()
        {
            _slider.onValueChanged.AddListener(Slider_OnValueChanged);
        }

        private void UnregisterEvents()
        {
            _slider.onValueChanged.RemoveListener(Slider_OnValueChanged);
        }

        public void OnSubmit(BaseEventData eventData)
        {
            SetEngaged();
            // There is no up and down events for this so for now just snap it back up after a bit of time
            _snapButtonBack = true;
            _timer = Time.unscaledTime;
        }

        private void Slider_OnValueChanged(float value)
        {
            _quantityLabel.text = value.ToString();
        }

        void IPointerEnterHandler.OnPointerEnter(PointerEventData eventData)
        {
            _hovered = true;

            if (!_engaged)
            {
                SetHighlighted();
            }
        }

        void IPointerExitHandler.OnPointerExit(PointerEventData eventData)
        {
            _hovered = false;

            if (!_engaged)
            {
                SetNormal();
            }
        }

        void ISelectHandler.OnSelect(BaseEventData eventData)
        {
            _selected = true;

            if (!_engaged)
            {
                SetHighlighted();
            }
        }

        void IDeselectHandler.OnDeselect(BaseEventData eventData)
        {
            _selected = false;

            if (!_engaged && !_hovered)
            {
                SetNormal();
            }
        }

        void IPointerDownHandler.OnPointerDown(PointerEventData eventData)
        {
            _engaged = true;
            SetEngaged();
        }

        void IPointerUpHandler.OnPointerUp(PointerEventData eventData)
        {
            _engaged = false;
            if (_hovered)
            {
                SetHighlighted();
            }
            else
            {
                SetNormal();
            }
            
            OnPointerUp?.Invoke();
        }


        // ACCESSORS

        public Selectable GetSelectable()
        {
            return Slider;
        }

        private void SetColor(UISliderColorScheme colorScheme)
        {
            switch (colorScheme)
            {
                default:
                case UISliderColorScheme.Normal:
                    // Same for both styles AFAIK
                    SetHandleColor(Constants.ATRIO_LIGHT_GREY_TEXT);
                    SetHandleOutlineColor(Constants.ATRIO_LIGHT_GREY_TEXT);
                    SetFillColor(Constants.ATRIO_LIGHT_GREY_TEXT);
                    SetBackgroundColor(Color.black);
                    SetQuantityLabelColor(Constants.ATRIO_LIGHT_GREY_TEXT);
                    break;
                case UISliderColorScheme.Highlighted:
                    if (_sliderColorSet == UISliderColorSet.TitleMenu)
                    {
                        SetHandleColor(Constants.ATRIO_TITLESCREEN_YELLOW);
                        SetHandleOutlineColor(Constants.ATRIO_TITLESCREEN_YELLOW);
                        SetFillColor(Constants.ATRIO_TITLESCREEN_YELLOW);
                        SetBackgroundColor(Color.black);
                        SetQuantityLabelColor(Constants.ATRIO_TITLESCREEN_YELLOW);
                    }
                    else
                    {
                        SetHandleColor(Constants.ATRIO_SELECTED_YELLOW);
                        SetHandleOutlineColor(Constants.ATRIO_SELECTED_YELLOW);
                        SetFillColor(Constants.ATRIO_SELECTED_YELLOW);
                        SetBackgroundColor(Color.black);
                        SetQuantityLabelColor(Constants.ATRIO_LIGHT_GREY_TEXT);
                    }
                    break;
                case UISliderColorScheme.Engaged:
                    if (_sliderColorSet == UISliderColorSet.TitleMenu)
                    {
                        SetHandleColor(Constants.ATRIO_TITLESCREEN_YELLOW);
                        SetHandleOutlineColor(Constants.ATRIO_TITLESCREEN_YELLOW.WithAlpha(backgroundAlphaSliderHandle));
                        SetFillColor(Constants.ATRIO_TITLESCREEN_YELLOW);
                        SetBackgroundColor(Color.black);
                        SetQuantityLabelColor(Constants.ATRIO_TITLESCREEN_YELLOW);
                    }
                    else
                    {
                        SetHandleColor(Constants.ATRIO_SELECTED_YELLOW);
                        SetHandleOutlineColor(Constants.ATRIO_SELECTED_YELLOW.WithAlpha(backgroundAlphaSliderHandle));
                        SetFillColor(Constants.ATRIO_SELECTED_YELLOW);
                        SetBackgroundColor(Color.black);
                        SetQuantityLabelColor(Constants.ATRIO_LIGHT_GREY_TEXT);
                    }
                    break;
            }
        }

        private void SetHandleColor(Color c)
        {
            if (_handle != null)
            {
                _handle.color = c;
            }
        }

        private void SetHandleOutlineColor(Color c)
        {
            if (_handleOutline != null)
            {
                _handleOutline.color = c;
            }
        }

        private void SetFillColor(Color c)
        {
            if (_fill != null)
            {
                _fill.color = c;
            }
        }

        private void SetBackgroundColor(Color c)
        {
            if (_background != null)
            {
                _background.color = c;
            }
        }

        private void SetQuantityLabelColor(Color c)
        {
            if (_quantityLabel != null)
            {
                _quantityLabel.color = c;
            }
        }


        // OTHER METHODS

        private void RefreshLabel()
        {
            _quantityLabel.text = Slider.value.ToString();
        }

        public void DecreaseValue()
        {
            Slider.value--;
        }

        public void IncreaseValue()
        {
            Slider.value++;
        }

        public void ChangeHighlightedLabel(TextMeshProUGUI newLabel)
        {
            if (_quantityLabel == newLabel)
            {
                return;
            }

            if (_quantityLabel != null)
            {
                newLabel.color = _quantityLabel.color;
                _quantityLabel.color = Constants.ATRIO_LIGHT_GREY_TEXT;
            }

            _quantityLabel = newLabel;
        }

        private void SetNormal()
        {
            if (_setSliderColors)
            {
                SetColor(UISliderColorScheme.Normal);
            }

            if (_toggleHandleOutline)
            {
                _handleOutline.enabled = false;
            }

            OnPointerExit?.Invoke();
        }

        private void SetHighlighted()
        {
            if (_setSliderColors)
            {
                SetColor(UISliderColorScheme.Highlighted);
            }

            if (_toggleHandleOutline)
            {
                _handleOutline.enabled = false;
            }
        }

        private void SetEngaged()
        {
            if (_setSliderColors)
            {
                SetColor(UISliderColorScheme.Engaged);
            }

            if (_toggleHandleOutline)
            {
                _handleOutline.enabled = true;
            }
        }

        [ContextMenu("Select this")]
        public void Select()
        {
            Slider.Select();
        }
    }
}