// Copyright Isto Inc.
using DG.Tweening;
using TMPro;
using UnityEngine;

namespace Isto.Core.UI
{
    /// <summary>
    /// This class name sucks because it's called "plus one creator" yet it doesn't simply do that, it's misleading.
    /// </summary>
    public class UIPlusOneCreator : MonoBehaviour
    {
        [SerializeField] protected GameObject plusOnePrefab;
        [SerializeField] protected GameObject plusOneParent;
        [HideInInspector] public GameObject plusOne;
        [HideInInspector] public TextMeshProUGUI plusOneText;

        // refactor
        Tweener tween;
        Tweener tween2;

        private bool Instantiate()
        {
            if (plusOne == null)
            {
                plusOne = Instantiate(plusOnePrefab, plusOneParent.transform);
                plusOneText = plusOne.GetComponent<TextMeshProUGUI>();
            }

            return true;
        }

        public void Destroy()
        {
            if (plusOne != null)
                Destroy(plusOne);

            if (tween != null)
            {
                tween.Kill();
                tween = null;
            }
        }

        public void ShowPlusOne()
        {
            ShowAnimatedText("+1"); // TODO: should be a constant
        }

        public void ShowFull()
        {
            ShowAnimatedText("FULL"); // TODO: should be a constant and probably localized
        }

        private void ShowAnimatedText(string text)
        {
            Instantiate();

            plusOneText.text = text;

            if (tween != null && tween2 != null)
            {
                if (tween.IsPlaying())
                {
                    tween.Restart();
                    tween2.Restart();
                }
            }
            else
            {
                tween = plusOne.transform.DOLocalMoveY(60, 2)
                                         .SetEase(Ease.OutQuint)
                                         .SetLoops(1)
                                         .OnComplete(PlusOneComplete);

                TextMeshProUGUI plusOneText = plusOne.GetComponent<TextMeshProUGUI>();
                tween2 = plusOneText.DOFade(0, 2);
            }
        }

        private void PlusOneComplete()
        {
            Destroy();
        }
    }
}