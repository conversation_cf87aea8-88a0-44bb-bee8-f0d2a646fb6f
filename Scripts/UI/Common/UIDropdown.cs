// Copyright Isto Inc.
using Isto.Core.Audio;
using Isto.Core.Localization;
using TMPro;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    /// <summary>
    /// Provides some help with event handling and updating the UI colors for TMP_Dropdown.
    /// The color scheme is basically defined throughout this class. Consider refactoring that info to be available at the top
    /// of the class or from outside the code. (I would prefer defining these colors in a scriptable than in Constants.cs)
    /// </summary>
    [RequireComponent(typeof(TMP_Dropdown))]
    public class UIDropdown : <PERSON>oBeh<PERSON>our, IPointerDownHandler, IPointerUpHandler, IPointerEnterHandler, IPointerExitHandler, ISelectHandler, IDeselectHandler, ISubmitHandler
    {
        public enum UIDropdownColorScheme
        {
            Normal, // Ready and standing by
            Highlighted, // Hovered or Selected
            Engaged, // Clicked or Pressed
            Disabled // Not navigatable or interactable
        }

        public TMP_Dropdown Dropdown
        {
            get
            {
                if (_dropdown == null)
                {
                    _dropdown = this.GetComponent<TMP_Dropdown>();
                }
                return _dropdown;
            }
        }

        private bool IsAnimated => _animate && _anim != null;

        [Header("Status")]
        [SerializeField] protected bool disabled;

        [Header("UI Hookup")]
        [SerializeField] protected bool setLabelColors = true;
        [SerializeField] protected bool setDropdownColors = true;
        [SerializeField] protected TextMeshProUGUI label;
        [SerializeField] protected TextMeshProUGUI setting;
        [SerializeField] protected UIDropdownContent contentTemplateRoot;
        [SerializeField] protected TextMeshProUGUI itemTemplateText;
        [SerializeField] protected Toggle itemTemplateToggle;
        [SerializeField] protected UIToggle selectableItemTemplateToggle;
        [SerializeField] protected Image arrow;
        [SerializeField] protected Image background;
        [SerializeField] protected Image outline;

        [Header("Animation")]
        [SerializeField] private bool _animate = true;
        [SerializeField] private string _selectedTrigger = "selected";
        [SerializeField] private string _deselectedTrigger = "deselected";
        [SerializeField] private string _pressedFlag = "down";

        public UnityEvent DropdownHighlighted;
        public UnityEvent DropdownOpened;

        // Warning: turns out DropdownClosed is not easy to provide and it won't be covering every possible scenario here.
        // 1.Dropdown will close when you mouse click on a new value in the list. This one we have.
        // 2.Dropdown will close when you mouse click on the current value in the list. This one doesn't trigger anything.
        //     (We could add some OnMouseDown events on all the individual list items to detect this, but working around it for now)
        // 3.Dropdown will close when you mouse click anywhere other than the list. We don't know about that either.
        //     (We probably would have to introduce a clickable invisible panel to intercept the click and handle this by hand)
        // 4.Dropdown will close when you press A while a new value is selected. This is the same flow as #1.
        // 5.Dropdown will close when you press A while the current value is selected. This is the same flow as #2.
        // 6.Dropdown will close when you press B. This one is handled by us using a special menu substate, so we can easily
        //     get the information when in context, but notably, it won't be possible to throw an event from UIDropdown unless
        //     we make it aware of the state machine or the substate by giving it a reference. I would consider fixing it if it was
        //     only missing scenario, but since we are cobbling together awareness of the dropdown closing from several sources
        //     anyway, right now I'm leaving it like this.
        //
        // For an example, look at how I cover my bases to know when the dropdown is closed in UISettingsGraphicsSubState.
        //
        // Post-Note: I have added a component inside the template and an event to know when it gets disabled. This gets the job done
        // for at least some of the missing scenarios, but I haven't thoroughly tested it nor updated the above list yet.
        public UnityEvent DropdownClosed;

        // Who are our customers? Do they need to know the difference between a pointer hover and a controller selection?
        // Probably not.
        public UnityEvent DropdownPointerEnter;
        public UnityEvent DropdownPointerExit;
        public UnityEvent DropdownSelected;
        public UnityEvent DropdownDeselected;


        private bool _hovered = false;
        private bool _engaged = false;

        // Cache
        private Animator _anim; // Not certain we'll need this
        private Navigation _nav;
        private TMP_Dropdown _dropdown;

        // Injected
        private UISounds _uiSounds;

        [Inject]
        public void Inject(UISounds uISounds)
        {
            _uiSounds = uISounds;
        }

        private void Awake()
        {
            _anim = this.GetComponent<Animator>();
            _nav = Dropdown.navigation;

            if (disabled)
                SetDisabled();
            else
                SetEnabled();
        }

        private void OnEnable()
        {
            Dropdown.onValueChanged.AddListener(OnDropdownValueChanged);
        }

        private void OnDisable()
        {
            Dropdown.onValueChanged.RemoveListener(OnDropdownValueChanged);
        }

        public void SetLabelText(string localizedText)
        {
            if (localizedText != "")
                Loc.SetTMProLocalized(label, localizedText);
        }

        public Selectable GetSelectable()
        {
            return Dropdown;
        }

        [ContextMenu("Select this")]
        public void Select()
        {
            Dropdown.Select(); // This should cascade into calling our handled events and set color etc.
        }

        public void SetSelected()
        {
            if (disabled)
                return;

            DropdownHighlighted?.Invoke();

            if (IsAnimated)
                _anim.SetTrigger(_selectedTrigger);

            SetColor(UIDropdownColorScheme.Highlighted);
        }

        public void SetDeselected()
        {
            if (IsAnimated)
                _anim.SetTrigger(_deselectedTrigger);

            SetColor(UIDropdownColorScheme.Normal);
        }

        public void SetDisabled()
        {
            SetColor(UIDropdownColorScheme.Disabled);

            disabled = true;
            Dropdown.interactable = false;
        }

        public void SetEnabled()
        {
            SetColor(UIDropdownColorScheme.Normal);

            disabled = false;
            Dropdown.interactable = true;
        }

        private void OnDropdownValueChanged(int value)
        {
            if (Dropdown.IsExpanded)
                DropdownClosed?.Invoke();
        }

        public void OnDropdownContentDisabled(UIDropdownContent disabledContent)
        {
            if (disabledContent.name != contentTemplateRoot.name)
                DropdownClosed?.Invoke();
        }

        // Using mouse the control will get hovered before press
        void IPointerEnterHandler.OnPointerEnter(PointerEventData eventData)
        {
            _hovered = true;

            if (disabled)
                return;

            if (!_engaged)
                SetSelected();

            DropdownPointerEnter?.Invoke();
        }

        void IPointerExitHandler.OnPointerExit(PointerEventData eventData)
        {
            _hovered = false;

            if (disabled)
                return;

            if (!_engaged)
                SetDeselected();

            DropdownPointerExit?.Invoke();
        }

        void IPointerDownHandler.OnPointerDown(PointerEventData eventData)
        {
            if (disabled)
                return;

            if (IsAnimated)
                _anim.SetBool(_pressedFlag, true);
        }

        void IPointerUpHandler.OnPointerUp(PointerEventData eventData)
        {
            if (disabled)
                return;

            OnInteracted();
        }

        // Using controller the element will get selected before submit
        void ISelectHandler.OnSelect(BaseEventData eventData)
        {
            if (disabled)
                return;

            if (!_engaged)
                SetSelected();

            DropdownSelected?.Invoke();
        }

        void IDeselectHandler.OnDeselect(BaseEventData eventData)
        {
            if (disabled)
                return;

            if (!_engaged && !_hovered)
                SetDeselected();

            DropdownDeselected?.Invoke();
        }

        void ISubmitHandler.OnSubmit(BaseEventData eventData)
        {
            if (disabled)
                return;

            OnInteracted();

            SetColor(UIDropdownColorScheme.Normal);
        }

        private void OnInteracted()
        {
            if (IsAnimated)
                _anim.SetBool(_pressedFlag, false);

            _uiSounds.PlayButtonClickSound();

            if (Dropdown.IsExpanded)
                // This is not working, at least for controller - the submit must be consumed by the inner toggle and not bubbled back to the dropdown
                // Not that it should matter now that the graphics from the toggle list hide the dropdown
                DropdownClosed?.Invoke();
            else
                DropdownOpened?.Invoke();
        }

        private void SetColor(UIDropdownColorScheme colorScheme)
        {
            switch (colorScheme)
            {
                case UIDropdownColorScheme.Normal:
                    SetArrowColor(Constants.ATRIO_LIGHT_GREY_TEXT);
                    SetOutlineColor(Constants.ATRIO_TITLESCREEN_YELLOW);
                    SetBackgroundColor(Constants.ATRIO_UI_BACKGROUND_GRAY.WithAlpha(1f));
                    SetCurrentSettingLabelColor(Constants.ATRIO_LIGHT_GREY_TEXT);
                    SetDropdownLabelColor(Constants.ATRIO_LIGHT_GREY_TEXT);
                    break;
                case UIDropdownColorScheme.Highlighted:
                    SetArrowColor(Constants.ATRIO_UI_BACKGROUND_GRAY);
                    SetOutlineColor(Constants.ATRIO_TITLESCREEN_YELLOW);
                    SetBackgroundColor(Constants.ATRIO_TITLESCREEN_YELLOW.WithAlpha(1f));
                    SetCurrentSettingLabelColor(Constants.ATRIO_UI_BACKGROUND_GRAY);
                    SetDropdownLabelColor(Constants.ATRIO_TITLESCREEN_YELLOW);
                    break;
                case UIDropdownColorScheme.Engaged:
                    SetArrowColor(Constants.ATRIO_UI_BACKGROUND_GRAY);
                    SetOutlineColor(Constants.ATRIO_TITLESCREEN_YELLOW);
                    SetBackgroundColor(Constants.ATRIO_TITLESCREEN_YELLOW.WithAlpha(1f));
                    SetCurrentSettingLabelColor(Constants.ATRIO_UI_BACKGROUND_GRAY);
                    SetDropdownLabelColor(Constants.ATRIO_TITLESCREEN_YELLOW);
                    break;
                case UIDropdownColorScheme.Disabled:
                default:
                    SetArrowColor(Constants.ATRIO_GREY_TEXT.WithAlpha(Constants.BACKGROUND_ALPHA_PRESSED));
                    SetOutlineColor(Constants.ATRIO_DISABLED_GREY1);
                    SetBackgroundColor(Constants.ATRIO_DISABLED_GREY2.WithAlpha(Constants.BACKGROUND_ALPHA_NORMAL));
                    SetCurrentSettingLabelColor(Constants.ATRIO_GREY_TEXT);
                    SetDropdownLabelColor(Constants.ATRIO_GREY_TEXT);
                    break;
            }
        }

        private void SetBackgroundColor(Color c)
        {
            if (background != null)
                background.color = c;
        }

        private void SetDropdownLabelColor(Color c)
        {
            if (setLabelColors && label != null)
                label.color = c;
        }

        private void SetCurrentSettingLabelColor(Color c)
        {
            if (setting != null)
                setting.color = c;
        }

        private void SetArrowColor(Color c)
        {
            if (arrow != null)
                arrow.color = c;
        }

        private void SetOutlineColor(Color c)
        {
            if (outline != null)
                outline.color = c;
        }
    }
}