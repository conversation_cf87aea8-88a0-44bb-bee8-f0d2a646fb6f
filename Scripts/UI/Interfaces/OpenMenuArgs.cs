// Copyright Isto Inc.

using Isto.Core.Automation;
using UnityEngine;

namespace Isto.Core.UI
{
    /// <summary>
    /// Base class for optional extra arguments that need to be passed to the menu controller when the menu you want
    /// to open needs extra info. A work in progress. The menu state machines are still a work in progress.
    /// </summary>
    public abstract class OpenMenuArgs
    {
        // For now I made the OpenMenuArgs an optional parameter and I am making this type abstract.
        // If we change our mind on how the base OpenMenuArgs should work then this static definition could be useful.
        //public static readonly OpenMenuArgs EMPTY = new OpenMenuArgs();
    }

    /// <summary>
    /// Arguments for opening the storage menu.
    /// This is being handled in Atrio in UIMainMenuStateMachine as it is intended to, but only in certain flows,
    /// because this flow is not originally supported in Atrio and we're still using mostly the legacy flow.
    /// (We try to avoid refactoring Atrio in places we're not going to reuse.)
    /// Note that in Atrio the storage menu is particularly egregious since it is used for a multitude of non-storage
    /// items and just shows extra bits of edge case UI depending on what item you're sending into it.
    /// In other words, expect to do heavy validation for any changes that could affect Atrio.
    /// </summary>
    public class OpenStorageMenuArgs : OpenMenuArgs
    {
        public IInventory inventory;
        public GameObject owner;
    }
}