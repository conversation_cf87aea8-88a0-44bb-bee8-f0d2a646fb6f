// Copyright Isto Inc.
using Isto.Core.Automation;
using UnityEngine;

namespace Isto.Core.UI
{
    public enum UIMessageHandlerType { Message, ProgressMessage, HighPriorityMessage }

    /// <summary>
    /// Interface for showing messages in the UI
    /// </summary>
    public interface IUIMessages
    {
        // Low priority - shows above the character
        void CreateMessage(string localizedMessage, Sprite icon = null, float displayTime = 0);

        // Adds item to the queue on the left of the equip menu (param 'message' not shown)
        void CreateProgressMessage(string message, CraftingQueue queue, Sprite sprite = null);

        // Shows on the right of the screen
        void CreateHighPriorityMsg(string localizedMessage, HighPriorityMessage.MessageType msgType, float displayTime = 0);

        void SetCraftingMessageLocations(Vector3 craftingQueueLocation, Vector3 craftingMessagesLocation);
    }
}