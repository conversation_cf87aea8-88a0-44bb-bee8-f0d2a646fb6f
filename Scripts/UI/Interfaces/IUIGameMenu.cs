// Copyright Isto Inc.

namespace Isto.Core.UI
{
    /// <summary>
    /// This interface serves to identify the menu state machine handling the main UI during gameplay
    /// </summary>
    public interface IUIGameMenu : IUIMenu
    {
        // Not sure if this should be really here, cutscene state is a common game concept but I try to put all
        // the specific menu-changing methods in IUIAtrioGameMenu in favor of using OpenMenu from now on instead.
        // Internally this just opens a different menu state (UICutSceneState) but it does have slightly different
        // conditions inside it than OpenMenu and CloseMenu.
        // and also GameMenus enum did not list it in its possible game menus list...
        // Because of chained dependencies we ended up having a class in Core that uses this method, and I would prefer
        // to just put the class in Atrio so it could use IUIAtrioGameMenu.EnterCutSceneState() but that requires some
        // refactoring.
        // (PlayerResearchModule -> TetheredBuilding -> BuildingFirstConnectAction -> IUIGameMenu.EnterCutSceneState())
        void EnterCutSceneState();
        void ExitCutSceneState();

        void HideHUD(float fadeTime);
        void ShowHUD(float fadeTime);
    }
}