// Copyright Isto Inc.

namespace Isto.Core.UI
{
    public interface IInputModuleSwitcher
    {
        /// <summary>
        /// Changes the Eventsystem to use the secondary axis for input control.
        /// </summary>
        void SwitchToSecondaryAxis();

        /// <summary>
        /// Changes the Eventsystem to use the primary axis for input control.
        /// </summary>
        void SwitchToPrimaryAxis();
    }
}