// Copyright Isto Inc.

using UnityEngine.UI;

namespace Isto.Core.UI
{
    /// <summary>
    /// This is an interface that will provide a selectable on demand, but which one is provided can change over time,
    /// so you are expected to get a new reference each time you want to work on the selectable.
    /// </summary>
    public interface IDynamicSelectableProvider
    {
        Selectable GetSelectable();
    }
}