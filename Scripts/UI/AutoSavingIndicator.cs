// Copyright Isto Inc.
using Isto.Core.Game;
using System;
using UnityEngine;
using Zenject;

namespace Isto.Core.UI
{
    public class AutoSavingIndicator : MonoBehaviour
    {
        [SerializeField]
        private GameObject _visualsRoot;

        private bool _callbacksRegistered = false;

        // Injected
        private GameState _gameState;

        [Inject]
        public void Inject(GameState gameState)
        {
            _gameState = gameState;
        }

        private void Awake()
        {
            _visualsRoot.SetActive(false);
            RegisterCallbacks();
        }

        private void OnDestroy()
        {
            UnregisterCallbacks();
        }

        private void RegisterCallbacks()
        {
            if (_callbacksRegistered)
                return;

            Events.Subscribe(Events.GAME_AUTOSAVING, OnAutoSaveBegin);
            Events.Subscribe(Events.GAME_SAVED, OnSaveComplete);

            _callbacksRegistered = true;
        }

        private void UnregisterCallbacks()
        {
            if (!_callbacksRegistered)
                return;

            Events.UnSubscribe(Events.GAME_AUTOSAVING, OnAutoSaveBegin);
            Events.UnSubscribe(Events.GAME_SAVED, OnSaveComplete);

            _callbacksRegistered = false;
        }

        private void OnAutoSaveBegin()
        {
            ShowIcon();
        }

        private void OnSaveComplete()
        {
            HideIcon();
        }

        private void ShowIcon()
        {
            _visualsRoot.SetActive(true);
        }

        private void HideIcon()
        {
            _visualsRoot.SetActive(false);
        }
    }
}