// Copyright Isto Inc.

using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;

namespace Isto.Core.UI
{
    /// <summary>
    /// This class provides a way to set up some command macros in the dashboard, which you can make as
    /// buttons or any other UI tools.
    /// </summary>
	public abstract class DashboardCommand : MonoBehaviour
    {
        // UNITY HOOKUP

        [Header("DashboardCommand Config")]
        [FormerlySerializedAs("Commands")]
        public List<UIConsole.DeveloperConsoleCommandEntry> ActivationCommands;

        public List<UIConsole.DeveloperConsoleCommandEntry> DeactivationCommands;


        // OTHER FIELDS

        protected CheatMenuState _console;

        protected virtual void Start()
        {
            _console = gameObject.GetComponentInParent<CheatMenuState>();

            if (_console == null)
                Debug.LogError("DashboardCommand not setup properly in the DeveloperConsole prefab", this);
        }

        protected virtual void OnEnable()
        {
            //register events
        }

        protected virtual void OnDisable()
        {
            //unregister events
        }

        protected virtual void OnDestroy()
        {

        }

        protected void ActivateCommand()
        {
            _console.Run(ActivationCommands);
        }

        protected void DeactivateCommand()
        {
            _console.Run(DeactivationCommands);
        }
    }
}