// Copyright Isto Inc.

using System;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Isto.Core.UI
{
    public class DashboardToggleCommand : DashboardCommand
    {
        // UNITY HOOKUP

        [Header("DashboardToggleCommand Config")]
        [SerializeField] private Button _button;

        [SerializeField] private Toggle _toggle;

        [SerializeField][Tooltip("Uses the toggle GameObject name to save in EditorPrefs")]
        private bool _saveStateInEditor;


        // OTHER FIELDS

        private ColorBlock _engagedButtonColors;
        private ColorBlock _normalButtonColors;


        // LIFECYCLE EVENTS

        protected override void Start()
        {
            if (_button == null || _toggle == null)
            {
                Debug.LogError("DashboardToggleCommand not setup properly in the CheaterDashboardToggle prefab", this);
            }

            base.Start();

#if UNITY_EDITOR
            if (_saveStateInEditor)
            {
                bool toggleState = EditorPrefs.GetBool(gameObject.name, false);
                SendActivateCommand(toggleState);
            }
#endif
        }

        protected override void OnEnable()
        {
            InitializeButtonColors();
            RegisterEvents();
        }

        protected override void OnDisable()
        {
            UnregisterEvents();
        }


        // EVENT HANDLING

        private void RegisterEvents()
        {
            _button.onClick.AddListener(Button_OnPressed);
            Events.SubscribeWithParams(Events.DEVELOPER_COMMAND, DeveloperCommand_HandleArguments);
        }

        private void UnregisterEvents()
        {
            _button.onClick.RemoveListener(Button_OnPressed);
            Events.UnSubscribeWithParams(Events.DEVELOPER_COMMAND, DeveloperCommand_HandleArguments);
        }

        private void DeveloperCommand_HandleArguments(object[] args)
        {
            if (_console == null)
                return;

            UpdateDashboard();
        }

        private void Button_OnPressed()
        {
            bool isActivateSelected = !_toggle.isOn;
            SendActivateCommand(isActivateSelected);
        }


        // ACCESSORS

        private bool IsActivationAndCurrentCommandsEqual()
        {
            bool isValid = true;
            foreach (var activationCommand in ActivationCommands)
            {
                if(!isValid)
                    continue;

                // Check if the CurrentCommandStates match the activation commands for this toggle
                isValid = _console.CurrentCommandStates.Any(commandEntry =>
                    commandEntry.command.Equals(activationCommand.command, StringComparison.OrdinalIgnoreCase) &&
                    commandEntry.arguments.SequenceEqual(activationCommand.arguments));

            }
            return isValid;
        }


        // OTHER METHODS

        private void SendActivateCommand(bool value)
        {
            if (value)
            {
                if (ActivationCommands != null && ActivationCommands.Count > 0)
                {
                    foreach (var activationCommand in ActivationCommands)
                    {
                        Debug.Log($"Activate Command {activationCommand.command}");
                    }
                }

                ActivateCommand();
            }
            else
            {
                if (DeactivationCommands != null && DeactivationCommands.Count > 0)
                {
                    foreach (var deactivationCommand in DeactivationCommands)
                    {
                        Debug.Log($"Deactivate Command {deactivationCommand.command}");
                    }
                }

                DeactivateCommand();
            }
        }

        private void InitializeButtonColors()
        {
            _normalButtonColors = _button.colors;
            _engagedButtonColors = _normalButtonColors;
            _engagedButtonColors.normalColor = _normalButtonColors.pressedColor;
            _engagedButtonColors.pressedColor = _normalButtonColors.normalColor;
        }

        private void UpdateDashboard()
        {
            bool isEngaged = IsActivationAndCurrentCommandsEqual();
            _toggle.isOn = isEngaged;
            _button.colors = isEngaged ? _engagedButtonColors : _normalButtonColors;

#if UNITY_EDITOR
            if (_saveStateInEditor)
            {
                EditorPrefs.SetBool(gameObject.name, isEngaged);
            }
#endif
        }
    }
}