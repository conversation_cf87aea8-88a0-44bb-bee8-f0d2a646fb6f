// Copyright Isto Inc.

using Isto.Core.Cheats;
using Isto.Core.Inputs;
using System;
using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.Serialization;

namespace Isto.Core.UI
{
    /// <summary>
    /// Console (or terminal simulator) which reads text input as command lines and activates the corresponding commands
    /// </summary>
    public class UIConsole : MonoBehaviour
    {
        public class ConsoleCommandDefinition
        {
            public string commandStr;
            public bool closeConsoleWhenUsed = true; // TODO: remove
            public List<CheatAction> actions;
        }

        [Serializable]
        public class DeveloperConsoleCommandEntry
        {
            public string command;
            public CheatAction action; // TODO: replace command with action
            public string[] arguments;
        }

        [Header("Terminal Display")]
        [FormerlySerializedAs("containerGroup")]
        public CanvasGroup terminalContainerGroup;
        public TMP_InputField inputField;
        public TextMeshProUGUI responseField;
        public string openMessage = "Dev Console.  Type 'commands' to see list of possible commands";


        // Private Variables

        private List<ConsoleCommandDefinition> _commands;

        private Queue<string> _responseMessages;
        private int _responseMessagesCount = 4;

        private Queue<string> _previousCommands = new Queue<string>(capacity: 5);
        private int _previousCmdsToKeep = 10;
        private int _previousCmdIndex = 0;


        // props

        public bool IsOpen { get; private set; }


        // events

        public event Action<ConsoleCommandDefinition, string[]> OnCommandActivated;

        // LIFECYCLE EVENTS

        protected void Awake()
        {
            _commands = new List<ConsoleCommandDefinition>();
            _responseMessages = new Queue<string>();
        }

        protected void Start()
        {
            RegisterEvents();
        }

        protected void OnDestroy()
        {
            UnregisterEvents();
        }

        private void OnEnable()
        {
        }

        protected void Update()
        {
            if (!IsOpen)
                return;

            // Check for console navigation
#if PLATFORM_GAMECORE && !UNITY_EDITOR
            const char backspaceChar = '\b';
            const char enterChar = '\r';
            // Right now input field is not working on console so we just pipe the keyboard input in it for partial support
            if (Input.anyKeyDown)
            {
                string inputString = Input.inputString;
                if (inputString == $"{backspaceChar}")
                {
                    SetInputFieldText(inputField.text.Substring(0, inputField.text.Length - 1));
                }
                else if (inputString == $"{enterChar}")
                {
                    OnConsoleSubmit();
                }
                else
                {
                    SetInputFieldText(inputField.text + inputString);
                }
            }
#else
            if (Input.anyKeyDown && EventSystem.current.currentSelectedGameObject != inputField.gameObject && !Controls.UsingController)
            {
                inputField.Select();
            }

            if (Input.GetKeyDown(KeyCode.UpArrow) && _previousCommands.Count > 0)
            {
                SetInputFieldText(GetNextPreviousCommand());
            }
            else if (Input.GetKeyDown(KeyCode.DownArrow) && _previousCommands.Count > 0)
            {
                SetInputFieldText(GetLastPreviousCommand());
            }
            else if (Input.GetKeyDown(KeyCode.Tab))
            {
                OnTabPressed();
            }
#endif
        }


        // EVENT HANDLING

        private void RegisterEvents()
        {
            inputField.onSubmit.AddListener(InputField_OnSubmit);
        }

        private void UnregisterEvents()
        {
            inputField.onSubmit.RemoveListener(InputField_OnSubmit);
        }

        private void InputField_OnSubmit(string fieldText)
        {
            OnConsoleSubmit();
        }


        // Methods

        public void RegisterCommand(ConsoleCommandDefinition commandDefinition)
        {
            _commands.Add(commandDefinition);
        }

        public void RegisterCommands(List<ConsoleCommandDefinition> commandDefinitions)
        {
            _commands.AddRange(commandDefinitions);
        }

        public void OpenConsole()
        {
            IsOpen = true;

            responseField.text = openMessage;

            inputField.enabled = true;
            inputField.text = "";

            terminalContainerGroup.SetCanvasValues(alpha: 1f, interactable: true, blockRaycast: true);

            SelectConsole();
        }

        public void SelectConsole()
        {
            if (EventSystem.current.currentSelectedGameObject != inputField.gameObject)
            {
                inputField.Select();
            }
        }

        public void CloseConsole()
        {
            IsOpen = false;

            terminalContainerGroup.SetCanvasValues(alpha: 0f, interactable: false, blockRaycast: false);

            inputField.enabled = false;
        }

        /// <summary>
        /// Called when the user presses enter in the input field text box
        /// </summary>
        public void OnConsoleSubmit()
        {
            if (!inputField.enabled || string.IsNullOrEmpty(inputField.text))
                return;

            AddCommandToPreviousQueue(inputField.text);

            ProcessInput(inputField.text);

            SetInputFieldText("");

            inputField.DeactivateInputField();
            inputField.ActivateInputField();

            DisplayResponseText();
        }

        private void AddCommandToPreviousQueue(string text)
        {
            _previousCommands.Enqueue(text);

            if (_previousCommands.Count >= _previousCmdsToKeep)
                _previousCommands.Dequeue();

            _previousCmdIndex = 0;
        }

        private string GetNextPreviousCommand()
        {
            // Grabbing from back of array as that's where queue puts new items
            string next = _previousCommands.ToArray()[_previousCommands.Count - 1 - _previousCmdIndex];

            _previousCmdIndex = Mathf.Clamp(_previousCmdIndex + 1, 0, _previousCommands.Count - 1);

            return next;
        }

        private string GetLastPreviousCommand()
        {
            _previousCmdIndex--;

            // Roll back around if over
            if (_previousCmdIndex < 0)
            {
                _previousCmdIndex = 0;
                return "";
            }

            // Grabbing from back of array as that's where queue puts new items
            string next = _previousCommands.ToArray()[_previousCommands.Count - 1 - _previousCmdIndex];

            return next;
        }

        public void RunCommands(List<DeveloperConsoleCommandEntry> sequence)
        {
            if (sequence == null)
            {
                Debug.LogWarning("DeveloperConsole trying to run a null command list");
                return;
            }

            for (int i = 0; i < sequence.Count; i++)
            {
                string line = sequence[i].command;
                for (int j = 0; j < sequence[i].arguments.Length; j++)
                {
                    line += " " + sequence[i].arguments[j];
                }
                //UpdateCurrentCommand(sequence[i]);
                ProcessInput(line);
            }
        }

        public void OnTabPressed()
        {
            if (inputField.text.Length == 0)
            {
                ShowAllAvailableCommands();
                DisplayResponseText();
                return;
            }

            string[] splitInput = inputField.text.ToLower().Split(' ');

            ConsoleCommandDefinition devCommand = GetCommand(splitInput[0]);

            if (devCommand == null)
            {
                // If no command is set, try and find one
                string commandName = FindFirstCommand(splitInput[0]);
                if (!String.IsNullOrEmpty(commandName))
                    SetInputFieldText(commandName);
            }
            else if (splitInput.Length == 1)
            {
                // If user didn't press space, cycle command options
                SetInputFieldText(FindNextCommand(splitInput[0]));
            }
            else
            {
                if (splitInput.Any(x => x.StartsWith("\"")))
                {
                    splitInput = GroupTokensByQuotationMarks(splitInput);
                }

                // Once user pressed space, or started writing something more, we are trying to find parameters
                for (int i = 0; i < devCommand.actions.Count; i++)
                {
                    CheatAction action = devCommand.actions[i];

                    string[] actionInputs = new string[splitInput.Length - 1];
                    Array.Copy(splitInput, 1, actionInputs, 0, actionInputs.Length);

                    if (action.TryAutoCompleteParameters(actionInputs, out string result))
                    {
                        //TODO: If many actions are sequenced, avoid overriding each other's autocomplete?
                        SetInputFieldText($"{devCommand.commandStr} {result}");
                    }
                }
            }
        }

        /// <summary>
        /// This method expects that we've first broken down our commands and parameters by whitespaces, and does a
        /// second pass to put back together any parameters that were surrounded by quotation marks while also trimming
        /// those quotation marks so the parameter logic doesn't have to know about them.
        /// </summary>
        /// <param name="splitInput">The inputs list broken down by white spaces</param>
        /// <returns>The new version of the inputs list with quotation marks applied</returns>
        private string[] GroupTokensByQuotationMarks(string[] splitInput)
        {
            List<string> fixedList = new List<string>();
            List<string> buffer = new List<string>();
            List<string> currentGroup = fixedList;
            for (int i = 0; i < splitInput.Length; i++)
            {
                string token = splitInput[i];

                if (splitInput[i].StartsWith("\"") && !splitInput[i].EndsWith("\""))
                {
                    currentGroup = buffer;
                }

                if (token.StartsWith("\""))
                {
                    token = token.Substring(1);
                }

                if (token.EndsWith("\""))
                {
                    token = token.Substring(0, token.Length - 1);
                }

                currentGroup.Add(token);

                if (splitInput[i].EndsWith("\"") && !splitInput[i].StartsWith("\""))
                {
                    fixedList.Add(string.Join(" ", buffer));
                    buffer.Clear();
                    currentGroup = fixedList;
                }
            }

            return fixedList.ToArray();
        }

        private string FindFirstCommand(string input)
        {
            for (int i = 0; i < _commands.Count; i++)
            {
                if (_commands[i].commandStr.StartsWith(input, StringComparison.CurrentCultureIgnoreCase))
                    return _commands[i].commandStr;
            }

            return "";
        }

        private string FindNextCommand(string input)
        {
            for (int i = 0; i < _commands.Count; i++)
            {
                if (_commands[i].commandStr.StartsWith(input, StringComparison.CurrentCultureIgnoreCase))
                {
                    if (Input.GetKey(KeyCode.LeftShift))
                        i = (i == 0) ? _commands.Count - 1 : i - 1;
                    else
                        i = (i + 1) % _commands.Count;

                    return _commands[i].commandStr;
                }
            }

            return "";
        }

        private void ProcessInput(string consoleInput)
        {
            string input = consoleInput.Trim();
            string[] splitInput = input.ToLower().Split(' ');

            ConsoleCommandDefinition devCommand = GetCommand(splitInput[0]);

            if (devCommand == null)
            {
                AddResponseText("Unkown command: " + splitInput[0]);
            }
            else
            {
                OnCommandActivated?.Invoke(devCommand, splitInput);
            }
        }

        public void ShowAllAvailableCommands()
        {
            string commandList = "";

            for (int i = 0; i < _commands.Count; i++)
            {
                commandList += _commands[i].commandStr;
                if (i < _commands.Count - 1)
                    commandList += ", ";
            }

            _responseMessages.Enqueue(commandList);
        }

        /// <summary>
        /// Gets the command that goes with the commandStr passed in.  Returns null if none is found
        /// </summary>
        /// <param name="commandStr"></param>
        /// <returns>Command that matches the string, null otherwise</returns>
        private ConsoleCommandDefinition GetCommand(string commandStr)
        {
            for (int i = 0; i < _commands.Count; i++)
            {
                if (_commands[i].commandStr.Equals(commandStr, System.StringComparison.OrdinalIgnoreCase))
                {
                    return _commands[i];
                }
            }

            return null;
        }

        /// <summary>
        /// Adds a message to be displayed in the response text field.
        /// </summary>
        /// <param name="text"></param>
        public void AddResponseText(string text)
        {
            if (IsOpen)
            {
                if (_responseMessages.Count == _responseMessagesCount)
                    _responseMessages.Dequeue();

                _responseMessages.Enqueue(text);
            }
            else
            {
                Debug.Log($"[DevConsole] {text}");
            }
        }

        /// <summary>
        /// Goes through each message in the queue and displays it in the response field
        /// </summary>
        public void DisplayResponseText()
        {
            responseField.text = "";

            foreach (string message in _responseMessages)
            {
                responseField.text += message + '\n';
            }

            _responseMessages.Clear();
        }

        public void SetInputFieldText(string text)
        {
            inputField.text = text;
            inputField.caretPosition = text.Length;
        }
    }
}