// Copyright Isto Inc.

#if REBINDING_LOGGING
using System;
#endif

using Isto.Core.Inputs;
using Rewired;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    public abstract class UIControlRebindingBase : MonoBehaviour
    {
        private struct TargetMapping {
            public ControllerMap controllerMap;
            public int actionElementMapId;
        }

        // UNITY HOOKUP

        [Header("Display")]
        [SerializeField] protected TextMeshProUGUI _labelTextbox;
        [SerializeField] protected TextMeshProUGUI _controlKeyTextbox;
        [SerializeField] protected Image _keyImage;
        [SerializeField] protected Sprite _glyphOverride;

        [Header("Interaction")]
        [SerializeField] protected UISettingsSelectable _rebindButton;

        [Header("Action Settings")]
        [SerializeField] protected bool _isKeyboardAndMouse;
        [SerializeField] protected bool _allowForControllerTriggers = false;
        [SerializeField] protected Controls.InputDevice _device;
        [SerializeField] protected Controls.RewiredCategory _category;
        [Tooltip("Other categories that also contain this action and should always be using the same input as this action")]
        [SerializeField] protected List<Controls.RewiredCategory> _pairedCategories;


        // OTHER FIELDS

        protected int _actionId;
        protected ActionElementMap _aem;
        protected Sprite _cachedSprite;
        protected InputMapper.ConflictFoundEventData _conflictData;
        protected UISettingsControlsSubState _controlsSettings;
        protected bool _isInitialized;
        protected ControllerMap _map;
        protected bool _pollingForKeypress;
        private TargetMapping _replaceTargetMapping;

        // This is the GUID for the Xbox One controller
        protected System.Guid DEFAULT_CONTROLLER_GUID = new System.Guid("19002688-7406-4f4a-8340-8d25335406c8");

        private static readonly int LEFT_TRIGGER_ELEMENT_IDENTIFIER_ID = 4;
        private static readonly int RIGHT_TRIGGER_ELEMENT_IDENTIFIER_ID = 5;

        private static readonly List<UserActions> FORBIDDEN_ACTIONS_CONTROLLER = new List<UserActions>
        {
            // Restrict the UI navigation, confirm and cancel controls or user can get stuck
            // On controller, these should be the D-Pad and the left stick both, make sure we handle both possible conflicts in our check
            // for forbidden actions
            UserActions.UIHORIZONTAL,
            UserActions.UIVERTICAL,
            UserActions.UISUBMIT,
            UserActions.UICANCEL,
            // If Game Menu is lost, you can't save your game nor fix the control settings (unless you quit your game... that you can't save)
            UserActions.TOGGLEGAMEMENU,
            // We decided to prevent remapping the console open action for now, although I see no reason it could not be if we wanted
            UserActions.TOGGLEDEVCONSOLE,
            // I locked this one probably because of the double mapping to L2 and R2 as we can't really reflect that without making 2 entries
            // and I think it doesn't make too much sense to have 2. User will just have to be surprised when R2 also blocks it.
            UserActions.CANCELDISMANTLING
        };

        private static readonly List<UserActions> FORBIDDEN_ACTIONS_KEYBOARD = new List<UserActions>
        {
            // If Game Menu is lost, you can't save your game nor fix the control settings (unless you quit your game... that you can't save)
            UserActions.TOGGLEGAMEMENU,
            // Our UI relied on a mixture of UICancel and Pause to close its parts, and tab is mapped to both when in UI category
            // This is a bit weird but it would need de-entangling of these logics and removal of the UICancel assignment to be
            // OK to remap it. For now the fact it's locked to this double mapping makes it problematic to remap.
            UserActions.PAUSE,
            // We decided to prevent remapping the console open action for now, although I see no reason it could not be if we wanted
            UserActions.TOGGLEDEVCONSOLE,
            // These cancels are used in placement and dismantling mode. They are mapped on escape and tab, so we need to protect the
            // actions to ensure the protected escape binding does not get deleted as collateral from a tab rebinding in another map
            UserActions.CANCELBUILDING,
            UserActions.CANCELDISMANTLING,
            // This is left control and it's used to split stacks in conjunction with left click
            UserActions.UIMODIFIERACTION
        };

        private static readonly List<Controls.MovementAxis> FORBIDDEN_AXES = new List<Controls.MovementAxis>
        {
            // Restrict the UI navigation, confirm and cancel controls or user can get stuck
            Controls.MovementAxis.UIHorizontal,
            Controls.MovementAxis.UIVertical,
            Controls.MovementAxis.UISecondaryHorizontal,
            Controls.MovementAxis.UISecondaryVertical
        };

        private string _cachedXML;
        private InputMapper _primaryInputMapper;
        private InputMapper _secondaryInputMapper; // This is used when we are listening for multiple inputs (i.e. keyboard and mouse)


        // PROPERTIES

        public abstract string InputActionName { get; }

        // AxisType: Joysticks are Normal. I guess Split is triggers? Need to check. Everything else is None, even if it's mapped on a axis action.
        public abstract bool IsAxis { get; }

        // AxisRange: notably our single pole matching keys on keyboard actually are set as Full instead of Positive or Negative in ReWired
        // As you can see in UIAxisRebinding, we instead return a specific polarity for this ActionAxisRange for those controls.
        // I'm sure there is a reason for it, but we can't rely on that to be the case when looking at the existing mappings.
        // Might be that this value is meant to be ignored when using keyboard bindings? Otherwise not sure what it implies.
        public virtual AxisRange ActionAxisRange => AxisRange.Full;

        // I think this value is only meaningful if the mapping is of type ControllerElementType.Axis and maybe only if the control is AxisType.None?
        public virtual Pole AxisDirection => Pole.Positive;
        public string ActionName => _labelTextbox.text;
        protected UISettingsSelectable Button => _rebindButton;
        private bool IsRebindingForJoystick => _device == Controls.InputDevice.Joystick;


        // INJECTION

        private Controls _controls;
        private ControllerGlyphs _glyphs;

        [Inject]
        public void Inject(IControls controls, ControllerGlyphs glyphs)
        {
            _controls = controls as Controls;
            _glyphs = glyphs;
        }


        // LIFECYCLE EVENTS

        public void Awake()
        {
            _controlsSettings = GetComponentInParent<UISettingsControlsSubState>();

            // Since we define the control only once, but we don't know what category map we'll be editing when the conflict happens,
            // we want all of the categories to be in this list (for algorithmic simplicity). But that's not intuitive so I'll make it
            // happen here in code to be sure
            if (_pairedCategories == null)
                _pairedCategories = new List<Controls.RewiredCategory>();
            if (!_pairedCategories.Contains(_category))
                _pairedCategories.Add(_category);
        }

        private void OnEnable()
        {
            _isInitialized = false;
        }


        // EVENT HANDLING

        /// <summary>
        /// Callback from the Rewired InputMapper when a conflict is detected
        /// There seems to be no clear documentation about what actualy counts as a conflict anywhere, so here are my findings.
        /// A conflict is when a input key or button already is mapped to any action across all relevant categories.
        /// (the Rewired editor allows us to define which map categories look at which map categories to detect these conflicts)
        /// Config Note:
        /// In Atrio our UI categories are protected and our gameplay categories are set to only check against themselves individually.
        /// </summary>
        private void InputMapper_OnConflictFound(InputMapper.ConflictFoundEventData data)
        {
#if REBINDING_LOGGING
            Debug.LogWarning($"Conflicts found : {data.conflicts.Count}");
            foreach(var conflict in data.conflicts)
            {
                Debug.Log($"conflict: keyCode={conflict.keyCode}, element={conflict.elementDisplayName}, category={GetRewiredCatName(conflict.controllerMap.categoryId)}, action={conflict.action.name}");
            }
#endif
            if (data.conflicts.Count > 1)
            {
                // There should never be a conflict with more than 1 other mapping normally, except some rare exceptions.
                // And if it happens, it's only as long as the user has not remapped it yet, so we know there is no consequence
                // to overriding both at once and that it is correct. Once user has remapped once, current UI does not allow
                // for new double mappings.
#if REBINDING_LOGGING
                Debug.LogWarning($"More than one conflict found. (there are {data.conflicts.Count})");
#endif
            }

            _conflictData = data;

            if (IsToBeIgnored(data))
            {
#if REBINDING_LOGGING
                Debug.LogWarning("Conflict ignored");
#endif
                data.responseCallback(InputMapper.ConflictResponse.Replace);
                _pollingForKeypress = false;
            }
            else if (IsDataProtected(data.isProtected, data.conflicts))
            {
#if REBINDING_LOGGING
                Debug.LogWarning("Data protected. Cancelling binding attempt.");
#endif
                data.responseCallback(InputMapper.ConflictResponse.Cancel);
                QuitRebindingImmediate();

                _controlsSettings.ShowBindingForbiddenPopup(SelectControllerRebind);
            }
            else
            {
                for (int i = 0; i < data.conflicts.Count; i++)
                {
                    if (data.conflicts[i].controllerMap == _map ||
                        (_isKeyboardAndMouse && (data.conflicts[i].controllerMap.controllerType == ControllerType.Keyboard || data.conflicts[i].controllerMap.controllerType == ControllerType.Mouse)))
                    {
                        if (_isKeyboardAndMouse)
                        {
                            if (data.conflicts[i].controllerMap.controllerType == ControllerType.Keyboard)
                            {
                                _secondaryInputMapper.Stop();
                            }

                            if (data.conflicts[i].controllerMap.controllerType == ControllerType.Mouse)
                            {
                                _primaryInputMapper.Stop();
                            }
                        }
                        // When we have a conflict, we first need to check if the proposed change is valid.
                        // If the new binding is valid, then we notify the player that there is a conflict
                        // If the new binding is not valid, then we show that the override has failed.
                        ElementAssignmentConflictInfo currentConflict = data.conflicts[i];
                        if (IsNewBindingValid(currentConflict.elementMap))
                        {
                            // Warning:
                            // If you respond with "rebind" then all the conflicting maps are deleted
                            // Then a single map gets created to replace them, matching the button in question with the action we requested initially
                            ShowConflictInformationPopUp(currentConflict);
                        }
                        else
                        {
                            QuitRebindingImmediate();
                            DeleteMapping(currentConflict.elementMap);
                            ApplyCachedControllerMap();
                            _controlsSettings.ShowOverrideFailedPopup(SelectControllerRebind);
                        }
                        return;
                    }
                }
            }

            // In case we didn't match the controller maps from the conflicts
            _pollingForKeypress = false;
        }

        private void InputMapper_OnInputMapped(InputMapper.InputMappedEventData data)
        {
#if REBINDING_LOGGING
            Debug.LogError($"Input mapped! keyCode={data.actionElementMap.keyCode}, element={data.actionElementMap.elementIdentifierName}, category={GetRewiredCatName(data.actionElementMap.controllerMap.categoryId)}, action={data.actionElementMap.actionDescriptiveName}");
#endif

            QuitRebindingImmediate();

            // Replicate any deleted bindings from our conflict to their own pairs first so as to free the binding for assignation
            if (CanBeAppliedToAllPairedCategories(data))
            {
                if (TryClearConflictCollaterals(data))
                {
                    // Replicate the new binding onto our pairs
                    ApplyToPairedCategories(data);
                }
                else
                {
#if REBINDING_LOGGING
                    Debug.LogError("Protected conflict found in collaterals - cancelling rebinding");
#endif
                    // Cancel the new binding and then apply the previously cached map
                    DeleteMapping(data.actionElementMap);
                    ApplyCachedControllerMap();
                    _controlsSettings.ShowOverrideFailedPopup(SelectControllerRebind);
                }
            }
            else
            {
#if REBINDING_LOGGING
                Debug.LogError("Protected conflict found in paired category - cancelling rebinding");
#endif
                // Cancel the new binding and then apply the previously cached map
                DeleteMapping(data.actionElementMap);
                ApplyCachedControllerMap();
                _controlsSettings.ShowOverrideFailedPopup(SelectControllerRebind);
            }

            if (_isKeyboardAndMouse)
            {
                if(_replaceTargetMapping.controllerMap != null) { // we have a replacement

                    // Remove the replacement from the other map if the binding was made on the opposite device type
                    if(data.actionElementMap.controllerMap != _replaceTargetMapping.controllerMap) {
                        _replaceTargetMapping.controllerMap.DeleteElementMap(_replaceTargetMapping.actionElementMapId);
                        _device = GetInputDevice(data.actionElementMap.controllerMap.controllerType);
                    }
                }
            }
        }

        private Controls.InputDevice GetInputDevice(ControllerType controllerType)
        {
            Controls.InputDevice inputDevice = Controls.InputDevice.Joystick;

            if (controllerType == ControllerType.Keyboard)
            {
                inputDevice = Controls.InputDevice.Keyboard;
            }
            else if (controllerType == ControllerType.Mouse)
            {
                inputDevice = Controls.InputDevice.Mouse;
            }

            return inputDevice;
        }

        private void InputMapper_OnTimedOut(InputMapper.TimedOutEventData timedOutEventData)
        {
            QuitRebindingImmediate();
            RefreshDisplay();
        }

        /// <summary>
        /// Registered to a callback from Rewired giving us the opportunity to validate the user's key press.
        /// If we return false, the press is simply ignored and the polling continues.
        /// Ideally we would be providing some feedback to the user about this. The polling popup could update
        /// to display a message saying e.g. "Esc is not allowed please try another key".
        /// </summary>
        private bool InputMapper_IsElementValid(ControllerPollingInfo info)
        {
            if (!_pollingForKeypress)
                return false;

#if REBINDING_LOGGING
            Debug.LogError($"IsElementValid check fo keyboardKey={info.keyboardKey}, element={info.elementIdentifierName}, elementType={info.elementType}, controllerType={info.controllerType}");
#endif

            if (_device == Controls.InputDevice.Keyboard || _device == Controls.InputDevice.Mouse)
            {
                // TODO: can we add support for extra mouse buttons here? we could lock left, right and middle click, but allow others
                // but right now IsElementValid doesn't even seem to pick up mouse inputs so there must be some config to change somewhere.
                if (info.keyboardKey == KeyCode.Escape || info.keyboardKey == KeyCode.Tab || info.elementType == ControllerElementType.Axis)
                    return false;
            }
            else // Controller
            {
                // Not sure if Custom is used but including just incase people have different peripherals to we don't account for
                if (info.controllerType != ControllerType.Joystick && info.controllerType != ControllerType.Custom)
                    return false;

                if (IsAxis)
                {
                    if (info.elementType != ControllerElementType.Axis)
                    {
#if REBINDING_LOGGING
                        Debug.Log("refused non-axis binding for axis action");
#endif
                        return false;
                    }
                    // how do I detect a trigger here?
                    //info.axisPole is positive or negative to reflect which half of the axis is in use
                    // need to find AxisRange instead
                    // normally the ActionElementMap has this, but this is simply a button press, need to infer it??
                    // apparently elementIndex in combination with controllerType can get me the exact axis index, maybe from there I can know the range
                    // For now I think this is of low importance so I'll figure this out later.
                }
                else if (info.elementType == ControllerElementType.Axis)
                {
                    // Can't outright refuse Axis type bindings because triggers like L2 and R2 need to be compatible.
                    // Ideally if a full range axis is used here, we'd turn the control into a half axis type control.
                    // However we'd need to make sure that you can rebind both halves of the axis independently without
                    // causing a conflict, which right now I don't think is how things work out.

                    // Despite the above information, we just decided to lock all Axis control changes for now

                    // Update: JP.2024-06-27 - The decision is to ensure that the users get some feedback as to why
                    // they are unable to use L2 and R2. We cancel the controller polling and give the Forbidden Pop up
                    // so they know why they can't assign triggers. This is ATRIO specific

                    // Update: JP.2024-09-23 - We want the user to be able to apply L2 and R2 to button controls.
                    if (!(_allowForControllerTriggers && (info.elementIdentifierId == LEFT_TRIGGER_ELEMENT_IDENTIFIER_ID
                                                          || info.elementIdentifierId == RIGHT_TRIGGER_ELEMENT_IDENTIFIER_ID)))
                    {
                        QuitRebindingImmediate();
                        _controlsSettings.ShowBindingForbiddenPopup(SelectControllerRebind);
                        return false;
                    }
                }
            }

            return true;
        }

        private void ControlSettings_OnConflictPopUpClosed(bool replaceBinding)
        {
            _pollingForKeypress = false;

            SelectControllerRebind();

            if (replaceBinding)
            {
                _conflictData.responseCallback(InputMapper.ConflictResponse.Replace);
            }
            else
            {
                _conflictData.responseCallback(InputMapper.ConflictResponse.Cancel);
            }

            _controlsSettings.RefreshControlUIState();
        }


        // ACCESSORS

        protected ControllerMap GetControllerMapForCategory(Controls.RewiredCategory category)
        {
            Controller actionController = GetActionController();
            Player player = ReInput.players.GetPlayer(Controls.DEFAULT_REWIRED_PLAYER_ID);
            ControllerMap controllerMap = null;

            if (_isKeyboardAndMouse)
            {
                // Find the first ActionElementMap that maps to this Action and is compatible with this field type
                for (int j = 0; j < 2; j++) {
                    // Search the Keyboard Map first, then the Mouse Map
                    ControllerType controllerType = j == 0 ? ControllerType.Keyboard : ControllerType.Mouse;
                    ControllerMap currentMap = player.controllers.maps.GetMap(controllerType, 0, category.ToString(), "Default");
                    foreach (var actionElementMap in currentMap.ElementMapsWithAction(_actionId)) {
                        if (actionElementMap.ShowInField(AxisRange.Full))
                        {
                            controllerMap = currentMap;
                            _device = GetInputDevice(controllerType);
                            break;
                        }
                    }
                    if (controllerMap != null) break; // found one
                }
            }

            // If action controller is null, grab the default template map
            if (actionController == null)
            {
                controllerMap = ReInput.mapping.GetJoystickMapInstance(DEFAULT_CONTROLLER_GUID, category.ToString(), "Default");
            }
            else
            {
                if(controllerMap == null)
                    controllerMap = player.controllers.maps.GetMap(actionController, category.ToString(), "Default");
            }

            return controllerMap;
        }

        protected Sprite GetGlyphForAction(ActionElementMap aem)
        {
            Sprite glyphSprite;

            switch (_device)
            {
                case Controls.InputDevice.Joystick:
                    glyphSprite = GetJoystickGlyph(aem);
                    break;
                case Controls.InputDevice.Mouse:
                    glyphSprite = GetMouseOrKeyboardGlyph(aem);
                    break;
                case Controls.InputDevice.Keyboard:
                default:
                    glyphSprite = _glyphs.GetKeyboardGlyph(aem.elementIdentifierId);
                    break;
            }

            return glyphSprite;
        }

        /// <summary>
        /// This retrieves the first ActionElementMap that has the same action name within the controller map given. Can
        /// give an axis direction if needed, but will default to positive as buttons are always marked as positive. If
        /// no action element map can be found, then null is returned.
        /// </summary>
        /// <param name="controllerMap">The controller map that contains the Action Element Map</param>
        /// <param name="actionName">The name of the action contained in the controller map</param>
        /// <param name="axisDirection">Pole direction for action names. Defaults to positive for buttons.</param>
        /// <returns> First action element map found in controller map with action name. Returns null if not found.</returns>
        protected ActionElementMap GetFirstElementMapFromControllerMap(ControllerMap controllerMap, string actionName, Pole axisDirection = Pole.Positive)
        {
            ActionElementMap[] aems = controllerMap.GetElementMapsWithAction(actionName);

            for (int i = 0; i < aems.Length; i++)
            {
                ActionElementMap aem = aems[i];

                if (aem.axisContribution == axisDirection)
                    return aem;
            }

            return null;
        }

#if REBINDING_LOGGING
        // Debug tool only
        // Had to build this logic to find the category name because the category IDs are different than in the editor, no idea why
        // Note : code probably fails if joystick is not plugged in, also it's not meant for keyboard
        private string GetRewiredCatName(int id)
        {
            var player = ReInput.players.GetPlayer(Controls.DEFAULT_REWIRED_PLAYER_ID);
            var actionController = player.controllers.GetController(ControllerType.Joystick, 0);
            var categories = Enum.GetNames(typeof(Controls.RewiredCategory));
            //var map = player.controllers.maps.GetMap(actionController, Controls.RewiredCategory.GameplayJoy.ToString(), "Default");
            foreach (string catName in categories)
            {
                var map = player.controllers.maps.GetMap(actionController, catName, "Default");
                if (map != null)
                {
                    //Debug.LogWarning($"Reminder: category {catName} exists with mapId={map.id}, catId={map.categoryId}, controllerType={map.controllerType}, containing {map.elementMapCount} elements.");
                    if (map.categoryId == id)
                        return catName;
                }
            }
            return "NOT FOUND";
        }
#endif

        /// <summary>
        /// Evaluates if the conflict given to us by Rewired is with an untouchable mapping.
        /// </summary>
        private bool IsDataProtected(bool isProtected, IList<ElementAssignmentConflictInfo> conflicts)
        {
            // You can configure Map Categories in Rewired Editor to be User Assignable or not, and if they are not, this
            // flag should be true. But we don't use that right now
            if (isProtected)
            {
#if REBINDING_LOGGING
                Debug.LogWarning("Conflict concerns a map category that is protected from user assignment in rewired settings.");
#endif
                return true;
            }

            foreach (ElementAssignmentConflictInfo conflict in conflicts)
            {
                // also if the conflict is on a restricted action from UI category, auto-cancel it (e.g. UIHorizontal, UIConfirm, UICancel)
                if (IsActionForbidden(conflict.action))
                {
#if REBINDING_LOGGING
                    Debug.LogWarning($"Conflict concerns action {conflict.elementDisplayName} from the Forbidden list hardcoded in UIControlRebindingBase");
#endif
                    return true;
                }

                // We generally won't hit this check because the actions
                if (IsAxisForbidden(conflict.action))
                {
#if REBINDING_LOGGING
                    Debug.LogWarning($"Conflict concerns axis {conflict.elementDisplayName} from the Forbidden list hardcoded in UIControlRebindingBase");
#endif
                    return true;
                }
            }

            return false;
        }

        private bool IsActionForbidden(InputAction action)
        {
            List<UserActions> forbiddenActions = IsRebindingForJoystick ? FORBIDDEN_ACTIONS_CONTROLLER : FORBIDDEN_ACTIONS_KEYBOARD;
            for (int i = 0; i < forbiddenActions.Count; i++)
            {
                int rewiredActionId = _controls.GetIDForAction(forbiddenActions[i]);
                if (action.id == rewiredActionId)
                    return true;
            }
            return false;
        }

        private bool IsAxisForbidden(InputAction action)
        {
            if (!IsRebindingForJoystick)
                return false;

            for (int i = 0; i < FORBIDDEN_AXES.Count; i++)
            {
                int rewiredAxisId = _controls.GetIDForAxis(FORBIDDEN_AXES[i]);
                if (action.id == rewiredAxisId)
                    return true;
            }

            return false;
        }

        // Some dev tools we've put in the rewired config to enable testing could cause conflicts, so ignore those
        private bool IsToBeIgnored(InputMapper.ConflictFoundEventData data)
        {
            List<ElementAssignmentConflictInfo> conflictsToIgnore = new List<ElementAssignmentConflictInfo>();

            foreach (ElementAssignmentConflictInfo conflict in data.conflicts)
            {
                if (conflict.actionId == _controls.GetIDForAction(UserActions.FORCEGC))
                {
                    conflictsToIgnore.Add(conflict);
                    continue;
                }

                if (conflict.keyCode == KeyCode.Semicolon && conflict.actionId == _controls.GetIDForAction(UserActions.TOGGLEDEVCONSOLE))
                {
                    conflictsToIgnore.Add(conflict);
                }
            }

            if (data.conflicts.Count == conflictsToIgnore.Count)
            {
                // Full ignore
                return true;
            }

            // Partial ignore
            // Try to remove the unwanted conflicts and tell the algorithm to go on, hope it will handle the remaining conflicts correctly
            for (int i = 0; i < conflictsToIgnore.Count; i++)
                data.conflicts.Remove(conflictsToIgnore[i]);

            return false;
        }

        private bool IsConflictInSameMap(InputMapper.ConflictFoundEventData data)
        {
            for (int i = 0; i < data.conflicts.Count; i++)
            {
                if (data.conflicts[i].controllerMap == _map)
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// This method is just a convenient way to initialize an ElementAssignmentConflictInfo.
        /// I didn't see if there was a good way to generate one of these, I think it's only meant to be created by Rewired.
        /// The conflict info we generate seems mostly accurate, but if is not guaranteed to be compatible if we were to try to
        /// inject it in Rewired logic. For now it is only meant to be used in our own logic, and for that purpose, it is good enough.
        /// </summary>
        private ElementAssignmentConflictInfo GetFakeConflictInfoFromMapping(ActionElementMap mapping)
        {
            ElementAssignmentConflictInfo info = new ElementAssignmentConflictInfo(
                                                        false,
                                                        true,
                                                        0,
                                                        ControllerType.Custom,
                                                        0,
                                                        mapping.controllerMap.id,
                                                        0,
                                                        mapping.actionId,
                                                        mapping.elementType,
                                                        mapping.elementIdentifierId,
                                                        mapping.keyCode,
                                                        mapping.modifierKeyFlags);
            return info;
        }

        private ElementAssignment GetAssignmentFromMapping(ActionElementMap mapping)
        {
            ElementAssignment assignment = new ElementAssignment();
            assignment.actionId = mapping.actionId;
            assignment.axisContribution = mapping.axisContribution;
            assignment.axisRange = mapping.axisRange;
            assignment.elementIdentifierId = mapping.elementIdentifierId;
            assignment.invert = mapping.invert;
            assignment.keyboardKey = mapping.keyCode;
            assignment.modifierKeyFlags = mapping.modifierKeyFlags;
            return assignment;
        }

        private Controller GetActionController()
        {
            var player = ReInput.players.GetPlayer(Controls.DEFAULT_REWIRED_PLAYER_ID);

            Controller actionController;

            switch (_device)
            {
                case Controls.InputDevice.Joystick:
                    actionController = player.controllers.GetController(ControllerType.Joystick, 0);
                    break;
                case Controls.InputDevice.Mouse:
                    actionController = player.controllers.GetController(ControllerType.Mouse, 0);

                    if (actionController == null)
                        actionController = player.controllers.GetController(ControllerType.Keyboard, 0);
                    break;
                case Controls.InputDevice.Keyboard:
                    actionController = player.controllers.GetController(ControllerType.Keyboard, 0);
                    break;
                default:
                    Debug.LogError("Unhandled case for get controller for remapping. Device:" + _device, this.gameObject);
                    actionController = player.controllers.Keyboard;
                    break;
            }

            return actionController;
        }

        private Sprite GetJoystickGlyph(ActionElementMap aem)
        {
            Controller actionController = GetActionController();

            Sprite sprite;

            // If a joystick is connected use that one for finding glpyhs, otherwise, just pull the default joystick gylphs
            if (actionController != null)
            {
                sprite = _glyphs.GetJoystickGlyph(actionController.hardwareTypeGuid, aem.elementIdentifierId, aem.axisRange);
            }
            else
            {
                sprite = _glyphs.GetJoystickGlyph(DEFAULT_CONTROLLER_GUID, aem.elementIdentifierId, aem.axisRange);
            }

            if (sprite == null)
            {
                sprite = _glyphs.GetDefaultControllerGlyph();
            }

            return sprite;
        }

        private Sprite GetMouseOrKeyboardGlyph(ActionElementMap aem)
        {
            Sprite sprite = _glyphs.GetMouseGlyph(aem.elementIdentifierId);

            if (sprite == null)
            {
                sprite = _glyphs.GetKeyboardGlyph(aem.elementIdentifierId);
            }

            return sprite;
        }


        // OTHER METHODS

        public abstract void RefreshDisplay();

        public void BindingButtonClicked()
        {

            // If no controller is plugged in, for controller remappers, the mapper will not countdown, and our UI will be stuck there...
            if (_controlsSettings.CurrentDevice == UISettingsControlsSubState.TargetInputDevice.Controller && !_controls.IsControllerAvailable)
            {
                _controlsSettings.ShowControllerRequiredPopup(SelectControllerRebind);
                return;
            }

            if (!_pollingForKeypress)
            {
                if (_isKeyboardAndMouse)
                {
                    Player player = ReInput.players.GetPlayer(Controls.DEFAULT_REWIRED_PLAYER_ID);
                    ControllerMap keyboardMap = player.controllers.maps.GetMap(ControllerType.Keyboard, 0, _category.ToString(), "Default");
                    ControllerMap mouseMap = player.controllers.maps.GetMap(ControllerType.Mouse, 0, _category.ToString(), "Default");

                    // Cannot replace a keyboard binding on a Mouse Map or vice versa
                    // Replacement cross device has to be done by removing the other
                    // binding manually after input is mapped.
                    // Determine which map the replacement binding exists on and store that information.
                    ControllerMap controllerMapWithReplacement = null;

                    // If there is no _aem, then the current setting has nothing registered
                    if (_aem != null)
                    {
                        // Determine if the replacement is on the keyboard or mouse map
                        if (keyboardMap.ContainsElementMap(_aem.id))
                        {
                            controllerMapWithReplacement = keyboardMap;
                        }
                        else if (mouseMap.ContainsElementMap(_aem.id))
                        {
                            controllerMapWithReplacement = mouseMap;
                        }

                        // Store the information about the replacement if any
                        _replaceTargetMapping = new TargetMapping {
                            actionElementMapId = _aem.id,
                            controllerMap = controllerMapWithReplacement
                        };
                    }

                    StartCoroutine(PollForKeyboardAndMousePress(keyboardMap, mouseMap));
                }
                else
                {
                    StartCoroutine(PollForKeyPress());
                }
            }
        }

        public void ClearRebindingCache()
        {
            _isInitialized = false;
            RefreshDisplay();
        }

        private IEnumerator PollForKeyPress()
        {
            _pollingForKeypress = true;

            _controlsSettings.ShowRebindingPopUp(ActionName);

            _conflictData = null;

            AxisRange axisRange = ActionAxisRange;

            InputMapper.Context context = new InputMapper.Context
            {
                actionId = _actionId,
                controllerMap = _map,
                actionRange = axisRange,
                actionElementMapToReplace = _aem
            };

            _cachedXML = _map.ToXmlString();

            SetupInputMapper(context);

            while (_pollingForKeypress)
            {
                // Using Input instead of Controls as in theory Rewired could have its inputs changed somehow
                if (Input.GetKeyDown(KeyCode.Escape))
                {
                    QuitRebindingImmediate();
                }

                _controlsSettings.SetRebindingTimeRemaining(_primaryInputMapper.timeRemaining);

                yield return null;
            }

            SelectControllerRebind();
        }

        private IEnumerator PollForKeyboardAndMousePress(ControllerMap keyboardMap, ControllerMap mouseMap)
        {
            _pollingForKeypress = true;

            _controlsSettings.ShowRebindingPopUp(ActionName);

            _conflictData = null;

            AxisRange axisRange = ActionAxisRange;

            InputMapper.Context keyboardContext = new InputMapper.Context
            {
                actionId = _actionId,
                controllerMap = keyboardMap,
                actionRange = axisRange,
                actionElementMapToReplace = _aem == null ? null : keyboardMap.GetElementMap(_aem.id)
            };

            InputMapper.Context mouseContext = new InputMapper.Context
            {
                actionId = _actionId,
                controllerMap = mouseMap,
                actionRange = axisRange,
                actionElementMapToReplace = _aem == null ? null : mouseMap.GetElementMap(_aem.id)
            };

            _cachedXML = _map.ToXmlString();

            SetupInputMappers(keyboardContext, mouseContext);

            while (_pollingForKeypress)
            {
                // Using Input instead of Controls as in theory Rewired could have its inputs changed somehow
                if (Input.GetKeyDown(KeyCode.Escape))
                {
                    QuitRebindingImmediate();
                }

                _controlsSettings.SetRebindingTimeRemaining(_primaryInputMapper.timeRemaining);

                yield return null;
            }

            SelectControllerRebind();
        }

        private void SetupInputMapper(InputMapper.Context context)
        {
            // If there is an input mapper already in place, we need to destroy it before creating a new one
            if (_primaryInputMapper != null)
            {
                TearDownInputMapper(_primaryInputMapper);
            }

            _primaryInputMapper = GetUpInputMapper();

            _primaryInputMapper.Start(context);
        }

        private void SetupInputMappers(InputMapper.Context keyboardContext, InputMapper.Context mouseContext)
        {
            // If there is an input mapper already in place, we need to destroy it before creating a new one
            if (_primaryInputMapper != null)
            {
                TearDownInputMapper(_primaryInputMapper);
            }

            // If there is an input mapper already in place, we need to destroy it before creating a new one
            if (_secondaryInputMapper != null)
            {
                TearDownInputMapper(_secondaryInputMapper);
            }

            _primaryInputMapper = GetUpInputMapper();
            _secondaryInputMapper = GetUpInputMapper();

            _primaryInputMapper.Start(keyboardContext);
            _secondaryInputMapper.Start(mouseContext);
        }

        private InputMapper GetUpInputMapper()
        {
            InputMapper inputMapper = new InputMapper();
            // Options when polling for input
            inputMapper.options.ignoreMouseXAxis = true;
            inputMapper.options.ignoreMouseYAxis = true;
            inputMapper.options.isElementAllowedCallback = InputMapper_IsElementValid;
            inputMapper.options.allowKeyboardModifierKeyAsPrimary = true;
            inputMapper.options.allowKeyboardKeysWithModifiers = false;

            inputMapper.options.timeout = 5;

            inputMapper.ConflictFoundEvent += InputMapper_OnConflictFound;
            inputMapper.InputMappedEvent += InputMapper_OnInputMapped;
            inputMapper.TimedOutEvent += InputMapper_OnTimedOut;

            return inputMapper;
        }

        private void TearDownInputMapper(InputMapper inputMapper)
        {
            inputMapper.ConflictFoundEvent -= InputMapper_OnConflictFound;
            inputMapper.InputMappedEvent -= InputMapper_OnInputMapped;
            inputMapper.TimedOutEvent -= InputMapper_OnTimedOut;

            inputMapper.Stop();
        }

        private void DeleteMapping(ActionElementMap mapping)
        {
#if REBINDING_LOGGING
            Debug.Log("Delete mapping for " + mapping.actionDescriptiveName);
#endif
            ControllerMap elementMap = GetControllerMapForCategory(_category);
            if (elementMap == null)
            {
#if REBINDING_LOGGING
                Debug.LogError($"could not find ControllerMap for category {_category}");
#endif
                return;
            }

            bool removeSuccess = elementMap.DeleteElementMap(mapping.id);
            int removedCount = removeSuccess ? 1 : 0;

#if REBINDING_LOGGING
            Debug.Log("removed " + removedCount + " elements!");
#endif
        }

        private void ApplyCachedControllerMap()
        {
            Player player = ReInput.players.GetPlayer(Controls.DEFAULT_REWIRED_PLAYER_ID);

            Controller temp = GetActionController();

            ControllerMap controllerMap = ControllerMap.CreateFromXml(temp.type, _cachedXML);
            player.controllers.maps.AddMap(temp, controllerMap);
        }

        private bool CanBeAppliedToAllPairedCategories(InputMapper.InputMappedEventData data)
        {
            return IsNewBindingValid(data.actionElementMap);
        }

        private bool IsNewBindingValid(ActionElementMap newBinding)
        {
            bool result = true;

            foreach (Controls.RewiredCategory pairedCat in _pairedCategories)
            {
#if REBINDING_LOGGING
                Debug.Log("Inspecting paired category " + pairedCat + " for " + InputActionName);
#endif
                if (pairedCat == _category)
                {
#if REBINDING_LOGGING
                    Debug.Log("Category skipped because it is our own category.");
#endif
                    continue;
                }

                ControllerMap pairedMap = GetControllerMapForCategory(pairedCat);
                if (pairedMap == null)
                    continue;

                // To get here we have validated that there is no conflict in the current map
                // But some of our controls need to be validated against more than one map
                // Maybe we should be checking this in the conflict callback and try to add extra conflicts in the list
                // For now just detect them here and try to use our IsDataProtected check to know if we want to abort
                List<ActionElementMap> conflictsInPairedMap = new List<ActionElementMap>();
                pairedMap.GetElementMapMatches(x => x.elementIdentifierName == newBinding.elementIdentifierName, conflictsInPairedMap);
                foreach (ActionElementMap conflict in conflictsInPairedMap)
                {
#if REBINDING_LOGGING
                    Debug.LogWarning($"Found conflicting element in paired map! keyCode={conflict.keyCode}, element={conflict.elementIdentifierName}, category={GetRewiredCatName(conflict.controllerMap.categoryId)}, action={conflict.actionDescriptiveName}");
#endif
                    if (IsDataProtected(false, new List<ElementAssignmentConflictInfo> { GetFakeConflictInfoFromMapping(conflict) }))
                    {
                        result = false;
                        return result;
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// Looks for conflicts across our paired categories because we only have ReWired check conflicts against the control's own map.
        /// Since this happens post user decision, we assume we should clear out those conflicts if found.
        /// If some conflicts are against protected data, then we fail with no changes applied to the controls.
        /// </summary>
        /// <returns>True if there are no conflicts left</returns>
        private bool TryClearConflictCollaterals(InputMapper.InputMappedEventData data)
        {
            bool result = true;

#if REBINDING_LOGGING
            Debug.Log("Checking for conflict collaterals.");
#endif
            if (_conflictData == null || _conflictData.conflicts.Count == 0)
            {
#if REBINDING_LOGGING
                Debug.Log("No conflicts found.");
#endif
                return result;
            }

            // Check all maps from all conflicts first before applying any changes in case we need to cancel operation
            //Note
            // When remapping button A onto UseSelectedMainItem (X) then the conflict data will be with Interact (A)
            // What I need to worry about is the conflicts from my paired maps, not this one
            // If this detected conflict was protected it would have been detected in OnConflictFound
            foreach (ElementAssignmentConflictInfo conflict in _conflictData.conflicts)
            {
#if REBINDING_LOGGING
                Debug.Log($"checking for collateral in conflict mapping: keyCode={conflict.keyCode}, element={conflict.elementDisplayName}, category={GetRewiredCatName(conflict.controllerMap.categoryId)}, action={conflict.action.name}");
#endif
                UIControlRebindingBase rebinder = _controlsSettings.GetControlRebinder(conflict.action.name, AxisDirection);
                List<Controls.RewiredCategory> collateralCategories = rebinder?._pairedCategories;
                if (collateralCategories != null && collateralCategories.Count > 0)
                {
                    foreach (Controls.RewiredCategory cat in collateralCategories)
                    {
#if REBINDING_LOGGING
                        Debug.Log("checking conflict's paired category: " + cat);
#endif
                        ControllerMap otherMap = GetControllerMapForCategory(cat);
                        if (otherMap == null)
                            continue; // No map, no problem - although I don't think this should happen. An empty map should still exist

                        string actionName = rebinder.InputActionName;
                        ActionElementMap otherAem = otherMap.GetFirstElementMapWithAction(actionName);

                        if (otherAem != null && IsDataProtected(false, new List<ElementAssignmentConflictInfo> { GetFakeConflictInfoFromMapping(otherAem) }))
                        {
#if REBINDING_LOGGING
                            Debug.Log($"Found that {actionName} is protected. Aborting the conflict clearing.");
#endif
                            result = false;
                            return result;
                        }
                    }
                }
            }

            if (_device != Controls.InputDevice.Keyboard)
            {

                foreach (ElementAssignmentConflictInfo conflict in _conflictData.conflicts)
                {
#if REBINDING_LOGGING
                Debug.Log($"checking for collateral in conflict mapping: keyCode={conflict.keyCode}, element={conflict.elementDisplayName}, category={GetRewiredCatName(conflict.controllerMap.categoryId)}, action={conflict.action.name}");
#endif
                    UIControlRebindingBase rebinder =
                        _controlsSettings.GetControlRebinder(conflict.action.name, AxisDirection);
                    List<Controls.RewiredCategory> collateralCategories = rebinder?._pairedCategories;
                    if (collateralCategories != null && collateralCategories.Count > 0)
                    {
                        foreach (Controls.RewiredCategory cat in collateralCategories)
                        {
#if REBINDING_LOGGING
                        Debug.Log($"checking conflict's paired category: " + cat.ToString());
#endif
                            ControllerMap otherMap = GetControllerMapForCategory(cat);
                            if (otherMap == null)
                                continue; // No map, no problem - although I don't think this should happen. An empty map should still exist

                            string actionName = rebinder.InputActionName;
                            ActionElementMap otherAem = otherMap.GetFirstElementMapWithAction(actionName);

                            if (otherAem == null)
                                continue;

                            // otherAem is paired with the action I conflicted on, so that is the one I want to clear here
                            // if there WAS any double conflict on that map, I don't think I would want to ripple into deleting them?
                            otherMap.DeleteElementMap(otherAem.id);
                        }
                    }
                }
            }

            return result;
        }

        private void ApplyToPairedCategories(InputMapper.InputMappedEventData data)
        {
            ActionElementMap newBinding = data.actionElementMap;

#if REBINDING_LOGGING
            Debug.Log($"Checking for paired categories to copy {newBinding.actionDescriptiveName} binding onto.");
            if (_pairedCategories == null || _pairedCategories.Count == 0)
                Debug.Log("No paired categories for " + InputActionName);
#endif

            foreach (Controls.RewiredCategory pairedCat in _pairedCategories)
            {
                if (pairedCat == _category)
                {
#if REBINDING_LOGGING
                    Debug.Log("Skipping over our own category, " + pairedCat + ", for " + InputActionName);
#endif
                    continue;
                }

#if REBINDING_LOGGING
                Debug.Log("Inspecting paired category " + pairedCat + " for " + InputActionName + " in pole direction " + AxisDirection);
#endif
                ControllerMap pairedMap = GetControllerMapForCategory(pairedCat);
                if (pairedMap == null)
                    continue;

                // To get here we have validated that there is no conflict in the current map
                // But some of our controls need to be validated against more than one map
                // Maybe we should be checking this in the conflict callback and try to add extra conflicts in the list
                // For now just detect them here and assume it's ok to override them
                List<ActionElementMap> conflictsInPairedMap = new List<ActionElementMap>();
                // Also we want to conflict with all elements that use the same input, not just mappings that match our action,
                // so we can't just use RemoveElementAssignmentConflicts or some such Rewired method
                pairedMap.GetElementMapMatches(x => x.elementIdentifierName == newBinding.elementIdentifierName, conflictsInPairedMap);

                if (conflictsInPairedMap.Count > 0)
                {
                    foreach (ActionElementMap conflict in conflictsInPairedMap)
                    {
#if REBINDING_LOGGING
                        Debug.LogWarning($"Found conflicting element in paired map! keyCode={conflict.keyCode}, element={conflict.elementIdentifierName}, category={GetRewiredCatName(conflict.controllerMap.categoryId)}, action={conflict.actionDescriptiveName}");
#endif
                        bool removeSuccess = pairedMap.DeleteElementMap(conflict.id);
#if REBINDING_LOGGING
                        int removedCount = removeSuccess ? 1 : 0;
                        Debug.Log("removed " + removedCount + " elements!");
#endif
                    }
                }

                ActionElementMap pairedBinding = GetFirstElementMapFromControllerMap(pairedMap, InputActionName, AxisDirection);

                if (pairedBinding == null)
                {
#if REBINDING_LOGGING
                    Debug.Log($"Compatible mapping not found. Adding new element binding for {newBinding.elementIdentifierName}");
#endif
                    // We're paired with this category so we should have a copy of our binding in that map at all times.
                    // If it's non-existent, we create it (it probably got removed as part of an earlier step in the rebinding process)
                    if (!pairedMap.CreateElementMap(GetAssignmentFromMapping(newBinding), out pairedBinding))
                        Debug.LogError($"Could not create new action element map for {InputActionName} in category {pairedCat}");
                }
                else
                {
                    if (_device == Controls.InputDevice.Keyboard)
                    {
#if REBINDING_LOGGING
                        Debug.Log($"found compatible mapping. replacing keyboard element {pairedBinding.keyCode}/{pairedBinding.keyboardKeyCode}");
#endif
                        pairedBinding.keyCode = newBinding.keyCode;
                        pairedBinding.keyboardKeyCode = newBinding.keyboardKeyCode;
                        pairedBinding.axisContribution = newBinding.axisContribution;
                    }
                    else
                    {
#if REBINDING_LOGGING
                        Debug.Log($"found compatible mapping. replacing joystick element {pairedBinding.elementIdentifierName} (id={pairedBinding.elementIdentifierId})");
#endif
                        pairedBinding.elementIdentifierId = newBinding.elementIdentifierId;
                    }
                }
            }
        }

        private void ShowConflictInformationPopUp(ElementAssignmentConflictInfo conflictInfo)
        {
            StopAllCoroutines();

            _controlsSettings.HideRebindingPopUp();
            _controlsSettings.ShowConflictPopUp(conflictInfo, ControlSettings_OnConflictPopUpClosed);
        }

        private void QuitRebindingImmediate()
        {
            _pollingForKeypress = false;
            TearDownInputMapper(_primaryInputMapper);
            if (_isKeyboardAndMouse)
            {
                TearDownInputMapper(_secondaryInputMapper);
            }

            _controlsSettings.HideRebindingPopUp();
        }

        private void SelectControllerRebind()
        {
            _controlsSettings.RefreshControlUIState();

            if (!Controls.UsingController)
                return;

            _rebindButton.Select();
        }
    }
}