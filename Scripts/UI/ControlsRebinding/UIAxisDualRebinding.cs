// Copyright Isto Inc.
using Isto.Core.Inputs;
using Rewired;
using UnityEngine;

namespace Isto.Core.UI
{
    /// <summary>
    /// Displays a control that maps to one axis.
    /// Meant to be used for the stick inputs from the joystick.
    /// </summary>
    public class UIAxisDualRebinding : UIControlRebindingBase
    {
        public override string InputActionName => _axis.ToString();
        public override bool IsAxis => true;

        [SerializeField] private Controls.MovementAxis _axis;

        public override void RefreshDisplay()
        {
            _actionId = ReInput.mapping.GetActionId(_axis.ToString());
            _map = GetControllerMapForCategory(_category);
            _aem = GetFirstElementMapFromControllerMap(_map, _axis.ToString());

            if (_aem != null)
            {
                Sprite actionSprite = GetGlyphForAction(_aem);

                if (actionSprite != null)
                {
                    _keyImage.sprite = actionSprite;
                    _keyImage.enabled = true;

                    _controlKeyTextbox.enabled = false;
                }
                else
                {
                    _keyImage.enabled = false;
                    _controlKeyTextbox.text = _aem.keyCode.ToString();
                    _controlKeyTextbox.enabled = true;
                }
            }
            // If null no mapping currently exists
            else
            {
                _keyImage.sprite = null;
                _controlKeyTextbox.text = "";
                _controlKeyTextbox.enabled = true;
            }

            if (_glyphOverride != null)
            {
                _keyImage.sprite = _glyphOverride;
            }

            if (!_isInitialized)
            {
                _cachedSprite = _keyImage.sprite;
                _isInitialized = true;
            }
            else
            {
                Button.SetDirtyFlag(_cachedSprite != _keyImage.sprite);
            }
        }
    }
}