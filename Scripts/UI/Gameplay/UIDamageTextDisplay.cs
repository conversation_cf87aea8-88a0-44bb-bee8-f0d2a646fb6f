// Copyright Isto Inc.
using Isto.Core.Beings;
using System.Collections;
using TMPro;
using UnityEngine;
using Zenject;

namespace Isto.Core.UI
{
    [RequireComponent(typeof(TextMeshProUGUI))]
    public class UIDamageTextDisplay : MonoBehaviour
    {
        [SerializeField]
        private AnimationCurve _alphaCurve = AnimationCurve.Constant(0, 1, 0.5f);
        [SerializeField]
        private AnimationCurve _movementCurve;
        [SerializeField]
        private float _height = 0f;
        [SerializeField]
        private float _displayTime = 2f;

        private TextMeshProUGUI _damageText;
        private UIDamageTextDisplay.Pool _pool;
        private Canvas _canvas;

        [Inject]
        public void Inject(UIDamageTextDisplay.Pool pool)
        {
            _pool = pool;
        }

        private void Awake()
        {
            _damageText = GetComponent<TextMeshProUGUI>();
            _damageText.alpha = 0f;
        }

        internal void DisplayDamage(float damage, Vector3 worldPosition, Health.DamageTypeEnum dmgType)
        {
            if (dmgType == null)
                dmgType = Health.DamageTypeEnum.PHYSICAL;

            _damageText.text = ((int)damage).ToString();
            _damageText.overrideColorTags = true;
            _damageText.color = GetColorForDamge(damage, dmgType);

            _canvas = GetComponentInParent<Canvas>();

            transform.position = UnityUtils.TransformWorldToCanvasPoint(worldPosition, _canvas);

            StartCoroutine(ShowText(worldPosition));
        }

        protected virtual Color GetColorForDamge(float damage, Health.DamageTypeEnum dmgType)
        {
            // Default color is red
            Color textColor = damage < 0f ? Color.red : Color.green;

            if (dmgType == Health.DamageTypeEnum.STUN)
            {
                textColor = damage < 0f ? Color.cyan : Color.green;
            }

            return textColor;
        }

        private IEnumerator ShowText(Vector3 worldPosition)
        {
            float timer = 0f;

            Vector3 startPosition = UnityUtils.TransformWorldToCanvasPoint(worldPosition, _canvas);

            while (timer < _displayTime)
            {
                timer += Time.deltaTime;

                _damageText.alpha = _alphaCurve.Evaluate(timer / _displayTime);

                // Update world position to account for screen moving
                startPosition = UnityUtils.TransformWorldToCanvasPoint(worldPosition, _canvas);

                // Move text upwards
                transform.position = Vector3.Lerp(startPosition, startPosition + Vector3.up * _height, timer / _displayTime);

                yield return null;
            }

            _damageText.alpha = 0f;

            _pool.Despawn(this);
        }

        public class Pool : MonoMemoryPool<float, Vector3, Health.DamageTypeEnum, UIDamageTextDisplay>
        {
            protected override void Reinitialize(float damage, Vector3 worldPosition, Health.DamageTypeEnum dmgType, UIDamageTextDisplay item)
            {
                item.DisplayDamage(damage, worldPosition, dmgType);
            }
        }
    }
}
