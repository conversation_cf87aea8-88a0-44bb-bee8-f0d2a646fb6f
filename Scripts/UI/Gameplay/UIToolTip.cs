// Copyright Isto Inc.
using Isto.Core.Localization;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    /// <summary>
    /// In game - when you mouse over something, it pops up some text
    /// </summary>
	public class UIToolTip : MonoBehaviour
    {
        // Public Variables

        public VerticalLayoutGroup vertGroup;
        public TextMeshProUGUI nameText;

        public Canvas Canvas { get { return _parentCanvas; } }

        // Private Variables

        private Canvas _parentCanvas;
        private UIToolTipRow.Pool _rowPool;
        private List<UIToolTipRow> _currentRows;

        // Methods		

        [Inject]
        public void Construct(UIToolTipRow.Pool pool)
        {
            _rowPool = pool;

            _parentCanvas = transform.GetComponentInParent<Canvas>();
        }

        private void Awake()
        {
            _currentRows = new List<UIToolTipRow>();
        }

        public void AddRow(Sprite icon, string toolTip, Sprite secondaryIcon = null)
        {
            UIToolTipRow newRow = _rowPool.Spawn(icon, toolTip, secondaryIcon);
            newRow.transform.SetParent(vertGroup.transform, true);

            _currentRows.Add(newRow);
        }

        public void DespawnRows()
        {
            int rowCount = _currentRows.Count;

            for (int i = rowCount; i > 0; i--)
            {
                _rowPool.Despawn(_currentRows[i - 1]);
            }

            _currentRows.Clear();
        }

        public class Pool : MonoMemoryPool<string, UIToolTip>
        {
            protected override void Reinitialize(string itemName, UIToolTip item)
            {
                if (itemName == null)
                {
                    Debug.LogError($"Attempting to Reinitialize UIToolTip with a null itemName, is there still a localization issue?");
                    return;
                }

                Loc.SetTMProLocalized(item.nameText, itemName.ToUpper());
            }

            protected override void OnDespawned(UIToolTip item)
            {
                item.DespawnRows();

                base.OnDespawned(item);
            }
        }
    }


}