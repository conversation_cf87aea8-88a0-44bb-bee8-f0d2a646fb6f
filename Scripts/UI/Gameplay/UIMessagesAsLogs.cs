// Copyright Isto Inc.
using Isto.Core.Automation;
using Isto.Core.UI;
using UnityEngine;

/// <summary>
/// Normally the IUIMessages and IUIDisplayMessage implementations are expected to be on a monobehavior but that should
/// not be a problem in theory.
/// We'll keep it to a simple class in this case because we only want a dummy with logging.
/// </summary>
public class UIMessagesAsLogs : IUIMessages, IUIDisplayMessage
{
    public void CreateHighPriorityMsg(string localizedMessage, HighPriorityMessage.MessageType msgType, float displayTime = 0)
    {
        Debug.Log($"UI HP Message: [{msgType}] {localizedMessage}");
    }

    public void CreateMessage(string localizedMessage, Sprite icon = null, float displayTime = 0)
    {
        Debug.Log($"UI Message: {localizedMessage}");
    }

    public void CreateProgressMessage(string message, CraftingQueue queue, Sprite sprite = null)
    {
        Debug.Log($"UI Queue Message: {message}");
    }

    public void DisplayMessage(string message, float time)
    {
        throw new System.NotImplementedException();
    }

    public void SetCraftingMessageLocations(Vector3 craftingQueueLocation, Vector3 craftingMessagesLocation)
    {
    }
}
