// Copyright Isto Inc.

using Isto.Core.Enums;

namespace Isto.Core.UI
{
    public class MenusEnum : Int32Enum<MenusEnum>
    {
        // Note: can't really enter this state via the editor button right now, it's just not one of the handled
        // enum values, probably because it's representing the absence of an open menu rather than a menu per se.
        // We could add support for it, it's not the most intuitive use of OpenMenu but it makes sense in the context
        // of using the editor button to test the menus (especially since the editor does not have a close option AFAIK)
        public static readonly MenusEnum CLOSED = new MenusEnum((int)GameMenusEnum.GameMenus.Closed, nameof(CLOSED));

        // Newly introduced enum names have no pre-associated value so they can auto-generate
        // Note that these particular examples of enum values can't be introduced right now because their associated
        // UI states use an alternate flow to get in an out of the state machine.
        //public static readonly MenusEnum ERROR_DIALOG = new MenusEnum(nameof(ERROR_DIALOG));
        //public static readonly MenusEnum MODAL_CHOICE_DIALOG = new MenusEnum(nameof(MODAL_CHOICE_DIALOG));

        public MenusEnum(string name) : base(name)
        {

        }

        public MenusEnum(int value, string name) : base(value, name)
        {

        }
    }
}