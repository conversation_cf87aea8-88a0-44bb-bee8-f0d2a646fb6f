// Copyright Isto Inc.
using Isto.Core.Game;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    public class LoadingProgressIndicator : MonoBehaviour
    {
        [SerializeField] private Image _progressFilling;
        [SerializeField] private Image _progressCompleteIcon;
        [SerializeField] private TextMeshProUGUI _progressStepInfo;


        private bool _callbacksRegistered = false;


        // Injection

        private GameState _gameState;

        [Inject]
        public void Inject(GameState gameState)
        {
            _gameState = gameState;
        }


        // Lifecycle

        private void Awake()
        {
            if (_progressFilling != null)
            {
                _progressFilling.fillAmount = 0f;
            }

            if (_progressCompleteIcon != null && _progressCompleteIcon.gameObject.activeSelf)
            {
                _progressCompleteIcon.gameObject.SetActive(false);
            }

            if (_progressStepInfo != null)
            {
                _progressStepInfo.text = "";
            }

            RegisterCallbacks();
        }

        private void OnDestroy()
        {
            UnregisterCallbacks();
        }

        private void Update()
        {
            if (_progressFilling != null)
            {
                _progressFilling.fillAmount = GameState.LoadProgress;
            }

            if (_progressCompleteIcon != null)
            {
                if (!_progressCompleteIcon.gameObject.activeSelf && GameState.LoadProgress >= 1f)
                {
                    _progressCompleteIcon.gameObject.SetActive(true);
                }
            }

            if (_progressStepInfo != null && _progressStepInfo.text != GameState.LoadingStep)
            {
                _progressStepInfo.text = GameState.LoadingStep;
            }
        }


        // Callbacks

        private void RegisterCallbacks()
        {
            if (_callbacksRegistered)
                return;

            Events.Subscribe(Events.GAME_START_FADE_IN, OnLoadComplete);

            _callbacksRegistered = true;
        }

        private void UnregisterCallbacks()
        {
            if (!_callbacksRegistered)
                return;

            Events.UnSubscribe(Events.GAME_START_FADE_IN, OnLoadComplete);

            _callbacksRegistered = false;
        }

        private void OnLoadComplete()
        {
            if (this == null || this.gameObject == null)
                return;

            UnregisterCallbacks();

            // once the scene is loaded I don't think we want to show the loading thing anymore, until we actually load a new scene instance
            //this.gameObject.SetActive(false);
        }
    }
}