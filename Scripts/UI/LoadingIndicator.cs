// Copyright Isto Inc.
using Isto.Core.Game;
using System;
using UnityEngine;
using Zenject;

namespace Isto.Core.UI
{
    public class LoadingIndicator : MonoBehaviour
    {

        private bool _callbacksRegistered = false;

        // Injected
        private GameState _gameState;

        [Inject]
        public void Inject(GameState gameState)
        {
            _gameState = gameState;
        }

        private void Awake()
        {
            RegisterCallbacks();
        }

        private void OnDestroy()
        {
            UnregisterCallbacks();
        }

        private void RegisterCallbacks()
        {
            if (_callbacksRegistered)
                return;

            //_gameState.LoadComplete += OnLoadComplete;
            Events.Subscribe(Events.GAME_START_FADE_IN, OnLoadComplete);

            _callbacksRegistered = true;
        }

        private void UnregisterCallbacks()
        {
            if (!_callbacksRegistered)
                return;

            //_gameState.LoadComplete -= OnLoadComplete;
            Events.UnSubscribe(Events.GAME_START_FADE_IN, OnLoadComplete);

            _callbacksRegistered = false;
        }

        private void OnLoadComplete(object sender, EventArgs e)
        {
            OnLoadComplete();
        }

        private void OnLoadComplete()
        {
            if (this == null || this.gameObject == null)
                return;

            UnregisterCallbacks();

            // once the scene is loaded I don't think we want to show the loading thing anymore, until we actually load a new scene instance
            this.gameObject.SetActive(false);
        }
    }
}