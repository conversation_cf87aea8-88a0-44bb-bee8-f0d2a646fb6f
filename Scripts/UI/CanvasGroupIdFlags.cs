// Copyright Isto Inc.

using Isto.Core.Enums;
using System;

namespace Isto.Core.UI
{
    public class CanvasGroupIdFlags : Int32Flags<CanvasGroupIdFlags>
    {
        // The values of these flags have been inherited from Atrio
        public static readonly CanvasGroupIdFlags NONE = new CanvasGroupIdFlags(BIT_0, nameof(NONE));
        public static readonly CanvasGroupIdFlags SCENE_BLACKOUT = new CanvasGroupIdFlags(BIT_1, nameof(SCENE_BLACKOUT));
        public static readonly CanvasGroupIdFlags UI_GAMEPLAY = new CanvasGroupIdFlags(BIT_6, nameof(UI_GAMEPLAY));
        public static readonly CanvasGroupIdFlags UI_GAMEPLAY_INDEPENDENT = new CanvasGroupIdFlags(BIT_11, nameof(UI_GAMEPLAY_INDEPENDENT));

        public static readonly CanvasGroupIdFlags UI_EQUIP_MENU = new CanvasGroupIdFlags(BIT_16, nameof(UI_EQUIP_MENU));
        //This is a subcanvasgroup of UI_EQUIP_MENU
        public static readonly CanvasGroupIdFlags UI_EQUIP_MENU_ITEMS = new CanvasGroupIdFlags(BIT_17, nameof(UI_EQUIP_MENU_ITEMS));

        public static readonly CanvasGroupIdFlags ALL = new CanvasGroupIdFlags(BIT_ALL, nameof(ALL));


        // Constructor for testing flags at runtime
        public CanvasGroupIdFlags() : base()
        {
        }

        // Constructor for defining new flags
        public CanvasGroupIdFlags(UInt32 value, string name) : base(value, name)
        {
        }
    }
}