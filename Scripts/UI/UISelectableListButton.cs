// Copyright Isto Inc.
using Isto.Core.Audio;
using Isto.Core.Inputs;
using Isto.Core.UI;
using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core
{
    /// <summary>
    /// Used in save slot list.
    /// </summary>
    public class UISelectableListButton : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerDownHandler, IPointerUpHandler, IPointerEnterHandler, IPointerExitHandler, ISelectHandler, IDeselectHandler, ISubmitHandler
    {
        public event Action<UISelectableListButton> OnSelected;

        public RectTransform RectTransform => this.transform as RectTransform;
        public Button Button => GetComponent<Button>();
        public bool IsSelected => _selected;

        [Header("UI Hookup")]

        [SerializeField] protected Image background;
        [SerializeField] protected Image outline;
        [SerializeField] protected TextMeshProUGUI buttonDescription;
        [SerializeField] protected TextMeshProUGUI buttonMode;
        [SerializeField] protected bool disabled;

        private Color notSelectedBackgroundColor = Constants.ATRIO_DISABLED_GREY1;
        private Color highlightedBackgroundColor = Constants.ATRIO_PINK_LIGHT;
        private Color highlightedTextColor = Constants.ATRIO_DARK_PINK;
        private Color selectedBackgroundColor = Constants.ATRIO_TITLESCREEN_YELLOW;
        private Color selectedTextColor = Constants.ATRIO_DISABLED_GREY1;
        private Color pointerEnterSelectedTextColor = Color.black;
        private Color pointerEnterTextColor = Constants.ATRIO_TITLESCREEN_YELLOW;
        private Color pointerExitTextColor = Constants.ATRIO_LIGHT_GREY_TEXT;

        protected float selectedBackgroundAlpha = 1f;
        protected float notSelectedBackgroundAlpha = 0f; // This is for when you've selected a disabled button?
        protected Animator _anim;
        protected UISounds _uiSounds;
        private bool _selected = false;
        private HashSet<string> _animatorParams = new HashSet<string>();

        [Inject]
        public void Inject(UISounds uISounds)
        {
            _uiSounds = uISounds;
        }

        public void Awake()
        {
            _anim = GetComponent<Animator>();

            if (_anim != null)
            {
                for (int i = 0; i < _anim.parameterCount; i++)
                {
                    _animatorParams.Add(_anim.parameters[i].name);
                }
            }
        }

        public Color SetColorAlpha(Color inputColor, float alpha)
        {
            return new Color(inputColor.r, inputColor.g, inputColor.b, alpha);
        }

        public void SetText(string description, string mode)
        {
            //If it's, just use the default text. Don't be overwriting it. That's just annoying
            if (description != "")
                buttonDescription.text = description;

            if (mode != "")
                this.buttonMode.text = mode;
        }

        // This is a selection helper for when using the controller. We highlight first, then select.
        public void Highlighted()
        {
            if (_anim != null && _animatorParams.Contains("highlighted"))
                _anim.SetTrigger("highlighted");

            if (buttonDescription != null)
                buttonDescription.color = highlightedTextColor;
            if (buttonMode != null)
                buttonMode.color = highlightedTextColor;
            if (background != null)
                background.color = SetColorAlpha(highlightedBackgroundColor, selectedBackgroundAlpha);

            CoreUISaveSlotList list = this.GetComponentInParent<CoreUISaveSlotList>();
            list.RectTransform.EnsureChildIsVisibleVertically(this.RectTransform, UIScrollListSelectableItem.ScrollListAdjustmentDriver.SiblingIndexDriven);
        }

        public void NotHighlighted()
        {
            if (_selected)
                Selected();
            else
                NotSelected();
        }

        // This is a mouse click or controller face button press to select this list item
        // Note that going through the highlighted mode first is optional (this is how it's defined in the animator)
        public virtual void Selected()
        {
            _selected = true;

            if (buttonDescription != null)
                buttonDescription.color = selectedTextColor;
            if (buttonMode != null)
                buttonMode.color = selectedTextColor;
            if (background != null)
                background.color = SetColorAlpha(selectedBackgroundColor, selectedBackgroundAlpha);
        }

        public virtual void NotSelected()
        {
            _selected = false;

            if (_anim != null)
            {
                _anim.SetBool("down", false); // Just in case you're doing something wacky
                _anim.SetTrigger("deselected");
            }

            if (buttonDescription != null)
                buttonDescription.color = pointerExitTextColor;
            if (buttonMode != null)
                buttonMode.color = pointerExitTextColor;
            if (background != null)
                background.color = SetColorAlpha(notSelectedBackgroundColor, notSelectedBackgroundAlpha);
        }

        public void LinkForNavigation(UISelectableListButton nextElement)
        {
            Selectable upper = Button;
            Selectable lower = nextElement.Button;
            UIUtils.SetNavigation(upper, UIUtils.NavigationDirection.Down, lower);
            UIUtils.SetNavigation(lower, UIUtils.NavigationDirection.Up, upper);
        }

        // UI events

        void IPointerDownHandler.OnPointerDown(PointerEventData eventData)
        {
#if BUTTON_LOGGING
            Debug.Log($"{gameObject.name} OnPointerDown", this.gameObject);
#endif

            if (disabled)
                return;

            EngageButton();
        }

        void IPointerUpHandler.OnPointerUp(PointerEventData eventData)
        {
#if BUTTON_LOGGING
            Debug.Log($"{gameObject.name} OnPointerUp", this.gameObject);
#endif

            if (disabled)
                return;

            ReleaseButton();
        }

        void IPointerExitHandler.OnPointerExit(PointerEventData eventData)
        {
            if (disabled)
                return;

            if (_selected)
            {
                if (buttonDescription != null)
                    buttonDescription.color = selectedTextColor;
                if (buttonMode != null)
                    buttonMode.color = selectedTextColor;
            }
            else
            {
                if (buttonDescription != null)
                    buttonDescription.color = pointerExitTextColor;
                if (buttonMode != null)
                    buttonMode.color = pointerExitTextColor;
            }
        }

        void IPointerEnterHandler.OnPointerEnter(PointerEventData eventData)
        {
            if (disabled)
                return;

            if (_selected)
            {
                if (buttonDescription != null)
                    buttonDescription.color = pointerEnterSelectedTextColor;
                if (buttonMode != null)
                    buttonMode.color = pointerEnterSelectedTextColor;
            }
            else
            {
                if (buttonDescription != null)
                    buttonDescription.color = pointerEnterTextColor;
                if (buttonMode != null)
                    buttonMode.color = pointerEnterTextColor;
            }
        }

        // This is called when controller highlights the list button, pre-submit
        void ISelectHandler.OnSelect(BaseEventData eventData)
        {
#if BUTTON_LOGGING
            Debug.Log($"{gameObject.name} OnSelect", this.gameObject);
#endif

            Highlighted();
        }

        void IDeselectHandler.OnDeselect(BaseEventData eventData)
        {
#if BUTTON_LOGGING
            Debug.Log($"{gameObject.name} OnDeselect", this.gameObject);
#endif

            NotHighlighted();
        }

        // This is called when using controller for input
        void ISubmitHandler.OnSubmit(BaseEventData eventData)
        {
#if BUTTON_LOGGING
            Debug.Log($"{gameObject.name} OnSubmit", this.gameObject);
#endif

            ReleaseButton();
        }

        // Hooked up to OnClick event from Button component on our gameobject
        public void ButtonClicked()
        {
#if BUTTON_LOGGING
            Debug.Log($"{gameObject.name} Button_OnClick", this.gameObject);
#endif

            _uiSounds.PlayButtonClickSound();

            Selected();

            OnSelected.Invoke(this);
        }

        // Button reacts visually to click action - goes down
        private void EngageButton()
        {
            if (_anim != null)
            {
                _anim.SetBool("down", true);
            }
        }

        // Button reacts visually to click action - comes back up
        private void ReleaseButton()
        {
            if (!Controls.UsingController)
            {
                // Normally a selectable you click stays *unity* selected until you click another one.
                // We want to avoid that because we don't want it to LOOK selected (pink highlight).
                // Otherwise it's weird with the one selectable from the list that is on display (yellow highlight).
                // In particular, you can drag the list, which won't cause a click and should not cause a selection.
                // (Note that we need those separate highlights for the controller, but the mouse doesn't need it.)
                EventSystem.current.SetSelectedGameObject(null);
            }

            if (_anim != null)
            {
                _anim.SetBool("down", false);
            }
        }

        /// <summary>
        /// Much like a toggle group, when another selectable from our list receives the selection, we deselect
        /// </summary>
        /// <param name="button">The new selection</param>
        public void SelectableListButtonClicked(UISelectableListButton button)
        {
            if (button != this)
            {
                NotSelected();
            }
        }

        [ContextMenu("ForceSelect")]
        private void ForceSelection()
        {
            this.gameObject.GetComponent<Button>().Select();
        }
    }
}
