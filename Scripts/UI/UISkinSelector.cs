// Copyright Isto Inc.
using Isto.Core.Inputs;
using Isto.Core.Skins;
using Isto.Core.Themes;
using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using Zenject;

namespace Isto.Core.UI
{
    public class UISkinSelector : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerDownHandler, IPointerUpHandler
    {
        // Publics

        [Header("Assignations")]
        [SerializeField] private TMP_Dropdown _themeDropdown;
        [Space]
        [SerializeField] private TMP_Dropdown _playerSkinSetDropdown;
        [SerializeField] private TMP_Dropdown _playerHatDropdown;
        [SerializeField] private TMP_Dropdown _playerHeadDropdown;
        [SerializeField] private TMP_Dropdown _playerTorsoDropdown;
        [Space]
        [SerializeField] private TMP_Dropdown _miniDeerDropdown;
        [SerializeField] private TMP_Dropdown _pushbackDropdown;
        [Space]
        [SerializeField] private TMP_Dropdown _advancedItemsDropdown;
        [Space]
        [SerializeField] private CoreButtonController _applyUIButton;
        [SerializeField] private UnityEngine.UI.Button _applyButton;

        // Privates

        private bool _dragging;
        private Vector2 _previousMousePos;

        // Injected References

        private IControls _controls;
        private ThemeSetup _themeSetup;
        private MasterSkinList _masterSkinList;
        private ThemeManager _themeManager;

        [Inject]
        public void Inject(IControls controls, ThemeSetup themeSetup, MasterSkinList masterSkinList, ThemeManager themeManager)
        {
            _controls = controls;
            _themeSetup = themeSetup;
            _masterSkinList = masterSkinList;
            _themeManager = themeManager;
        }

        private void OnEnable()
        {
            Initialize();
            PopulateDropdowns();
            RegisterEvents();
        }

        private void OnDisable()
        {
            UnregisterEvents();
        }

        private void Update()
        {
            // Allowing to drag the window around in case it's blocking the view of the assets you want to look at.
            // A bit janky but does the job.
            if (_dragging)
            {
                Vector2 delta = _controls.GetPointerPosition() - _previousMousePos;
                _previousMousePos = _controls.GetPointerPosition();
                RectTransform rect = this.transform as RectTransform;
                rect.anchoredPosition += new Vector2(delta.x, delta.y);
            }
        }

        private void Initialize()
        {
            DisableUnusedControls();
        }

        private void DisableUnusedControls()
        {
            _playerHeadDropdown.gameObject.SetActive(false);
            _pushbackDropdown.gameObject.SetActive(false);
            _applyButton.gameObject.SetActive(false);
        }

        private void PopulateDropdowns()
        {
            IEnumerable<string> themeNames = _themeSetup.themes.Select(x => x.internalName);
            PopulateDropdown(_themeDropdown, themeNames);
            OnThemeChanged();

            IEnumerable<string> pSkinSets = _masterSkinList.playerSets.Select(x => x.skinSetInternalName);
            PopulateDropdown(_playerSkinSetDropdown, pSkinSets);
            OnPlayerSkinSetChanged();

            IEnumerable<string> pHatSkins = _masterSkinList.playerParts.Where(x => x.type == PlayerSkinPart.SkinPartType.Hat).Select(x => x.skinPartInternalName);
            PopulateDropdown(_playerHatDropdown, pHatSkins);
            OnPlayerHatChanged();

            IEnumerable<string> pBodySkins = _masterSkinList.playerParts.Where(x => x.type == PlayerSkinPart.SkinPartType.Body).Select(x => x.skinPartInternalName);
            PopulateDropdown(_playerTorsoDropdown, pBodySkins);
            OnPlayerBodyChanged();

            IEnumerable<string> deerSkins = _masterSkinList.creatureSets.Where(x => x.type == Identifier.IdentifierFlags.MiniDeer).Select(x => x.skinSetInternalName);
            PopulateDropdown(_miniDeerDropdown, deerSkins);
            OnMiniDeerSkinChanged();

            IEnumerable<string> pushbackSkins = _masterSkinList.creatureSets.Where(x => Identifier.IdentifierFlags.AllPushbacks.HasFlag(x.type)).Select(x => x.skinSetInternalName);
            PopulateDropdown(_pushbackDropdown, pushbackSkins);
            OnPushbackSkinChanged();

            IEnumerable<string> advancedItemsSkins = _masterSkinList.itemGroups.Select(x => x.skinGroupInternalName);
            PopulateDropdown(_advancedItemsDropdown, advancedItemsSkins);
            OnAdvancedItemsSkinChanged();

            _playerHeadDropdown.ClearOptions();
        }

        private void PopulateDropdown(TMP_Dropdown dropdown, IEnumerable<string> options)
        {
            dropdown.ClearOptions();
            List<TMP_Dropdown.OptionData> optionData = new List<TMP_Dropdown.OptionData>();
            foreach (string optionName in options)
            {
                if (optionName != null)
                    optionData.Add(new TMP_Dropdown.OptionData(optionName));
            }
            dropdown.AddOptions(optionData);
        }

        private void RegisterEvents()
        {
            Events.Subscribe(Events.THEME_CHANGED, OnThemeChanged);
            Events.Subscribe(Events.PLAYER_SKIN_SET_CHANGED, OnPlayerSkinSetChanged);
            Events.Subscribe(Events.PLAYER_SKIN_HAT_CHANGED, OnPlayerHatChanged);
            Events.Subscribe(Events.PLAYER_SKIN_BODY_CHANGED, OnPlayerBodyChanged);
            Events.Subscribe(Events.MINIDEER_SKIN_CHANGED, OnMiniDeerSkinChanged);
            //Events.Subscribe(Events.PUSHBACK_SKIN_CHANGED, OnPushbackSkinChanged);
            Events.Subscribe(Events.ADVANCED_ITEMS_SKIN_CHANGED, OnAdvancedItemsSkinChanged);

            _themeDropdown.onValueChanged.AddListener(OnThemeSelectionChanged);
            _playerSkinSetDropdown.onValueChanged.AddListener(OnPlayerSkinSetSelectionChanged);
            _playerHatDropdown.onValueChanged.AddListener(OnPlayerHatSelectionChanged);
            _playerTorsoDropdown.onValueChanged.AddListener(OnPlayerBodySelectionChanged);
            _miniDeerDropdown.onValueChanged.AddListener(OnMiniDeerSkinSelectionChanged);
            //_pushbackDropdown.onValueChanged.AddListener(OnPushbackSkinSelectionChanged);
            _advancedItemsDropdown.onValueChanged.AddListener(OnAdvancedItemsSkinSelectionChanged);
        }

        private void UnregisterEvents()
        {
            Events.UnSubscribe(Events.THEME_CHANGED, OnThemeChanged);
            Events.UnSubscribe(Events.PLAYER_SKIN_SET_CHANGED, OnPlayerSkinSetChanged);
            Events.UnSubscribe(Events.PLAYER_SKIN_HAT_CHANGED, OnPlayerHatChanged);
            Events.UnSubscribe(Events.PLAYER_SKIN_BODY_CHANGED, OnPlayerBodyChanged);
            Events.UnSubscribe(Events.MINIDEER_SKIN_CHANGED, OnMiniDeerSkinChanged);
            //Events.UnSubscribe(Events.PUSHBACK_SKIN_CHANGED, OnPushbackSkinChanged);
            Events.UnSubscribe(Events.ADVANCED_ITEMS_SKIN_CHANGED, OnAdvancedItemsSkinChanged);

            _themeDropdown.onValueChanged.RemoveListener(OnThemeSelectionChanged);
            _playerSkinSetDropdown.onValueChanged.RemoveListener(OnPlayerSkinSetSelectionChanged);
            _playerHatDropdown.onValueChanged.RemoveListener(OnPlayerHatSelectionChanged);
            _playerTorsoDropdown.onValueChanged.RemoveListener(OnPlayerBodySelectionChanged);
            _miniDeerDropdown.onValueChanged.RemoveListener(OnMiniDeerSkinSelectionChanged);
            //_pushbackDropdown.onValueChanged.RemoveListener(OnPushbackSkinSelectionChanged);
            _advancedItemsDropdown.onValueChanged.RemoveListener(OnAdvancedItemsSkinSelectionChanged);
        }

        private int GetOptionIndex(string optionName, TMP_Dropdown dropdown)
        {
            int index = -1;

            for (int i = 0; i < dropdown.options.Count; i++)
            {
                if (dropdown.options[i].text == optionName)
                {
                    index = i;
                    break;
                }
            }

            if (index < 0)
                Debug.LogError($"UISkinSelector: Option \"{optionName}\" not found in dropdown {dropdown.transform.parent.name}", dropdown);

            return index;
        }

        private void OnThemeChanged()
        {
            int index = GetOptionIndex(_themeManager.CurrentTheme.internalName, _themeDropdown);
            _themeDropdown.SetValueWithoutNotify(index);
        }

        private void OnThemeSelectionChanged(int selected)
        {
            string themeName = _themeDropdown.options[selected].text;
            ThemeDefinition themeDef = _themeSetup.GetTheme(themeName);
            _themeManager.CurrentTheme = themeDef;
        }

        private void OnPlayerSkinSetChanged()
        {
            string currentSkinName = _themeManager.CurrentPlayerSet.skinSetInternalName;
            _playerSkinSetDropdown.SetValueWithoutNotify(GetOptionIndex(currentSkinName, _playerSkinSetDropdown));
        }

        private void OnPlayerSkinSetSelectionChanged(int selected)
        {
            string skinName = _playerSkinSetDropdown.options[selected].text;
            _themeManager.CurrentPlayerSet = _masterSkinList.GetPlayerSkinSet(skinName);
        }

        private void OnPlayerHatChanged()
        {
            _playerHatDropdown.SetValueWithoutNotify(GetOptionIndex(_themeManager.CurrentPlayerHat.skinPartInternalName, _playerHatDropdown));
        }

        private void OnPlayerHatSelectionChanged(int selected)
        {
            string skinName = _playerHatDropdown.options[selected].text;
            _themeManager.CurrentPlayerHat = _masterSkinList.GetPlayerHatSkin(skinName);
        }

        private void OnPlayerBodyChanged()
        {
            _playerTorsoDropdown.SetValueWithoutNotify(GetOptionIndex(_themeManager.CurrentPlayerBody.skinPartInternalName, _playerTorsoDropdown));
        }

        private void OnPlayerBodySelectionChanged(int selected)
        {
            string skinName = _playerTorsoDropdown.options[selected].text;
            _themeManager.CurrentPlayerBody = _masterSkinList.GetPlayerBodySkin(skinName);
        }

        private void OnMiniDeerSkinChanged()
        {
            _miniDeerDropdown.SetValueWithoutNotify(GetOptionIndex(_themeManager.CurrentMiniDeer.skinSetInternalName, _miniDeerDropdown));
        }

        private void OnMiniDeerSkinSelectionChanged(int selected)
        {
            string skinInternalName = _miniDeerDropdown.options[selected].text;
            _themeManager.CurrentMiniDeer = _masterSkinList.GetMiniDeerSkin(skinInternalName);
        }

        private void OnPushbackSkinChanged()
        {
            _pushbackDropdown.SetValueWithoutNotify(GetOptionIndex(_themeManager.CurrentPushback.skinSetInternalName, _pushbackDropdown));
        }

        private void OnPushbackSkinSelectionChanged(int selected)
        {
            string skinInternalName = _pushbackDropdown.options[selected].text;
            _themeManager.CurrentPushback = _masterSkinList.GetPushbackSkin(skinInternalName);
        }

        private void OnAdvancedItemsSkinChanged()
        {
            _advancedItemsDropdown.SetValueWithoutNotify(GetOptionIndex(_themeManager.CurrentItemGroup.skinGroupInternalName, _advancedItemsDropdown));
        }

        private void OnAdvancedItemsSkinSelectionChanged(int selected)
        {
            string skinInternalName = _advancedItemsDropdown.options[selected].text;
            _themeManager.CurrentItemGroup = _masterSkinList.GetItemSkinGroup(skinInternalName);
        }

        // Allowing to drag the window around in case it's blocking the view of the assets you want to look at.
        // A bit janky but does the job.

        public void OnPointerDown(PointerEventData eventData)
        {
            _dragging = true;
            _previousMousePos = _controls.GetPointerPosition();
        }

        public void OnPointerUp(PointerEventData eventData)
        {
            _dragging = false;
        }
    }
}