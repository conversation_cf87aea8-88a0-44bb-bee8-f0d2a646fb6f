//Copyrite Isto Inc. 2018
using Isto.Core.Inputs;
using Isto.Core.UI;
using System.Collections;
using UnityEngine;
using UnityEngine.UI;

namespace Isto.Core.UI
{
    public class CoreUIMenu : MonoBehaviour
    {
        // UNITY HOOKUP
        
        [Tooltip("Optional assignation that will attempt to tell the controller where the selection should land when this menu is shown")]
        [SerializeField] private Selectable _defaultSelection;

        
        // OTHER FIELDS
        
        private Animator _anim;

        
        // LIFECYCLE EVENTS
        
        public void Awake()
        {
            _anim = GetComponent<Animator>();
        }

        public void OnPointerEnter()
        {
            BroadcastMessage("MenuActive", true, SendMessageOptions.DontRequireReceiver);
        }

        public void OnPointerExit()
        {
            BroadcastMessage("MenuActive", false, SendMessageOptions.DontRequireReceiver);
        }
        
        
        // OTHER METHODS

        public void Setup(bool online)
        {
            BroadcastMessage("MenuOnline", online, SendMessageOptions.DontRequireReceiver);
        }

        public bool TryToHideMenu(bool hide)
        {
            CanvasGroup cg = GetComponent<CanvasGroup>();
            if (cg == null)
                return false;
            else
            {
                if (hide)
                    StartCoroutine(UIUtils.SetCanvasAlpha(cg, 0, 0, false));
                else
                    StartCoroutine(UIUtils.SetCanvasAlpha(cg, 1, 0, true, _defaultSelection));

                return true;
            }

        }

        public IEnumerator FadeIn()
        {
            if (gameObject.activeInHierarchy)
            {
                _anim = GetComponent<Animator>();
                _anim.SetTrigger("fadeIn");
                yield return 0;

                if (_defaultSelection != null && Controls.UsingController)
                    _defaultSelection.Select();
            }
        }

        public void FadeOut()
        {
            _anim.SetTrigger("fadeOut");
        }
    }
}

