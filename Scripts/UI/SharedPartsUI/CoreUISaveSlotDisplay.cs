// Copyright Isto Inc.
using I2.Loc;
using Isto.Core.Configuration;
using Isto.Core.Data;
using Isto.Core.Game;
using Isto.Core.Inputs;
using Isto.Core.Localization;
using Isto.Core.StateMachine;
using System;
using System.Collections;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    public class CoreUISaveSlotDisplay : MonoBehaviour
    {
        // UNITY HOOKUP

        [SerializeField] private CoreUISetSaveSlotSubState _parentMenu = null;
        [SerializeField] private RawImage _thumbnailDisplay = null;
        [SerializeField] private CoreButtonController _deleteButton = null;
        [FormerlySerializedAs("_slotButton")]
        [SerializeField] private CoreButtonController _saveOrLoadButton = null;
        [SerializeField] private TextMeshProUGUI buttonTextBox = null;
        [SerializeField] private Color _noSaveColor = new Color(101, 59, 59, 256);
        [SerializeField] private Settings _settings;

        [SerializeField] private LocalizedString _loadGameButtonKey;
        [SerializeField] private LocalizedString _saveGameButtonKey;
        [SerializeField] private LocalizedString _noDataButtonKey;
        [SerializeField] private LocalizedString _versionLabelKey;
        [SerializeField] private LocalizedString _playTimeLabelKey;
        [SerializeField] private LocalizedString _saveSlotNbLabelKey;
        [SerializeField] private LocalizedString _noSlotSelectedMsgKey;
        [SerializeField] private LocalizedString _overwriteSaveKey;

        [Header("Optional info fields")]
        [SerializeField] private TMP_InputField _saveFileName = null;
        [SerializeField] private TextMeshProUGUI _version = null;
        [SerializeField] private TextMeshProUGUI _playTime = null;
        [SerializeField] private TextMeshProUGUI _slotNumberLabel = null;
        [SerializeField] private TextMeshProUGUI _descriptiveName = null;
        [SerializeField] private TextMeshProUGUI _timeStamp = null;

        [Header("Optional Save Slot Deletion Confirmation Popup")]
        [SerializeField] private CanvasGroup _deleteSlotConfirmationDialog = null;
        [SerializeField] private Selectable _dialogDefaultSelection = null;

        [Header("Save Upgrade Sub Menu")]
        [SerializeField] private CoreUIUpgradeSaveSlotSubState _saveUpgradeSubMenu;
        //[SerializeField] private UIDeleteSlotSubState _deleteConfirmationSubMenu;
        
        [Header("Optional Features")]
        [SerializeField] private bool _thumbnailSupport = true;

        // OTHER FIELDS

        private int _slotNumber;
        private GameStateSaveData _metaData;
        private CoreUISetSaveSlotSubState.MenuMode _currentMode;
        private CoreButtonController _previousSelection;
        private Texture _originalThumbnailTexture = null;


        // PROPERTIES

        public bool IsBlockingForPopup { get; private set; }


        // INJECTION

        private IGameData _gameData;
        private GameState _gameState;
        private MonoPushdownStateMachine _menuController;
        private IControls _controls;

        [Inject]
        public void Inject(IGameData gameData, GameState gameState, [Inject(Optional = true)] SimpleGameMenuStateMachine inGameMenu, [Inject(Optional = true)] SimpleTitleMenuStateMachine titleMenuController,
            IControls controls)
        {
            _gameData = gameData;
            _gameState = gameState;

#if UNITY_2020 // Atrio is on old version of C# so we have to handle this line more capricously.
            _menuController = inGameMenu != null ? inGameMenu : titleMenuController as MonoPushdownStateMachine;
#else
            _menuController = inGameMenu != null ? inGameMenu : titleMenuController;
#endif

            if (_menuController == null)
            {
                Debug.LogError("No menu controller found for UISaveSlotDisplay.");
            }

            _controls = controls;
        }


        // LIFECYCLE METHODS

        private void Awake()
        {
            _slotNumber = transform.GetSiblingIndex();
            _originalThumbnailTexture = _thumbnailDisplay.texture;
        }

        private void OnEnable()
        {
            _saveFileName?.onValueChanged.AddListener(OnSaveSlotNameChanged);
            _saveFileName?.onSubmit.AddListener(OnSaveSlotNameSubmit);
        }

        private void OnDisable()
        {
            _saveFileName?.onValueChanged.RemoveListener(OnSaveSlotNameChanged);
            _saveFileName?.onSubmit.RemoveListener(OnSaveSlotNameSubmit);
        }

        // We're using LateUpdate to make sure we only remove the blocking flag after update is done so the input is not also
        // consumed by another menu. Not perfect but simpler, and considering there is nothing else in update I think it's ok
        private void LateUpdate()
        {
            if (_controls.GetButtonDown(UserActions.UICANCEL) && IsBlockingForPopup)
            {
                OnDeleteSlotCancelButton();
            }
        }

        // EVENT HANDLERS

        private void OnSaveSlotNameChanged(string newName)
        {
            if (newName.Length > _settings.MaxCharactersInSaveName)
            {
                _saveFileName.SetTextWithoutNotify(newName.Substring(0, _settings.MaxCharactersInSaveName));
            }
        }

        private void OnSaveSlotNameSubmit(string newName)
        {
            _descriptiveName.text = newName;

            Debug.Assert(_metaData != null, $"Save Slot MetaData cannot be null while you are editing a save slot's name");

            bool isRename = _metaData.saveSlotName != newName;

            _metaData.saveSlotName = newName;
            _metaData.isAutoSave = false;

            _gameData.SaveGameMetaData(_slotNumber, _metaData, (success) =>
            {
                Debug.Log($"UpdateSaveSlotMetaData {(success ? "successfully" : "failed to")} update data for slot#{_slotNumber}");
                _parentMenu.RefreshSaveList(selectedSlot: _slotNumber);

                if (isRename)
                {
                    _parentMenu.ShowRenameTextMessage();
                }
            });
        }

        private void OnUpgradeSaveChosen()
        {
            Debug.Log("Time to upgrade save");
        }

        public void SlotButtonClicked()
        {
            if (_slotNumber < 0)
                return;

            DoesSaveNeedUpgrade(_metaData, (needsUpgrade, upgradeSaveVersion) =>
            {
                if (_currentMode == CoreUISetSaveSlotSubState.MenuMode.Load && needsUpgrade)
                {
                    Debug.Log("This save needs upgrading");
                    _previousSelection = _saveOrLoadButton;

                    _saveUpgradeSubMenu.SetSaveSlot(_slotNumber, upgradeSaveVersion);

                    _menuController.EnterSubState(_saveUpgradeSubMenu);
                }
                else
                {
                    if (_currentMode == CoreUISetSaveSlotSubState.MenuMode.Save)
                    {
                        _metaData.saveSlotName = _saveFileName.text;

                        _gameData.SaveGameMetaData(_slotNumber, _metaData, (success) =>
                        {
                            Debug.Log($"UpdateSaveSlotMetaData {(success ? "successfully" : "failed to")} update data for slot#{_slotNumber}");
                        });
                    }

                    _gameState.LoadSaveSlotMetaData(_slotNumber, (metaData) =>
                    {
                        _parentMenu.SaveOrLoadButtonClicked(_slotNumber);
                    });
                }
            });
        }

        public void DeleteSlotButtonClicked()
        {
            if (_slotNumber < 0)
                return;

            _previousSelection = _deleteButton;

            if (_deleteSlotConfirmationDialog != null)
            {
                OpenDeleteSlotDialog();
            }
            else
            {
                DeleteSaveSlot();
            }
        }

        public void OnDeleteSlotConfirmButton()
        {
            CloseDeleteSlotDialog();
            DeleteSaveSlot();
        }

        public void OnDeleteSlotCancelButton()
        {
            CloseDeleteSlotDialog();

            if (_saveOrLoadButton != null && Controls.UsingController)
            {
                _saveOrLoadButton.Select();
            }
        }


        // OTHER METHODS

        public IEnumerator SetupSaveSlot(int slotnumber, CoreUISetSaveSlotSubState.MenuMode mode, GameStateSaveData metaData)
        {
            _currentMode = mode;
            _metaData = metaData;
            _slotNumber = slotnumber;
            bool hasSaveData = false;

            if (_slotNumber >= 0)
            {
                bool waitingForThumb = true;
                if(_thumbnailSupport)
                {
                    _gameData.LoadThumbnailForSaveSlot(_slotNumber,
                    (thumb) =>
                    {
                        if (_thumbnailDisplay.texture != null && _thumbnailDisplay.texture != _originalThumbnailTexture)
                        {
                            // Once we start putting loaded data in this display, we need to destroy it when we are done
                            // otherwise on xbox this causes memory leaks (not sure on PC)
                            GameObject.Destroy(_thumbnailDisplay.texture);
                        }
                        _thumbnailDisplay.texture = thumb;
                        waitingForThumb = false;
                    });
                }
                else
                {
                    waitingForThumb = false;
                }

                bool waitingForSaveData = true;
                _gameData.HasSaveData(_slotNumber,
                    (has) =>
                    {
                        hasSaveData = has;
                        waitingForSaveData = false;
                    });

                while (waitingForThumb || waitingForSaveData)
                {
                    yield return null;
                }
            }

            _thumbnailDisplay.color = hasSaveData ? Color.white : _noSaveColor;

            if (hasSaveData)
            {
                _saveOrLoadButton.SetEnabled();
            }
            else
            {
                _saveOrLoadButton.SetDisabled();
            }

            if (_metaData != null)
            {
                if (_saveFileName != null)
                {
                    Loc.SetTMProFont(_saveFileName);
                    _saveFileName.SetTextWithoutNotify(_metaData.saveSlotName);
                }

                if (_version != null)
                {
                    Loc.SetTMProLocalized(_version, $"{Loc.Get(_versionLabelKey)} {_metaData.gameVersion}");
                }

                if (_playTime != null)
                {
                    Loc.SetTMProLocalized(_playTime, $"{Loc.Get(_playTimeLabelKey)}{Loc.GetTimeLocalizedAndFormated(_metaData.totalGameTimeSeconds)}");
                }

                if (_slotNumberLabel != null)
                {
                    Loc.SetTMProLocalized(_slotNumberLabel, $"{Loc.Get(_saveSlotNbLabelKey)} {_slotNumber}");
                }

                if (_descriptiveName != null)
                {
                    Loc.SetTMProLocalized(_descriptiveName, _metaData.saveSlotName);
                }

                if (_timeStamp != null)
                {
                    _timeStamp.text = _metaData.saveDate.ToString("HH:mm:ss tt, MMM d, yyyy");
                }
            }
            else
            {
                if (_saveFileName != null)
                {
                    Loc.SetTMProFont(_saveFileName);
                    _saveFileName.SetTextWithoutNotify(Loc.Get(_noSlotSelectedMsgKey));
                }

                if (_version != null)
                {
                    Loc.SetTMProLocalized(_version, $"{Loc.Get(_versionLabelKey)} ---");
                }

                if (_playTime != null)
                {
                    Loc.SetTMProLocalized(_playTime, $"{Loc.Get(_playTimeLabelKey)} ---");
                }

                if (_slotNumberLabel != null)
                {
                    Loc.SetTMProLocalized(_slotNumberLabel, $"{Loc.Get(_saveSlotNbLabelKey)} ---");
                }

                if (_descriptiveName != null)
                {
                    _descriptiveName.text = "";
                }

                if (_timeStamp != null)
                {
                    _timeStamp.text = "";
                }
            }

            switch (mode)
            {
                case CoreUISetSaveSlotSubState.MenuMode.Save:
                    _saveFileName.enabled = true;

                    if (hasSaveData)
                    {
                        SetSaveButtonText(slotnumber, metaData);
                    }

                    break;
                case CoreUISetSaveSlotSubState.MenuMode.Load:
                    if (hasSaveData)
                    {
                        Loc.SetTMPro(buttonTextBox, _loadGameButtonKey);
                    }
                    else
                    {
                        Loc.SetTMPro(buttonTextBox, _noDataButtonKey);
                    }

                    // Diable input text field in load mode
                    _saveFileName.enabled = false;
                    break;
                default:
                    Debug.LogWarning($"No handler for menu mode {mode}");
                    break;
            }
        }

        public void SetSaveButtonText(int slotnumber, GameStateSaveData metaData)
        {
            if (_gameState.IsCurrentlyLoadedSaveGame(metaData, slotnumber))
                Loc.SetTMPro(buttonTextBox, _saveGameButtonKey);
            else
                Loc.SetTMPro(buttonTextBox, _overwriteSaveKey);
        }

        public void RestorePreviousSelection()
        {
            _previousSelection.Select();
        }

        private void DoesSaveNeedUpgrade(GameStateSaveData metaData, Action<bool, string> onResult)
        {
            _gameData.HasSaveData(_slotNumber, (hasSaveData) =>
            {
                bool needsUpgrade = false;
                string upgradeSaveVersion;
                if (!hasSaveData)
                {
                    upgradeSaveVersion = "";
                }
                else
                {
                    needsUpgrade = IsSaveOutOfDate(metaData, out upgradeSaveVersion);
                }
                onResult?.Invoke(needsUpgrade, upgradeSaveVersion);
            });
        }

        protected virtual bool IsSaveOutOfDate(GameStateSaveData gameStateData, out string versionToUpgradeTo)
        {
            versionToUpgradeTo = "";
            return false;
        }

        private void OpenDeleteSlotDialog()
        {
            IsBlockingForPopup = true;

            _deleteSlotConfirmationDialog.alpha = 1f;
            _deleteSlotConfirmationDialog.interactable = true;
            _deleteSlotConfirmationDialog.blocksRaycasts = true;

            if (_dialogDefaultSelection != null && Controls.UsingController)
                _dialogDefaultSelection.Select();
        }

        // Might get called more than once if using controller, but it shouldn't matter.
        private void CloseDeleteSlotDialog()
        {
            IsBlockingForPopup = false;
            _deleteSlotConfirmationDialog.alpha = 0f;
            _deleteSlotConfirmationDialog.interactable = false;
            _deleteSlotConfirmationDialog.blocksRaycasts = false;
        }

        private void DeleteSaveSlot()
        {
            _gameData.ClearSaveData(_slotNumber, (_) =>
            {
                _parentMenu.RefreshSaveList(selectedSlot: -1); //Should reset to default selection, aka first or empty
            });
        }
    }
}