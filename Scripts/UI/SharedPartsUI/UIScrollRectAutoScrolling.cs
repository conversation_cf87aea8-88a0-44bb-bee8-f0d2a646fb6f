using Isto.Core.Inputs;
using UnityEngine;
using UnityEngine.EventSystems;
using Zenject;

namespace Isto.Atrio
{
    /// <summary>
    /// This uses a different method than the UIControllerScrolling script to move the scroll rect based on the position of the current selection
    /// </summary>
    public class UIScrollRectAutoScrolling : MonoBehaviour
    {
        [SerializeField] private RectTransform _contentPanel;
        [SerializeField] private RectTransform _scrollRectTransform;
        [SerializeField] private int _scrollPadding;



        private IControls _controls;

        private GameObject _lastSelected;

        [Inject]
        public void Inject(IControls controls)
        {
            _controls = controls;
        }

        private void Update()
        {
            if (!Controls.UsingController)
                return;

            // Get the currently selected UI element from the event system.
            GameObject selected = EventSystem.current.currentSelectedGameObject;

            if (selected == null)
                return;

            // Return if the selected game object is not inside the scroll rect.
            if (!selected.transform.IsChildOf(_contentPanel.transform))
                return;

            // Return if the selected game object is the same as it was last frame,
            // meaning we haven't moved.
            if (selected == _lastSelected)
            {
                return;
            }
            // Get the rect tranform for the selected game object.
            RectTransform selectedRectTransform = selected.GetComponent<RectTransform>();
            // The position of the selected UI element is the absolute anchor position,
            // ie. the local position within the scroll rect + its height if we're
            // scrolling down. If we're scrolling up it's just the absolute anchor position.
            float selectedPositionY = Mathf.Abs(selectedRectTransform.anchoredPosition.y) + selectedRectTransform.rect.height;
            // The upper bound of the scroll view is the anchor position of the content we're scrolling.
            float scrollViewMinY = _contentPanel.anchoredPosition.y;
            // The lower bound is the anchor position + the height of the scroll rect.
            float scrollViewMaxY = _contentPanel.anchoredPosition.y + _scrollRectTransform.rect.height;

            // If the selected position is below the current lower bound of the scroll view we scroll down.
            if (selectedPositionY > scrollViewMaxY - _scrollPadding)
            {
                float newY = selectedPositionY - _scrollRectTransform.rect.height + _scrollPadding;
                _contentPanel.anchoredPosition = new Vector2(_contentPanel.anchoredPosition.x, newY);
            }
            // If the selected position is above the current upper bound of the scroll view we scroll up.
            else if (Mathf.Abs(selectedRectTransform.anchoredPosition.y) < scrollViewMinY + _scrollPadding)
            {
                _contentPanel.anchoredPosition = new Vector2(_contentPanel.anchoredPosition.x, (Mathf.Abs(selectedRectTransform.anchoredPosition.y) - _scrollPadding));
            }

            _lastSelected = selected;
        }
    }
}
