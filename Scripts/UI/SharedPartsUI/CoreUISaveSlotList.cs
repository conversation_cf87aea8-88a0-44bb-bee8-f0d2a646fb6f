// Copyright Isto Inc.
using I2.Loc;
using Isto.Core.Data;
using Isto.Core.Game;
using Isto.Core.Inputs;
using Isto.Core.Localization;
using Isto.Core.UI;
using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core
{
    public class CoreUISaveSlotList : MonoBehaviour
    {
        // UNITY HOOKUP

        [Header("UI rig references")]
        [SerializeField] private CoreUISetSaveSlotSubState _parentMenu = null;
        [SerializeField] private CoreUISaveSlotDisplay _selectedSaveSlotDisplay = null;
        [SerializeField] private Button _newSaveSlotButton = null;
        [SerializeField] private Button _deleteButton = null;
        [SerializeField] private Button _saveLoadButton = null;
        [SerializeField] private TMP_InputField _saveRenameField = null;
        [Header("Prefabs")]
        [SerializeField] private CoreUISaveSlotListItem _slotListItemPrefab = null;
        [Header("Loc Keys")]
        [SerializeField] private LocalizedString _emptySlotNameKey;
        [SerializeField] private LocalizedString _defaultSlotNameKey;
        [SerializeField] private LocalizedString _newSlotNameKey;


        // OTHER FIELDS

        private List<CoreUISaveSlotListItem> _slotList;
        private CoreUISetSaveSlotSubState.MenuMode _currentMode;
        private bool _waitingForSaves = false;


        // PROPERTIES

        public RectTransform RectTransform => this.transform as RectTransform;
        public TMP_InputField SaveRenameField => _saveRenameField;
        public Button NewSaveFileButton => _newSaveSlotButton;


        // INJECTION

        private IGameData _gameData;
        private GameState _gameState;
        private DiContainer _container;

        [Inject]
        public void Inject(IGameData gameData, GameState gameState, DiContainer container)
        {
            _gameData = gameData;
            _gameState = gameState;
            _container = container;
        }


        //LIFECYCLE EVENTS

        private void Awake()
        {
            _slotList = new List<CoreUISaveSlotListItem>(gameObject.GetComponentsInChildren<CoreUISaveSlotListItem>());
        }


        // OTHER METHODS

        public void ClearListItems()
        {
            foreach (CoreUISaveSlotListItem slot in _slotList)
            {
                UISelectableListButton slotButton = slot.GetComponent<UISelectableListButton>();
                slotButton.OnSelected -= OnSlotButtonClicked;

                GameObject.Destroy(slot.gameObject);
            }

            _slotList.Clear();

            StartCoroutine(_selectedSaveSlotDisplay.SetupSaveSlot(-1, _currentMode, null));
        }

        public void SetSlotMenuMode(CoreUISetSaveSlotSubState.MenuMode mode)
        {
            _currentMode = mode;
            _newSaveSlotButton.gameObject.SetActive(_currentMode == CoreUISetSaveSlotSubState.MenuMode.Save);
        }

        public void SetupSaveSlot(int slotnumber, bool selected = false, Action onFinished = null)
        {
            // Get save info
            _gameData.LoadGameMetaData(slotnumber, (metaData) =>
            {
                // The rest of the logic is mostly about figuring out what to put in the meta data if it is missing
                bool dataChanged = false;
                bool setTemporaryNewFileName = false;

                if (metaData == null)
                {
                    // This should be an old game from before we had metadata or a bug caused this slot to not create its data
                    // (we're not creating an empty save slot on purpose anymore)
                    metaData = new GameStateSaveData();
                    dataChanged = true;
                    setTemporaryNewFileName = true;

                    // Careful because this means the save will be upgraded and loadable but won't necessarily be in a good state
                    // for instance it will default to story mode but the player position is not reset when that happens so if the
                    // slot was saved from creative mode you can land outside the story map bounds.
                    metaData.gameModeName = "---";
                    metaData.gameVersion = _gameState.version.ActiveVersion.VersionText;
                    metaData.internalBuildVersion = "";
                    metaData.saveDate = DateTime.Now;
                }

                // If save data exists, try to get the real data to fill the fields
                _gameData.HasSaveData(slotnumber, (hasSaveData) =>
                {
                    // Add button
                    Transform parent = this.transform;
                    CoreUISaveSlotListItem instance = _container.InstantiatePrefabForComponent<CoreUISaveSlotListItem>(_slotListItemPrefab, this.transform);

                    // note: order is not important in this list, we're only tracking what we've created
                    _slotList.Add(instance);

                    if (_slotList.Count >= _gameData.MaximumSaveSlots)
                        _newSaveSlotButton.gameObject.SetActive(false);


                    UISelectableListButton selectable = instance.GetComponent<UISelectableListButton>();
                    selectable.OnSelected += OnSlotButtonClicked;

                    // Setup selection
                    if (selected)
                    {
                        _selectedSaveSlotDisplay.SetSaveButtonText(slotnumber, metaData);

                        if (selectable != null && !selectable.IsSelected)
                        {
                            selectable.Selected(); // this means it is selected as in Clicked! the contents are displayed
                        }
                        else
                        {
                            Debug.LogWarning("Navigation component UISelectableListButton was expected but missing on UISaveSlotListItem prefab", instance.gameObject);
                        }
                    }

                    if (!hasSaveData)
                    {
                        // User can name a save slot even if it is still empty
                        // (Note: we now auto-save when you press new slot, so this case doesn't happen anymore for now)
                        if (string.IsNullOrEmpty(metaData.saveSlotName))
                        {
                            metaData.saveSlotName = $"{Loc.Get(_emptySlotNameKey)}{slotnumber}";
                        }

                        // This information doesn't matter as long as there is not actually a game saved in the slot
                        metaData.gameModeName = "---";
                        metaData.gameVersion = _gameState.version.ActiveVersion.VersionText;
                        metaData.internalBuildVersion = _gameState.version.ActiveVersion.shortDate;
                        metaData.saveDate = DateTime.Now;

                        if (setTemporaryNewFileName)
                        {
                            metaData.gameModeName = _gameState.CurrentGameMode ? _gameState.CurrentGameMode.InternalName : "---";
                            metaData.saveSlotName = $"{Loc.Get(_newSlotNameKey)}{slotnumber}";
                        }
                    }
                    else
                    {
                        if (string.IsNullOrEmpty(metaData.saveSlotName))
                        {
                            metaData.saveSlotName = $"{Loc.Get(_defaultSlotNameKey)}{slotnumber}";
                            dataChanged = true;
                        }

                        if (string.IsNullOrEmpty(metaData.gameModeName))
                        {
                            metaData.gameModeName = "---";
                            dataChanged = true;
                        }

                        if (string.IsNullOrEmpty(metaData.gameVersion))
                        {
                            metaData.gameVersion = _gameState.version.ActiveVersion.VersionText;
                            dataChanged = true;
                        }
                    }

                    // parent ourselves in the vertical layout group (this needs to be done before setup)
                    // and sort ourselves in the list now what we know what our meta data is
                    if (metaData.isAutoSave)
                    {
                        int index = 0;
                        foreach (Transform child in parent)
                        {
                            index++;

                            CoreUISaveSlotListItem childItem = child.GetComponent<CoreUISaveSlotListItem>();

                            if (childItem != null && childItem.MetaData != null)
                            {
                                if (!childItem.MetaData.isAutoSave) // autosaves are first so there are no more
                                    break;

                                if (childItem.MetaData.saveDate > metaData.saveDate)
                                    continue;
                            }

                            break;
                        }

                        instance.transform.SetParent(parent, false);
                        instance.transform.SetSiblingIndex(index - 1);
                    }
                    else
                    {
                        int index = 0;
                        foreach (Transform child in parent)
                        {
                            index++;

                            CoreUISaveSlotListItem childItem = child.GetComponent<CoreUISaveSlotListItem>();

                            if (childItem != null && childItem.MetaData != null)
                            {
                                if (childItem.MetaData.isAutoSave) // should be first real slot if it exists
                                    continue;

                                if (childItem.MetaData.saveDate > metaData.saveDate)
                                    continue;
                            }

                            instance.transform.SetParent(parent, false);
                            instance.transform.SetSiblingIndex(index - 1);
                            break;
                        }
                    }

                    // Populate the slot
                    instance.SetupSaveSlot(slotnumber, metaData);

                    if (selected)
                    {
                        // this means it is selected as in controller navigation! not yet clicked.
                        // but the default clicked button should also be selected by default
                        instance.Select();

                        SlotButtonClicked(instance);
                    }

                    // Persist any values we generated
                    if (dataChanged)
                    {
                        _gameData.SaveGameMetaData(slotnumber, metaData, (success) =>
                        {
                            Debug.Log($"UpdateSaveSlotMetaData {(success ? "successfully" : "failed to")} update data for slot#{slotnumber}");
                        });
                    }

                    onFinished?.Invoke();
                });
            });
        }

        [ContextMenu("RebuildNavigation")]
        public void RebuildNavigation()
        {
            Transform parent = this.transform;

            for (int i = 0; i < parent.childCount; i++)
            {
                Transform child = parent.GetChild(i);

                UISelectableListButton selectable = child.GetComponent<UISelectableListButton>();

                if (selectable != null)
                {
                    Button button = selectable.Button;

                    UIUtils.SetNavigation(button, UIUtils.NavigationDirection.Right, _saveLoadButton);

                    if (i == 0 && _newSaveSlotButton.isActiveAndEnabled)
                    {
                        UIUtils.SetNavigation(button, UIUtils.NavigationDirection.Up, _newSaveSlotButton);
                        UIUtils.SetNavigation(_newSaveSlotButton, UIUtils.NavigationDirection.Down, button);
                    }
                    else if (i != 0 && parent.childCount > 1)
                    {
                        UISelectableListButton other = parent.GetChild(i - 1).GetComponent<UISelectableListButton>();
                        other.LinkForNavigation(selectable);
                    }
                }
                else
                {
                    Debug.LogWarning("Navigation component UISelectableListButton was expected but missing on UISaveSlotListItem prefab", child.gameObject);
                }
            }
        }

        public void SelectFirstSlot()
        {
            if (this.transform.childCount <= 0)
                return;

            Transform firstItem = this.transform.GetChild(0);

            // Select the first item in the list so user can navigate from there
            if (Controls.UsingController && firstItem.gameObject.activeSelf)
            {
                Button button = firstItem.GetComponent<Button>();
                button.Select();
            }

            // Show some info on the right, ideally this will match the default selection
            CoreUISaveSlotListItem instance = this.transform.GetChild(0).GetComponent<CoreUISaveSlotListItem>();

            if (instance == null)
                return;

            SlotButtonClicked(instance);
        }

        public void SlotButtonClicked(CoreUISaveSlotListItem slot)
        {
            StartCoroutine(_selectedSaveSlotDisplay.SetupSaveSlot(slot.SlotNumber, _currentMode, slot.MetaData));
            _parentMenu.SetCurrentSelectedSaveSlot(slot.SlotNumber);

            // I think it might be more useful that navigation takes you back to the selected slot
            // But it it possible that this would be better if it returned to the last highlighted list item
            Button button = slot.GetComponent<Button>();
            UIUtils.SetNavigation(_saveLoadButton, UIUtils.NavigationDirection.Left, button);
            UIUtils.SetNavigation(_deleteButton, UIUtils.NavigationDirection.Left, button);

            // This doesn't mean much using controller as it's redundant but it makes it so selecting with the mouse
            // also forces the list to show the whole item which I think is helpful?
            RectTransform.EnsureChildIsVisibleVertically(slot.RectTransform, UIScrollListSelectableItem.ScrollListAdjustmentDriver.SiblingIndexDriven);
        }

        // Above method is called from UISaveSlotListItem, and this method is from an event of UISelectableListButton.
        // Both are on the same object so they are sort of redundant, do we want to remove one and consolidate the logic?
        private void OnSlotButtonClicked(UISelectableListButton seletedButton)
        {
            Transform parent = this.transform;

            for (int i = 0; i < parent.childCount; i++) // TODO: do we check children or _slotList???
            {
                Transform child = parent.GetChild(i);
                UISelectableListButton selectable = child.GetComponent<UISelectableListButton>();
                selectable.SelectableListButtonClicked(seletedButton);
            }
        }

        // This method doesn't seem like it's hooked up anywhere - perhaps it's part of the old system? consider deleting it
        public void NewSaveFileClicked()
        {
            if (_waitingForSaves)
                return;

            _waitingForSaves = true;

            _gameData.GetLowestAvailableSlotNumber((newSlotNumber) =>
            {
                if (newSlotNumber == -1)
                {
                    Debug.LogError("GetLowestAvailableSlotNumber failed to give us a valid index. User could have reached maximum");
                }
                else
                {
                    //Debug.Log($"Creating a new save slot with number {newSlotNumber}.");
                    _gameState.SelectSaveGame(newSlotNumber);
                    _gameState.LoadGameStateData(null, -1);
                    _gameData.SaveGameData(newSlotNumber, createBackup: false, (success) =>
                    {
                        if (success)
                        {
                            Events.RaiseEvent(Events.GAME_SAVED);
                            _gameState.LoadSaveSlotMetaData(newSlotNumber, null);
                            _parentMenu.ShowGameSavedConfirmationMessage();
                        }
                        else
                        {
                            // The internal cause of the error should be causing an error dialog popup to happen
                            Debug.LogError($"SaveGameData for slot#{newSlotNumber} has failed ");
                        }

                        // Refresh either way because in case of failure a new slot probably has still been created
                        _parentMenu.RefreshSaveList(selectedSlot: newSlotNumber, () => _waitingForSaves = false);
                    });
                }
            });
        }
    }
}