// Copyright Isto Inc.
using I2.Loc;
using Isto.Core;
using Isto.Core.Localization;
using TMPro;
using UnityEngine;
using Zenject;

public class ULastTimeSaved : MonoBehaviour
{
    // UNITY HOOKUP

    [SerializeField] private TextMeshProUGUI _label;
    [SerializeField] private LocalizedString _lastAutoSaveLocKey;
    [SerializeField] private LocalizedString _secondsPrefixLocKey;
    [SerializeField] private LocalizedString _secondsSuffixLocKey;

    // OTHER FIELDS

    private float _lastAutoSaveTime;
    private int _secondsCountToDisplay = 0;
    private string _autoSaveMessageLocalized;
    private string _timeUnitPrefixLocalized;
    private string _timeUnitSuffixLocalized;

    // INJECTION

    private ILocalizationProvider _locProvider;

    [Inject]
    void Inject(ILocalizationProvider loc)
    {
        _locProvider = loc;
    }


    // LIFECYCLE EVENTS

    private void OnDestroy()
    {
        UnregisterEvents();
    }

    private void OnEnable()
    {
        UpdateLoc();
    }

    private void Start()
    {
        // Assuming that this is about when the game loads, we'll count this as our initial "save point"
        _lastAutoSaveTime = Time.unscaledTime;

        RegisterEvents();
    }

    private void Update()
    {
        int wait = Mathf.FloorToInt(Time.unscaledTime - _lastAutoSaveTime);
        if(_secondsCountToDisplay != wait)
        {
            _secondsCountToDisplay = wait;
            UpdateLabel();
        }
    }


    // EVENT HANDLING

    private void RegisterEvents()
    {
        Events.Subscribe(Events.GAME_AUTOSAVING, Events_OnAutoSave);
        LocalizationManager.OnLocalizeEvent += LocalizationManager_OnLocalizeEvent;
    }

    private void UnregisterEvents()
    {
        Events.UnSubscribe(Events.GAME_AUTOSAVING, Events_OnAutoSave);
        LocalizationManager.OnLocalizeEvent -= LocalizationManager_OnLocalizeEvent;
    }

    private void Events_OnAutoSave()
    {
        _lastAutoSaveTime = Time.unscaledTime;
    }

    private void LocalizationManager_OnLocalizeEvent()
    {
        UpdateLoc();
    }


    // OTHER METHODS

    private void UpdateLoc()
    {
        _autoSaveMessageLocalized = _locProvider.GetLocalizedText(_lastAutoSaveLocKey.mTerm);
        _timeUnitPrefixLocalized = _locProvider.GetLocalizedText(_secondsPrefixLocKey.mTerm);
        _timeUnitSuffixLocalized = _locProvider.GetLocalizedText(_secondsSuffixLocKey.mTerm);
    }

    private void UpdateLabel()
    {
        _label.text = $"{_autoSaveMessageLocalized} {_timeUnitPrefixLocalized}{_secondsCountToDisplay} {_timeUnitSuffixLocalized}";
    }
}