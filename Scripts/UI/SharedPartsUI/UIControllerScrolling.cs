//Copyrite Isto Inc. 2018
using Isto.Core.Inputs;
using UnityEngine;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    /// <summary>
    /// This component allows you to have a scroll bar react to controller input without having to navigate onto it
    /// e.g. if you want the selection to be the OK button from a UI panel but the joysticks to scroll something inside it.
    /// </summary>
    public class UIControllerScrolling : MonoBehaviour
    {
        [SerializeField] private Scrollbar _scrollbar;

        private IControls _controls;

        [Inject]
        public void Inject(IControls controls)
        {
            _controls = controls;
        }

        private void Update()
        {
            if (!Controls.UsingController)
                return;

            bool inverted = false;
            if (_scrollbar.direction == Scrollbar.Direction.RightToLeft || _scrollbar.direction == Scrollbar.Direction.TopToBottom)
            {
                inverted = true;
            }

            bool horizontal = false;
            if (_scrollbar.direction == Scrollbar.Direction.LeftToRight || _scrollbar.direction == Scrollbar.Direction.RightToLeft)
            {
                horizontal = true;
            }

            float movement = 0f;
            if (horizontal)
            {
                float hAxis1 = _controls.GetRawAxis(Controls.MovementAxis.UIHorizontal);
                float hAxis2 = _controls.GetRawAxis(Controls.MovementAxis.UISecondaryHorizontal);
                movement = hAxis1 + hAxis2;
            }
            else
            {
                float vAxis1 = _controls.GetRawAxis(Controls.MovementAxis.UIVertical);
                float vAxis2 = _controls.GetRawAxis(Controls.MovementAxis.UISecondaryVertical);
                movement = vAxis1 + vAxis2;
            }

            movement *= _scrollbar.size * 2f * Time.unscaledDeltaTime;

            if (inverted)
                movement = -movement;

            _scrollbar.value = Mathf.Clamp01(_scrollbar.value + movement);
        }
    }
}