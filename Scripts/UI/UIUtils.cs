// Copyright Isto Inc.
using Isto.Core.Inputs;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using static Isto.Core.UI.UIScrollListSelectableItem;

namespace Isto.Core.UI
{
    /// <summary>
    /// Uitilies specific to just the UI
    /// </summary>
    public static class UIUtils
    {
        private static readonly float FULL_OPACITY = 1f;
        private static readonly float NO_OPACITY = 0f;
        private static readonly bool INTERACTABLE = true;
        private static readonly bool NON_INTERACTABLE = false;

        /// <summary>
        /// Lerps from the current alpha of the passed in canvas to the target alpha, taking the specified amount of time.
        /// Sets the interaction state of the canvas after the lerp is complete.
        /// </summary>
        /// <param name="group">CanvasGroup to operate on</param>
        /// <param name="targetAlpha">Final alpha for the group.</param>
        /// <param name="fadeTime">Time to lerp from current alpha to target</param>
        /// <param name="interactable">Final interactable state of the canvas group</param>
        /// <param name="selection">Selectable to set as selected when fade is complete</param>
        /// <returns></returns>
        public static IEnumerator SetCanvasAlpha(CanvasGroup group, float targetAlpha, float fadeTime, bool interactable, Selectable selection = null)
        {
            group.interactable = interactable;
            group.blocksRaycasts = interactable;

            float timer = 0f;
            float startAlpha = group.alpha;

            while (timer < fadeTime)
            {
                group.alpha = Mathf.Lerp(startAlpha, targetAlpha, timer / fadeTime);

                timer += Time.unscaledDeltaTime;

                yield return null;
            }

            group.alpha = targetAlpha;

            if (selection != null && Controls.UsingController)
                selection.Select();
        }

        public static IEnumerator FadeOutAndDisableCanvas(CanvasGroup group, float fadeTime, Canvas canvas, Action callback = null)
        {
            yield return SetCanvasAlpha(group, NO_OPACITY, fadeTime, NON_INTERACTABLE);

            if (canvas)
                canvas.enabled = false;

            if (callback != null)
                callback.Invoke();
        }

        public static IEnumerator EnableAndShowCanvas(CanvasGroup group, float fadeTime, bool interactable, Canvas canvas, Selectable selection = null)
        {
            yield return SetCanvasAlpha(group, FULL_OPACITY, fadeTime, interactable, selection);

            if (canvas)
            {
                canvas.enabled = true;
            }
        }

        public static IEnumerator FadeOutAndDeactivateObject(CanvasGroup group, float fadeTime, GameObject gameObject)
        {
            yield return SetCanvasAlpha(group, NO_OPACITY, fadeTime, NON_INTERACTABLE);
            gameObject.SetActive(false);
        }

        public static IEnumerator FadeInAndEnableCanvasGroup(CanvasGroup group, float fadeTime)
        {
            yield return SetCanvasAlpha(group, FULL_OPACITY, fadeTime, INTERACTABLE);
        }

        public static void SetCameraForCanvas(GameObject source)
        {
            if (source.TryGetComponent(out Canvas canvas))
            {
                canvas.worldCamera = Camera.main;
            }
        }

        /// <summary>
        /// Sets the navigation for the Selectable object and changes it's navigation mode to Explicit.  Pass null to not have
        /// a navigation set for a particular direction
        /// </summary>
        /// <param name="selectable">Selectable object to set its navigation</param>
        /// <param name="down">Select on down object</param>
        /// <param name="left">Select on left object</param>
        /// <param name="right">Select on right object</param>
        /// <param name="up">Select on up object</param>
        public static void SetNavigation(Selectable selectable, Selectable down, Selectable left, Selectable right, Selectable up)
        {
            Navigation nav = selectable.navigation;

            nav.selectOnDown = down;
            nav.selectOnLeft = left;
            nav.selectOnRight = right;
            nav.selectOnUp = up;

            nav.mode = Navigation.Mode.Explicit;

            selectable.navigation = nav;
        }

        public enum NavigationDirection
        {
            Up,
            Down,
            Left,
            Right
        }

        /// <summary>
        /// Sets the navigation for one direction for the Seletable, does not impact any other directions
        /// </summary>
        /// <param name="selectable">Selectable to update</param>
        /// <param name="direction">Navigation Direction</param>
        /// <param name="target">Target for the navigation to point to</param>
        public static void SetNavigation(Selectable selectable, NavigationDirection direction, Selectable target)
        {
            Navigation nav = selectable.navigation;

            switch (direction)
            {
                case NavigationDirection.Up:
                    nav.selectOnUp = target;
                    break;
                case NavigationDirection.Down:
                    nav.selectOnDown = target;
                    break;
                case NavigationDirection.Left:
                    nav.selectOnLeft = target;
                    break;
                case NavigationDirection.Right:
                    nav.selectOnRight = target;
                    break;
                default:
                    break;
            }

            selectable.navigation = nav;
        }

        /// <summary>
        /// Sets the navigation values for the provided selectables based on, assuming that they are in order of
        /// ther vertical positions
        /// </summary>
        /// <param name="navigationElements">The selectables to link</param>
        public static void SetupVerticalButtonsNavigation(List<Selectable> navigationElements)
        {
            Selectable prev = null;
            Selectable current = null;
            Selectable next = null;

            for (int i = 0; i < navigationElements.Count; i++)
            {
                prev = (i > 0) ? navigationElements[i - 1] : null;
                current = navigationElements[i];
                next = (i + 1 < navigationElements.Count) ? navigationElements[i + 1] : null;

                SetNavigation(current, NavigationDirection.Up, prev);
                SetNavigation(current, NavigationDirection.Down, next);
            }
        }

        /// <summary>
        /// Sets the nagivation values for the button based on the parent grid layout group.  Assumes the Button is a child of a GridLayoutGroup.
        /// </summary>
        /// <param name="button"></param>
        public static void SetupGridNavigationForButton(Selectable button)
        {
            // Get Grid Layout group
            GridLayoutGroup gridGroup = button.GetComponentInParent<GridLayoutGroup>();

            if (gridGroup == null)
            {
                Debug.LogWarning("No GridLayoutGroup for button trying to use SetupGridNavigationForButton.  This method should not be called for this button: " + button.name);
                return;
            }

            int columnCount = gridGroup.constraintCount;

            // Figure out which child we are
            int slotNumber = button.transform.GetSiblingIndex();
            int slotCount = button.transform.parent.childCount;

            // Setup navigation
            Navigation nav = button.navigation;

            // Left Nav (Only override if something hasn't been set already
            if (slotNumber > 0 && nav.selectOnLeft == null)
                nav.selectOnLeft = button.transform.parent.GetChild(slotNumber - 1).GetComponent<Selectable>();

            // Right Nav
            if (slotNumber < slotCount - 1 && (slotNumber + 1 % columnCount != 0))
                nav.selectOnRight = button.transform.parent.GetChild(slotNumber + 1).GetComponent<Selectable>();

            // Up Nav
            if (slotNumber - columnCount >= 0)
                nav.selectOnUp = button.transform.parent.GetChild(slotNumber - columnCount).GetComponent<Selectable>();

            // Bottom Nav
            if (slotNumber + columnCount < slotCount)
                nav.selectOnDown = button.transform.parent.GetChild(slotNumber + columnCount).GetComponent<Selectable>();

            button.navigation = nav;
        }


        /// <summary>
        /// Checks if the Selectable is in the rightmost column of it's parent GridLayoutGroup.
        /// This is intended to be for UI navigation purposes, so in this case a slot that is the
        /// last enabled slot on its row will be considered "rightmost" even if it's not technically
        /// the rightmost possible position, as we don't want to have to navigate through disabled
        /// slots (currently this is mostly important for factories).
        /// </summary>
        /// <param name="button"></param>
        /// <returns>True if in rightmost column, false otherwise</returns>
        public static bool IsInRightColumn(Selectable button)
        {
            if (!button.gameObject.activeSelf)
                return false;

            int slotNumber = button.transform.GetSiblingIndex();
            int totalSiblings = button.transform.parent.childCount;

            //TODO: make a non-recursive extension method to check in children non recursively
            //button.transform.parent.GetComponentsInChildren<Transform>(includeInactive: false, activeSiblings);
            List<Transform> activeSiblings = new List<Transform>();
            foreach (Transform child in button.transform.parent)
            {
                if (child.gameObject.activeSelf)
                    activeSiblings.Add(child);
                else
                    break; // We know in a UIItemGrid all the inactive elements are at the end.
            }

            // Right now we're only using this in UIItemGrid context. In that context, we know that and inactive
            // slots are at the end, which simplifies the situation. If eventually that is not the case, we'll have to refine this.
            if (activeSiblings.Count == 1 || (activeSiblings.Count - 1) == slotNumber)
                return true;

            // Under our assumptions this should have been covered by the "last active sibling" check, but to be clear about it,
            // the last active child must be a "rightmost" slot.
            if (totalSiblings - 1 == slotNumber)
                return true;

            // Get Grid Layout group
            GridLayoutGroup gridGroup = button.GetComponentInParent<GridLayoutGroup>();
            if (gridGroup == null)
            {
                Debug.LogWarning("No GridLayoutGroup for button trying to use UIUtils.IsInRightColumn(...).  This method should not be called for this button: " + button.name);
                return false;
            }

            // Normally the rightmost item is simply in this column
            int columnCount = gridGroup.constraintCount;
            bool isInLastColumn = (slotNumber + 1) % columnCount == 0;
            bool isIsLastRow = ((activeSiblings.Count - 1) / columnCount) == (slotNumber / columnCount);

            if (!isInLastColumn)
            {
                if (isIsLastRow)
                {
                    // Because we know we are not dead last, we can freely check our neighbour
                    if (!button.transform.parent.GetChild(slotNumber + 1).gameObject.activeSelf)
                    {
                        // Again, per item grid rules, if next guy is disabled, then we must be last.
                        isInLastColumn = true;
                    }
                }
            }

            return isInLastColumn;
        }

        public static bool IsInBottomRow(Selectable button)
        {
            if (!button.gameObject.activeSelf)
                return false;

            // Figure out which child we are
            int slotNumber = button.transform.GetSiblingIndex();

            //TODO: make a non-recursive extension method to check in children non recursively
            //button.transform.parent.GetComponentsInChildren<Transform>(includeInactive: false, activeSiblings);
            List<Transform> activeSiblings = new List<Transform>();
            foreach (Transform child in button.transform.parent)
            {
                if (child.gameObject.activeSelf)
                    activeSiblings.Add(child);
                else
                    break; // We know in a UIItemGrid all the active elements are at the end.
            }

            // Get Grid Layout group
            GridLayoutGroup gridGroup = button.GetComponentInParent<GridLayoutGroup>();
            if (gridGroup == null)
            {
                Debug.LogWarning("No GridLayoutGroup for button trying to use UIUtils.IsInBottomRow(...).  This method should not be called for this button: " + button.name);
                return false;
            }

            // Normally the bottom item is simply in the last row
            int columnCount = gridGroup.constraintCount;

            // because we wrap horizontally, the last 6 active elements should always be at the bottom
            bool isInLastRow = slotNumber + columnCount >= activeSiblings.Count;

            return isInLastRow;
        }

        public static string ConvertSecondsToMinutes(float time)
        {

            int seconds = (int)time % 60;
            int minutes = (int)time / 60;
            return minutes + "m:" + seconds + "s";
        }

        /// <summary>
        /// Finds out if a scroll view layout group needs to be repositioned in order for a certain element to be entirely visible.
        ///
        /// Warning: Works horizontally or vertically, but not with grids. We are using the child index to situate the child.
        ///
        /// This method assumes you are using the standard unity setup for scroll views, so there should be a Mask component on
        /// a parent of a layout object, which organizes its children (one of which is elementTransform) and resizes itself
        /// to reflect the total size of its contents. (The Mask component is explicitely required, but the LayoutGroup and
        /// ContentSizeFitter themselves are not, as long as you have something that behaves in the same way)
        /// </summary>
        /// <param name="layoutTransform">The list or grid root</param>
        /// <param name="childTransform">The element we want to see</param>
        /// <param name="getDimension">The dimension getter who knows which axis you are looking to align against</param>
        /// <returns>The anchored position the list should be at.</returns>
        public static float GetClosestLayoutPosToMakeChildFullyVisible(RectTransform layoutTransform, RectTransform childTransform, Func<Vector2, float> getDimension, ScrollListAdjustmentDriver adjustmentDriver, bool tempFix)
        {
            Mask mask = layoutTransform.GetComponentInParent<Mask>();
            Debug.Assert(mask != null, "GetClosestListPosToMakeElementFullyVisible expects to work on children of a Mask component.");

            // Size + Pos info
            Vector2 anchoredPos = layoutTransform.anchoredPosition;
            float layoutPos = getDimension(anchoredPos);
            float childSize = getDimension(childTransform.sizeDelta);
            float viewSize = getDimension(mask.rectTransform.sizeDelta);

            // Limits
            float childStartPoint = childTransform.GetSiblingIndex() * childSize; // Note: the sibling index trick will not do for grids
            if (adjustmentDriver == ScrollListAdjustmentDriver.PositionDriven)
                childStartPoint = getDimension(childTransform.anchoredPosition);

            if (tempFix)
            {
                // Not a great fix for the option dropdown, it doesn't really work but it makes it at least somewhat useable until I get to it.
                childStartPoint -= childSize;
                if (childStartPoint < 0f)
                    childStartPoint = 0f;
            }

            float childEndPoint = childStartPoint + childSize;
            float viewStart = layoutPos;
            float viewEnd = viewStart + viewSize;

            // Are we entirely visible? Pull the list into view so that we are if needed.
            float correction = 0f;
            if (childStartPoint < viewStart)
            {
                correction = childStartPoint - childSize * 1.5f - viewStart; // trying to introduce a buffer of 1 element size so you can see ahead
            }
            else if (childEndPoint > viewEnd)
            {
                correction = childEndPoint + childSize * 1.5f - viewEnd; // trying to introduce a buffer of 1 element size so you can see ahead
            }

            return layoutPos + correction;
        }

        private static float GetX(Vector2 v)
        {
            return v.x;
        }

        private static float GetY(Vector2 v)
        {
            return v.y;
        }

        public static float GetClosestHorizontalLayoutPosToMakeChildFullyVisible(RectTransform layoutTransform, RectTransform elementTransform, ScrollListAdjustmentDriver adjustmentDriver, bool tempFix)
        {
            return GetClosestLayoutPosToMakeChildFullyVisible(layoutTransform, elementTransform, GetX, adjustmentDriver, tempFix);
        }

        public static float GetClosestVerticalLayoutPosToMakeChildFullyVisible(RectTransform layoutTransform, RectTransform elementTransform, ScrollListAdjustmentDriver adjustmentDriver, bool tempFix)
        {
            return GetClosestLayoutPosToMakeChildFullyVisible(layoutTransform, elementTransform, GetY, adjustmentDriver, tempFix);
        }

        /// <summary>
        /// Assumes it can find a Mask component in a parent object to know what position will be visible.
        /// </summary>
        public static void EnsureChildIsVisibleHorizontally(this RectTransform layoutGroup, RectTransform child, ScrollListAdjustmentDriver adjustmentDriver, bool tempFix = false)
        {
            Vector2 pos = layoutGroup.anchoredPosition;
            // horizontally we move the background left to scroll forward
            pos.x = -GetClosestHorizontalLayoutPosToMakeChildFullyVisible(layoutGroup, child, adjustmentDriver, tempFix);
            layoutGroup.anchoredPosition = pos;
        }

        /// <summary>
        /// Assumes it can find a Mask component in a parent object to know what position will be visible.
        /// </summary>
        public static void EnsureChildIsVisibleVertically(this RectTransform layoutGroup, RectTransform child, ScrollListAdjustmentDriver adjustmentDriver, bool tempFix = false)
        {
            Vector2 pos = layoutGroup.anchoredPosition;
            // vertically we move the background up to scroll forward (i.e. down)
            pos.y = GetClosestVerticalLayoutPosToMakeChildFullyVisible(layoutGroup, child, adjustmentDriver, tempFix);
            layoutGroup.anchoredPosition = pos;
        }
    }
}