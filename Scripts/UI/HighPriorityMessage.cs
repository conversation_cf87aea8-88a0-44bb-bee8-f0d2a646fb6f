// Copyright Isto Inc.
using UnityEngine;

namespace Isto.Core.UI
{
    /// <summary>
    /// Defines the look and importance that can be associated with messages we want to show to the user.
    /// </summary>
    [CreateAssetMenu(fileName = "HiPriMessage", menuName = "Scriptables/UI/HighPriorityMessage")]
    public class HighPriorityMessage : ScriptableObject
    {
        public enum MessageType { warning, success, error };
        public MessageType type;
        public Sprite icon;
        public Color color;
    }
}
