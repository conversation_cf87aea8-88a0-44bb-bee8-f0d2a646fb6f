// Copyright Isto Inc.

using UnityEngine.EventSystems;

namespace Isto.Core.UI
{
    /// <summary>
    /// This interface is based on Unity's design principles for their IEventSystemHandler. It is meant to be inherited
    /// by any isto interface that introduces extra input events on a UnityEngine.UI.Selectable object.
    /// </summary>
    public interface IAdditionalInputEventSystemHandler : IEventSystemHandler
    {
    }
}