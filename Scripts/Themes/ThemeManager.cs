// Copyright Isto Inc.
using Isto.Core.Analytics;
using Isto.Core.Data;
using Isto.Core.Skins;
using UnityEngine;
using Zenject;

namespace Isto.Core.Themes
{
    public class ThemeManager : MonoBehaviour
    {
        public ThemeDefinition CurrentTheme
        {
            get
            {
                return _currentTheme;
            }
            set
            {
                if (_currentTheme == null || _currentTheme.internalName != value.internalName)
                {
                    _currentTheme = value;
                    Events.RaiseEvent(Events.THEME_CHANGED);
                    _analytics.RegisterThemeChanged(_currentTheme.internalName);
                    ApplyCurrentTheme();
                }
            }
        }

        public PlayerSkinSet CurrentPlayerSet
        {
            get
            {
                return _currentPlayerSet;
            }
            set
            {
                if (_currentPlayerSet == null || _currentPlayerSet.skinSetInternalName != value.skinSetInternalName)
                {
                    _currentPlayerSet = value;
                    if (_currentPlayerSet.skinSetInternalName != _currentTheme?.skinSet?.player?.skinSetInternalName)
                    {
                        SetCustomTheme();
                    }
                    Events.RaiseEvent(Events.PLAYER_SKIN_SET_CHANGED);
                    ApplyCurrentPlayerSkinSet();
                }
            }
        }

        public PlayerSkinPart CurrentPlayerHat
        {
            get
            {
                return _currentPlayerHat;
            }
            set
            {
                if (_currentPlayerHat == null || _currentPlayerHat.skinPartInternalName != value.skinPartInternalName)
                {
                    _currentPlayerHat = value;
                    if (_currentPlayerHat.skinPartInternalName != _currentPlayerSet?.hat?.skinPartInternalName)
                    {
                        SetCustomPlayerSkinSet();
                    }
                    Events.RaiseEvent(Events.PLAYER_SKIN_HAT_CHANGED);
                }
            }
        }

        public PlayerSkinPart CurrentPlayerBody
        {
            get
            {
                return _currentPlayerBody;
            }
            set
            {
                if (_currentPlayerBody == null || _currentPlayerBody.skinPartInternalName != value.skinPartInternalName)
                {
                    _currentPlayerBody = value;
                    if (_currentPlayerBody.skinPartInternalName != _currentPlayerSet?.body?.skinPartInternalName)
                    {
                        SetCustomPlayerSkinSet();
                    }
                    Events.RaiseEvent(Events.PLAYER_SKIN_BODY_CHANGED);
                }
            }
        }

        public CreatureSkinSet CurrentMiniDeer
        {
            get
            {
                return _currentMiniDeer;
            }
            set
            {
                if (_currentMiniDeer == null || _currentMiniDeer.skinSetInternalName != value.skinSetInternalName)
                {
                    _currentMiniDeer = value;
                    if (_currentMiniDeer.skinSetInternalName != _currentTheme?.skinSet?.miniDeer?.skinSetInternalName)
                    {
                        SetCustomTheme();
                    }
                    Events.RaiseEvent(Events.MINIDEER_SKIN_CHANGED);
                }
            }
        }

        public CreatureSkinSet CurrentPushback
        {
            get
            {
                return _currentPushback;
            }
            set
            {
                if (_currentPushback == null || _currentPushback.skinSetInternalName != value.skinSetInternalName)
                {
                    _currentPushback = value;
                    if (_currentPushback.skinSetInternalName != _currentTheme?.skinSet?.pushback?.skinSetInternalName)
                    {
                        SetCustomTheme();
                    }
                    Events.RaiseEvent(Events.PUSHBACK_SKIN_CHANGED);
                }
            }
        }

        public ItemSkinGroup CurrentItemGroup
        {
            get
            {
                return _currentItemGroup;
            }
            set
            {
                if (_currentItemGroup == null || _currentItemGroup.skinGroupInternalName != value.skinGroupInternalName)
                {
                    _currentItemGroup = value;
                    if (_currentItemGroup.skinGroupInternalName != _currentTheme?.skinSet?.itemGroup?.skinGroupInternalName)
                    {
                        SetCustomTheme();
                    }
                    Events.RaiseEvent(Events.ADVANCED_ITEMS_SKIN_CHANGED);
                }
            }
        }

        // Cached constant references

        private ThemeDefinition _defaultTheme;

        private ThemeDefinition _customTheme;
        private PlayerSkinSet _customPlayerSkinSet;

        // Properties Cache

        private ThemeDefinition _currentTheme;

        private PlayerSkinSet _currentPlayerSet;
        private PlayerSkinPart _currentPlayerHat;
        private PlayerSkinPart _currentPlayerBody;

        private CreatureSkinSet _currentMiniDeer;
        private CreatureSkinSet _currentPushback;

        private ItemSkinGroup _currentItemGroup;

        // Injected

        private MasterSkinList _masterSkinList;
        private ThemeSetup _themeSetup;
        private IAnalyticsHandler _analytics;

        private void Awake()
        {
            _defaultTheme = _themeSetup.GetTheme(Constants.DEFAULT_THEME_NAME);
            _customTheme = _themeSetup.GetTheme(Constants.CUSTOM_THEME_NAME);
            _customPlayerSkinSet = _masterSkinList.GetPlayerSkinSet(Constants.CUSTOM_SKIN_SET_NAME);

            _currentTheme = _defaultTheme;

            Events.RaiseEvent(Events.THEME_CHANGED);
            ApplyCurrentTheme();
        }

        [Inject]
        public void Inject(MasterSkinList masterSkinList, ThemeSetup themeSetup, IAnalyticsHandler analytics)
        {
            _masterSkinList = masterSkinList;
            _themeSetup = themeSetup;
            _analytics = analytics;
        }

        private void SetCustomTheme()
        {
            CurrentTheme = _customTheme;
        }

        private void SetCustomPlayerSkinSet()
        {
            CurrentPlayerSet = _customPlayerSkinSet;
        }

        private void ApplyCurrentTheme()
        {
            if (_currentTheme.skinSet?.player != null)
                CurrentPlayerSet = _currentTheme.skinSet.player;

            if (_currentTheme.skinSet?.miniDeer != null)
                CurrentMiniDeer = _currentTheme.skinSet.miniDeer;

            if (_currentTheme.skinSet?.pushback != null)
                CurrentPushback = _currentTheme.skinSet.pushback;

            if (_currentTheme.skinSet?.itemGroup != null)
                CurrentItemGroup = _currentTheme.skinSet.itemGroup;
        }

        private void ApplyCurrentPlayerSkinSet()
        {
            if (_currentPlayerSet.hat != null)
                CurrentPlayerHat = _currentPlayerSet.hat;

            if (_currentPlayerSet.body != null)
                CurrentPlayerBody = _currentPlayerSet.body;
        }

        public CreatureSkinSet GetCurrentCreatureSkinSet(GameObject creature)
        {
            Identifier id = creature.GetComponent<Identifier>();
            if (id == null)
                return null;

            // Only mini deer are setup for now anyway
            if (id.Flag == Identifier.IdentifierFlags.MiniDeer)
                return CurrentMiniDeer;
            else
                return null;
        }

        public ThemeManagerData GetData()
        {
            // TODO: some of all of this should not be in core
            ThemeManagerData data = new ThemeManagerData();
            data.themeInternalName = CurrentTheme?.internalName;
            data.playerSetInternalName = CurrentPlayerSet?.skinSetInternalName;
            data.playerHatInternalName = CurrentPlayerHat?.skinPartInternalName;
            data.playerBodyInternalName = CurrentPlayerBody?.skinPartInternalName;
            data.miniDeerInternalName = CurrentMiniDeer?.skinSetInternalName;
            data.pushbackInternalName = CurrentPushback?.skinSetInternalName;
            data.itemGroupInternalName = CurrentItemGroup?.skinGroupInternalName;
            return data;
        }

        public void SetData(ThemeManagerData data)
        {
            // We're not loading themes or skins for now
        }
    }
}