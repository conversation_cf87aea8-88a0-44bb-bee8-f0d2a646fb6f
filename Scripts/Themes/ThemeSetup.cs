// Copyright Isto Inc.
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Themes
{
    /// <summary>
    /// Scriptable object that holds the parameters for default menus/skins/sprites to show
    /// </summary>
    [CreateAssetMenu(fileName = "Scriptables/New Themes Data", menuName = "Scriptables/New Themes Data")]
    public class ThemeSetup : ScriptableObject
    {
        // Public Variables

        public List<ThemeDefinition> themes;

        public ThemeDefinition GetTheme(string targetName)
        {
            ThemeDefinition theme = null;
            foreach (ThemeDefinition definition in themes)
            {
                if (definition.internalName.Equals(targetName, System.StringComparison.OrdinalIgnoreCase))
                {
                    theme = definition;
                    break;
                }
            }
            return theme;
        }
    }
}