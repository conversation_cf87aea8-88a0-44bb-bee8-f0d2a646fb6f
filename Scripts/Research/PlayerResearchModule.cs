// Copyright Isto Inc.
using FMODUnity;
using I2.Loc;
using Isto.Core.Analytics;
using Isto.Core.Audio;
using Isto.Core.Automation;
using Isto.Core.Beings;
using Isto.Core.Data;
using Isto.Core.Game;
using Isto.Core.Inputs;
using Isto.Core.Items;
using Isto.Core.Localization;
using Isto.Core.Power;
using Isto.Core.UI;
using System;
using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.Research
{
    /// <summary>
    /// Class for controlling Research Module Building.  Monitors attached ItemContainer for changes and checks
    /// if research is completed on current research item. 
    /// </summary>
    public class PlayerResearchModule : InteractableBuilding, IResearcher, IPoweredBuilding
    {
        // UNITY HOOKUP

        [FormerlySerializedAs("itemPlacementPrefab")]
        [SerializeField] private GameObject _itemPlacementPrefab = default;
        [FormerlySerializedAs("itemPlacementPosition")]
        [SerializeField] private Transform _itemPlacementPosition = default;

        [SerializeField] private List<ResearchItem> _startingResearchItems = new List<ResearchItem>();
        [SerializeField] private Animator _animator = null;
        [SerializeField] private Transform _interactionTarget = default;
        [SerializeField] private BuildingLevel _buildingLevel = default;
        [SerializeField] [EventRef] private string _researchCompleteSFX;
        [SerializeField] [EventRef] private string _researchRunningSFXLoop;
        [SerializeField] [EventRef] private string _noPowerOnInteractionSFX;

        [Header("Power")]
        [SerializeField] private TetheredBuilding _buildingPower;

        [Header("Info Messages")]
        [SerializeField] private LocalizedString _researchCompleteMessageKey;
        [SerializeField] private float _researchCompleteTimeBetweenMessages = 30f;

        [Header("Boosters")]
        [SerializeField] private TextMeshProUGUI _boosterFactorTextbox;

        [Header("Progress")]
        [SerializeField] private Image _progressSlider = default;
        [FormerlySerializedAs("progressContainer")]
        [SerializeField] private GameObject _progressContainer = default;
        [SerializeField] private Canvas _progressCanvas;
        [SerializeField] private float _additionalInteractionRange = 1; //custom just to the player research menu


        // OTHER FIELDS

        private bool _buildingOpened = false;

        private List<ResearchItem> _researchedItems = new List<ResearchItem>();
        private GameObject _itemPlacementObject;

        private List<AutomationResearchBooster> _boosters = new List<AutomationResearchBooster>();
        private float _previousBoost;

        private float _researchTimer;
        private bool _researchCompleteMessageDisplayed;

        private ResearchItem _currentResearch;
        private float _currentBoostFactor;


        // PROPERTIES

        public BuildingLevel BuildingLevel => _buildingLevel;
        public float CurrentBoostFactor => _currentBoostFactor;
        public bool IsResearching => CurrentResearch != null; // Stays "researching" until research dialog is shown
        public bool ResearchComplete => CurrentResearch != null && _researchTimer >= CurrentResearchResearchTime;
        public ResearchItem CurrentResearch => _currentResearch;
        public float CurrentResearchResearchTime => ResearchTimeOverride != null ? ResearchTimeOverride.Value
                                                                                 : CurrentResearch.researchTime;
        public float? ResearchTimeOverride { private get; set; }


        // EVENTS

        public event Action<ResearchItem> OnResearchComplete;
        public event Action OnBuildingUpgraded;


        // INJECTION

        private IUIGameMenu _mainMenu;
        private IGameSounds _sounds;
        private IAnalyticsHandler _analytics;
        private IUIMessages _uIMessages;
        private PlayerUpgrades _playerUpgrades;
        private PlayerProgress _playerProgress;

        [Inject]
        public void Inject(IUIGameMenu mainMenu, IGameSounds gameSounds, IAnalyticsHandler analytics,
            [Inject(Id = UIMessageHandlerType.HighPriorityMessage)] IUIMessages uIMessages,
            [InjectOptional] PlayerUpgrades playerUpgrades, [InjectOptional] PlayerProgress playerProgress)
        {
            _mainMenu = mainMenu;
            _sounds = gameSounds;
            _analytics = analytics;
            _uIMessages = uIMessages;

            _playerProgress = playerProgress;

            
            if(_playerUpgrades != null)
            {
                _playerUpgrades = playerUpgrades;
            }
            else
            {
                _playerUpgrades = this.gameObject.GetComponent<PlayerUpgrades>();
            }
        }


        // LIFECYCLE EVENTS

        protected override void Awake()
        {
            _actionList.Clear();
            _actionList.Add(UserActions.INTERACT);

            base.Awake();
        }

        private void Start()
        {
            // This is not the normal flow (in which I believe Start will run before the data is setup and as such starting upgrades are not detected)
            // But when starting from scene, I think the normal flow (OnDataLoadComplete) is not happening, so we need the logic to be here.
            if (!GameState.LoadingFromSave)
            {
                foreach (UpgradeItem upgrade in _playerUpgrades.GetActiveUpgrades())
                {
                    ResearchItem research = _masterItemList.researchItems.Where(x => x.upgradeUnlocks.Any(y => y.itemID == upgrade.itemID)).FirstOrDefault();
                    if (research != null)
                    {
                        _researchedItems.Add(research);
                    }
                }
            }

        }

        protected override void Update()
        {
            _previousBoost = CurrentBoostFactor;
            _currentBoostFactor = GetBoostFactor();

            if (_previousBoost != CurrentBoostFactor)
                UpdateBoostFactorText();

            if (_buildingPower != null && !_buildingPower.powered)
                return;

            if (CurrentResearch == null)
                return;

            _researchTimer += Time.deltaTime * CurrentBoostFactor;

            _progressSlider.fillAmount = GetCurrentResearchPercent();

            if (ResearchComplete && !_researchCompleteMessageDisplayed)
            {
                _researchCompleteMessageDisplayed = true;

                InvokeRepeating(nameof(ShowResearchCompleteMessageRepeating), 0.1f, _researchCompleteTimeBetweenMessages);

                _sounds.PlayOneShot(_researchCompleteSFX, transform.position);

                if(_buildingDialogue != null)
                {
                    _buildingDialogue.ShowAlertMessage(Loc.Get(_researchCompleteMessageKey));
                }

                HideProgressVisuals();
            }
        }

        public override void OnDataLoadComplete()
        {
            // This is the normal flow to initialize the starting research when coming from title menu
            if (!GameState.LoadingFromSave)
            {
                foreach (UpgradeItem upgrade in _playerUpgrades.GetActiveUpgrades())
                {
                    ResearchItem research = _masterItemList.researchItems.Where(x => x.upgradeUnlocks.Any(y => y.itemID == upgrade.itemID)).FirstOrDefault();
                    if (research != null)
                    {
                        _researchedItems.Add(research);
                    }
                }

            }

            if (_buildingOn)
            {
                _animator.SetBool("On", true);

                if (!_buildingOpened)
                {
                    ShowTutorialArrow();
                }
            }
        }


        // OTHER METHODS

        private void UpdateBoostFactorText()
        {
            _boosterFactorTextbox?.SetText(CurrentBoostFactor > 1 ? $"+{CurrentBoostFactor - 1}x" : "");
        }

        public void LoadData(PlayerResearchSaveData data)
        {
            if (GameState.LoadingFromSave)
            {
                _buildingOn = data.moduleOnline;
                _buildingOpened = data.moduleInspected;
            }

            BuildingLevel.SetStartLevel(data.buildingLevel);
            OnBuildingUpgraded?.Invoke();

            _currentResearch = string.IsNullOrEmpty(data.currentResearchItemID) ? null : _masterItemList.GetResearchItemByID(data.currentResearchItemID, "");

            _researchTimer = CurrentResearch == null ? 0 : data.currentResearchedPercent * CurrentResearch.researchTime;

            for (int i = 0; i < data.researchedItemsIds.Count; i++)
            {
                ResearchItem item = _masterItemList.GetResearchItemByID(data.researchedItemsIds[i], "");

                if (item != null)
                {
                    _researchedItems.Add(item);
                }
            }

            if (CurrentResearch != null)
            {
                _animator.SetBool("researching", true);

                SetInteractive(true);
                ShowProgressVisuals();
            }
        }

        public PlayerResearchSaveData GetResearchSaveData()
        {
            PlayerResearchSaveData data = new PlayerResearchSaveData();

            data.currentResearchItemID = CurrentResearch != null ? CurrentResearch.ID : "";
            data.currentResearchedPercent = GetCurrentResearchPercent();
            data.moduleOnline = _buildingOn;
            data.moduleInspected = _buildingOpened;
            data.buildingLevel = BuildingLevel.Level;

            for (int i = 0; i < _researchedItems.Count; i++)
            {
                data.researchedItemsIds.Add(_researchedItems[i].ID);
            }

            return data;
        }

        // Override Methods		

        public override void PlayerInteraction(AutomationPlayerController player, UserActions action)
        {
            float distance = Vector3.Distance(player.transform.position, GetInteractionPosition(player.transform.position));

            if (distance < player.InteractionRange + _additionalInteractionRange /*&& _heartboxController.CurrentState.stateID >= enabledInState*/)
            {
                if (_buildingPower.powered)
                {
                    if (_itemPlacementObject != null)
                    {
                        Destroy(_itemPlacementObject);
                        _buildingOpened = true;
                    }

                    _mainMenu.OpenMenu(GameMenusEnum.PLAYER_RESEARCH);

                }
                else
                {
                    _sounds.PlayOneShot(_noPowerOnInteractionSFX, transform.position);
                }
            }
            else
            {
                player.SetMoveToAndInteractWithItem(this, 1.5f + _additionalInteractionRange);
            }
        }

        public override Vector3 GetInteractionPosition(Vector3 playerPosition)
        {
            return _interactionTarget.position;
        }

        public override string GetItemName()
        {
            return Loc.GetString(Constants.BUILDING_NAME_RESEARCHMODULE);
        }

        [ContextMenu("Trigger Off to On animation")]
        public void TurnOn()
        {
            if (_buildingOn)
                return;

            _buildingOn = true;
            _animator.SetTrigger("offToOn");

            if (!_buildingOpened)
            {
                ShowTutorialArrow();
            }

            BuildingTurnedOn?.Invoke(this, EventArgs.Empty);
        }

        public void TurnOff()
        {
            _animator.SetBool("On", false);
            _buildingOn = false;
        }

        private void ShowTutorialArrow()
        {
            if (_itemPlacementObject == null)
            {
                _itemPlacementObject = Instantiate(_itemPlacementPrefab, _itemPlacementPosition.position, Quaternion.identity, transform);
            }
        }

        public override string GetToolTip(UserActions action)
        {
            return Loc.GetString(Constants.BUILDING_NAME_RESEARCHMODULE);
        }

        public override List<UserActions> GetValidActions()
        {
            return _actionList;
        }

        // Public Methods

        public bool HasItemBeenResearched(ResearchItem researchItem)
        {
            for (int i = 0; i < _researchedItems.Count; i++)
            {
                if (_researchedItems[i].ID.Equals(researchItem.ID))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Starts the research process
        /// </summary>
        /// <param name="researchItem"></param>
        public void SetResearchItem(ResearchItem researchItem)
        {
            if (HasItemBeenResearched(researchItem))
            {
                Debug.LogError($"Trying to research something already been researched.  ItemID:{researchItem.ID}");
                return;
            }

            _currentResearch = researchItem;
            _researchTimer = 0f;

            _animator.SetBool("researching", true);
            _sounds.PlayLoop(_researchRunningSFXLoop, transform.position);
            _researchCompleteMessageDisplayed = false;
        }

        /// <summary>
        /// Returns Percentage as a value between 0 and 1
        /// </summary>
        /// <returns></returns>
        public float GetCurrentResearchPercent()
        {
            if (CurrentResearch == null)
                return 0;

            return Mathf.Clamp01(_researchTimer / CurrentResearchResearchTime);
        }

        public void CancelResearch()
        {
            _currentResearch = null;
            _animator.SetBool("researching", false);

            HideProgressVisuals();

            _sounds.StopLoop(_researchRunningSFXLoop);
            _researchCompleteMessageDisplayed = false;
        }

        public void CompleteResearch()
        {
            CancelInvoke(nameof(ShowResearchCompleteMessageRepeating));

            if (CurrentResearch == null)
            {
                Debug.LogWarning("Trying to complete research that has not been set.");
                return;
            }

            _analytics.RegisterResearchUnlocked(CurrentResearch.ID);

            ActivateResearchItems(CurrentResearch);
            ActivateResearchUpgrades(CurrentResearch);

            _researchedItems.Add(CurrentResearch);

            OnResearchComplete?.Invoke(CurrentResearch);
            Events.RaiseEvent(Events.RESEARCH_COMPLETE_EVENT);

            _currentResearch = null;

            _animator.SetBool("researching", false);
            _sounds.StopLoop(_researchRunningSFXLoop);

            if (_buildingDialogue != null)
            {
                _buildingDialogue.HideAlertMessage();
            }
            
            HideProgressVisuals();
        }

        public void CompleteAllResearch()
        {
            DeleteAllResearch();

            for (int i = 0; i < _masterItemList.researchItems.Count; i++)
            {
                ResearchItem item = _masterItemList.researchItems[i];
                ActivateResearchItems(item);
                ActivateResearchUpgrades(item);
                _researchedItems.Add(item);
                OnResearchComplete?.Invoke(item);
            }

            for (int i = 0; i < 3; i++)
            {
                _buildingLevel.IncreaseLevel();
            }

            Events.RaiseEvent(Events.RESEARCH_COMPLETE_EVENT);
        }

        public void DeleteAllResearch()
        {
            if (CurrentResearch != null)
            {
                CompleteResearch();
            }

            _playerProgress.UnlockedItems.Clear();
            _playerProgress.UnlockStartingItemSet();

            _playerUpgrades.UninstallAllUpgrades();
            _playerUpgrades.InstallStartingUpgrades();

            _researchedItems.Clear();
        }

        /// <summary>
        /// Reduces the time remaining for research by amount
        /// </summary>
        /// <param name="amount"></param>
        public void BoostResearch(float amount)
        {
            _researchTimer += amount;
        }

        public void AddBooster(AutomationResearchBooster booster)
        {
            if (!_boosters.Contains(booster))
            {
                _boosters.Add(booster);
            }
        }

        public void RemoveBooster(AutomationResearchBooster booster)
        {
            _boosters.Remove(booster);
        }

        private float GetBoostFactor()
        {
            float factor = 1;

            for (int i = 0; i < _boosters.Count; i++)
            {
                factor += _boosters[i].BoostFactor;
            }

            return factor;
        }

        // Info Messages

        private void ShowResearchCompleteMessageRepeating()
        {
            _uIMessages.CreateHighPriorityMsg(Loc.Get(_researchCompleteMessageKey), HighPriorityMessage.MessageType.success, Constants.DEFAULT_INFO_MESSAGE_LONG_DISPLAY_TIME);
        }

        public void UpgradeModule()
        {
            _buildingLevel.IncreaseLevel();
            OnBuildingUpgraded?.Invoke();
            _analytics.RegisterResearchCenterLevelUp(_buildingLevel.Level);
        }

        public void HideProgressVisuals()
        {
            _progressCanvas.enabled = false;
            _progressContainer.SetActive(false);
        }

        public void ShowProgressVisuals()
        {
            if (CurrentResearch != null && !ResearchComplete)
            {
                _progressCanvas.enabled = true;
                _progressContainer.SetActive(true);
            }
        }

        // Internal Methods

        private void ActivateResearchItems(ResearchItem researched)
        {
            for (int i = 0; i < researched.itemUnlocks.Count; i++)
            {
                _playerProgress.UnlockItem(researched.itemUnlocks[i]);
            }
        }

        private void ActivateResearchUpgrades(ResearchItem researched)
        {
            for (int i = 0; i < researched.upgradeUnlocks.Count; i++)
            {
                _playerUpgrades.InstallUpgrade(researched.upgradeUnlocks[i]);
            }
        }
    }
}