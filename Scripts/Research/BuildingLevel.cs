// Copyright Isto Inc.
using Isto.Core.Items;
using UnityEngine;

namespace Isto.Core.Research
{
    public class BuildingLevel : MonoBehaviour
    {
        // Public Variables

        public int Level { get; private set; }
        public Recipe[] UpgradeRecipies => _upgradeRecipes;

        // Private Variables

        [SerializeField] private Recipe[] _upgradeRecipes = default;

        // Methods		

        private void Awake()
        {
            Level = 1;
        }

        /// <summary>
        /// Sets the starting level for the building.  Will set to 1 if zero passed in, otherwise sets to the level passed in.
        /// </summary>
        /// <param name="level"></param>
        public void SetStartLevel(int level)
        {
            Level = level <= 0 ? 1 : level;
        }

        public void IncreaseLevel()
        {
            Level++;
        }

        /// <summary>
        /// Trys to get the current recipe required to upgrade the building if one exists.
        /// </summary>
        /// <param name="recipe">If found will contain recipe, null otherwise.</param>
        /// <returns>True if recipe found, false otherwise.</returns>
        public bool TryGetUpgradeRecipe(out Recipe recipe)
        {
            if (Level <= _upgradeRecipes.Length)
            {
                recipe = _upgradeRecipes[Level - 1];
                return true;
            }

            recipe = null;
            return false;
        }

        public bool CanUpgrade(ItemContainer inventory)
        {
            if (TryGetUpgradeRecipe(out Recipe recipe))
            {
                return Recipe.CanMakeRecipe(recipe, inventory);
            }

            return false;
        }
    }
}