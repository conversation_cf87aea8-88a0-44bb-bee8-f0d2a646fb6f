// Copyright Isto Inc.
using Isto.Core.Items;
using UnityEngine;

namespace Isto.Core.Research
{
    public class ResearchBoosterValidPositions : MonoBehaviour
    {
        // Public Variables

        public int xRadius = 5;
        public int zRadius = 5;

        public bool showOnEnable = false;

        [SerializeField] private AdvancedItem _boosterItem;
        [SerializeField] private LineRenderer _borderLine;

        public void OnEnable()
        {
            RecalculatePoints();

            if (showOnEnable)
                ShowBorder();
        }

        public void OnDisable()
        {
            if (showOnEnable)
                HideBorder();
        }

        public void RecalculatePoints()
        {
            Vector3[] linePoints = new Vector3[4]
            {
                new Vector3(-xRadius, 0, -zRadius),
                new Vector3(-xRadius, 0, zRadius),
                new Vector3(xRadius, 0, zRadius),
                new Vector3(xRadius, 0, -zRadius),
            };

            _borderLine.positionCount = 4;
            _borderLine.SetPositions(linePoints);
        }

        public bool IsValidPosition(Vector3 position)
        {
            Vector3 basePosition = transform.position.GetSnappedPosition(1);

            if (position.x <= basePosition.x + xRadius && position.x > basePosition.x - xRadius + 2)
            {
                if (position.z < basePosition.z + zRadius - 1 && position.z >= basePosition.z - zRadius)
                    return true;
            }

            return false;
        }

        public void ShowBorder()
        {

            _borderLine.enabled = true;
        }

        public void HideBorder()
        {

            _borderLine.enabled = false;
        }

        // Gizmos

        public Color validPlaceColor = Color.black;

        public void OnDrawGizmosSelected()
        {
            Vector3 basePosition = transform.position.GetSnappedPosition(1) + Vector3.up * 0.05f;
            Vector3 offset = new Vector3(xRadius, 0, zRadius);

            Gizmos.color = validPlaceColor;
            Gizmos.DrawCube(basePosition, offset * 2);
        }
    }
}