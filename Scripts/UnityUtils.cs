// Copyright Isto Inc.
#if UNITY_EDITOR
//using Isto.Core.Tests;
#endif

using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.SceneManagement;
using Random = UnityEngine.Random;

namespace Isto.Core
{
    /// <summary>
    /// Helpers methods for common operations we perform in code
    /// </summary>
    public static class UnityUtils
    {
        /// <summary>
        /// Iterates over all the colliders attached to the gameobject and returns the point closest to the
        /// sourcePosition that is on a collider on the valid layers.
        /// </summary>
        /// <param name="target">Object to find closest position on.</param>
        /// <param name="sourcePosition">Position away from object.</param>
        /// <param name="validLayers">Valid layers that the colliders are on.</param>
        /// <returns>Vector3 position that is on a collider attached to the target nearest the source position.</returns>
        public static Vector3 GetNearestPositionOnCollider(GameObject target, Vector3 sourcePosition, LayerMask validLayers)
        {
            List<Collider> targetColliders = new List<Collider>();

            targetColliders.AddRange(target.GetComponents<Collider>());
            targetColliders.AddRange(target.GetComponentsInChildren<Collider>());

            // Filter the list to only colliders on the valid layers
            targetColliders = targetColliders.Where(x => validLayers.Contains(x.gameObject.layer)).ToList();

            // If there are no colliders, just return the objects position (shouldn't really happen though)
            if (targetColliders.Count == 0)
            {
                Debug.LogWarning("Looking for colliders on item without any. Item: " + target.name);
                return target.transform.position;
            }

            // Set first collider as closest
            Vector3 nearestPoint = targetColliders[0].ClosestPoint(sourcePosition);
            float closestDistance = Vector3.Distance(sourcePosition, nearestPoint);

            // Check any other colliders to see if they are closer to the source
            for (int i = 1; i < targetColliders.Count; i++)
            {
                Vector3 closestPointOnCollider = targetColliders[i].ClosestPoint(sourcePosition);
                float nextDistance = Vector3.Distance(sourcePosition, closestPointOnCollider);

                if (nextDistance < closestDistance)
                {
                    nearestPoint = closestPointOnCollider;
                    closestDistance = nextDistance;
                }
            }

            // Setting to zero to get point on ground
            nearestPoint.y = 0f;

            return nearestPoint;
        }

        /// <summary>
        /// Takes a buffer of colliders from a physics check and finds the closest one from the position
        /// passed in.  Checks distance based on the gameobject attached to the hit collider.
        /// </summary>
        /// <param name="hitBuffer">Buffer containing the hit objects</param>
        /// <param name="hitCount">The number of hit objects in the buffer</param>
        /// <param name="fromPosition">Position to check distance from</param>
        /// <returns>Closest GameObject to position passed in.</returns>
        public static GameObject GetClosestHitObject(Collider[] hitBuffer, int hitCount, Vector3 fromPosition)
        {
            Collider closest = hitBuffer[0];
            float closestDistance = Vector3.Distance(closest.transform.position, fromPosition);

            //Find the closest item
            for (int i = 1; i < hitCount; i++)
            {
                float currentDistance = Vector3.Distance(hitBuffer[i].transform.position, fromPosition);

                if (currentDistance < closestDistance)
                {
                    closestDistance = currentDistance;
                    closest = hitBuffer[i];
                }
            }

            return closest.gameObject;
        }

        /// <summary>
        /// Takes a buffer of colliders and finds the closest object that also has the specified component attached
        /// to it's parent gameobject.
        /// </summary>
        /// <typeparam name="T">Required Component on parent GameObject</typeparam>
        /// <param name="hitBuffer">Buffer containing the hit objects</param>
        /// <param name="hitCount">The number of hit objects in the buffer</param>
        /// <param name="fromPosition">Position to check distance from</param>
        /// <returns>Closest GameObject to position passed in with the specified component, null if none was found.</returns>
        public static GameObject GetClosestHitObject<T>(Collider[] hitBuffer, int hitCount, Vector3 fromPosition)
        {
            Collider closest = null;
            float closestDistance = float.PositiveInfinity;

            //Find the closest item
            for (int i = 0; i < hitCount; i++)
            {
                float currentDistance = Vector3.Distance(hitBuffer[i].transform.position, fromPosition);

                bool hasComponent = hitBuffer[i].GetComponentInParent<T>() != null;

                if (currentDistance < closestDistance && hasComponent)
                {
                    closestDistance = currentDistance;
                    closest = hitBuffer[i];
                }
            }

            return closest == null ? null : closest.gameObject;
        }

        /// <summary>
        /// Gets if the main object is to the left or right of the other object
        /// </summary>
        /// <param name="main"></param>
        /// <param name="other"></param>
        /// <returns>-1 if main is to the left of other, 1 if to the right.</returns>
        public static float GetRelativeHorizontalDirection(Vector3 main, Vector3 other)
        {
            Vector3 normal = Vector3.up;
            Vector3 forward = new Vector3(-1, 0, 1); // Constants.FORWARD
            float sign = Mathf.Sign(Vector3.Dot(normal, Vector3.Cross(forward, main - other)));

            return sign;
        }

        /// <summary>
        /// Transforms a world point to a Canvas Point in screen space
        /// </summary>
        /// <param name="worldPoint"></param>
        /// <param name="canvas"></param>
        /// <returns></returns>
        public static Vector2 TransformWorldToCanvasPoint(Vector3 worldPoint, Canvas canvas)
        {
            Vector2 screenPoint = RectTransformUtility.WorldToScreenPoint(Camera.main, worldPoint);

            Vector2 localRectPoint;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                canvas.transform as RectTransform,
                screenPoint,
                canvas.worldCamera,
                out localRectPoint);

            return canvas.transform.TransformPoint(localRectPoint);
        }

        /// <summary>
        /// Moves the camera along the camera Z-Axis the amount specified in world units.
        /// </summary>
        /// <param name="amount">World units to move the camera</param>
        /// <param name="time">Time for move to complete.</param>
        /// <returns></returns>
        public static IEnumerator ZoomCamera(float amount, float time)
        {
            Transform camTransform = Camera.main.transform;

            Vector3 startPosition = camTransform.localPosition;

            float timer = 0f;

            while (timer < time)
            {
                timer += Time.deltaTime;

                float nextDistance = Mathf.Lerp(0, amount, timer / time);

                camTransform.localPosition = startPosition + camTransform.forward * nextDistance;

                yield return null;
            }
        }

        /// <summary>
        /// Generates a random Vector3 in the x,z plane with max magnitude of size parameter.
        /// </summary>
        /// <param name="size">Maximum magnitude of random vector</param>
        /// <returns></returns>
        public static Vector3 RandomXZVector(float size)
        {
            float sizeFactor = Random.Range(-size / 2, size / 2);
            Vector3 position = Random.insideUnitSphere * sizeFactor;
            position.y = 0f;

            return position;
        }

        public static Vector3 RandomXZVectorLinearDistribution(float size)
        {
            float sizeFactor = RandomFromDistribution.RandomRangeLinear(-size, size, 1);
            Vector3 position = Random.insideUnitSphere * sizeFactor;
            position.y = 0f;

            return position;
        }

        /// <summary>
        /// Lerps the intensity value of the light from it's current value to the target value over the specified time frame.
        /// </summary>
        /// <param name="light">Light to update.</param>
        /// <param name="targetIntensity">Final light intensity after lerp.</param>
        /// <param name="lerpTime">Time to transition to target intensity.</param>
        /// <returns></returns>
        public static IEnumerator LerpLightIntensity(Light light, float targetIntensity, float lerpTime)
        {
            float timer = 0f;
            float startIntensity = light.intensity;

            while (timer < lerpTime)
            {
                timer += Time.deltaTime;

                light.intensity = Mathf.Lerp(startIntensity, targetIntensity, timer / lerpTime);

                yield return null;
            }
        }

        /// <summary>
        /// Converts a screen space or input axis direction to an isometric vector3 in world space.
        /// </summary>
        /// <param name="horizontal">Horizontal movement in screen space.</param>
        /// <param name="vertical">Vertical movement in screen space.</param>
        /// <returns></returns>
        public static Vector3 ConvertScreenVectorToIsometric(float horizontal, float vertical)
        {
            Vector3 isoDirection = new Vector3();

            isoDirection.x += (horizontal / 2);
            isoDirection.z += (horizontal / 2);

            isoDirection.x -= (vertical / 2);
            isoDirection.z += (vertical / 2);

            return isoDirection;
        }

        /// <summary>
        /// Gets a random world position along the screen edge.  Uses a raycast against the ground layers to find world position.
        /// </summary>
        /// <param name="insideOffset">How far inwards from the edge should the random positions be.</param>
        /// <returns>Random world position near the edge of the screen.</returns>
        public static Vector3 GetRandomWorldPositionNearScreenEdge(float insideOffset)
        {
            Vector2 randomPos = GetRandomViewPortPositionNearEdge(insideOffset);

            return GetViewPortPositionOnGround(randomPos);
        }

        public static Vector3 GetViewPortPositionOnGround(Vector2 viewportPos)
        {
            Ray rayToPosition = Camera.main.ViewportPointToRay(new Vector3(viewportPos.x, viewportPos.y, 0f));

            Vector3 result = Vector3.zero;

            if (Physics.Raycast(rayToPosition, out RaycastHit raycastHit, Layers.GROUND_LAYERS_MASK))
            {
                result = raycastHit.point;
                result.y = 0f;

                Vector3 forward = new Vector3(-1, 0, 1); // Constants.FORWARD

                // Shifting forward to account for camera tilt
                result -= forward * 2;

                return result;
            }
            else
            {
                Debug.LogError("Looking for teleport position could not find ground with raycast.");
                return result;
            }
        }

        /// <summary>
        /// Gets a random screen space position near the edge of the screen.
        /// </summary>
        /// <param name="insideOffset"></param>
        /// <param name="normalDistribution">If set, will use a normal distribution (bell curve) instead of linear distribution (default).</param>
        /// <returns></returns>
        public static Vector2 GetRandomViewPortPositionNearEdge(float insideOffset, bool normalDistribution = false)
        {
            // Choose randomly which edge of screen to spawn on, vertical or hoizontal
            bool verticalEdge = Random.value > 0.5f;

            float posX;
            float posY;

            if (verticalEdge)
            {
                posX = Random.value > 0.5f ? 1 - insideOffset : insideOffset;

                if (normalDistribution)
                    posY = RandomFromDistribution.RandomRangeNormalDistribution(insideOffset, 1 - insideOffset, RandomFromDistribution.ConfidenceLevel_e._99);
                else
                    posY = Random.Range(insideOffset, 1 - insideOffset);
            }
            else
            {
                posY = Random.value > 0.5f ? 1 - insideOffset : insideOffset;

                if (normalDistribution)
                    posX = RandomFromDistribution.RandomRangeNormalDistribution(insideOffset, 1 - insideOffset, RandomFromDistribution.ConfidenceLevel_e._99);
                else
                    posX = Random.Range(insideOffset, 1 - insideOffset);
            }

            return new Vector2(posX, posY);
        }

        /// <summary>
        /// Checks if a world position is currently on screen by converting it to ViewPort space and checking if it's in the correct range
        /// </summary>
        /// <param name="worldPosition"></param>
        /// <returns></returns>
        public static bool IsWorldPositionOnScreen(Vector3 worldPosition, Camera camera)
        {
            Vector2 viewPortPosition = camera.WorldToViewportPoint(worldPosition);

            // If position outside of view port space, it is off screen so return false.
            return !(viewPortPosition.x < 0 || viewPortPosition.x > 1 || viewPortPosition.y < 0 || viewPortPosition.y > 1);
        }

        /// <summary>
        /// Calculates the range of a light adjusted to ground level.  Determines the radius of the circle at ground level
        /// that intersects the sphere of the light.
        /// </summary>
        /// <param name="light">Light to calculate range.</param>
        /// <returns>The radius of the light range at y=0.</returns>
        public static float GetLightRangeAtGroundLevel(Light light)
        {
            return UnityUtils.CalculateLightRangeAtGroundLevel(light.range, light.transform.position.y);
        }

        /// <summary>
        /// Similar to GetLightRangeAtGroundLevel but just takes the required values as parameters to be more efficient than
        /// passing in the Light
        /// </summary>
        /// <param name="range"></param>
        /// <param name="height"></param>
        /// <returns></returns>
        public static float CalculateLightRangeAtGroundLevel(float range, float height)
        {
            // Adding bonus 1f to range to handle the light fall off on edge of light circle
            return Mathf.Sqrt((range * range) - (height * height)) + 1f;
        }

        /// <summary>
        /// Converts strings to Vector3 and supports at least the format of unity's Vector3.ToString()
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static bool TryParseToVector3(string input, out Vector3 result)
        {
            bool success = true;
            result = Vector3.zero;

            if (input.StartsWith("(") && input.EndsWith(")"))
            {
                input = input.Substring(1, input.Length - 2);
            }

            string[] components = input.Split(',');

            success &= (components.Length == 3);
            if (success)
            {
                success &= float.TryParse(components[0], out float x);
                success &= float.TryParse(components[1], out float y);
                success &= float.TryParse(components[2], out float z);

                if (success)
                {
                    result = new Vector3(x, y, z);
                }
            }

            return success;
        }

        /// <summary>
        /// Finds the closest direction along the x or z axis.
        /// </summary>
        /// <param name="direction"></param>
        /// <returns>Normalized Vector3 pointing in the nearest direction along the x or z axis.</returns>
        public static Vector3 GetNearestAxisDirection(Vector3 direction)
        {
            return GetNearestAxisDirection(_directions, direction);
        }

        private static List<Vector3> _directions = new List<Vector3>() { Vector3.back, Vector3.forward, Vector3.left, Vector3.right };

        private static Vector3 backLeft = (Vector3.back + Vector3.left).normalized;
        private static Vector3 backRight = (Vector3.back + Vector3.right).normalized;
        private static Vector3 frontLeft = (Vector3.forward + Vector3.left).normalized;
        private static Vector3 frontRight = (Vector3.forward + Vector3.right).normalized;

        private static List<Vector3> _octoDirections = new List<Vector3>() { Vector3.back, backLeft, backRight, Vector3.forward, frontLeft, frontRight, Vector3.left, Vector3.right };

        public static Vector3 GetNearestOctoDirection(Vector3 direction)
        {
            List<Vector3> directions = new List<Vector3>();
            directions.Add(Vector3.back);
            directions.Add(backLeft);
            directions.Add(backRight);
            directions.Add(Vector3.forward);
            directions.Add(frontLeft);
            directions.Add(frontRight);
            directions.Add(Vector3.left);
            directions.Add(Vector3.right);

            return GetNearestAxisDirection(_octoDirections, direction);
        }

        /// <summary>
        /// Finds the closest direction along the x or y axis.
        /// </summary>
        /// <param name="direction"></param>
        /// <returns>Normalized Vector3 pointing in the nearest direction along the x or y axis.</returns>
        public static Vector3 GetNearestScreenSpaceAxisDirection(Vector3 direction)
        {
            return GetNearestAxisDirection(_directions, direction);
        }

        /// <summary>
        /// Finds the closest axis direction among any supplied axis.
        /// </summary>
        /// <param name="sourceDirection"></param>
        /// <returns>Normalized Vector3 pointing along the nearest axis.</returns>
        public static Vector3 GetNearestAxisDirection(List<Vector3> axisDirections, Vector3 sourceDirection)
        {
            if (sourceDirection == Vector3.zero)
                return Vector3.zero;

            float longestVector = 0;
            int axisIndex = 0;

            for (int i = 0; i < axisDirections.Count; i++)
            {
                float length = Vector3.Angle(sourceDirection, axisDirections[i]) < 90 ? Vector3.Project(sourceDirection, axisDirections[i]).magnitude : 0f;

                if (longestVector < length)
                {
                    longestVector = length;
                    axisIndex = i;
                }
            }

            return axisDirections[axisIndex];
        }

        public static void SetAllChildAnimatorsState(GameObject parent, bool enabled)
        {
            Animator[] animators = parent.GetComponentsInChildren<Animator>();

            for (int i = 0; i < animators.Length; i++)
            {
                animators[i].enabled = enabled;
            }
        }

        public static void SetCornersForSearchArea(Vector3 center, float radius, out Vector3Int topLeft, out Vector3Int botRight)
        {
            Vector3 topLeftFloat = new Vector3(center.x - radius, 0f, center.z + radius);
            Vector3 botRightFloat = new Vector3(center.x + radius, 0f, center.z - radius);

            topLeft = Vector3Int.RoundToInt(topLeftFloat);
            botRight = Vector3Int.RoundToInt(botRightFloat);
        }

        // Gizmos and Handles

        /// <summary>
        /// Using Unity's Handles class, draws a flat circle of radius specified.  If a string is passed for the label, will also draw the
        /// label offset above the position.
        /// </summary>
        /// <param name="position">Center position in World space to draw the circle</param>
        /// <param name="radius">Radius of the circle</param>
        /// <param name="ringColor">Solid color to use for ring of the circle and the label text.</param>
        /// <param name="fillColor">Fill color for the circle with alpha used.</param>
        /// <param name="label">String to use for label, if empty string passed in, no label will be drawn.</param>
        /// <param name="labelOffset">Distance above the center position to draw the label.</param>
        public static void DrawCircleRadius(Vector3 position, float radius, Color ringColor, Color fillColor, string label, float labelOffset = 4f)
        {
#if UNITY_EDITOR
            UnityEditor.Handles.zTest = UnityEngine.Rendering.CompareFunction.LessEqual;

            // Draw handle label
            if (!string.IsNullOrEmpty(label))
            {
                GUIStyle textStyle = new GUIStyle();
                textStyle.normal.textColor = ringColor;
                UnityEditor.Handles.Label(position + Vector3.up * labelOffset, label, textStyle);
            }

            // Draw solid circle first
            UnityEditor.Handles.color = ringColor;
            UnityEditor.Handles.DrawWireDisc(position, Vector3.up, radius);

            // Draw faded filled solid circle
            UnityEditor.Handles.color = fillColor;
            UnityEditor.Handles.DrawSolidDisc(position, Vector3.up, radius);
#endif
        }

        /// <summary>
        /// Using Unity's Handles class, draws a flat circle outline of radius specified.  If a string is passed for the label, will also draw the
        /// label offset above the position.
        /// </summary>
        /// <param name="position">Center position in World space to draw the circle</param>
        /// <param name="radius">Radius of the circle</param>
        /// <param name="ringColor">Solid color to use for ring of the circle and the label text.</param>
        /// <param name="fillColor">Fill color for the circle with alpha used.</param>
        /// <param name="label">String to use for label, if empty string passed in, no label will be drawn.</param>
        /// <param name="labelOffset">Distance above the center position to draw the label.</param>
        public static void DrawCircleWire(Vector3 position, float radius, Color ringColor, Color labelColor, string label, float labelOffset = 0f)
        {
#if UNITY_EDITOR
            UnityEditor.Handles.zTest = UnityEngine.Rendering.CompareFunction.LessEqual;


            // Draw handle label
            if (!string.IsNullOrEmpty(label))
            {
                Vector3 pos1 = new Vector3(position.x + radius, position.y, position.z);
                Vector3 pos2 = new Vector3(position.x - radius, position.y, position.z);
                Vector3 pos3 = new Vector3(position.x, position.y, position.z + radius);
                Vector3 pos4 = new Vector3(position.x, position.y, position.z - radius);

                GUIStyle textStyle = new GUIStyle();
                textStyle.normal.textColor = labelColor;
                UnityEditor.Handles.Label(pos1 + Vector3.up * labelOffset, label, textStyle);
                UnityEditor.Handles.Label(pos2 + Vector3.up * labelOffset, label, textStyle);
                UnityEditor.Handles.Label(pos3 + Vector3.up * labelOffset, label, textStyle);
                UnityEditor.Handles.Label(pos4 + Vector3.up * labelOffset, label, textStyle);
            }

            // Draw solid circle first
            UnityEditor.Handles.color = ringColor;
            UnityEditor.Handles.DrawWireDisc(position, Vector3.up, radius, 1);

#endif
        }

        /// <summary>
        /// Draws a box with outline to match the Bounds object passed in.
        /// </summary>
        /// <param name="boxBounds">Bounds for the box to draw</param>
        /// <param name="edgeColor">Color for the edges of the box</param>
        /// <param name="fillColor">Color for the faces of the box.</param>
        /// <param name="label">Label for the box</param>
        /// <param name="labelOffset">Y Offset for the position of the label relative to the box center</param>
        public static void DrawBox(Bounds boxBounds, Color edgeColor, Color fillColor, Color labelColor, string label, float labelOffset = 1f)
        {
#if UNITY_EDITOR
            UnityEditor.Handles.zTest = UnityEngine.Rendering.CompareFunction.LessEqual;

            // Draw handle label
            if (!string.IsNullOrEmpty(label))
            {
                if (labelColor == null)
                    labelColor = edgeColor;
                GUIStyle textStyle = new GUIStyle();
                textStyle.normal.textColor = labelColor + new Color(0, 0, 0, 1);
                UnityEditor.Handles.Label(boxBounds.center + Vector3.up * labelOffset, label, textStyle);
            }

            // Calculate vertices for box
            Vector3[] verts = new Vector3[]
            {
                new Vector3(boxBounds.center.x - boxBounds.extents.x, boxBounds.center.y, boxBounds.center.z - boxBounds.extents.z),
                new Vector3(boxBounds.center.x - boxBounds.extents.x, boxBounds.center.y, boxBounds.center.z + boxBounds.extents.z),
                new Vector3(boxBounds.center.x + boxBounds.extents.x, boxBounds.center.y, boxBounds.center.z + boxBounds.extents.z),
                new Vector3(boxBounds.center.x + boxBounds.extents.x, boxBounds.center.y, boxBounds.center.z - boxBounds.extents.z),
            };

            // Draw rectangle
            UnityEditor.Handles.DrawSolidRectangleWithOutline(verts, fillColor, edgeColor);
#endif
        }

        /// <summary>
        /// Draws a circle on the ground showing the range of the light at ground level
        /// </summary>
        /// <param name="light"></param>
        /// <param name="label"></param>
        public static void DrawRangeForLight(Light light, string label)
        {
            float groundRange = UnityUtils.GetLightRangeAtGroundLevel(light);

            Vector3 groundPosition = light.transform.position;
            groundPosition.y = 0.1f;

            UnityUtils.DrawCircleRadius(groundPosition, groundRange, new Color(1, 1, 1, 1f), new Color(0, 0, 0, 0f), label);
        }

        public static void DrawBoxFromCorners(Vector3Int corner1, Vector3Int corner2, Color fillColor)
        {
            Vector3 center = ((Vector3)corner1 + corner2) / 2f;
            float xSize = Mathf.Abs(corner1.x - corner2.x);
            float zSize = Mathf.Abs(corner1.z - corner2.z);

            Gizmos.color = fillColor;
            Gizmos.DrawCube(center, new Vector3(xSize, 1f, zSize));
        }

        // Scene Management

        /// <summary>
        /// Coroutine that loads all the scenes by name and sets the last scene in the array as the active scene
        /// </summary>
        /// <param name="sceneNames">Array of scene names, must be in the build settings</param>
        /// <returns></returns>
        public static IEnumerator LoadMultipleScenes(string[] sceneNames, string sceneToSetActive)
        {
            Debug.Log($"Loading {sceneNames.Length} Scenes");

            for (int i = 0; i < sceneNames.Length; i++)
            {
                AsyncOperation sceneLoad = SceneManager.LoadSceneAsync(sceneNames[i], LoadSceneMode.Additive);

                if (sceneLoad == null)
                {
                    Debug.LogError("Unable to load scene: " + sceneNames[i]);
                    yield break;
                }

                while (!sceneLoad.isDone)
                    yield return null;
            }

            SceneManager.SetActiveScene(SceneManager.GetSceneByName(sceneToSetActive));

            bool sceneNotActivated = true;
            float maxLoadTime = 120;

            while (sceneNotActivated && maxLoadTime > 0)
            {
                maxLoadTime -= Time.deltaTime;

                Scene activeScene = SceneManager.GetActiveScene();

                if (activeScene.name != null && activeScene.name.Equals(sceneToSetActive))
                    sceneNotActivated = false;

                yield return null;
            }

            if (maxLoadTime < 0)
                Debug.LogError("Unable to load all scenes");
        }

        public static bool TryGetComponentFromRootObjects<T>(Scene scene, out T component)
        {
            GameObject[] rootObjects = scene.GetRootGameObjects();

            for (int i = 0; i < rootObjects.Length; i++)
            {
                if (rootObjects[i].TryGetComponent<T>(out T comp))
                {
                    component = comp;
                    return true;
                }
            }

            component = default;
            return false;
        }

        // Not sure how relevant it is to name this Thread Safe but basically we just need to avoid referring to
        // anything else in this method
        public static void DebugLogThreadSafe(string logMessage)
        {
            // the postfix strings are to aid readability i the XBWatson log by adding a little white
            // space between the log string and the "UnityEngine.Loggers.Log....." which follows it
            Debug.Log($"<color=green>{logMessage}</color>    |");

            //Note: do not use this here
            // but for styling purposes, here is an example of how ordinary logs were setup in the xbox example code (non-threadsafe)
            //Debug.Log($"[{Time.frameCount}] <color=green>{logMessage}</color>    |");
        }

        public static void DebugLogWarningThreadSafe(string logMessage)
        {
            Debug.Log($"<color=yellow>{logMessage}</color>    |");
        }

        // Note: these platform-specific logic methods are project specific and probably should be in some
        // platform-related class rather than here.
        public static bool IsCameraZoomSupported()
        {
            // Because we have to declare manually each platform that doesn't support zoom, it is safer to default
            // to allowing it, in case something goes wrong.
            bool support = true;

#if PLATFORM_GAMECORE_XBOXONE && !UNITY_EDITOR
            // Note that UnityEngine.GameCore.Hardware.version is only available on xbox hardware.
            switch (UnityEngine.GameCore.Hardware.version)
            {
                case UnityEngine.GameCore.HardwareVersion.XboxOne:
                case UnityEngine.GameCore.HardwareVersion.XboxOneS:
                    support = false;
                    break;
                case UnityEngine.GameCore.HardwareVersion.Unknown:
                case UnityEngine.GameCore.HardwareVersion.Pc:
                case UnityEngine.GameCore.HardwareVersion.XboxOneX:
                case UnityEngine.GameCore.HardwareVersion.XboxOneXDevkit:
                case UnityEngine.GameCore.HardwareVersion.XboxSeriesS:
                case UnityEngine.GameCore.HardwareVersion.XboxSeriesX:
                case UnityEngine.GameCore.HardwareVersion.XboxScarlettDevkit:
                    break;
                default:
                    break;
            }
#endif

            return support;
        }

        public static bool IsAutoSaveSupported()
        {
            // Because we have to declare manually each platform that doesn't support autosave, it is safer to default
            // to allowing it, in case something goes wrong.
            bool support = true;

#if PLATFORM_GAMECORE_XBOXONE && !UNITY_EDITOR
            // Note that UnityEngine.GameCore.Hardware.version is only available on xbox hardware.
            switch (UnityEngine.GameCore.Hardware.version)
            {
                case UnityEngine.GameCore.HardwareVersion.XboxOne:
                case UnityEngine.GameCore.HardwareVersion.XboxOneS:
                    support = false;
                    break;
                case UnityEngine.GameCore.HardwareVersion.Unknown:
                case UnityEngine.GameCore.HardwareVersion.Pc:
                case UnityEngine.GameCore.HardwareVersion.XboxOneX:
                case UnityEngine.GameCore.HardwareVersion.XboxOneXDevkit:
                case UnityEngine.GameCore.HardwareVersion.XboxSeriesS:
                case UnityEngine.GameCore.HardwareVersion.XboxSeriesX:
                case UnityEngine.GameCore.HardwareVersion.XboxScarlettDevkit:
                    break;
                default:
                    break;
            }
#endif

            return support;
        }

        /// <summary>
        /// Gets the GameObject that this component is attached to.
        /// This method mostly just forces you to cast the interface you are working with before checking if it's safe to
        /// either access the GameObject or access the interface methods, because if you null check the interface you don't
        /// get unity's overrides.
        /// </summary>
        /// <param name="interfaceAsComponent"></param>
        /// <param name="gameObject">The GameObject of the component, or null if it was invalid.</param>
        /// <returns>True if the GameObject was found and valid.</returns>
        public static bool TryGetGameObjectFromInterface(Component interfaceAsComponent, out GameObject gameObject)
        {
            bool success = false;
            gameObject = null;

            // I haven't really tested this difference yet, but I've read that the explicit unity operator override for == is not as
            // good as evaluating the object implicitly as a bool, so I would not use IsNullOrDestroyed() in this case.
            if (interfaceAsComponent)
            {
                gameObject = interfaceAsComponent.gameObject;
                success = true;
            }

            return success;
        }
        
        /// <summary>
        /// Converts a TimeSpan into a string that displays the total days,
        /// as well as the hours and minutes in two-digit format ("DD:HH:MM").
        /// </summary>
        /// <param name="timeSpan">The TimeSpan to format.</param>
        /// <returns>A string that represents the total days, hours, and minutes of the specified TimeSpan.</returns>
        public static string ConvertTimespanToTotalDaysHoursMinutes(TimeSpan timeSpan)
        {
            return string.Format("{0}:{1:D2}:{2:D2}",
                (int) timeSpan.TotalDays,
                timeSpan.Hours,
                timeSpan.Minutes);
        }

        /// <summary>
        /// Converts a TimeSpan into a string that displays the total hours,
        /// as well as the minutes and seconds in two-digit format ("HH:MM:SS").
        /// </summary>
        /// <param name="timeSpan">The TimeSpan to format.</param>
        /// <returns>A string that represents the total hours, minutes, and seconds of the specified TimeSpan.</returns>
        public static string ConvertTimespanToTotalHoursMinutesSeconds(TimeSpan timeSpan)
        {
            return string.Format("{0}:{1:D2}:{2:D2}",
                (int) timeSpan.TotalHours,
                timeSpan.Minutes,
                timeSpan.Seconds);
        }
        
        /// <summary>
        /// Converts a TimeSpan into a string that displays the total minutes,
        /// as well as the seconds and milliseconds in two-digit format and three-digit for ms ("MM:SS:MSS").
        /// </summary>
        /// <param name="timeSpan">The TimeSpan to format.</param>
        /// <returns>A string that represents the total minutes, seconds, and milliseconds of the specified TimeSpan.</returns>
        public static string ConvertTimespanToTotalMinutesSecondsMilliseconds(TimeSpan timeSpan)
        {
            return string.Format("{0}:{1:D2}:{2:D3}",
                (int) timeSpan.TotalMinutes,
                timeSpan.Seconds,
                timeSpan.Milliseconds);
        }
    }
}