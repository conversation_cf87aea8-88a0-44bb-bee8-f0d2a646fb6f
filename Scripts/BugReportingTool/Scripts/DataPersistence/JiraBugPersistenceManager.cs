using Isto.Core.Data;
using System;
using System.IO;
using UnityEngine;

namespace Isto.Core.BugReportingTool
{
    public class JiraBugPersistenceManager
    {
        public void SaveJiraBugData(JiraBugData jiraBugData)
        {
            // TODO - FG - MTM-210/MTM-211 : don't branch into platform specific logic for unsupported/untested platforms
            // consider using UNITY_STANDALONE_WIN instead

            // details...
            // When I said we need to consider other platforms, I didn't mean we should implement other platforms,
            // unless we want to actually support them. We don't code things we don't need.
            // However SaveJiraBugDataPlatformPC is platform specific, so what you can do is make sure you only run
            // that on PC builds. But you don't determine that by excluding every other platform one at a time, that
            // would never end. Just use a PC specific if to include it.
            // More important than that though is making sure that your prefab with the BugReportingToolManager on it
            // doesn't get instantiated in builds that don't explicitly support it. Thus this code won't be called at
            // all. Then you just need to make sure that you don't have code that won't build on other platforms.
            // Which means from this class's perspective, branching out off SaveJiraBugDataPlatformPC is not really
            // what matters. What matters is if calls like Directory.Exists don't build on certain platforms. It's the
            // inside of SaveJiraBugDataPlatformPC that might need some #if checks, not the method call, although it's
            // also fine to cut out the whole thing.
            // -Frank

#if PLATFORM_GAMECORE && !UNITY_EDITOR
            //TODO: Sept 15, 2023 - JP - MTM-210: Create an XBox specific JiraBugPersistenceManager Save/Load system
#elif UNITY_WEBGL
            //TODO: Sept 15, 2023 - JP - MTM-211: Create a WebGL specific JiraBugPersistenceManager Save/Load system
#else
            SaveJiraBugDataPlatformPC(jiraBugData);
#endif
        }
        
        public JiraBugData LoadJiraBugData()
        {
            // TODO - FG - MTM-210/MTM-211 : don't branch into platform specific logic for unsupported/untested platforms
            // consider using UNITY_STANDALONE_WIN instead
            // make sure to have a return value so that the default code also builds
#if PLATFORM_GAMECORE && !UNITY_EDITOR
            //TODO: Sept 15, 2023 - JP - MTM-210: Create an XBox specific JiraBugPersistenceManager Save/Load system 
            
            // Note: if you add defines and put no code in them, and the method expects a return value, the code
            // doesn't build and the project doesn't work when targeting this platform, this careless disabling
            // is very disruptive. I had to add this to fix the project, not knowing what it means for the bug
            // reporting tool. Hopefully we don't confuse this with working code in the future. -Frank
            return new JiraBugData();
#elif UNITY_WEBGL
            //TODO: Sept 15, 2023 - JP - MTM-211: Create a WebGL specific JiraBugPersistenceManager Save/Load system
#else
            return LoadJiraBugDataPlatformPC();
#endif
        }
        
        public void ClearJiraBugData()
        {
            // TODO - FG - MTM-210/MTM-211 : don't branch into platform specific logic for unsupported/untested platforms
            // consider using UNITY_STANDALONE_WIN instead
#if PLATFORM_GAMECORE && !UNITY_EDITOR
            //TODO: Sept 15, 2023 - JP - MTM-210: Create an XBox specific JiraBugPersistenceManager Save/Load system 
#elif UNITY_WEBGL
            //TODO: Sept 15, 2023 - JP - MTM-211: Create a WebGL specific JiraBugPersistenceManager Save/Load system
#else
            ClearJiraBugDataPlatformPC();
#endif
        }

        // TODO - FG - MTM-210/MTM-211 : don't just branch off the method call while keeping the method code...
        // the content of SaveJiraBugDataPlatformPC is what you really need to protect other platforms from.
        // i/o are especially risky
        private void SaveJiraBugDataPlatformPC(JiraBugData jiraBugData)
        {
            string fullPath = FilePathConstants.JIRA_DEBUG_FILE_FULL_PATH;
            Debug.Log("Full Path: " + fullPath );
            try
            {
                if (!Directory.Exists(FilePathConstants.DEBUG_JIRA_FOLDER_PATH))
                {
                    // throws IOException, UnauthorizedAccessException, ArgumentException, ArgumentNullException, PathTooLongException, DirectoryNotFoundException, NotSupportedException
                    Directory.CreateDirectory(FilePathConstants.DEBUG_JIRA_FOLDER_PATH);
                }

                string jsonData = JsonUtility.ToJson(jiraBugData, true);

                using (FileStream stream = new FileStream(fullPath, FileMode.Create))
                {
                    using (StreamWriter writer = new StreamWriter(stream))
                    {
                        // throws ObjectDisposedException, NotSupportedException, IOException
                        writer.Write(jsonData);
                    }
                }
            }
            catch (PathTooLongException e)
            {
                // The specified path, file name, or both exceed the system-defined maximum length.
                Debug.LogError(
                    $"Error occured when trying to save the Jira bug to file {fullPath}. Exception: {e.Message}\n{e.InnerException?.Message}");
            }
            catch (UnauthorizedAccessException e)
            {
                // The specified path cannot be written to due to invalid security credentials.
                Debug.LogError(
                    $"Error occured when trying to save the Jira bug to file {fullPath}. Exception: {e.Message}\n{e.InnerException?.Message}");
            }
            catch (IOException e)
            {
                // General I/O exception that can occur during the process will be caught here.
                Debug.LogError(
                    $"Error occured when trying to save the Jira bug to file {fullPath}. Exception: {e.Message}\n{e.InnerException?.Message}");
            }
            catch (Exception e)
            {
                // Any other exception that may have been missed is caught here.
                Debug.LogError(
                    $"Error occured when trying to save the Jira bug to file {fullPath}. Exception: {e.Message}\n{e.InnerException?.Message}");
            }
        }

        // TODO - FG - MTM-210/MTM-211 : don't just branch off the method call while keeping the method code...
        // the content of LoadJiraBugDataPlatformPC is what you really need to protect other platforms from.
        private JiraBugData LoadJiraBugDataPlatformPC()
        {
            string fullPath = FilePathConstants.JIRA_DEBUG_FILE_FULL_PATH;
            JiraBugData jiraBugData = new JiraBugData();

            if (File.Exists(fullPath))
            {
                try
                {
                    string jsonData;
                    using (FileStream stream = new FileStream(fullPath, FileMode.Open))
                    {
                        using (StreamReader reader = new StreamReader(stream))
                        {
                            // throws OutOfMemoryException, IOException
                            jsonData = reader.ReadToEnd();
                        }
                    }

                    // throws ArgumentNullException
                    jiraBugData = JsonUtility.FromJson<JiraBugData>(jsonData);
                }
                catch (OutOfMemoryException e)
                {
                    // Any other exception that may have been missed is caught here.
                    Debug.LogError(
                        $"Error occured when trying to load the Jira bug from file {fullPath}. Exception: {e.Message}\n{e.InnerException?.Message}");
                }
                catch (IOException e)
                {
                    // General I/O exception that can occur during the process will be caught here.
                    Debug.LogError(
                        $"Error occured when trying to load the Jira bug from file {fullPath}. Exception: {e.Message}\n{e.InnerException?.Message}");
                }
                catch (Exception e)
                {
                    // Any other exception that may have been missed is caught here.
                    Debug.LogError(
                        $"Error occured when trying to load the Jira bug from file {fullPath}. Exception: {e.Message}\n{e.InnerException?.Message}");
                }
            }

            return jiraBugData;
        }

        // TODO - FG - MTM-210/MTM-211 : don't just branch off the method call while keeping the method code...
        // the content of ClearJiraBugDataPlatformPC is what you really need to protect other platforms from.
        static void ClearJiraBugDataPlatformPC()
        {
            string fullPath = FilePathConstants.JIRA_DEBUG_FILE_FULL_PATH;
            if (File.Exists(fullPath))
            {
                File.Delete(fullPath);
            }
        }
    }
}