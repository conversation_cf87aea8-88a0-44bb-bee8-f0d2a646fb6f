// Copyright Isto Inc.
using Isto.Core.Data;
using Isto.Core.Inputs;
using Isto.Core.Screenshot;
using System.Collections;
using System.IO;
using TMPro;
using UnityEngine;
using Zenject;

namespace Isto.Core.BugReportingTool
{
    public class BugReportingToolManager : MonoBehaviour
    {
        // UNITY HOOKUP

        [SerializeField] private JiraUser _currentJiraUser;
        [SerializeField] private BugReportPanel _gamePanel;
        [SerializeField] private TMP_Text _jiraSubmissionResult;


        // OTHER FIELDS

        private JiraConnection _jiraConnection;
        private bool _isTakingScreenshot;


        // INJECTION

        private IControls _controls;

        [Inject]
        public void Inject(IControls controls)
        {
            _controls = controls;
        }


        // LIFECYCLE EVENTS

        private void Awake()
        {
            _jiraConnection = new JiraConnection(_currentJiraUser);
        }

        void Update()
        {
            if (_controls == null || _controls.GetControlMode() == Controls.Mode.BugReportingTool)
                return;

            if (!_isTakingScreenshot && _controls.GetButtonDown(UserActions.TOGGLEBUGREPORTER))
            {
                _controls.SetControlMode(Controls.Mode.BugReportingTool);
                StartCoroutine(ScreenshotRoutine());
            }
        }

        private void OnEnable()
        {
            RegisterEvents();
        }

        private void OnDisable()
        {
            UnregisterEvents();
        }


        // EVENT HANDLING

        private void RegisterEvents()
        {
            _gamePanel.OnBugSubmitted += BugReportPanel_SubmitBugs;
            _jiraConnection.SubmissionResult += JiraConnection_UpdatePanelResult;
        }

        private void UnregisterEvents()
        {
            _gamePanel.OnBugSubmitted -= BugReportPanel_SubmitBugs;
            _jiraConnection.SubmissionResult -= JiraConnection_UpdatePanelResult;
        }

        void BugReportPanel_SubmitBugs(JiraBugDataArgs jiraBugDataArgs)
        {
            StartCoroutine(_jiraConnection.ProcessRequest(jiraBugDataArgs.JiraBugData));
        }

        private void JiraConnection_UpdatePanelResult(object sender, string result)
        {
            _jiraSubmissionResult.text = result;
        }


        // OTHER METHODS

        private IEnumerator ScreenshotRoutine()
        {
            if (_isTakingScreenshot)
                yield break;

            _isTakingScreenshot = true;

            // It is convenient to wait until the end of the frame which is right
            // before the frame is rendered by the camera
            // If you don't want the GUI to appear in your screenshot then
            // remove this and rather start the routine from LateUpdate
            yield return new WaitForEndOfFrame();

            ScreenshotFile screenshotFile = ScreenshotUtil.TakeDebugScreenshot();

            // Wait until the according file is actually created
            while (!File.Exists(screenshotFile.FilePath))
            {
                yield return null;
            }

            _isTakingScreenshot = false;
            _gamePanel.gameObject.SetActive(true);
        }
    }
}