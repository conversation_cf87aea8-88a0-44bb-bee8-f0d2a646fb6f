using Isto.Core.Data;
using System;
using System.Globalization;

namespace Isto.Core.BugReportingTool
{
    public static class JiraBugTypeConverter
    {
        public static JiraJsonContainer JiraBugDataToJiraJsonContainer(JiraBugData jiraBugData, JiraUser currentJiraUser)
        {
            JiraJsonContainer jiraJsonContainer = new JiraJsonContainer();
            
            JiraJsonContainer.JiraProject jiraProject = new JiraJsonContainer.JiraProject
            {
                key = currentJiraUser.JiraProjectKey
            };

            JiraJsonContainer.JiraIssueType jiraIssueType = new JiraJsonContainer.JiraIssueType
            {
                name = "Bug"
            };
            
            jiraJsonContainer.fields = new JiraJsonContainer.JiraFields
            {
                project = jiraProject,
                summary = jiraBugData.Title,
                description = CreateDescription(jiraBugData),
                environment = CreateEnvironment(jiraBugData),
                issuetype = jiraIssueType
            };
            
            return jiraJsonContainer;
        }

        private static string CreateEnvironment(JiraBugData jiraBugData)
        {
            string environment = "{panel:bgColor=#E9EAEF}\n" +
                                 $"* *Reported by :* {jiraBugData.ReportedBy}\n" +
                                 $"* *Found in :* {jiraBugData.RuntimeScenario}\n" +
                                 $"* *Scene :* {jiraBugData.CurrentScene}\n" +
                                 $"* *Version :* {jiraBugData.CurrentVersion}\n" +
                                 $"* *Date :* {FormatDate(jiraBugData.Date)}\n" +
                                 "{panel}";
            return environment;
        }

        private static string CreateDescription(JiraBugData jiraBugData)
        {
             string description = $"{jiraBugData.Description}\n" +
                                  $"h2. +Repo Steps+ \n{jiraBugData.ReproductionSteps}\n" +
                                  $"h2. +Useful Info+ \n{jiraBugData.UsefulInformation}\n";
            
            return description;
        }

        private static string FormatDate(string date)
        {
            DateTime dateTime = DateTime.Parse(date);
            string monthDayYear = dateTime.ToString("MMM d, yyyy", CultureInfo.InvariantCulture); // datetime needs to be culture agnostic
            string time = dateTime.ToString("h:mm tt");
            return monthDayYear + " at " + time;
        }
    }
}