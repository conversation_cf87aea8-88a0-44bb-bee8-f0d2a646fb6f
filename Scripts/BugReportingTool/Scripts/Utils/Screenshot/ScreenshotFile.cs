using System.IO;
using UnityEngine;

namespace Isto.Core.Screenshot
{
    /// <summary>
    /// The screenshot file class is designed to have a consistent way for us to take screenshots within our games. The
    /// screenshot file is used for both debugging and save game purposes. In the future, this can be expanded upon so
    /// our users can take screenshots that can house more data within them. Future iterations could contain data such
    /// as the game the user took a screenshot in, the users gamertags, the cameras specifications (wide angle lens, vignette)
    /// or other data such as which console the user has taken it on.
    /// </summary>
    public class ScreenshotFile
    {
        // 100 pixels is the default used for sprite.Create() method.
        private static readonly float PIXELS_PER_UNIT = 100.0f;
        private string _filePath;
        public string FilePath => _filePath;
        
        public ScreenshotFile()
        {
            _filePath = "";
        }

        public ScreenshotFile(string filePath)
        {
            _filePath = filePath;
        }
        
        //TODO: Sept 15, 2023 - JP - MTM-213: Turn the GetFileAsSprite into an extension method for the File class
        public Sprite GetScreenshotAsSprite()
        {
            Texture2D texture = RetrieveTextureFromFile();
            Vector2 pivotPointMiddle = new Vector2(0.5f, 0.5f);
            Rect textureSizedRect = new Rect(0.0f, 0.0f, texture.width, texture.height);
            Sprite sprite = Sprite.Create(texture,textureSizedRect , pivotPointMiddle, PIXELS_PER_UNIT);
            
            return sprite;
        }
        
        private Texture2D RetrieveTextureFromFile()
        {
            byte[] fileData = File.ReadAllBytes(_filePath);
            Texture2D texture = new Texture2D(2, 2); //Size does not matter as LoadImage will resize the texture
            
            texture.LoadImage(fileData);
            return texture;
        }
    }
}