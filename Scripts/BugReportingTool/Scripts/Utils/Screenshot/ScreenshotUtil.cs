using System;
using System.Globalization;
using System.IO;
using System.Linq;
using UnityEngine;

namespace Isto.Core.Screenshot
{
    public static class ScreenshotUtil
    {
        private static readonly int DEBUG_SCALED_RESOLUTION = 1;
        private static readonly string SCREENSHOT_PREFIX = "Screenshot_";
        private static readonly string DEBUG_SCREENSHOT_PREFIX = "IstoDebugScreenshot_";
        private static readonly string SCREENSHOT_FILE_TYPE = ".png";
        private static ScreenshotFile _latestScreenshotFile;
        
        public static ScreenshotFile TakeRuntimeScreenshot(string folderPath = null, string fileName = null, int scaledResolution = 1, bool isLogging = false)
        {
            if (folderPath == null)
            {
                folderPath = FilePathConstants.DEFUALT_USER_SCREENSHOTS_FOLDER_PATH;
            }
            
            TryCreateScreenshotDirectory(folderPath);

            if (fileName == null)
            {
                fileName = SCREENSHOT_PREFIX + DateTime.Now.ToString("dd-MM-yyyy-HH-mm-ss") + SCREENSHOT_FILE_TYPE;
            }
            
            return TakeScreenshot(folderPath, fileName, scaledResolution, isLogging);
        }
        
        public static ScreenshotFile TakeDebugScreenshot()
        {
            TryCreateScreenshotDirectory(FilePathConstants.DEBUG_SCREENSHOTS_FOLDER_PATH);
            
            string fileName = DEBUG_SCREENSHOT_PREFIX + DateTime.Now.ToString("dd-MM-yyyy-HH-mm-ss", CultureInfo.InvariantCulture) + SCREENSHOT_FILE_TYPE;
            
            return TakeScreenshot(FilePathConstants.DEBUG_SCREENSHOTS_FOLDER_PATH, fileName, DEBUG_SCALED_RESOLUTION, true);
        }

        public static ScreenshotFile GetLatestDebugScreenshot()
        {
            TryCreateScreenshotDirectory(FilePathConstants.DEBUG_SCREENSHOTS_FOLDER_PATH);
            
            DirectoryInfo temp = new DirectoryInfo(FilePathConstants.DEBUG_SCREENSHOTS_FOLDER_PATH);
            FileInfo[] files = temp.GetFiles();

            if (files.Length == 0)
            {
                _latestScreenshotFile = new ScreenshotFile();
            }
            else
            {
                FileInfo fileInfo = files.OrderByDescending(o => o.CreationTime).FirstOrDefault();
                string filePath = fileInfo?.FullName;
                _latestScreenshotFile = new ScreenshotFile(filePath);
            }
            
            return _latestScreenshotFile;
        }

        public static void ClearDebugScreenshots()
        {
            if (!Directory.Exists(FilePathConstants.DEBUG_SCREENSHOTS_FOLDER_PATH)) return;
            
            FileInfo[] debugScreenshotFiles = new DirectoryInfo(FilePathConstants.DEBUG_SCREENSHOTS_FOLDER_PATH).GetFiles(); 
            foreach (FileInfo file in debugScreenshotFiles)
            {
                if(Path.GetFileName(file.FullName) != Path.GetFileName(_latestScreenshotFile.FilePath))
                    file.Delete(); 
            }
        }
        
        private static ScreenshotFile TakeScreenshot(string folderPath, string fileName, int scaledResolution, bool isLogging)
        {
            string pathName = Path.Combine(folderPath, fileName);
            ScreenCapture.CaptureScreenshot(pathName, scaledResolution);

            _latestScreenshotFile = new ScreenshotFile(pathName);

            if (isLogging)
            {
                Debug.Log($"Screen Capture taken. Filepath: {pathName}");
            }

            return _latestScreenshotFile;
        }
        
        private static void TryCreateScreenshotDirectory(string directoryPath)
        {
            if (!Directory.Exists(directoryPath))
            {
                try
                {
                    Directory.CreateDirectory(directoryPath);
                }
                catch (PathTooLongException e)
                {
                    // The specified path, file name, or both exceed the system-defined maximum length.
                    Debug.LogError(
                        $"Error occured when trying to create a the directory {directoryPath}. Exception: {e.Message}\n{e.InnerException?.Message}");
                }
                catch (UnauthorizedAccessException e)
                {
                    // The specified path cannot be written to due to invalid security credentials.
                    Debug.LogError(
                        $"Error occured when trying to create a the directory {directoryPath}. Exception: {e.Message}\n{e.InnerException?.Message}");
                }
                catch (IOException e)
                {
                    // General I/O exception that can occur during the process will be caught here.
                    Debug.LogError(
                        $"Error occured when trying to create a the directory {directoryPath}. Exception: {e.Message}\n{e.InnerException?.Message}");
                }
                catch (Exception e)
                {
                    // Any other exception that may have been missed is caught here.
                    Debug.LogError(
                        $"Error occured when trying to create a the directory {directoryPath}. Exception: {e.Message}\n{e.InnerException?.Message}");
                }
            }
        }
    }
}