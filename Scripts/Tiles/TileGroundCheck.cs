// Copyright Isto Inc.
using UnityEngine;
using Zenject;

namespace Isto.Core.Tiles
{
    public class TileGroundCheck : MonoBehaviour
    {
        // Public Variables
        public bool hittingTile;
        private TilemapGlobalController _tilemapController;

        public Environment.GroundType CurrentGround { get; private set; }

        [Inject]
        public void Inject(TilemapGlobalController tilemapGlobalController)
        {
            _tilemapController = tilemapGlobalController;
        }

        private void Update()
        {
            Environment.GroundType typeAtPosition = _tilemapController.GetGroundTypeForPosition(transform.position);

            if (typeAtPosition != Environment.GroundType.NotSet)
                CurrentGround = typeAtPosition;
            else
                CurrentGround = Environment.GroundType.Grass;
        }
    }
}

