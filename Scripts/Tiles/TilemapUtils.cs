// Copyright Isto Inc.
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Tilemaps;
using System.IO;
using UnityEngine.SceneManagement;
using Isto.Core.Automation;
using Isto.Core.Items;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Isto.Core.Tiles
{
    public static class TilemapUtils
    {
        public static readonly string BAKED_MAP_TILES_RELATIVE_FOLDER = "BakingTiles/";
        public static readonly string BAKED_MAP_TILES_ABSOLUTE_FOLDER = "Assets/Game Items/Environment/TileMap/" + BAKED_MAP_TILES_RELATIVE_FOLDER;
        public static readonly string BAKED_MAP_TILES_EXTENSION = ".asset";

        public static Environment.GroundType GetGroundTypeForPosition(Vector3 position, Tilemap[] tilemaps)
        {
            // Subtracting x from position because the tilemaps are based on left corner of square instead of bottom
            Vector3Int searchPosition = Vector3Int.RoundToInt(position - Vector3.right);

            for (int i = 0; i < tilemaps.Length; i++)
            {
                Tilemap map = tilemaps[i];

                GroundRuleTile ruleTile = map.GetTile<GroundRuleTile>(map.WorldToCell(position));

                if (ruleTile != null)
                    return ruleTile.groundType;

                GroundTile groundTile = map.GetTile<GroundTile>(map.WorldToCell(position));

                if (groundTile != null)
                    return groundTile.groundType;
            }

            return Environment.GroundType.NotSet;
        }

        public static List<Tilemap> GetAllTilemapsInSceneDecendingSortOrder()
        {
            List<Tilemap> maps = new List<Tilemap>();
            maps = GameObject.FindObjectsOfType<Tilemap>().Where(x => x.gameObject.activeInHierarchy).ToList();

            // Remove any tile maps with small scaling as those are just for decoration items
            for (int i = maps.Count - 1; i >= 0; i--)
            {
                if (maps[i].transform.localScale.x < 0.49f)
                    maps.RemoveAt(i);
            }

            // Sort list from highest sorting order to lowest
            maps.Sort((x, y) => y.GetComponent<TilemapRenderer>().sortingOrder.CompareTo(x.GetComponent<TilemapRenderer>().sortingOrder));

            return maps;
        }

#if UNITY_EDITOR

        public static TilemapCullingSettings GetGroundMap(string sceneName)
        {
            string[] results = UnityEditor.AssetDatabase.FindAssets("t:TilemapCullingSettings");

            if (results.Length == 0)
            {
                Debug.LogError("Cannot find any TilemapCullingSettings assets. Please make sure one has been created");
            }

            for (int i = 0; i < results.Length; i++)
            {
                TilemapCullingSettings map = UnityEditor.AssetDatabase.LoadAssetAtPath<TilemapCullingSettings>(UnityEditor.AssetDatabase.GUIDToAssetPath(results[i]));

                if (map.sceneName.ToLower().Equals(sceneName.ToLower()))
                {
                    return map;
                }
            }

            Debug.LogError($"Cannot Find TilemapCullingSettings for scene: {sceneName}.  Make sure it has been created and that the scene name matches this scene");

            return null;
        }

        public static TilemapCullingController GetCullingController()
        {
            TilemapCullingController[] cullingControllers = GameObject.FindObjectsOfType<TilemapCullingController>(true);

            if (cullingControllers.Length == 0)
                return null;

            if (cullingControllers.Length == 1)
                return cullingControllers[0];

            Selection.objects = cullingControllers;

            throw new UnityException("More than one culling controller exists in loaded scenes. Please make sure there is only one and that it is in the base scene for the area, not in the tilemap specific scene");
        }

        public static void BakeTilemapGroundTypesToAsset(Grid tilemapGrid, TilemapCullingSettings groundMappings)
        {
            if (groundMappings == null)
            {
                return;
            }

            groundMappings.tiles.Clear();

            List<Tilemap> maps = GetSortedTilemapsFromGrid(tilemapGrid);

            HashSet<int> seenPositions = new HashSet<int>();

            for (int i = 0; i < maps.Count; i++)
            {
                Tilemap currentMap = maps[i];

                foreach (var cellPosition in currentMap.cellBounds.allPositionsWithin)
                {
                    Vector3 worldPosition = currentMap.CellToWorld(cellPosition);

                    int hashedPosition = AutomationGrid.HashCoordinate(Mathf.RoundToInt(worldPosition.x), Mathf.RoundToInt(worldPosition.z));

                    GroundRuleTile tile = currentMap.GetTile<GroundRuleTile>(cellPosition);

                    if (tile != null && !seenPositions.Contains(hashedPosition))
                    {
                        Sprite spriteAtPosition = currentMap.GetSprite(cellPosition);

                        seenPositions.Add(hashedPosition);
                        groundMappings.tiles.Add(new TileValues(Vector3Int.RoundToInt(worldPosition), tile.groundType, tile.navigatable, spriteAtPosition));
                    }
                }
            }

            int largestLength;
            Vector3 corner;

            GetTilemapDimensions(maps.ToArray(), out largestLength, out corner);

            groundMappings.bottomCornerPosition = Vector3Int.RoundToInt(corner);
            groundMappings.tileMapLargestSide = largestLength;

            EditorUtility.SetDirty(groundMappings);

            Debug.Log($"Tilemap Ground Types baked to {groundMappings.name} Asset.  {groundMappings.tiles.Count} tiles mapped");
        }

        public static void AddDefaultTilesToBakedTilemap(TilemapCullingSettings groundMappings, List<DefaultTileSetup> defaultSetups)
        {
            if (!ValidateDefaultSetups(defaultSetups))
                throw new UnityException("Cannot add default tiles to tilemap asset");

            HashSet<int> existingTiles = new HashSet<int>();

            for (int i = 0; i < groundMappings.tiles.Count; i++)
            {
                Vector3 tilePosition = groundMappings.tiles[i].tileWorldPosition;

                existingTiles.Add(AutomationGrid.HashCoordinate(Mathf.RoundToInt(tilePosition.x), Mathf.RoundToInt(tilePosition.z)));
            }

            List<Vector3> positionsBuffer = new List<Vector3>();

            for (int i = 0; i < defaultSetups.Count; i++)
            {
                int counter = 0;

                DefaultTileSetup setup = defaultSetups[i];

                Spawner.GetAllPositionsInBounds(setup.bounds, ref positionsBuffer);

                for (int j = 0; j < positionsBuffer.Count; j++)
                {
                    Vector3 position = positionsBuffer[j];

                    int hashedPosition = AutomationGrid.HashCoordinate(Mathf.RoundToInt(position.x), Mathf.RoundToInt(position.z));

                    if (!existingTiles.Contains(hashedPosition))
                    {
                        groundMappings.tiles.Add(new TileValues(Vector3Int.RoundToInt(position), setup.defaultTile.groundType, setup.defaultTile.navigatable, setup.defaultTile.m_DefaultSprite));
                        counter++;

                        existingTiles.Add(hashedPosition);
                    }
                }

                Debug.Log($"{counter} {setup.defaultTile.name} default tiles added to tilemap settings");
            }

            EditorUtility.SetDirty(groundMappings);
        }

        public static bool ValidateDefaultSetups(List<DefaultTileSetup> defaultSetups)
        {
            List<Vector3> positionsBuffer = new List<Vector3>();
            HashSet<int> existingTiles = new HashSet<int>();

            for (int i = 0; i < defaultSetups.Count; i++)
            {
                DefaultTileSetup setup = defaultSetups[i];

                Spawner.GetAllPositionsInBounds(setup.bounds, ref positionsBuffer);

                for (int j = 0; j < positionsBuffer.Count; j++)
                {
                    Vector3 position = positionsBuffer[j];

                    int hashedPosition = AutomationGrid.HashCoordinate(Mathf.RoundToInt(position.x), Mathf.RoundToInt(position.z));

                    if (!existingTiles.Contains(hashedPosition))
                    {
                        existingTiles.Add(hashedPosition);
                    }
                    else
                    {
                        Debug.LogError($"Default Tile setups have overlaping areas. This will break baking. Setup:{setup.defaultTile}, Center:{setup.bounds.center}. Overlap:{position}");
                        return false;
                    }
                }
            }

            return true;
        }

        public static List<Tilemap> GetSortedTilemapsFromGrid(Grid grid)
        {
            List<Tilemap> maps = grid.GetComponentsInChildren<Tilemap>().ToList();

            // Remove any tile maps with small scaling as those are just for decoration items
            for (int i = maps.Count - 1; i >= 0; i--)
            {
                if (maps[i].transform.localScale.x < 0.98f)
                    maps.RemoveAt(i);
            }

            // Sort list from highest sorting order to lowest
            maps.Sort((x, y) => y.GetComponent<TilemapRenderer>().sortingOrder.CompareTo(x.GetComponent<TilemapRenderer>().sortingOrder));

            return maps;
        }

        public static void GetTilemapDimensions(Tilemap[] maps, out int largestSideLength, out Vector3 corner)
        {
            for (int i = 0; i < maps.Length; i++)
            {
                maps[i].CompressBounds();
                maps[i].ResizeBounds();
            }

            int minX = int.MaxValue;
            int minY = int.MaxValue;
            int maxX = 0;
            int maxY = 0;

            for (int i = 0; i < maps.Length; i++)
            {
                minX = Math.Min(maps[i].cellBounds.min.x, minX);
                maxX = Math.Max(maps[i].cellBounds.max.x, maxX);
                minY = Math.Min(maps[i].cellBounds.min.y, minY);
                maxY = Math.Max(maps[i].cellBounds.max.y, maxY);
            }

            largestSideLength = Math.Max(maxX - minX, maxY - minY);

            corner = new Vector3(minX, 0, minY);
        }

        public static void CleanUpPreviousTilemapPrefabs(TilemapCullingSettings groundMappings)
        {
            foreach (var item in groundMappings.tilemapPrefabs)
            {
                if (item.prefab != null)
                {
                    string prefabLocation = AssetDatabase.GetAssetPath(item.prefab);

                    //Debug.Log($"Deleting Previous Tilemap prefab {item.prefab.name} at {prefabLocation} ");

                    AssetDatabase.DeleteAsset(prefabLocation);
                }
            }

            groundMappings.tilemapPrefabs.Clear();

            AssetDatabase.Refresh();

            return;
        }

        public static void SliceTileMap(Tilemap map, int sliceSize, Vector3Int bottomCornerPosition, int tileMapLength, TilemapCullingSettings tilemapSettings, Scene targetScene)
        {
            if (sliceSize == 0)
            {
                Debug.LogError("Slice size of zero passed to SliceTileMap function.  This will not work.  Check the settings asset for this scene and make sure slice size is set there");
                return;
            }

            //Debug.Log($"Slicing Tilemap {map.gameObject.name} into tiles of size {sliceSize}x{sliceSize}.");

            int numberOfSubMapsPerRow = Mathf.CeilToInt(tileMapLength / (float)sliceSize);

            for (int i = 0; i < numberOfSubMapsPerRow; i++)
            {
                for (int j = 0; j < numberOfSubMapsPerRow; j++)
                {
                    Vector3Int currentCorner = new Vector3Int(bottomCornerPosition.x + (i * sliceSize), 0, bottomCornerPosition.z + (j * sliceSize));

                    Tilemap nextMap = CreateTileMapCopy(map, $"{map.gameObject.scene.name}:{map.gameObject.name}:{i}-{j}");

                    // This code creates GameObjects at each corner position with an area set for the size to make debugging easier
                    //GameObject corner = new GameObject($"Corner:{nextMap.name}");
                    //corner.transform.position = currentCorner;
                    //DrawGizmoSquareOnGround gizmos = corner.AddComponent<DrawGizmoSquareOnGround>();
                    //gizmos.SetBounds(sliceSize, sliceSize, false);

                    CopyTilesToMap(map, nextMap, currentCorner, sliceSize);

                    if (nextMap != null)
                    {
                        CreatePrefabFromTileMap(nextMap, currentCorner, tilemapSettings, targetScene);
                    }

                    map.gameObject.SetActive(false);
                }
            }
        }

        public static int GetTileMapMaxDimension(Tilemap map)
        {
            map.CompressBounds();
            map.ResizeBounds();

            return Math.Max(map.cellBounds.size.x, map.cellBounds.size.y);
        }

        public static Tilemap CreateTileMapCopy(Tilemap original, string name)
        {
            GameObject newTileMap = new GameObject(name);
            newTileMap.transform.localScale = original.transform.lossyScale;
            newTileMap.transform.rotation = original.transform.rotation;
            newTileMap.transform.parent = original.transform.parent;
            newTileMap.transform.position = original.transform.position;

            Tilemap copyMap = newTileMap.AddComponent<Tilemap>();

            TilemapRenderer copyRenderer = newTileMap.AddComponent<TilemapRenderer>();
            TilemapRenderer renderer = original.GetComponent<TilemapRenderer>();

            copyRenderer.sortingOrder = renderer.sortingOrder;
            copyRenderer.mode = renderer.mode;
            copyRenderer.detectChunkCullingBounds = renderer.detectChunkCullingBounds;
            copyRenderer.sharedMaterial = renderer.sharedMaterial;
            copyRenderer.sortOrder = renderer.sortOrder;

            return copyMap;
        }

        public static void CopyTilesToMap(Tilemap source, Tilemap destination, Vector3Int cornerPosition, int rowSize)
        {
            bool tileSet = false;

            //Debug.Log($"Copying tiles from {source.gameObject.name} to {destination.gameObject.name} Tilemap with corner {cornerPosition}");

            cornerPosition += Vector3Int.left;

            for (int k = 0; k < rowSize; k++)
            {
                for (int l = 0; l < rowSize; l++)
                {
                    // Offsetting by half to get the world position in center of tile so we get the same cell in both source and destination
                    Vector3 nextTileWorldPos = new Vector3(cornerPosition.x + k + 0.5f, 0, cornerPosition.z + l + 0.5f);

                    Vector3Int tileCellPosition = source.WorldToCell(nextTileWorldPos);

                    GroundRuleTile groundRuleTile = source.GetTile<GroundRuleTile>(tileCellPosition);
                    Sprite spriteAtPosition = source.GetSprite(tileCellPosition);

                    if (groundRuleTile != null && spriteAtPosition != null)
                    {
                        string assetPath = BAKED_MAP_TILES_ABSOLUTE_FOLDER + GetTileAssetNameFromSprite(spriteAtPosition);

                        if (!File.Exists(assetPath))
                        {
                            CreateNormalTilesFromRuleTile(groundRuleTile);
                        }

                        Tile tile = AssetDatabase.LoadAssetAtPath<Tile>(assetPath);

                        if (tile == null)
                        {
                            Debug.LogError($"Tile expected but not found at path: {assetPath}", groundRuleTile);
                            continue;
                        }

                        Vector3Int destinationTilePosition = destination.WorldToCell(source.GetCellCenterWorld(tileCellPosition));

                        destination.SetTile(destinationTilePosition, tile);

                        //Check tile was set
                        Sprite spriteInDestination = destination.GetSprite(tileCellPosition);

                        if (spriteInDestination == null)
                            Debug.LogError($"NO SPRITE. Source sprite:{spriteAtPosition.name}. WorldPosition:{nextTileWorldPos}, SourceCell:{tileCellPosition}, DestCell:{destinationTilePosition}");
                        if (spriteAtPosition != spriteInDestination)
                            Debug.LogError($"Sprites are different. Source sprite:{spriteAtPosition.name}. WorldPosition:{nextTileWorldPos}, SourceCell:{tileCellPosition}, DestCell:{destinationTilePosition}");

                        tileSet = true;
                    }
                }
            }

            // If no tiles in that area, destroy the created tile map
            if (!tileSet)
            {
                //Debug.Log($"Tilemap:{source.gameObject.name}. No tiles set in area from {cornerPosition} to {cornerPosition + Vector3Int.left * rowSize + Vector3Int.forward * rowSize}. No Tilemap created for this area");

                Editor.DestroyImmediate(destination.gameObject);
            }
            else
            {
                destination.CompressBounds();
                destination.ResizeBounds();
                destination.RefreshAllTiles();
            }
        }

        public static void CreatePrefabFromTileMap(Tilemap tilemap, Vector3Int currentCorner, TilemapCullingSettings tilemapSettings, Scene targetScene)
        {
            string scenePath = Path.GetDirectoryName(targetScene.path);

            string prefabsPaths;

            if (scenePath.Contains("TilemapSetup"))
                prefabsPaths = scenePath + @"\";
            else
                prefabsPaths = scenePath + $"\\{tilemap.gameObject.scene.name}-TilemapSetup\\";

            try
            {
                if (!Directory.Exists(prefabsPaths))
                    Directory.CreateDirectory(prefabsPaths);

                AssetDatabase.Refresh();

                string finalPrefabPath = AssetDatabase.GenerateUniqueAssetPath(prefabsPaths + tilemap.gameObject.name + ".prefab");

                bool prefabCreated;

                GameObject createdPrefaB = PrefabUtility.SaveAsPrefabAsset(tilemap.gameObject, finalPrefabPath, out prefabCreated);

                if (prefabCreated)
                    tilemapSettings.tilemapPrefabs.Add(new TilemapPrefabSetup(createdPrefaB, currentCorner, tilemap.GetComponent<TilemapRenderer>().sortingOrder));
                else
                    Debug.LogError($"Unable to create prefab for Tilemap {tilemap.gameObject.name} at location {finalPrefabPath}");
            }
            catch (Exception ex)
            {
                Debug.LogError("Error saving tilemap prefab to " + prefabsPaths);

                throw ex;
            }
        }

        public static Vector3Int GetCornerPosition(Tilemap map)
        {
            map.CompressBounds();
            map.ResizeBounds();

            return new Vector3Int(map.cellBounds.min.x, 0, map.cellBounds.min.y);
        }

        // Navigation Related Code

        public static Transform GenerateNavMeshObstaclesFromMaps(List<Tilemap> maps)
        {
            List<Vector3> nonNavPositions = new List<Vector3>();

            for (int i = 0; i < maps.Count; i++)
            {
                Tilemap currentMap = maps[i];

                foreach (Vector3Int cellPosition in currentMap.cellBounds.allPositionsWithin)
                {
                    GroundRuleTile ruleTile = currentMap.GetTile<GroundRuleTile>(cellPosition);

                    if (ruleTile != null && !ruleTile.navigatable)
                        nonNavPositions.Add(currentMap.CellToWorld(cellPosition) + Vector3.right + Constants.GRID_CENTERING_OFFSET);
                }
            }

            GameObject navHolder = new GameObject();

            CreateNavObstaclesAtPositions(nonNavPositions, navHolder.transform);

            return navHolder.transform;
        }

        private static void CreateNavObstaclesAtPositions(List<Vector3> positions, Transform parent)
        {
            foreach (Vector3 position in positions)
            {
                GameObject navHolder = new GameObject("NAV Blocker");
                navHolder.transform.SetParent(parent);
                navHolder.transform.position = position;

                navHolder.layer = Layers.BUILDING;

                var collider = navHolder.AddComponent<CapsuleCollider>();
                collider.radius = 0.4f;
                collider.height = 2f;
            }
        }

        public static void ClearAllSlicedRuleTiles()
        {
            AssetDatabase.DeleteAsset(BAKED_MAP_TILES_ABSOLUTE_FOLDER);

            AssetDatabase.Refresh();
        }

        /// <summary>
        /// Looks for all rule tiles used in the specified tilemap and extracts all sprites that they use
        /// to put them into their individual tile assets for reference in the sliced tilemap prefabs.
        /// </summary>
        /// <param name="map">The tile map to look into for rule tiles.</param>
        public static void SliceRuleTilesFromTileMap(Tilemap map)
        {
            Debug.Log($"Slicing RuleTiles from Tilemap {map.gameObject.name}");

            BoundsInt bounds = map.cellBounds;
            for (int i = bounds.xMin; i < bounds.xMax; i++)
            {
                for (int j = bounds.yMin; j < bounds.yMax; j++)
                {
                    for (int k = bounds.zMin; k < bounds.zMax; k++)
                    {
                        Vector3Int pos = new Vector3Int(i, j, k);
                        GroundRuleTile tile = map.GetTile<GroundRuleTile>(pos);
                        if (tile != null)
                        {
                            CreateNormalTilesFromRuleTile(tile);
                        }
                    }
                }
            }

            AssetDatabase.Refresh();
        }

        /// <summary>
        /// Looks for all the sprites being used by the specified GroundRuleTile and creates individual Tile assets
        /// for each of them, directly in a shared BakingTiles/ folder, assuming they don't already exist (which means
        /// using the same sprite in several GroundRuleTiles should not cause any duplicates).
        /// </summary>
        /// <param name="sourceTile">The GroundRuleTile to slice the tiles out of.</param>
        public static void CreateNormalTilesFromRuleTile(GroundRuleTile sourceTile)
        {
            if (sourceTile == null)
            {
                return;
            }

            string assetDestinationFolder = BAKED_MAP_TILES_ABSOLUTE_FOLDER;

            if (!Directory.Exists(assetDestinationFolder))
            {
                Directory.CreateDirectory(assetDestinationFolder);
            }

            List<Sprite> sprites = sourceTile.GetAllValidSprites();
            
            for (int i = 0; i < sprites.Count; i++)
            {
                Sprite current = sprites[i];

                if (current == null)
                {
                    continue;
                }

                string assetDestinationPath = assetDestinationFolder + GetTileAssetNameFromSprite(current);

                if (!File.Exists(assetDestinationPath))
                {
                    Tile tile = ScriptableObject.CreateInstance<Tile>();
                    tile.sprite = current;
                    AssetDatabase.CreateAsset(tile, assetDestinationPath);
                }
            }
        }

        /// <summary>
        /// Formats the name of the sprite and its texture as a unique string for use in representing that sprite as a
        /// file for a unity project (they all go in the same folder).
        /// This method assumes that there are no two textures with the same name.
        /// Example result: Downtown-0-RoadLines--Horizontal_1.asset
        /// </summary>
        /// <param name="s">The sprite the name is for.</param>
        /// <returns>A unique name representing this sprite.</returns>
        private static string GetTileAssetNameFromSprite(Sprite s)
        {
            if (s == null)
                return null;

            return s.texture.name + "--" + s.name + BAKED_MAP_TILES_EXTENSION;
        }
#endif
    }
}