// Copyright Isto Inc.
using Isto.Core.Automation;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Tiles
{
    [CreateAssetMenu(fileName = "New Tile Ground Type Map", menuName = "Scriptables/Tilemap/Tile Ground Type Map")]
    public class TilemapCullingSettings : ScriptableObject
    {
        public bool SlicingActive => sliceSize > 0;

        public string sceneName;

        public List<TilemapPrefabSetup> tilemapPrefabs = new List<TilemapPrefabSetup>();

        [HideInInspector] public List<TileValues> tiles = new List<TileValues>();

        [Tooltip("The square side length of each slice of the tilemap.  If set to zero, the tilemaps will not be sliced at all")]
        public int sliceSize = 100;
        public Vector3Int bottomCornerPosition;
        public int tileMapLargestSide;

        // This is created at run time as a faster access method for getting ground types
        private Dictionary<int, TileValues> groundTypesMap = new Dictionary<int, TileValues>();

        public Environment.GroundType GetGroundForWorldPosition(Vector3 position)
        {
            if (TryGetTileValuesForPosition(position, out TileValues tileValues))
                return tileValues.groundType;
            else
                return Environment.GroundType.NotSet;
        }

        public bool IsPositionNavigatable(Vector3 position)
        {
            if (TryGetTileValuesForPosition(position, out TileValues tileValues))
                return tileValues.navigatable;
            else
                return true;
        }

        public bool DoesTileContainAnySprite(Vector3 position, List<Sprite> sprites)
        {
            if (tiles.Count > 0 && groundTypesMap.Count == 0)
                InitializeMapDictionary();

            // Subtracting x from position because the tilemaps are based on left corner of square instead of bottom
            Vector3Int searchPosition = Vector3Int.RoundToInt(position - Vector3.right);

            int hashedCoord = AutomationGrid.HashCoordinate(searchPosition.x, searchPosition.z);

            if (groundTypesMap.TryGetValue(hashedCoord, out TileValues tileValue))
            {
                for (int i = 0; i < sprites.Count; i++)
                {
                    if (tileValue.tileSprite == sprites[i])
                        return true;
                }
            }

            return false;
        }

        private void InitializeMapDictionary()
        {
            Debug.Log("Initializing Tile Map Dictionary");

            for (int i = 0; i < tiles.Count; i++)
            {
                int hashedCoord = AutomationGrid.HashCoordinate(tiles[i].tileWorldPosition.x, tiles[i].tileWorldPosition.z);

                if (groundTypesMap.ContainsKey(hashedCoord))
                {
                    Debug.LogError($"{tiles[i].tileWorldPosition} already exists in dictionary.  Hashed Coord:{hashedCoord}.  Tile type:{tiles[i].groundType}.  THIS SHOULDN'T HAPPEN, IGNORING TILE FOR NOW");
                }
                else
                {
                    groundTypesMap.Add(hashedCoord, tiles[i]);
                }
            }
        }

        private bool TryGetTileValuesForPosition(Vector3 position, out TileValues tileValues)
        {
            if (tiles.Count > 0 && groundTypesMap.Count == 0)
                InitializeMapDictionary();

            // Subtracting x from position because the tilemaps are based on left corner of square instead of bottom
            Vector3Int searchPosition = Vector3Int.RoundToInt(position - Vector3.right);

            int hashedCoord = AutomationGrid.HashCoordinate(searchPosition.x, searchPosition.z);

            return groundTypesMap.TryGetValue(hashedCoord, out tileValues);
        }

        // Debug Helper Methods

        [ContextMenu("Get Number Of Unique Corners")]
        public void GetCornerCount()
        {
            HashSet<int> seenCorners = new HashSet<int>();

            for (int i = 0; i < tilemapPrefabs.Count; i++)
            {
                Vector3Int corner = tilemapPrefabs[i].tilemapCornerPosition;

                int hashed = AutomationGrid.HashCoordinate(corner.x, corner.z);

                if (!seenCorners.Contains(hashed))
                    seenCorners.Add(hashed);
            }

            Debug.Log($"{seenCorners.Count} unique corners");
        }

#if UNITY_EDITOR
        public Vector3Int samplePosition;

        [ContextMenu("Get Tileinfo for position")]
        public void GetTileInfo()
        {
            for (int i = 0; i < tiles.Count; i++)
            {
                if (tiles[i].tileWorldPosition == samplePosition)
                {
                    Debug.Log($"Tile found. Ground type:{tiles[i].groundType}, Navigatable:{tiles[i].navigatable}, Sprite:{tiles[i].tileSprite.name}");
                }
            }
        }
#endif
    }

    [Serializable]
    public class TileValues
    {
        public Vector3Int tileWorldPosition;
        public Environment.GroundType groundType;
        public bool navigatable = true;
        public Sprite tileSprite;

        public TileValues(Vector3Int tileWorldPosition, Environment.GroundType groundType, bool navigatable, Sprite tileSprite)
        {
            this.tileWorldPosition = tileWorldPosition;
            this.groundType = groundType;
            this.navigatable = navigatable;
            this.tileSprite = tileSprite;
        }
    }

    [Serializable]
    public class TilemapPrefabSetup
    {
        public GameObject prefab;
        public Vector3Int tilemapCornerPosition;
        public int layer;

        public TilemapPrefabSetup(GameObject prefab, Vector3Int tilemapCornerPosition, int layer)
        {
            this.prefab = prefab;
            this.tilemapCornerPosition = tilemapCornerPosition;
            this.layer = layer;
        }
    }
}