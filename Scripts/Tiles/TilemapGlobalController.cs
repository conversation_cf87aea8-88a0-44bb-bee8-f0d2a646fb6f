// Copyright Isto Inc.
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Tilemaps;

namespace Isto.Core.Tiles
{
    public class TilemapGlobalController : MonoBehaviour
    {
        private List<TilemapCullingSettings> _loadedSettings = new List<TilemapCullingSettings>();

        private Tilemap[] _maps;
        private bool _settingsValidated = false;

        public Environment.GroundType GetGroundTypeForPosition(Vector3 position)
        {
            if (_loadedSettings.Count > 0)
            {
                if (!_settingsValidated)
                {
                    // Check to make sure the Tile ground types have been baked in already
                    for (int i = 0; i < _loadedSettings.Count; i++)
                    {
                        if (_loadedSettings[i].tiles.Count == 0)
                        {
                            Debug.LogError($"Tilemaps for scene {gameObject.scene.name} with settings {_loadedSettings[i].name} has not had ground types baked yet.  Will not work properly!");
                        }
                    }

                    _settingsValidated = true;
                }

                for (int i = 0; i < _loadedSettings.Count; i++)
                {
                    TilemapCullingSettings tilemapSetting = _loadedSettings[i];

                    var ground = tilemapSetting.GetGroundForWorldPosition(position);

                    if (ground != Environment.GroundType.NotSet)
                        return ground;
                }

                return Environment.GroundType.NotSet;
            }
            else
            {
                // This is a fallback incase the scene is not using the new tile map culling system yet
                if (_maps == null)
                    _maps = TilemapUtils.GetAllTilemapsInSceneDecendingSortOrder().ToArray();

                return TilemapUtils.GetGroundTypeForPosition(position, _maps);
            }
        }

        public bool IsTileNavigatable(Vector3 position)
        {
            if (_loadedSettings.Count > 0)
            {
                if (!_settingsValidated)
                {
                    // Check to make sure the Tile ground types have been baked in already
                    for (int i = 0; i < _loadedSettings.Count; i++)
                    {
                        if (_loadedSettings[i].tiles.Count == 0)
                        {
                            Debug.LogError($"Tilemaps for scene {gameObject.scene.name} with settings {_loadedSettings[i].name} has not had ground types baked yet.  Will not work properly!");
                        }
                    }

                    _settingsValidated = true;
                }

                for (int i = 0; i < _loadedSettings.Count; i++)
                {
                    TilemapCullingSettings tilemapSetting = _loadedSettings[i];

                    bool navigatable = tilemapSetting.IsPositionNavigatable(position);

                    if (!navigatable)
                        return false;
                }

                // Returns true by default, if any of the settings were set to non-navigatable, the tile should not be navigatable at all
                return true;
            }
            else
            {
                return true;
            }
        }

        public void AddTilemapSettings(TilemapCullingSettings settings)
        {
            if (settings != null && !_loadedSettings.Contains(settings))
            {
                _loadedSettings.Add(settings);
            }
        }
    }
}
