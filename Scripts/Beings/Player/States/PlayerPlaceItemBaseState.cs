// Copyright Isto Inc.
using Isto.Core.Automation;
using Isto.Core.Cameras;
using Isto.Core.Inputs;
using Isto.Core.Items;
using Isto.Core.Pooling;
using Isto.Core.StateMachine;
using System;
using UnityEngine;
using UnityEngine.AddressableAssets;
using Zenject;

namespace Isto.Core.Beings
{
    /// <summary>
    /// Base Player State for player states that use the "item placement" control pattern.
    /// </summary>
    public abstract class PlayerPlaceItemBaseState : State
    {
        // Order matters
        protected enum PlacementFlowStates
        {
            Inactive = 0,
            WaitingForFirstSpawn,
            PlacingFirstItem,
            WaitingForNextSpawn,
            PlacingNextItem
        }

        // UNITY HOOKUP

        public float moveSpeed;
        [Tooltip("Time per grid move when moving item with the controller")]
        public float controllerItemMoveDelay = 0.1f;
        public float controllerAxisDeadZone = 0.25f;
        [Tooltip("Offset to center object in grid space with mouse")]
        public Vector3 pointerOffset = new Vector3(-0.75f, 0, 0.75f);
        public float itemMoveDelta = 0.75f; // Not in use now because code is commented
        public float minDragThreshold = 0.75f;
        [Range(0f, 1f)]
        [Tooltip("How far the mouse can go before a drag gets recorded")]
        public float minDragScreenHeightPercent = 0.05f;


        // OTHER FIELDS

        public static int PlacementCount = 0;

        protected ItemPlacementController _placementControl;

        protected Vector3 _lastdragDirection; //The last direction of the mouse drag, if button was held
        protected Vector3 _itemRelativePosition = Vector3.zero; // Tracks how much the player moved to move the item as well when using controller
        protected Vector3 _currentMouseObjectPosition;
        protected Vector3? _lastDropPosition;
        protected Vector3? _lastClickPosition;
        protected Vector3 _lastClickWorldPosition;

        protected ItemPlacement _placement;
        protected AdvancedItem _itemToDrop;
        protected Transform _dropItemTrans;

        protected PlacementFlowStates _currentFlowState = PlacementFlowStates.Inactive;
        protected bool _dropComplete;

        // PROPERTIES

        public bool IsDragging { get { return _lastdragDirection != Vector3.zero; } }
        public bool IsRunning { get; private set; }

        public bool IsSpawning { get { return _currentFlowState == PlacementFlowStates.WaitingForFirstSpawn || _currentFlowState == PlacementFlowStates.WaitingForNextSpawn; } }
        protected bool IsFirstDrop { get { return _currentFlowState < PlacementFlowStates.WaitingForNextSpawn; } }
        protected bool IsPlacingInitialized { get { return _currentFlowState >= PlacementFlowStates.PlacingFirstItem; } }

        public ItemPlacementController CurrentItem => _placementControl;


        // EVENTS

        public event EventHandler<ItemEventArgs> ItemPlaced;


        // INJECTION

        protected IControls _controls;

        protected PlayerItemInteraction _playerInteraction;
        protected PlayerInventory _playerInventory;

        protected CameraController _camControl;
        protected DiContainer _diContainer;

        protected PooledItemFactory _pooledItemFactory;
        protected AutomationSystem _automation;

        // Some function calls happen before Enter() is called, so injection ensures that we have our reference before
        protected AutomationPlayerController _playerController;

        [Inject]
        public void Inject(IControls controls, PlayerItemInteraction interaction, PlayerInventory playerInventory,
                           AutomationPlayerController playerController, CameraController camControl, DiContainer diContainer,
                           [InjectOptional] PooledItemFactory pooledItemFactory, AutomationSystem automation)
        {
            _controls = controls;

            _playerInteraction = interaction;
            _playerInventory = playerInventory;
            _camControl = camControl;
            _diContainer = diContainer;
            _pooledItemFactory = pooledItemFactory;
            _automation = automation;

            // We could get this reference in Enter but this is safer because the PlayerController
            // sometimes interacts with States outside of their execution flow
            _playerController = playerController;

            IsRunning = false;
        }


        // LIFECYCLE EVENTS

        public override void Enter(ScriptableStateMachine controller)
        {
            IsRunning = true;

            _playerController.SetMoveSpeed(moveSpeed);

            _playerInteraction.AllowInteractions(false);
        }

        public override void Exit(ScriptableStateMachine controller)
        {
            IsRunning = false;

            SetFlowState(PlacementFlowStates.Inactive);

            _placementControl?.CancelDrop(exitFromPlacement: false);

            _placementControl = null;
            _dropItemTrans = null;
            _lastDropPosition = null;
            _lastClickPosition = null;

            _controls.SetControlMode(Controls.Mode.Gameplay);
            _controls.DisableControls(Constants.BUTTON_SPAM_DELAY);

            _playerInteraction.AllowInteractions(true);

            _lastdragDirection = Vector3.zero;
            _lastClickPosition = null;
        }


        // OTHER METHODS

        protected void UpdatePlayerMovement()
        {
            if (!_playerController.IsMoving)
                _playerController.SetCharacterIdle();

            MovePlayer();
        }

        /// <summary>
        /// Checks the primary movement axis to see if the player should move.  Ignores mouse clicks as movement as the mouse
        /// controls the placement of the item in this state.
        /// </summary>
        private void MovePlayer()
        {
            // Dont allow moving the player if using controls, as left stick will move the object
            //if (_controls.UsingJoystick())
            //    return;

            float horizontal = _controls.GetAxis(Controls.MovementAxis.MoveHorizontal);
            float vertical = _controls.GetAxis(Controls.MovementAxis.MoveVertical);

            //If keyboard or joystick axis was used to change movement, move the character by using agent.Move()
            if (horizontal != 0f || vertical != 0f)
            {
                _playerController.MoveCharacter(horizontal, vertical);
            }
        }

        // Called before Enter
        public virtual void SetupItemDrop(CoreItem dropItem)
        {
#if DISMANTLE_LOGGING || PLACEMENT_LOGGING
            Debug.LogWarning($"PlacementFlowState {_currentFlowState} during SetupItemDrop");
#endif
            switch (_currentFlowState)
            {
                case PlacementFlowStates.Inactive:
                    SetFlowState(PlacementFlowStates.WaitingForFirstSpawn);
                    break;
                case PlacementFlowStates.PlacingFirstItem:
                case PlacementFlowStates.PlacingNextItem:
                    SetFlowState(PlacementFlowStates.WaitingForNextSpawn);
                    break;
                case PlacementFlowStates.WaitingForFirstSpawn:
                    // This was normally an unexpected flow state however with pooling (in the case of erasers for now)
                    // we end up not having to wait for the item to spawn and go directly here. So we stay in this state and it works for now.
                    break;
                default:
                    Debug.LogError($"Unexpected PlacementFlowState {_currentFlowState} during SetupItemDrop");
                    // Bail out into move state
                    _playerController.ChangeToMoveState();
                    break;
            }

            _itemToDrop = dropItem as AdvancedItem;
            _dropItemTrans = null;

            Vector3 dropPosition = GetStartingPositionForDrop();

            GameObject obtainedItem = _pooledItemFactory?.GetPooledItem(_itemToDrop);
            if (obtainedItem != null)
            {
                AdvancedItem.InitializeNewItem(obtainedItem, false);
                CompleteItemInitialization(obtainedItem);
                return;
            }

            // Original item creation flow kept for non pooled items until everything can be pooled
            //Debug.LogWarning($"Placing item that is not pooled: {dropItem.itemID}");

            if (!string.IsNullOrEmpty(dropItem.addressableAsset.AssetGUID))
            {
                Addressables.LoadResourceLocationsAsync(dropItem.addressableAsset).Completed += op =>
                {
                    if (op.Result != null && op.Result.Count > 0)
                    {
                        // Ideally we would make the item look good (by being in the proper state) before instantiation because it seems that
                        // there is no way to avoid the first frame of existence being visible. Alternatively the spawned item could be the one
                        // that drops and the hologram could be constant, but this requires a big amount of refactoring.
                        // For now, I'll just spawn the item outside the screen so by the time we update its position it will look proper.
                        dropItem.addressableAsset.InstantiateAsync(dropPosition + Vector3.up * 1000f, Quaternion.identity, null).Completed += op =>
                        {
                            if (op.Status == UnityEngine.ResourceManagement.AsyncOperations.AsyncOperationStatus.Succeeded)
                            {
                                GameObject createdItem = op.Result;

                                // Number the items to help with debugging
                                createdItem.name += String.Format("{0:D4}", PlacementCount++);
                                if (PlacementCount > 9999)
                                    PlacementCount = 0;

                                AdvancedItem.InitializeNewItem(createdItem, false);

                                CompleteItemInitialization(createdItem);
                            }
                        };
                    }
                    else
                    {
                        Debug.LogError($"Cannot find item {dropItem.itemID} with key {dropItem.addressableAsset.RuntimeKey.ToString()}");
                    }
                };
            }
            else
            {
                Debug.LogWarning("No valid AddressableAsset GUID for " + dropItem.itemName + ". Cannot drop");
            }
        }

        protected virtual Vector3 GetStartingPositionForDrop()
        {
            if (!_lastDropPosition.HasValue)
            {
                if (!_controls.UsingJoystick())
                {
                    _lastClickPosition ??= _controls.GetPointerPosition();
                    Ray cameraRay = Camera.main.ScreenPointToRay(_lastClickPosition.Value);
                    if (Physics.Raycast(cameraRay, out RaycastHit hit2, 2000f, Layers.GROUND_LAYERS_MASK))
                    {
                        _lastClickWorldPosition = hit2.point;
                    }
                    _lastDropPosition = _controls.GetPointerPositionOnGround() + pointerOffset;
                }
                else
                {
                    _lastDropPosition = _playerInteraction.ActiveInteractableGameObject.transform.position;
                }
            }

            return _lastDropPosition.Value;
        }

        protected virtual void CompleteItemInitialization(GameObject createdObject)
        {
            _dropItemTrans = createdObject.transform;

            _placementControl = createdObject.GetComponent<ItemPlacementController>();

            if (_placementControl == null)
            {
                Debug.LogError($"Trying to drop {createdObject.name} advanced item that does not have a ItemPlacement component.  Please correct this");
                return;
            }

            switch (_currentFlowState)
            {
                case PlacementFlowStates.WaitingForFirstSpawn:
                    _placementControl.IsFirstOfFastPlacement = true;
                    _lastdragDirection = Vector3.zero;
                    Vector3 dropPosition = GetStartingPositionForDrop();
                    _dropItemTrans.position = dropPosition.GetSnappedPosition(_itemToDrop.gridSnapSize);

                    SetFlowState(PlacementFlowStates.PlacingFirstItem);
                    break;
                case PlacementFlowStates.WaitingForNextSpawn:
                    SetFlowState(PlacementFlowStates.PlacingNextItem);
                    break;
                default:
                    Debug.LogError($"Unexpected PlacementFlowState {_currentFlowState} during CompleteItemInitialization");
                    break;
            }
        }

        /// <summary>
        /// Invokes the ItemPlaced event, passing in the current item as a parameter
        /// </summary>
        public void SendItemPlacedEvent()
        {
            ItemPlaced?.Invoke(this, new ItemEventArgs(null, _itemToDrop, placement: _placementControl));
        }

        public virtual void SetDropComplete(bool stopDropping, bool wasCancelled = false)
        {
            if (stopDropping)
            {
                _placementControl = null;
                _dropItemTrans = null;
                _lastDropPosition = null;
                _lastClickPosition = null;

                _controls.SetControlMode(Controls.Mode.Gameplay);
                _playerController.ChangeToMoveState();
            }
            else
            {
                // Zero out the y value in case the item is still animating being dropped
                _lastDropPosition = Vector3.Scale(_dropItemTrans.position, new Vector3(1, 0, 1));

                StartNewDrop();
            }
        }

        protected abstract void StartNewDrop();

        public virtual void SetMovementState(bool active)
        {
            _playerController.SetPlayerPauseState(!active);
        }

        [ContextMenu("Force Cancel Drop")]
        public void ForceCancelDrop()
        {
            if (IsRunning && _placementControl != null)
                _placementControl.CancelDrop();
        }

        protected virtual void SetObjectPosition()
        {
            // If using mouse, move object using mouse position
            if (!_controls.UsingJoystick())
            {
                SetObjectPositionWithMouse();
            }
            else
            {
                SetObjectPositionWithController();
            }
        }

        /// <summary>
        /// Moves the object based on the pointer position on the ground
        /// </summary>
        protected abstract void SetObjectPositionWithMouse();

        /// <summary>
        /// Moves the object using the controller secondary axis
        /// </summary>
        protected abstract void SetObjectPositionWithController();

        protected Vector3 GetMouseGroundDragDirection(Vector3 mouseGroundPosition)
        {
            Vector3 direction;
            Vector3 lastToMouse = mouseGroundPosition - _lastDropPosition.Value;

            if (lastToMouse.sqrMagnitude < Mathf.Pow(minDragThreshold, 2f))
            {
                direction = Vector3.zero;
            }
            else
            {
                Vector3 mouseDirection = UnityUtils.GetNearestAxisDirection(lastToMouse);

                direction = mouseDirection;
            }

            return direction;
        }

        protected Vector3 GetMouseScreenDragDirection()
        {
            Vector3 direction;
            Vector2 lastToMouse = ((Vector3) _controls.GetPointerPosition()) - _lastClickPosition.Value;

            float pixelDelta = Screen.height * minDragScreenHeightPercent;
            if (lastToMouse.sqrMagnitude < Mathf.Pow(pixelDelta, 2f))
            {
                direction = Vector3.zero;
            }
            else
            {
                Vector3 worldLastToMouse = _camControl.MainCamera.cameraToWorldMatrix.MultiplyVector(lastToMouse);
                direction = UnityUtils.GetNearestAxisDirection(worldLastToMouse);
            }

            return direction;
        }

        protected void SetFlowState(PlacementFlowStates nextState)
        {
            _currentFlowState = nextState;
        }

        protected void TryTransitionFlowState(PlacementFlowStates from, PlacementFlowStates to)
        {
            if (_currentFlowState == from)
                SetFlowState(to);
        }
    }
}