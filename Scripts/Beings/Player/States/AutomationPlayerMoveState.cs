// Copyright Isto Inc.
using Isto.Core.Inputs;
using Isto.Core.StateMachine;
using UnityEngine;
using Zenject;

namespace Isto.Core.Beings
{
    [CreateAssetMenu(fileName = "New Player AutomationMoveState", menuName = "Scriptables/State/Automation Player Move State")]
    public class AutomationPlayerMoveState : State
    {
        // Public Variables

        public float speed;

        // Private Variables

        private AutomationPlayerController _controller;

        // Injected
        private IControls _controls;

        [Inject]
        public void Inject(IControls controls)
        {
            _controls = controls;
        }

        public override void Enter(ScriptableStateMachine controller)
        {
            _controller = controller as AutomationPlayerController;
            if (_controller.IsSpeedOverrideEnabled())
                _controller.SetMoveSpeed(_controller.GetMoveSpeedOverride());
            else
                _controller.SetMoveSpeed(speed);

            // If button is pressed from interaction during previous state, don't translate that into a movement input
            if (_controls.GetButton(UserActions.INTERACT))
                _controller.SetCurrentInteractionConsumed();

#if PLAYER_LOGGING
                Debug.Log("Entering Player Move State");
#endif
        }

        public override void Exit(ScriptableStateMachine controller)
        {
#if PLAYER_LOGGING
                Debug.Log("Exiting Player Move State");
#endif
        }

        public override IState Run(ScriptableStateMachine controller)
        {
            State interruptState = _controller.ProcessMovementAndInteractions(movement: true, ability: true, itemInteraction: true);

            if (interruptState == null && !_controller.IsMoving)
                _controller.SetCharacterIdle();

            return interruptState ?? (this);
        }
    }
}