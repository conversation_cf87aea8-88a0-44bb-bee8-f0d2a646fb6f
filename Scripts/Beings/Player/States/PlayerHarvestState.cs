// Copyright Isto Inc.
using FMODUnity;
using Isto.Core.Audio;
using Isto.Core.Inputs;
using Isto.Core.Items;
using Isto.Core.StateMachine;
using UnityEngine;
using Zenject;

namespace Isto.Core.Beings
{
    [CreateAssetMenu(fileName = "New Player Harvest State", menuName = "Scriptables/State/Player Harvest State")]
    public class PlayerHarvestState : State
    {
        // Public Variables

        public float HarvestDPS
        {
            get { return _cheaterDPS ?? harvestdps; }
            set { _cheaterDPS = value; }
        }

        public float MinHarvestDistance
        {
            get { return _cheaterMinHarvestDistance ?? minHarvestDistance; }
            set { _cheaterMinHarvestDistance = value; }
        }

        public float harvestdps;
        [Tooltip("Minimum distance the player must be from harvest position to start harvesting.")]
        public float minHarvestDistance = 0.25f;


        [Header("Audio")]
        [EventRef]
        public string miningAudio;

        public GameObject CurrentTarget { get { return _interactionObject; } }

        private bool HasAnimationController => _playerAnimation != null;

        // Private Variables

        protected AutomationPlayerMoveState _moveState;
        protected IControls _controls;
        protected AutomationPlayerController _controller;
        private PlayerProgress _progress;
        private PlayerHealth _playerHealth;
        private IGameSounds _sounds;
        private IPlayerAnimationController _playerAnimation;
        private Vector3 _interactionPosition;
        private bool _harvesting;
        private float _spamDelay;

        //Cheater values
        private float? _cheaterDPS = null;
        private float? _cheaterMinHarvestDistance = null;
        public bool skipHarvestAnimation = false; // FOR CHEATING

        protected HarvestableController _harvestItem;
        protected GameObject _interactionObject;
        private bool _beamActive;

        // Lifecycle Events

        [Inject]
        public void Inject(AutomationPlayerController playerController, AutomationPlayerMoveState moveState, IControls controls, PlayerProgress playerProgress,
            PlayerHealth playerHealth, IGameSounds gameSounds, [InjectOptional] IPlayerAnimationController playerAnimation)
        {
            _controller = playerController;
            _controls = controls;
            _moveState = moveState;
            _progress = playerProgress;
            _playerHealth = playerHealth;
            _sounds = gameSounds;
            _playerAnimation = playerAnimation;
        }

        public override void Enter(ScriptableStateMachine controller)
        {
#if PLAYER_LOGGING
                Debug.Log("Entering Player Harvest State");
#endif
            _harvesting = false;

            // If already in range, don't move and clear path to get rid of any active movement.
            if (IsTargetInInteractionRange())
            {
                Harvest(HarvestDPS * Time.deltaTime);
            }
            else
            {
                SetMovementTowardsItem();
            }

            _spamDelay = 0f;

            if (!_controls.UsingJoystick())
            {
                _controls.DisableControls(Constants.BUTTON_SPAM_DELAY);
            }

            _playerHealth.TookDamage += OnPlayerTakeDamage;

            if (HasAnimationController)
            {
                _playerAnimation.AnimationComplete += OnAnimationComplete;
            }
        }


        private void OnPlayerTakeDamage(object sender, HealthEventArgs e)
        {
            _controller.ChangeToMoveState();
        }

        public override void Exit(ScriptableStateMachine controller)
        {
#if PLAYER_LOGGING
                Debug.Log("Exiting Player Harvest State");
#endif
            // Add interaction delay so if player in range of Heart box or factory, holding button doesn't instantly open menu
            _controller.DelayItemInteraction(Constants.BUTTON_SPAM_DELAY + 0.25f);

            _sounds.StopLoop(miningAudio);

            if (HasAnimationController)
            {
                _playerAnimation.AnimationComplete -= OnAnimationComplete;
            }
            _playerHealth.TookDamage -= OnPlayerTakeDamage;

            if (_harvestItem != null)
            {
                _harvestItem.HarvestingStopped();
            }

            StopHarvestAnimation();
        }

        public override IState Run(ScriptableStateMachine controller)
        {
            _spamDelay += Time.deltaTime;

            if (_spamDelay < 0.25f)
                return this;

            // If player holding button, don't look for new interactions.
            bool checkForInteractions = !_controls.GetButton(UserActions.INTERACT);

            State interruptState = _controller.ProcessMovementAndInteractions(true, true, checkForInteractions);

            if (interruptState != null && interruptState != this)
                return interruptState;

            if (_controls.UsingJoystick())
            {
                // If button down, keep harvesting.
                if (!_controls.GetButton(UserActions.DISMANTLE)) // TODO: map this to a form of cancel instead of dismantle
                {
                    // If in range, harvest item
                    if (IsTargetInInteractionRange())
                    {
                        Harvest(HarvestDPS * Time.deltaTime);

                        // If item null, it may have completed harvesting, or check harvest health
                        if (_interactionObject == null || _harvestItem.HarvestHealth <= 0f)
                            return _moveState;
                    }

                    // Else should just keep moving towards item
                }
                // If button not held, exit state
                else
                    return _moveState;
            }
            else
            {
                if (IsTargetInInteractionRange() || _harvesting)
                {
                    if (!_progress.PlayerCanHarvest(_harvestItem.item))
                        return _moveState;
                    else
                        Harvest(HarvestDPS * Time.deltaTime);

                    // If item null, it may have completed harvesting, or check harvest health
                    if (_interactionObject == null || _harvestItem.HarvestHealth <= 0f)
                        return _moveState;
                }
            }

            return this;
        }

        private void StopHarvestAnimation()
        {
            if (HasAnimationController)
            {
                _playerAnimation.StopHarvestAnimation();
            }

            _beamActive = false;
        }

        // Methods

        /// <summary>
        /// Should be called before entering into state to set the target object to harvest.
        /// </summary>
        /// <param name="targetObject"></param>
        public void SetHarvestItem(GameObject targetObject)
        {
            if (targetObject.layer != Layers.HARVESTABLE)
                throw new UnityException("Non-harvestable item passed to Harvest state. Item: " + targetObject.name);

            _interactionObject = targetObject;
            _harvestItem = targetObject.GetComponent<HarvestableController>();

            // The layermask parameter doesn't matter for havestable items
            _interactionPosition = _harvestItem.GetInteractionPosition(_controller.transform.position);

#if PLAYER_LOGGING
            Debug.Log("Player Harvest State: Interaction position: " + _interactionPosition.ToString());
#endif
        }

        /// <summary>
        /// Checks if the target object is within the interaction range set in this state.
        /// </summary>
        /// <returns></returns>
        protected bool IsTargetInInteractionRange()
        {
            float distance = Vector3.Distance(_interactionPosition, _controller.transform.position);

            return distance < MinHarvestDistance; // && _controller.IsTouching(_interactionObject);
        }

        private void SetMovementTowardsItem()
        {
#if PLAYER_LOGGING
            Debug.Log("Player Harvest State: Moving player to: " + _interactionPosition);
#endif

            // Set move to interaction position.
            _controller.MoveCharacterToPosition(_interactionPosition, _moveState.speed);
        }

        protected void Harvest(float damage)
        {
            // If the player can't harvest, just return so scan animation is not triggered
            if (_harvestItem == null || _interactionObject == null || !_progress.PlayerCanHarvest(_harvestItem.item))
            {
                Debug.LogError("PlayerHarvestState could not harvest the target");
                _controller.SetCharacterIdle();
                return;
            }

            if (!_harvesting)
            {
                _controller.StopCharacter();
                _controller.SetFacingDirection(_interactionObject.transform.position - _controller.transform.position);

                if (HasAnimationController)
                {
                    _playerAnimation.StartHarvestAnimation();
                }

                _controller.TeleportCharacter(_interactionPosition);

                _harvesting = true;

                _sounds.PlayLoop(miningAudio, _controller.transform.position);
            }

            if (_beamActive || skipHarvestAnimation)
            {
                // Make sure player is facing correct direction before passing to harvestable to avoid warnings of wrong direction
                if (skipHarvestAnimation)
                    _controller.SetFacingDirection(_interactionObject.transform.position - _controller.transform.position);

                _harvestItem.TakeHarvestDamageFromPlayer(damage, _controller.Facing);
            }
        }

        private void OnAnimationComplete(string animationName)
        {
            if (animationName.Contains(_playerAnimation.GetHarvestWarmupAnimationName()))
            {
                _beamActive = true;
            }
        }

        //For Cheats
        public void DeactiveDPSCheats()
        {
            _cheaterDPS = null;
            _cheaterMinHarvestDistance = null;
        }
    }
}