// Copyright Isto Inc.
using Isto.Core.Automation;
using Isto.Core.Inputs;
using Isto.Core.Items;
using Isto.Core.StateMachine;
using System;
using UnityEngine;
using UnityEngine.EventSystems;

namespace Isto.Core.Beings
{
    /// <summary>
    /// Player state for placing advanced non-simpledrop items (Items with ItemplacementController components).
    /// Allows the player to move using keys or joystick while placing item with mouse or secondary thumb stick.
    /// </summary>
    [CreateAssetMenu(fileName = "New Player Place Item State", menuName = "Scriptables/State/Player Place Item State")]
    public class PlayerPlaceItemState : PlayerPlaceItemBaseState
    {
        public static event Action OnPlacementModeEntered;
        public static event Action OnPlacementModeExited;

        // Private Variables

        private Vector3 _lastForwardDirection; //The forward direction of the last item when it was dropped.

        private bool _settingDistance; //Flag for when setting the distance on a spring board (for example) which stops moving the object to mouseposition

        private float _placementIndicatorFadeoutTimer = 0f;

        // State methods

        public override void Enter(ScriptableStateMachine playerController)
        {
            base.Enter(playerController);

            _lastForwardDirection = Vector3.zero;

            _lastHintDirection = Vector3.left;

            // Update placement rotation hints
            if (_placementPreview == null && _controls.UsingJoystick())
            {
                Transform placementPreview = GameObject.Instantiate<Transform>(_placementIndicatorPrefab);
                _placementPreview = placementPreview;
                _placementIndicatorRenderers = _placementPreview.GetComponentsInChildren<SpriteRenderer>();
                _placementIndicatorFadeoutTimer = Time.time;
            }
            else
            {
                _placementIndicatorRenderers = new SpriteRenderer[0];
            }

            OnPlacementModeEntered?.Invoke();
        }

        public override void Exit(ScriptableStateMachine playerController)
        {
#if PLAYER_LOGGING
            Debug.Log("Exiting placement state");
#endif
            OnPlacementModeExited?.Invoke();

            if (_placementPreview != null)
            {
                GameObject.Destroy(_placementPreview.gameObject);
                _placementIndicatorRenderers = null;
            }

            base.Exit(playerController);
        }

        public override IState Run(ScriptableStateMachine playerController)
        {
            if (Time.time - _placementIndicatorFadeoutTimer > 2f && !_controls.GetButton(UserActions.INTERACT))
            {
                if (_placementIndicatorRenderers != null)
                {
                    foreach (SpriteRenderer renderer in _placementIndicatorRenderers)
                    {
                        renderer.color = renderer.color.WithAlpha(Mathf.Clamp(renderer.color.a - Time.deltaTime * 0.5f, 0.2f, 1f));
                    }
                }
            }

            UpdatePlayerMovement();

            if (IsSpawning)
                return this;

            SetObjectPosition();

            _playerController.ItemInteraction.ProcessInputForEquippedItems();
            State abilityState = _playerController.ProcessMovementAndInteractions(false, true, false);

            // Returning the current state as ProcessInputForEquippedItems could've changed it if it was a simple drop or consumable item that was used
            return abilityState != null ? abilityState : _playerController.CurrentState;
        }

        // PlayerAdvItemState methods

        public override void SetupItemDrop(CoreItem dropItem)
        {
            if (IsRunning)
            {
                _placementControl?.CancelDrop(exitFromPlacement: false);

                if (_itemToDrop != dropItem)
                {
                    _itemToDrop = dropItem as AdvancedItem;
                    StartNewDrop();
                    return;
                }
            }

            base.SetupItemDrop(dropItem);
        }

        protected override void CompleteItemInitialization(GameObject createdObject)
        {
            base.CompleteItemInitialization(createdObject);

            // If last forward is zero, it hasn't been set yet, so just use the forward from the new item 
            if (_lastForwardDirection == Vector3.zero)
                _lastForwardDirection = _placementControl.Forward;

            _placementControl.SetStartRotation(_lastForwardDirection);

            _placementControl.enabled = true;

            if (!createdObject.activeSelf)
                createdObject.SetActive(true);

            if (_currentFlowState != PlacementFlowStates.Inactive)
            {
                ActivateItem();
            }
        }

        public override void SetDropComplete(bool stopDropping, bool wasCancelled = false)
        {
            _settingDistance = false;

            // CraftPlaceLightbulbTutorialAction sometimes has an strange exception when I'm placing a lightbulb and it
            // causes the game to enter a hard lock state. However order is important here so event has to happen first
            if (!wasCancelled)
                SendItemPlacedEvent();

            base.SetDropComplete(stopDropping);
        }

        public override void SetMovementState(bool active)
        {
            base.SetMovementState(active);

            if (!active)
                _settingDistance = true;
        }

        protected override void StartNewDrop()
        {
            if (_playerInventory.GetCountOfItem(_itemToDrop) > 0)
            {
                //Save the item forward direction for the next item drop
                _lastForwardDirection = _placementControl != null ? _placementControl.Forward : Vector3.right;

                // Clear the current transforms
                _placementControl = null;
                _dropItemTrans = null;

                SetupItemDrop(_itemToDrop);
            }
            else
            {
                // Stop dropping items
                SetDropComplete(stopDropping: true, wasCancelled: true);
            }
        }

        protected override Vector3 GetStartingPositionForDrop()
        {
            if (!_lastDropPosition.HasValue)
            {
                if (!_controls.UsingJoystick())
                {
                    _lastClickPosition ??= _controls.GetPointerPosition();
                    Ray cameraRay = Camera.main.ScreenPointToRay(_lastClickPosition.Value);
                    if (Physics.Raycast(cameraRay, out RaycastHit hit2, 2000f, Layers.GROUND_LAYERS_MASK))
                    {
                        _lastClickWorldPosition = hit2.point;
                    }
                    _lastDropPosition = _controls.GetPointerPositionOnGround() + pointerOffset;
                }
                else
                {
                    _lastDropPosition = ComputeObjectPositionFromControllerInput(forceUpdate: true);
                }
            }

            return _lastDropPosition.Value;
        }

        protected override void SetObjectPosition()
        {
            if (_settingDistance)
                return;

            base.SetObjectPosition();
        }

        protected override void SetObjectPositionWithMouse()
        {
            // If mouse over UI element, just return
            if (_controls.GetPointerOverUI() || _dropItemTrans == null)
                return;

            Vector3 groundPosition = _controls.GetPointerPositionOnGround() + pointerOffset;

            // If button held, snap movement to axis along direction of drag
            if (IsPlacingInitialized)
            {
                if (_controls.GetButton(UserActions.INTERACT))
                {
                    SetObjectPositionWithMouseDrag(groundPosition);
                }
                else
                {
                    SetFlowState(PlacementFlowStates.Inactive);
                }
            }
            else
            {
                _lastdragDirection = Vector3.zero;

                _dropItemTrans.position = groundPosition.GetSnappedPosition(_itemToDrop.gridSnapSize);
            }
        }

        private void SetObjectPositionWithMouseDrag(Vector3 groundPosition)
        {
            // If no drag set already check for one
            if (_lastdragDirection == Vector3.zero)
            {
                _currentMouseObjectPosition = groundPosition;

                Vector3 dragDirection = GetMouseGroundDragDirection(groundPosition);

                if (dragDirection != Vector3.zero)
                {
                    _lastdragDirection = dragDirection;
                }

                // If player holds button down while placing, we need to make sure the item position gets set to last position
                if (_dropItemTrans.position.x == 0 && _dropItemTrans.position.z == 0)
                {
                    _dropItemTrans.position = _currentMouseObjectPosition.GetSnappedPosition(_itemToDrop.gridSnapSize);
                }
            }
            else
            {
                Vector3 lastPositionToMouse = groundPosition - _lastDropPosition.Value;
                Vector3 projectionOnPlacementDirection = Vector3.Project(lastPositionToMouse, _lastdragDirection);

                // Figure out how far we need to move to clear the previously dropped item
                float minimumDisplacement;
                if (CurrentItem.Forward == _lastdragDirection || CurrentItem.Forward == -_lastdragDirection)
                {
                    minimumDisplacement = _itemToDrop.depth;
                }
                else
                {
                    minimumDisplacement = _itemToDrop.width;
                }

                // If projection length is greater than item size, mouse has moved far enough to increment position by one item width.
                // This fixed progress increment method should prevent items from being spaced out as you pull the mouse away arbitrarily.
                Vector3 projectedPosition;
                if (projectionOnPlacementDirection.sqrMagnitude < Mathf.Pow(minimumDisplacement, 2f))
                {
                    projectedPosition = _lastDropPosition.Value;
                }
                else
                {
                    projectedPosition = _lastDropPosition.Value + projectionOnPlacementDirection.normalized * minimumDisplacement;
                }

                _currentMouseObjectPosition = projectedPosition;

                _dropItemTrans.position = _currentMouseObjectPosition.GetSnappedPosition(_itemToDrop.gridSnapSize);

                if (_placementControl.OverSwappableItem)
                {
                    _lastDropPosition = _dropItemTrans.position;
                }
            }
        }

        // Prototype code for new item placement for controller
        #region Controller Item Placement Prototype

        [SerializeField] private Transform _placementIndicatorPrefab;

        // Keep these settings existing so we do not lose them by accident
        [Header("Prototype Settings =========================================")]
        [Tooltip("The logic we chose is kept for comparison but it will be removed soon. Activate this flag to use the prorotype logic.")]
        public bool useOriginalPrototypeLogic = false;
        public bool useDeadZoneForRememberPosition = true;
        [Tooltip("Using this scheme, the movement is always 0 under the deadzone and 1 above it.")]
        public float rememberPositionDeadzone = 0.45f;
        public float movementSpeed = 2f;

        private int _placementRange = 2;
        private Vector3 _itemAnchorPosition = Vector3.zero;
        private SpriteRenderer[] _placementIndicatorRenderers;

        // TODO: refactor this
        protected override void SetObjectPositionWithController()
        {
            if (IsPlacingInitialized)
            {
                if (_controls.GetButton(UserActions.INTERACT))
                {
                    SetObjectPositionWithControllerDrag();
                }
                else
                {
                    SetFlowState(PlacementFlowStates.Inactive);

                    if (_placementIndicatorRenderers != null)
                    {
                        foreach (SpriteRenderer renderer in _placementIndicatorRenderers)
                        {
                            renderer.color = renderer.color.WithAlpha(1f);
                        }
                    }
                }
            }
            else
            {
                _lastdragDirection = Vector3.zero;

                SetObjectPositionWithControllerNormal();
            }
        }

        protected void SetObjectPositionWithControllerNormal()
        {
            if (_placementRange < 0)
                _placementRange = -_placementRange;

            bool forceUpdate = CheckForRangeChange();
            Vector3 pos = ComputeObjectPositionFromControllerInput(forceUpdate);
            // Assign the final decision to the item
            _dropItemTrans.position = pos;
        }

        // TODO: cleanup and refactor this content - it's mostly ported code from the mouse drag algorithm
        protected void SetObjectPositionWithControllerDrag()
        {
            if (_placementIndicatorRenderers != null)
            {
                foreach (SpriteRenderer renderer in _placementIndicatorRenderers)
                {
                    renderer.color = renderer.color.WithAlpha(0f);
                }
            }

            float xAxis = _controls.GetAxis(Controls.MovementAxis.MoveHorizontal);
            float zAxis = _controls.GetAxis(Controls.MovementAxis.MoveVertical);

            Vector3 playerMovementDirection = UnityUtils.GetNearestAxisDirection(UnityUtils.ConvertScreenVectorToIsometric(xAxis, zAxis));

            Vector3 groundPosition = ComputeObjectPositionFromControllerInput();

            // If no drag set already check for one
            if (_lastdragDirection == Vector3.zero)
            {
                _currentMouseObjectPosition = groundPosition;

                if (playerMovementDirection != Vector3.zero)
                {
                    _lastdragDirection = playerMovementDirection;
                }

                // If player holds button down while placing, we need to make sure the item position gets set to last position
                if (_dropItemTrans.position.x == 0 && _dropItemTrans.position.z == 0)
                {
                    _dropItemTrans.position = _currentMouseObjectPosition.GetSnappedPosition(_itemToDrop.gridSnapSize);
                }
            }
            else
            {
                Vector3 lastPositionToMouse = groundPosition - _lastDropPosition.Value;
                Vector3 projectionOnPlacementDirection = Vector3.Project(lastPositionToMouse, _lastdragDirection);

                // Figure out how far we need to move to clear the previously dropped item
                float minimumDisplacement;
                if (CurrentItem.Forward == _lastdragDirection || CurrentItem.Forward == -_lastdragDirection)
                {
                    minimumDisplacement = _itemToDrop.depth;
                }
                else
                {
                    minimumDisplacement = _itemToDrop.width;
                }

                // If projection length is greater than item size, player has moved far enough to increment position by one item width.
                Vector3 projectedPosition;
                if (projectionOnPlacementDirection.sqrMagnitude < Mathf.Pow(minimumDisplacement, 2f))
                {
                    projectedPosition = _lastDropPosition.Value;
                }
                else
                {
                    projectedPosition = _lastDropPosition.Value + projectionOnPlacementDirection.normalized * minimumDisplacement;
                }

                _currentMouseObjectPosition = projectedPosition;

                _dropItemTrans.position = _currentMouseObjectPosition.GetSnappedPosition(_itemToDrop.gridSnapSize);

                if (_placementControl.OverSwappableItem)
                {
                    _lastDropPosition = _dropItemTrans.position;
                }
            }
        }

        private bool CheckForRangeChange()
        {
            bool rangeChanged = false;

            //Let's try having the range depend on the item size so that it's mostly always the same distance from the player
            // Note: size is a float but I don't think it can be anything other than a round number.

            float itemSize = Mathf.Max(_itemToDrop.width, _itemToDrop.depth);
            int updatedPlacementRange = Mathf.CeilToInt(0.5f * itemSize) + 1;

            if (_placementRange != updatedPlacementRange)
            {
                _placementRange = updatedPlacementRange;
                rangeChanged = true;
            }

            return rangeChanged;
        }

        private Vector3 ComputeObjectPositionFromControllerInput(bool forceUpdate = false)
        {
            if (useOriginalPrototypeLogic)
                return ComputeObjectPositionFromControllerInputA(forceUpdate);
            else
                return ComputeObjectPositionFromControllerInputB(forceUpdate);
        }

        // This is the code from the prototype to serve as a reference until the new version is confirmed good
        private Vector3 ComputeObjectPositionFromControllerInputA(bool forceUpdate)
        {
            Vector3 objPosition;
            float xAxis = _controls.GetAxis(Controls.MovementAxis.SecondaryHorizontal);
            float zAxis = _controls.GetAxis(Controls.MovementAxis.SecondaryVertical);
            bool rightStickMoving = !xAxis.Approx(0f, epsilon: 0.01f) || !zAxis.Approx(0f, epsilon: 0.01f);

            float pureX = _controls.GetAxis(Controls.MovementAxis.SecondaryHorizontal);
            float pureY = _controls.GetAxis(Controls.MovementAxis.SecondaryVertical);

            Vector3 secondaryMovementDirection = ScreenVectorToWorldVector(pureX, pureY);
            if (forceUpdate && !rightStickMoving)
            {
                secondaryMovementDirection = _itemRelativePosition.normalized;
            }

            float axisStrength = new Vector2(pureX, pureY).magnitude * movementSpeed;

            // Apply movement/positionning from input onto item desired pos
            if (axisStrength > rememberPositionDeadzone || forceUpdate)
                _itemRelativePosition = secondaryMovementDirection * _placementRange;

            _itemAnchorPosition = _playerController.transform.position;

            AutomationGridSpace absoluteRefSpace = _automation.GetOrCreateGridSpace(_itemAnchorPosition);
            _itemAnchorPosition = absoluteRefSpace.position;

            // Now that we know where the item is supposed to end up, check for snapping settings and predictions

            // Snap to grid
            Vector3 nextPosition = _itemAnchorPosition + _itemRelativePosition;

            Vector3 nextSnappedPosition = nextPosition.GetSnappedPosition(_itemToDrop.gridSnapSize);

            objPosition = nextSnappedPosition;

            return objPosition;
        }

        private static readonly PlacementDirection DEFAULT_PLACEMENT_DIRECTION = PlacementDirection.UpLeft;
        public enum PlacementDirection { None, UpRight, DownLeft, UpLeft, DownRight }
        public enum MovementDirection { None, Up, Down, Left, Right, UpRight, UpLeft, DownRight, DownLeft }

        [Header("New Placement Settings =========================================")]
        [SerializeField] private int _maxTilesToIgnoreForSnap = 3;
        [SerializeField] private float _rightStickDeadzone = 0.35f;

        private PlacementDirection _lastUsedDirection = PlacementDirection.None;
        private Vector3 _lastUsedPosition = Vector3.zero;
        private Vector3 _lastHintDirection = Vector3.left;
        private Transform _placementPreview = null;

        // This is meant to be a new better written version of the placement position algorithm
        private Vector3 ComputeObjectPositionFromControllerInputB(bool forceUpdate)
        {
            float rightStickXAxis = _controls.GetAxis(Controls.MovementAxis.SecondaryHorizontal);
            float rightStickYAxis = _controls.GetAxis(Controls.MovementAxis.SecondaryVertical);
            bool rightStickMoving = !rightStickXAxis.Approx(0f, epsilon: 0.01f) || !rightStickYAxis.Approx(0f, epsilon: 0.01f);

            float leftStickXAxis = _controls.GetAxis(Controls.MovementAxis.MoveHorizontal);
            float leftStickYAxis = _controls.GetAxis(Controls.MovementAxis.MoveVertical);
            bool leftStickMoving = !leftStickXAxis.Approx(0f, epsilon: 0.01f) || !leftStickYAxis.Approx(0f, epsilon: 0.01f);

            if (!rightStickMoving && !leftStickMoving && !forceUpdate)
            {
                if (_placementPreview != null)
                {
                    _placementPreview.rotation = Quaternion.FromToRotation(Vector3.forward, _lastHintDirection);

                    // base center on item pos instead of player for a consistent relative position
                    Vector3 center = _lastUsedPosition - _lastHintDirection * _placementRange;
                    _placementPreview.position = center - new Vector3(0.5f, -0.02f, -0.5f);
                }

                // Never want the item to move by itself without user input, so don't waste time re-calculating
                return _lastUsedPosition;
            }

            // Right stick orients the item around the player
            PlacementDirection inputDirection;
            if (forceUpdate && !rightStickMoving)
            {
                if (_lastUsedDirection == PlacementDirection.None)
                    inputDirection = DEFAULT_PLACEMENT_DIRECTION;
                else
                    inputDirection = _lastUsedDirection;
            }
            else
            {
                inputDirection = ScreenVectorToPlacementDirection(rightStickXAxis, rightStickYAxis);
            }

            // Update relative position of item
            Vector3 worldPlacementDirection = PlacementDirectionToWorldVector(inputDirection);
            float axisStrength = new Vector2(rightStickXAxis, rightStickYAxis).magnitude;
            if (axisStrength > _rightStickDeadzone || forceUpdate || _itemRelativePosition == Vector3.zero)
            {
                _itemRelativePosition = worldPlacementDirection * _placementRange;

                if (_placementPreview != null)
                {
                    // align the prefab with the placement direction, it points to the other 3 directions by default
                    _placementPreview.rotation = Quaternion.FromToRotation(Vector3.forward, worldPlacementDirection);
                    _lastHintDirection = worldPlacementDirection;
                }
            }

            // Left stick moves the player so it will also drive the item position.
            // We need to know what direction the player is moving to apply the right behavior.
            MovementDirection movementDirection;
            if (leftStickMoving)
                movementDirection = ScreenVectorToMovementDirection(leftStickXAxis, leftStickYAxis);
            else
                movementDirection = MovementDirection.None;

            _itemAnchorPosition = _automation.GetOrCreateGridSpace(_playerController.transform.position).position;
            Vector3 updatedPosition = _itemAnchorPosition + _itemRelativePosition;
            Vector3 snappedPosition = updatedPosition.GetSnappedPosition(_itemToDrop.gridSnapSize);

            // Snapping should ideally progress along the direction that the player is running, which can be towards
            // the tile's vertices or towards its edges. Edges work well but vertices are very small so we need to help it stay true
            Vector3 finalPosition;
            if (inputDirection != _lastUsedDirection || forceUpdate || movementDirection == MovementDirection.None
             || movementDirection == MovementDirection.UpLeft || movementDirection == MovementDirection.UpRight
             || movementDirection == MovementDirection.DownLeft || movementDirection == MovementDirection.DownRight)
            {
                // By not fudging any numbers in any other situation than the four directions below, I think we are maximizing how
                // often the position is exactly where it should be, so we are minimizing the occurence of bad positions, and those
                // just might not be very noticeable to the unexpecting.
                finalPosition = snappedPosition;
            }
            else
            {
                // only allow the new tile if it is stricly the next one in the same direction we are going
                // which means moving one tile diagonally
                Vector3 nextTileOffset = Vector3.zero;
                switch (movementDirection)
                {
                    case MovementDirection.Up:
                        nextTileOffset = Vector3.forward + Vector3.left;
                        break;
                    case MovementDirection.Down:
                        nextTileOffset = Vector3.back + Vector3.right;
                        break;
                    case MovementDirection.Left:
                        nextTileOffset = Vector3.left + Vector3.back;
                        break;
                    case MovementDirection.Right:
                        nextTileOffset = Vector3.right + Vector3.forward;
                        break;
                }

                Vector3 nextTilePosition = _lastUsedPosition + nextTileOffset; // where we think the player would want it to go
                Vector3 drift = snappedPosition - _lastUsedPosition; // accumulated distance from where we can see it right now
                float overshoot = Mathf.Abs(drift.x) + Mathf.Abs(drift.z); // measure the orthogonal distance of the drift to count in tiles
                if (snappedPosition == nextTilePosition || overshoot >= _maxTilesToIgnoreForSnap)
                    finalPosition = snappedPosition;
                else
                    finalPosition = _lastUsedPosition;
            }

            if (_placementPreview != null)
            {
                _placementPreview.rotation = Quaternion.FromToRotation(Vector3.forward, _lastHintDirection);

                // base center on item pos instead of player for a consistent relative position
                Vector3 center = finalPosition - _lastHintDirection * _placementRange;
                _placementPreview.position = center - new Vector3(0.5f, -0.02f, -0.5f);
            }

            _lastUsedPosition = finalPosition;
            return finalPosition;
        }

        // Conversion from screen to world space based solely on what quadrant the joystick is pointing to
        static float quarterPI = 0.25f * Mathf.PI;
        float cosQuarterPI = Mathf.Cos(quarterPI);
        float sinQuarterPI = Mathf.Sin(quarterPI);
        private Vector3 ScreenVectorToWorldVector(float x, float y)
        {
            Vector3 worldVector = Vector3.zero;
            if (!x.Approx(0f, epsilon: 0.05f) || !y.Approx(0f, epsilon: 0.05f)) // epsilon is a bit generous but artificial deadzone is assumed to be much larger
            {
                // Rotate 45 degrees to align valid directions to axes
                Vector2 rotated = new Vector2(x * cosQuarterPI - y * sinQuarterPI, y * cosQuarterPI + x * sinQuarterPI);

                // Point strictly towards the strongest direction
                if (Mathf.Abs(rotated.x) > Mathf.Abs(rotated.y))
                    worldVector.x = Mathf.Sign(rotated.x);
                else
                    worldVector.z = Mathf.Sign(rotated.y);
            }
            return worldVector;
        }

        // TODO: Would it be better to create a GetNearestScreenSpaceAxisDirection that checks for diagonals and
        // simply test for the 4 possible vectors as we did in ScreenVectorToMovementDirection?
        private PlacementDirection ScreenVectorToPlacementDirection(float x, float y)
        {
            PlacementDirection dir = PlacementDirection.None;
            if (!x.Approx(0f, epsilon: 0.05f) || !y.Approx(0f, epsilon: 0.05f)) // epsilon is a bit generous but artificial deadzone is assumed to be much larger
            {
                // Rotate 45 degrees to align valid directions to axes
                Vector2 rotated = new Vector2(x * cosQuarterPI - y * sinQuarterPI, y * cosQuarterPI + x * sinQuarterPI);

                // Choose the strongest direction
                if (Mathf.Abs(rotated.x) > Mathf.Abs(rotated.y))
                {
                    if (rotated.x > 0)
                        dir = PlacementDirection.DownRight;
                    else
                        dir = PlacementDirection.UpLeft;
                }
                else
                {
                    if (rotated.y > 0)
                        dir = PlacementDirection.UpRight;
                    else
                        dir = PlacementDirection.DownLeft;
                }

            }
            return dir;
        }

        private Vector3 PlacementDirectionToWorldVector(PlacementDirection dir)
        {
            Vector3 worldVector = Vector3.zero;
            switch (dir)
            {
                case PlacementDirection.UpRight:
                    worldVector = Vector3.forward;
                    break;
                case PlacementDirection.DownLeft:
                    worldVector = Vector3.back;
                    break;
                case PlacementDirection.UpLeft:
                    worldVector = Vector3.left;
                    break;
                case PlacementDirection.DownRight:
                    worldVector = Vector3.right;
                    break;
            }
            return worldVector;
        }

        private MovementDirection ScreenVectorToMovementDirection(float x, float y)
        {
            MovementDirection dir = MovementDirection.None;

            // Same input conversion as in PlayerController.MoveCharacter but snap it to the eight directions
            Vector3 movementDirection = UnityUtils.GetNearestOctoDirection(UnityUtils.ConvertScreenVectorToIsometric(x, y));
            if (movementDirection == Vector3.forward)
                dir = MovementDirection.UpRight;
            else if (movementDirection == Vector3.back)
                dir = MovementDirection.DownLeft;
            else if (movementDirection == Vector3.left)
                dir = MovementDirection.UpLeft;
            else if (movementDirection == Vector3.right)
                dir = MovementDirection.DownRight;
            else if (movementDirection == Constants.BACKLEFT_NORMALIZED)
                dir = MovementDirection.Left;
            else if (movementDirection == Constants.BACKRIGHT_NORMALIZED)
                dir = MovementDirection.Down;
            else if (movementDirection == Constants.FRONTLEFT_NORMALIZED)
                dir = MovementDirection.Up;
            else if (movementDirection == Constants.FRONTRIGHT_NORMALIZED)
                dir = MovementDirection.Right;
            else
                Debug.LogError("Due to what must be floating point innacuracies, none of the axes from GetNearestOctoDirection seem to correspond to its return value, despite an allowance of 1e-5");
            return dir;
        }

        // End of prototype code
        #endregion Controller Item Placement Prototype

        /// <summary>
        /// Enables the gameObject for the item being dropped.  Used to prevent the item from being active before being placed in the correct position
        /// </summary>
        private void ActivateItem()
        {
            // Set the position of the object before setting it active
            SetObjectPosition();
            _dropItemTrans.gameObject.SetActive(true);
        }
    }
}