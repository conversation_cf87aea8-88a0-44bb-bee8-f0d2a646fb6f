// Copyright Isto Inc.
using Isto.Core.Automation;
using Isto.Core.Inputs;
using Isto.Core.Items;
using Isto.Core.StateMachine;
using System.Collections.Generic;
using UnityEngine;

#if DISMANTLE_PROFILING
using UnityEngine.Profiling;
#endif

namespace Isto.Core.Beings
{
    /// <summary>
    /// Player state for dismantling aka placing erasers (as items with ItemplacementController components).
    /// Allows the player to move using keys or joystick while moving eraser with mouse or secondary thumb stick.
    /// </summary>
    [CreateAssetMenu(fileName = "New Player Dismantle State", menuName = "Scriptables/State/Player Dismantle State")]
    public class PlayerDismantleState : PlayerPlaceItemBaseState
    {
        public static readonly UserActions DismantleInput = UserActions.DISMANTLE;

        // Public variables

        [Range(0.5f, 3f)]
        [Tooltip("Magic number to line up perceived mouse travel with displacement line progression. 1 is neutral.")]
        public float dragAggressivity = 2f;

        // Private Variables

        private Stack<ItemPlacementController> _placedErasersChain;

        // Mostly computation values we cache to draw some gizmos
        private Vector3 _groundPosition, _mouseWorldPosition, _dragDelta, _projectionOnPlacementDirection;

        // State Methods

        public override void Enter(ScriptableStateMachine playerController)
        {
            base.Enter(playerController);

            if (_placedErasersChain == null)
                _placedErasersChain = new Stack<ItemPlacementController>();
        }

        public override void Exit(ScriptableStateMachine playerController)
        {
            // Need sanity check in case flow exit comes from pressing equip instead of releasing right click, aborting us aggressively as a side-effect
            if (_placedErasersChain.Count == 0 && _placementControl != null && _placementControl.GetComponent<Eraser>().EraseTarget != null)
                _placedErasersChain.Push(_placementControl);

            ApplyEraserToSelection();

#if PLAYER_LOGGING
            Debug.Log("Exiting dismantling state");
#endif
            base.Exit(playerController);
        }

        public override IState Run(ScriptableStateMachine playerController)
        {
            UpdatePlayerMovement();

            if (IsSpawning)
                return this;

            SetObjectPosition();

            // this lets you activate stuff but won't allow changing to an advanced placement item
            _playerController.ItemInteraction.ProcessInputForEquippedItems();
            // Returning the current state as ProcessInputForEquippedItems could've changed it if it was a simple drop or consumable item that was used
            return _playerController.CurrentState;
        }

        // PlayerAdvItemState Methods

        // Item drop logic but we should only be handling erasers so simpler
        public override void SetupItemDrop(CoreItem dropItem)
        {
            if (IsRunning)
            {
                _placementControl?.CancelDrop(exitFromPlacement: false);
            }

            base.SetupItemDrop(dropItem);
        }

        protected override Vector3 GetStartingPositionForDrop()
        {
            Vector3 defaultPosition = base.GetStartingPositionForDrop();
            GameObject activeInteractable = _playerInteraction.ActiveInteractableGameObject;

            if (!activeInteractable.IsNullOrDestroyed() && (IsFirstDrop || Controls.UsingController))
            {
                ISpecialPositioning specialPositionHandler = activeInteractable.GetComponent<ISpecialPositioning>();
                if (specialPositionHandler != null)
                {
                    Vector3 offset = specialPositionHandler.GetPositionOffset();
                    Vector3 position = activeInteractable.transform.position + offset;
                    defaultPosition = position;
                }
                else
                {
                    defaultPosition = activeInteractable.transform.position;
                }
            }
            else
            {
#if DISMANTLE_LOGGING
                Debug.Log("PlayerDismantleState: default pos is " + defaultPosition);
#endif
            }

            return defaultPosition;
        }

        protected override void CompleteItemInitialization(GameObject createdObject)
        {
            base.CompleteItemInitialization(createdObject);

            _placementControl.enabled = true;

            if (!createdObject.activeSelf)
                createdObject.SetActive(true);
        }

        public override void SetDropComplete(bool stopDropping, bool wasCancelled = false)
        {
            if (_placementControl != null)
            {
                _placedErasersChain.Push(_placementControl);
            }

            bool dismantlePressed = _controls.GetButton(DismantleInput);
            if (!dismantlePressed)
            {
                //Refuse to place more erasers if the player is already releasing the button
                stopDropping = true;
            }

            base.SetDropComplete(stopDropping);
        }

        protected override void StartNewDrop()
        {
            // Clear the current transforms
            _placementControl = null;
            _dropItemTrans = null;

            SetupItemDrop(_itemToDrop);
        }

        protected override void SetObjectPositionWithMouse()
        {
            // If mouse over UI element, just return
            if (_controls.GetPointerOverUI() || _dropItemTrans == null)
                return;

#if DISMANTLE_LOGGING
            //Debug.Log("SetObjectPositionWithMouse", _dropItemTrans.gameObject);
#endif

            if (IsFirstDrop)
            {
                _groundPosition = _playerInteraction.ActiveInteractableGameObject.transform.position;

                ISpecialPositioning specialPositionHandler = _playerInteraction.ActiveInteractableGameObject.GetComponent<ISpecialPositioning>();
                if (specialPositionHandler != null)
                {
                    Vector3 offset = specialPositionHandler.GetPositionOffset();
                    _groundPosition += offset;
                }

                _lastdragDirection = Vector3.zero;

                _dropItemTrans.position = _groundPosition.GetSnappedPosition(_itemToDrop.gridSnapSize);
            }
            else
            {
                bool dismantlePressed = _controls.GetButton(DismantleInput);
                // If button held, snap movement to axis along direction of drag
                if (dismantlePressed && IsPlacingInitialized)
                {
                    SetObjectPositionWithMouseDrag();
                }
                else
                {
                    _lastdragDirection = Vector3.zero;

                    _groundPosition = _controls.GetPointerPositionOnGround() + pointerOffset;
                    _dropItemTrans.position = _groundPosition.GetSnappedPosition(_itemToDrop.gridSnapSize);
                }
            }
        }

        private void SetObjectPositionWithMouseDrag()
        {
#if DISMANTLE_LOGGING
            Debug.Log($"SetObjectPositionWithMouseDrag on {_dropItemTrans.gameObject.name}", _dropItemTrans.gameObject);
#endif
#if DISMANTLE_PROFILING
            Profiler.BeginSample("SetObjectPositionWithMouseDrag", _dropItemTrans.gameObject);
#endif
            // If no drag set already check for one
            if (_lastdragDirection == Vector3.zero)
            {
                _currentMouseObjectPosition = _groundPosition;

                Vector3 dragDirection = GetMouseScreenDragDirection();

                if (dragDirection != Vector3.zero)
                {
                    _lastdragDirection = dragDirection;
                }
            }
            else
            {
                // The drag delta needs to be gotten in screen space, but the effect on the world has to be offset to the object's root position, where the eraser will be placed
                _mouseWorldPosition = _controls.GetPointerPositionOnGround();
                _dragDelta = _mouseWorldPosition - _lastClickWorldPosition;
                _projectionOnPlacementDirection = Vector3.Project(_dragDelta, _lastdragDirection) * dragAggressivity;

                //Debug.Log($"Delta:{_dragDelta.magnitude}, Projection delta:{_projectionOnPlacementDirection.magnitude}");

                // If projection length is greater than 1, mouse is moved onto a new space, increment position by one towards new mouse position.
                Vector3 updatedPosition;
                if (_projectionOnPlacementDirection.sqrMagnitude < 1)
                {
                    updatedPosition = _lastDropPosition.Value;
                }
                else
                {
                    updatedPosition = _lastDropPosition.Value + _projectionOnPlacementDirection.normalized;

                    // To be consistent with our item placement algorithm we gradually increase the "last click" position until it reaches the mouse position
                    Vector3 lastDropScreenPosition = _camControl.MainCamera.WorldToScreenPoint(_lastDropPosition.Value);
                    lastDropScreenPosition.z = 0f;
                    Vector3 updatedDropScreenPosition = _camControl.MainCamera.WorldToScreenPoint(updatedPosition);
                    updatedDropScreenPosition.z = 0f;
                    Vector3 screenSpaceDeltaPosition = updatedDropScreenPosition - lastDropScreenPosition;

                    _lastClickPosition += screenSpaceDeltaPosition;
                    _lastClickWorldPosition += _projectionOnPlacementDirection.normalized;
                }

                _currentMouseObjectPosition = updatedPosition;
                _dropItemTrans.position = _currentMouseObjectPosition.GetSnappedPosition(_itemToDrop.gridSnapSize); //Eraser's snap size is 1
                _lastDropPosition = _dropItemTrans.position;
            }
#if DISMANTLE_PROFILING
            Profiler.EndSample();
#endif
        }

        protected override void SetObjectPositionWithController()
        {
            // Do Nothing for now
            // Default position is the item position, and we want dismantling to "magically" work on any nearby item for now
            // Eventually we'll have to fix this flow so that the dismantling at least doesn't happen until you release the button.

#if DISMANTLE_LOGGING
            Debug.Log("SetObjectPositionWithController", _dropItemTrans.gameObject);
#endif

            if (IsPlacingInitialized)
            {
                bool dismantlePressed = _controls.GetButton(DismantleInput);
                if (dismantlePressed)
                {
                    SetObjectPositionWithControllerDrag();
                }
                else
                {
                    SetFlowState(PlacementFlowStates.Inactive);
                }
            }
            else
            {
                // Note: I think right now this flow never happens, unless maybe the pool runs out of erasers? To be tested.

                _lastdragDirection = Vector3.zero;

                SetObjectPositionWithControllerNormal();
            }
        }

        protected void SetObjectPositionWithControllerNormal()
        {
            Vector3 pos = ComputeObjectPositionFromControllerInput();

            // Assign the final decision to the item
            _dropItemTrans.position = pos;
        }

        private Vector3 _playerStartOfDragPos = Vector3.zero;

        private void SetObjectPositionWithControllerDrag()
        {
#if DISMANTLE_LOGGING
            Debug.Log("SetObjectPositionWithControllerDrag", _dropItemTrans.gameObject);
#endif
            float xAxis = _controls.GetAxis(Controls.MovementAxis.MoveHorizontal);
            float zAxis = _controls.GetAxis(Controls.MovementAxis.MoveVertical);

            Vector3 playerMovementDirection = UnityUtils.GetNearestAxisDirection(UnityUtils.ConvertScreenVectorToIsometric(xAxis, zAxis));
            Vector3 groundPosition = ComputeObjectPositionFromControllerInput();

            // If no drag set already check for one
            if (_lastdragDirection == Vector3.zero)
            {
                if (playerMovementDirection != Vector3.zero)
                {
                    _lastdragDirection = playerMovementDirection;
                }

                //TODO: fix the pushback with a generic solution
                // does ISpecialPositioning work?
                ISpecialPositioning specialPositionHandler = _playerInteraction.ActiveInteractableGameObject.GetComponent<ISpecialPositioning>();
                if (specialPositionHandler != null)
                {
                    Vector3 offset = specialPositionHandler.GetPositionOffset();
                    _lastDropPosition += offset;
                    Vector3 testPos = specialPositionHandler.GetAutomationPosition();

                    Debug.Log($"PlayerDismantleState: Is Pushback, eraser A is at {_lastDropPosition}");
                    Debug.Log($"PlayerDismantleState: Is Pushback, eraser B is at {testPos}");
                }
                else
                {
                    // TODO: check with minideerfactory and pickerpalstation in particular
                    Debug.Log($"PlayerDismantleState: Is nonp, eraser A is at {_lastDropPosition.Value}");

                }

                /*GameObject activeInteractable = _playerInteraction.ActiveInteractableGameObject;
                PushBackRotateState pushback = null;
                if (!activeInteractable.IsNullOrDestroyed())
                {
                    pushback = activeInteractable.GetComponent<PushBackRotateState>();
                }
                if (pushback != null)
                {
                    var pushbackController = pushback.GetComponent<ItemPlacementController>();
                    _lastDropPosition += pushbackController.GetDirection() * -1f;
                }*/

                _dropItemTrans.position = _lastDropPosition.Value;
                _playerStartOfDragPos = _playerController.transform.position;

                AutomationGridSpace playerSpace = _automation.GetOrCreateGridSpace(_playerStartOfDragPos);
                AutomationGridSpace dismantleSpace = _automation.GetOrCreateGridSpace(_dropItemTrans.position);
            }
            else
            {
                groundPosition = _playerController.transform.position - _playerStartOfDragPos;
                Vector3 projectionOnPlacementDirection = Vector3.Project(groundPosition, _lastdragDirection);

                // If projection length is greater than one space, item is moved onto a new space, increment position by one space towards new target
                Vector3 projectedPosition;
                if (projectionOnPlacementDirection.sqrMagnitude < 1)
                {
                    projectedPosition = _lastDropPosition.Value;
                }
                else
                {
                    projectedPosition = _lastDropPosition.Value + projectionOnPlacementDirection.normalized;
                    _playerStartOfDragPos = _playerController.transform.position;
                }

                _dropItemTrans.position = projectedPosition.GetSnappedPosition(_itemToDrop.gridSnapSize);

                _lastDropPosition = _dropItemTrans.position;
            }
        }

        private Vector3 ComputeObjectPositionFromControllerInput()
        {
            if (_dropItemTrans.position == Vector3.zero)
            {
                _dropItemTrans.position = _lastDropPosition.Value;
            }

            if (_dropItemTrans.position == Vector3.zero)
            {
                Debug.LogError("_dropItemTrans.position is zero!");
            }

            return _dropItemTrans.position;
        }

        private void ApplyEraserToSelection()
        {
            Eraser closestToPlayer = null;
            foreach (ItemPlacementController item in _placedErasersChain)
            {
                if (item.IsNullOrDestroyed())
                    continue;

                Eraser eraser = item.GetComponent<Eraser>();

                if (eraser == null || eraser.EraseTarget == null)
                    continue;

                if (closestToPlayer == null
                || ((eraser.transform.position - _playerController.transform.position).sqrMagnitude < (closestToPlayer.transform.position - _playerController.transform.position).sqrMagnitude))
                {
                    closestToPlayer = eraser;
                }
            }

            foreach (ItemPlacementController item in _placedErasersChain)
            {
                if (item.IsNullOrDestroyed())
                    continue;

                Eraser eraser = item.GetComponent<Eraser>();

                if (eraser == null || eraser.EraseTarget == null)
                    continue;

                item.GetComponent<EraseCompleteState>().FinishRemoval(batched: eraser != closestToPlayer);
            }

            _placedErasersChain.Clear();
        }

        /// <summary>
        /// Attempts to revert pending dismantle operations (aka erasers positioned over items), starting from the
        /// most recently defined one, until the specified one is reached.
        /// </summary>
        /// <param name="targetRecoverPosition">The latest placed eraser item that we want to keep</param>
        public void TryPartialDismantleRollback(ItemPlacementController targetRecoverPosition)
        {
            if (_placedErasersChain.Contains(targetRecoverPosition))
            {
                while (_placedErasersChain.Peek() != targetRecoverPosition)
                {
                    ItemPlacementController toRevert = _placedErasersChain.Pop();

                    Eraser.UnreadyObjectForDismantling(toRevert.GetComponent<Eraser>().EraseTarget.gameObject);
                    _pooledItemFactory?.ReturnPooledAdvancedItem(toRevert.GetComponent<PooledInteractableItem>());

                    // Zero out the y value in case the item is still animating being dropped?
                    _lastDropPosition = Vector3.Scale(targetRecoverPosition.transform.position, new Vector3(1, 0, 1));
                }
            }
            else
            {
                Debug.LogWarning("TryPartialDismantleRollback couldn't find proposed target in the eraser stack");
            }
        }

        public void AbortAllDismantling()
        {
            if (_placementControl != null)
            {
                if (_placementControl.GetComponent<Eraser>().EraseTarget != null)
                {
                    _placedErasersChain.Push(_placementControl);
                }
                else
                {
                    _placementControl.CancelDrop(exitFromPlacement: false);
                }

                _placementControl = null;
            }

            foreach (var item in _placedErasersChain)
            {
                Eraser eraser = item.GetComponent<Eraser>();
                if (eraser.EraseTarget.IsNullOrDestroyed())
                {
                    // TODO: With the controller we dismantle as we go, this seems to cause an unexpected situation when we
                    // manually cancel the dismantling. Look into this and avoid the incorrect logic at the source?
                    continue;
                }

                Eraser.UnreadyObjectForDismantling(eraser.EraseTarget.gameObject);
                _pooledItemFactory?.ReturnPooledAdvancedItem(item.GetComponent<PooledInteractableItem>());
            }

            _placedErasersChain.Clear();
        }

        public void DrawGizmos()
        {
            if (!IsRunning || _lastdragDirection == Vector3.zero)
                return;

            Gizmos.color = Color.cyan; //The actual line user is drawing on the screen, as the new source delta
            Gizmos.DrawLine(_lastClickWorldPosition, _mouseWorldPosition);

            Gizmos.color = Color.red; //The drag line on the ground that used to serve as the source delta (and still does for placement)
            Gizmos.DrawLine(_lastDropPosition.Value, _groundPosition);

            Gizmos.color = Color.white; //The projected item chaining line on the ground
            Gizmos.DrawLine(_lastDropPosition.Value, _lastDropPosition.Value + _projectionOnPlacementDirection);
        }
    }
}