// Copyright Isto Inc.

using Isto.Core.StateMachine;
using UnityEngine;

namespace Isto.Core.Beings
{
    [CreateAssetMenu(fileName = "SimpleCorePlayerState", menuName = "Scriptables/State/Simple Core Player State")]
    public class SimplePlayerMoveState : State
    {
        private SimplePlayerController _playerController;

        public override void Enter(ScriptableStateMachine controller)
        {
            _playerController = controller as SimplePlayerController;
        }

        public override IState Run(ScriptableStateMachine controller)
        {
            Vector2 input = _playerController.GetInputs();

            Vector3 movement = new Vector3(input.x, input.y, 0f) * Time.deltaTime * 20f;
            Vector3 newPosition = _playerController.GetPlayerPosition() + movement;
            newPosition.x = Mathf.Clamp(newPosition.x, -20f, 20f);
            newPosition.y = Mathf.Clamp(newPosition.y, -20f, 20f);

            _playerController.SetPlayerPosition(newPosition);

            return this;
        }

        public override void Exit(ScriptableStateMachine controller)
        {

        }
    }
}