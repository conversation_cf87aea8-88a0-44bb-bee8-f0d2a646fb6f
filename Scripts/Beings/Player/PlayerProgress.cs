// Copyright Isto Inc.
using Isto.Core.Audio;
using Isto.Core.Configuration;
using Isto.Core.Data;
using Isto.Core.Enums;
using Isto.Core.Game;
using Isto.Core.Items;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Beings
{
    /// <summary>
    /// Tracks the players progress through the game in terms of which items have been unlocked and built
    /// Either we should generalize this and not be just about items and harvestables, or we should rename
    /// this to be more specific.
    /// </summary>
    public class PlayerProgress : MonoBehaviour, IDataLoadCompleteHandler
    {
        // Extend this enumeration in your project.
        // Note: The values will be used to index into playerUnlocks[] so they need to progress incrementally from 0
        // This could be alleviated by finding entries matching to an enum instead of relying on the int value of the enum
        public class ItemUnlockStepsEnum : Int32Enum<ItemUnlockStepsEnum>
        {
            public static readonly ItemUnlockStepsEnum NONE = new ItemUnlockStepsEnum(0, nameof(NONE));

            public ItemUnlockStepsEnum(int value, string name) : base(value, name)
            {
            }
        }


        // UNITY HOOKUP

        public List<ItemList> playerUnlocks;

        [Header("Harvesting")]
        public List<HarvestableItem> defaultHarvestableItems;

        [Header("Testing only")]
        [Tooltip("TESTING ONLY, UNLOCKS ALL ITEMS")]
        public bool unlockAll;


        // OTHER FIELDS

        private HashSet<string> _unlockedItems = new HashSet<string>();
        private HashSet<string> _createdItems = new HashSet<string>(); // Using hashset for performance
        private HashSet<string> _seenItems = new HashSet<string>(); // Add if you've seen the notification
        private HashSet<string> _playerHarvestableItems;

        private CoreItem _trackedItem;


        // PROPERTIES

        public HashSet<string> UnlockedItems { get { return _unlockedItems; } }
        public HashSet<string> CreatedItems { get { return _createdItems; } }
        public HashSet<string> SeenItems { get { return _seenItems; } }


        // INJECTION

        private GameplaySettings _gameplaySettings;
        private GameState _gameState;

        [Inject]
        public void Inject(IGameSounds gameSounds, GameplaySettings gameplaySettings, GameState gameState)
        {
            _gameplaySettings = gameplaySettings;
            _gameState = gameState;
        }


        // LIFECYCLE EVENTS

        /// <summary>
        /// Loads all the starting created items into the internal list of created items
        /// </summary>
        private void Awake()
        {
            if (_playerHarvestableItems == null)
            {
                _playerHarvestableItems = new HashSet<string>();
            }

            for (int i = 0; i < defaultHarvestableItems.Count; i++)
            {
                _playerHarvestableItems.Add(defaultHarvestableItems[i].itemID);
            }

            if (playerUnlocks.Count > 0)
            {
                for (int i = 0; i < playerUnlocks[0].items.Count; i++)
                {
                    _unlockedItems.Add(playerUnlocks[0].items[i].itemID);
                }
            }
        }

        private void Start()
        {
            // Unlocking items here in case you're starting from editor with a custom set of items
            UnlockStartingItemSet();
        }

        private void OnEnable()
        {
            RegisterEvents();
        }

        private void OnDisable()
        {
            UnregisterEvents();
        }


        // EVENT HANDLING

        private void RegisterEvents()
        {
            GlobalGameplayEvents.ItemCrafted += OnItemCrafted;
            GlobalGameplayEvents.ItemAddedToPlayerInventory += OnItemAddedToPlayerInv;
        }

        private void UnregisterEvents()
        {
            GlobalGameplayEvents.ItemCrafted -= OnItemCrafted;
            GlobalGameplayEvents.ItemAddedToPlayerInventory -= OnItemAddedToPlayerInv;
        }

        /// <summary>
        /// This ensures that any items that have been unlocked by game progress are unlocked on load.
        /// </summary>
        public void OnDataLoadComplete()
        {
            UnlockMissingItemSetsForCurrentGameState();
        }

        private void OnItemCrafted(object sender, ItemEventArgs e)
        {
            AddCraftedItem(e.item);
        }

        /// <summary>
        /// Event handler for items being added to players inventory. Makes sure item is considered crafted in case
        /// the item was picked up but it hadn't been crafted before.
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void OnItemAddedToPlayerInv(object sender, ItemEventArgs e)
        {
            // Add item to seen list
            if (e.item != null)
            {
                _seenItems.Add(e.item.itemID);
            }
        }


        // OTHER METHODS

        public virtual void UnlockStartingItemSet()
        {
            AddStartingItems();
        }

        /// <summary>
        /// Override this to make sure the player unlocks the correct items.
        /// Should cover the case where previous unlocks are added and the player has a save that's past that state.
        /// </summary>
        public virtual void UnlockMissingItemSetsForCurrentGameState()
        {
            // Need to know how game progress is defined to be able to provide implementation.
            // Handle this in a specialized class for your project.
        }

        private void AddStartingItems()
        {
            ScenarioSetup currentSetup = _gameplaySettings.GetStartingSetup(); // Also known as GameSetup

            if (currentSetup == null)
                return;

            for (int i = 0; i < currentSetup.items.itemPiles.Count; i++)
            {
                ItemPile pile = currentSetup.items.itemPiles[i];

                AddCraftedItem(pile.item);
            }

            for (int i = 0; i < currentSetup.startingUpgrades.Count; i++)
            {
                AddCraftedItem(currentSetup.startingUpgrades[i]);
            }
        }

        public void AddCraftedItem(CoreItem item)
        {
            if (item != null)
            {
                _createdItems.Add(item.itemID);
                _unlockedItems.Add(item.itemID);
            }
            else
            {
                Debug.LogError("Null Item passed to PlayerProgress");
            }
        }

        public void UnlockItems(ItemList unlockList)
        {
            for (int i = 0; i < unlockList.items.Count; i++)
            {
                CoreItem nextItem = unlockList.items[i];

                if (!IsItemUnlocked(nextItem))
                {
                    UnlockItem(nextItem);
                }
            }
        }

        public void UnlockItemSet(ItemUnlockStepsEnum unlock)
        {
            ItemList list = GetAllItemsFromUnlock(unlock);
            UnlockItems(list);
        }

        /// <summary>
        /// We have a list of items that get unlocked associated with an enum (in atrio).
        /// Return it and check for errors
        /// </summary>
        /// <param name="unlock"></param>
        /// <returns></returns>
        public ItemList GetItemListFromUnlock(ItemUnlockStepsEnum unlock)
        {
            ItemList outputItems = ScriptableObject.CreateInstance(typeof(ItemList)) as ItemList;
            outputItems.items = new List<CoreItem>();

            if (unlock == ItemUnlockStepsEnum.NONE)
                return outputItems;

            int indexInList = unlock.Value;

            if (indexInList >= playerUnlocks.Count)
                return outputItems;

            outputItems.items.AddRange(playerUnlocks[indexInList].items);
            return outputItems;
        }

        public ItemList GetNextItemFromUnlock(ItemUnlockStepsEnum unlock)
        {
            ItemList outputItems = ScriptableObject.CreateInstance(typeof(ItemList)) as ItemList;
            outputItems.items = new List<CoreItem>();

            int indexInList = unlock.Value;
            if (indexInList + 1 < playerUnlocks.Count)
            {
                outputItems.items.AddRange(playerUnlocks[indexInList + 1].items);
            }

            return outputItems;
        }

        public ItemList GetAllItemsFromUnlock(ItemUnlockStepsEnum unlock)
        {
            ItemList outputItems = ScriptableObject.CreateInstance(typeof(ItemList)) as ItemList;
            outputItems.items = new List<CoreItem>();

            if (unlock == ItemUnlockStepsEnum.NONE)
                return outputItems;

            int indexInList = unlock.Value;

            if (indexInList > playerUnlocks.Count - 1)
            {
                indexInList = playerUnlocks.Count - 1;
                Debug.LogWarning("Not enough unlocks in PlayerProgress.PlayerUnlocks list for item unlock set: " + unlock);
            }

            for (int i = 0; i <= indexInList; i++)
            {
                if (i >= playerUnlocks.Count || playerUnlocks[i] == null)
                {
                    Debug.LogError("No Item Unlock" + i.ToString() + "Set for Player Progress. This will cause errors");
                    return null;
                }

                outputItems.items.AddRange(playerUnlocks[i].items);
            }

            return outputItems;
        }

        public ItemList GetLockedItems(ItemList itemList)
        {
            ItemList list = ScriptableObject.CreateInstance<ItemList>();
            list.items = new List<CoreItem>();
            for (int i = 0; i < itemList.items.Count; i++)
            {
                CoreItem nextItem = itemList.items[i];
                if (!IsItemUnlocked(nextItem))
                {
                    list.items.Add(nextItem);
                }
            }

            return list;
        }

        public void UnlockItem(CoreItem item)
        {
            if (item != null)
            {
                _unlockedItems.Add(item.itemID);
            }
            else
            {
                Debug.LogWarning("Trying to unlock null item in Player Progress.");
            }
        }

        public void ItemSeen(CoreItem item)
        {
            if (item == null)
                return;

            //Make sure not to re-add the notification
            _seenItems.Add(item.itemID);
        }

        /// <summary>
        /// Used by notifcations to check if player has seen the item in the crafting menu
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        public bool HasItemBeenSeen(CoreItem item)
        {
            if (item == null)
                return false;

            return _seenItems.Contains(item.itemID);
        }

        // Methods		
        /// <summary>
        /// Checks if the passed in item has been unlocked so the player can craft it.
        /// </summary>
        /// <param name="item"></param>
        /// <returns>True if item can be crafted, false otherwise.</returns>
        public bool IsItemUnlocked(CoreItem item)
        {
            if (item == null)
                return false;

            if (_gameState.CurrentGameMode != null && item.ForbiddenForCurrentGameMode(_gameState.CurrentGameMode))
                return false;

            return _unlockedItems.Contains(item.itemID) || unlockAll;
        }

        /// <summary>
        /// Checks if the specified item has been created previously by the player
        /// </summary>
        /// <param name="advancedItem"></param>
        /// <returns></returns>
        public bool HasItemBeenCreated(CoreItem advancedItem)
        {
            if (advancedItem == null)
                return false;

            return _createdItems.Contains(advancedItem.itemID);
        }

        /// <summary>
        /// Allows the item to be harvested by the player
        /// </summary>
        /// <param name="item"></param>
        public void UnlockPlayerHarvestableItem(HarvestableItem item)
        {
            if (_playerHarvestableItems == null)
            {
                _playerHarvestableItems = new HashSet<string>();
            }

            _playerHarvestableItems.Add(item.itemID);
        }

        public void LockPlayerHarvestableItem(HarvestableItem item)
        {
            _playerHarvestableItems.Remove(item.itemID);
        }

        public bool PlayerCanHarvest(HarvestableItem item)
        {
            bool isGrowingItem = item.droppedItemsWhileGrowing.Count > 0;

            return _playerHarvestableItems.Contains(item.itemID) || unlockAll || isGrowingItem;
        }
    }
}