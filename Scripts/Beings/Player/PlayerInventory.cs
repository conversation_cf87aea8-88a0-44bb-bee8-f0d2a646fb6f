// Copyright Isto Inc.

using I2.Loc;
using Isto.Core.Achievements;
using Isto.Core.Audio;
using Isto.Core.Automation;
using Isto.Core.Configuration;
using Isto.Core.Game;
using Isto.Core.Items;
using Isto.Core.Localization;
using Isto.Core.UI;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AddressableAssets;
using Zenject;

namespace Isto.Core.Beings
{
    /// <summary>
    /// Specialized item container for Player inventory.  Sends event messages when items are added.
    /// </summary>
    public class PlayerInventory : ItemContainer
    {
        // unity hookup

        [Header("Equip")]
        [SerializeField] private int _numEquipSlots = 8;

        [Header("Death")]
        [SerializeField] private bool _dropAllItemsOnDeath = true;
        public GameObject backpackPrefab; //GameObject that is created when instancing this item into the world
        public AssetReference backpackAddressableAsset;

        [Header("Message Params")]
        public LocalizedString fullInventoryMessageKey;
        [FMODUnity.EventRef] public string fullInventorySoundRef;


        // variables

        [HideInInspector] public ItemPileList startingItems;
        [HideInInspector] public ItemList startingEquippedItems;

        private bool _suppressFiringOnChangedEvents;
        private ItemPileList _equippedItems;


        // properties

        public int EquipSlotCount { get { return _numEquipSlots; } }


        // Inject

        private IUIDisplayMessage _playerMessageDisplay;
        private IGameSounds _gameSounds;
        private IAchievements _achievements;
        private IUIMessages _uIMessages;
        private PlayerHealth _playerHealth;
        private AutomationSystem _autoSystem;
        private GameplaySettings _gameplaySettings;
        private GameState _gameState;

        private MasterItemList _masterList;

        [Inject]
        public void Inject([InjectOptional] IUIDisplayMessage messageDisplay,
                           IGameSounds gameSounds, IAchievements achievements,
                           [Inject(Id = UIMessageHandlerType.Message, Optional = true)] IUIMessages uIMessages,
                           [InjectOptional] PlayerHealth playerHealth, AutomationSystem automationSystem,
                           GameplaySettings gameplaySettings, GameState gameState,
                           MasterItemList masterList)
        {
            _gameplaySettings = gameplaySettings;
            _playerMessageDisplay = messageDisplay;
            _uIMessages = uIMessages;
            _gameSounds = gameSounds;
            _autoSystem = automationSystem;

            _playerHealth = playerHealth;
            if (_playerHealth = null)
            {
                _playerHealth.Killed += OnPlayerKilled;
            }

            ScenarioSetup currentSetup = gameplaySettings.GetStartingSetup();

            startingItems = currentSetup?.items;
            startingEquippedItems = currentSetup?.equipped;

            _achievements = achievements;
            _gameState = gameState;

            _masterList = masterList;
        }


        // Lifecyce events

        public override void Awake()
        {
            _suppressFiringOnChangedEvents = true;

            // Needs to be created before adding items to the inventory
            _equippedItems = ScriptableObject.CreateInstance(typeof(ItemPileList)) as ItemPileList;
            _equippedItems.itemPiles = new List<ItemPile>();

            base.Awake();
        }

        protected void OnDisable()
        {
            if (_playerHealth != null)
            {
                _playerHealth.Killed -= OnPlayerKilled;
            }
        }

        /// <summary>
        /// Called externally by PlayerUpgrades to ensure all upgrades are installed before loading starting items
        /// </summary>
        public void LoadStartingItemsAndEquipment()
        {
            if (startingItems != null)
            {
                //Add all the starting items to the actual inventory
                for (int i = 0; i < startingItems.itemPiles.Count; i++)
                {
                    AddAt(startingItems.itemPiles[i], i, false);
                }
            }

            if (startingEquippedItems != null)
            {
                //Set up the equipped items (note that they may not be in the inventory and have a count of 0)
                int equipSlotsNeeded = startingEquippedItems.items.Count;
                if (equipSlotsNeeded > _numEquipSlots)
                {
                    Debug.LogError($"Your starting equipped items can be maximum {_numEquipSlots} but you have {equipSlotsNeeded}");
                }
                else
                {
                    for (int i = 0; i < _numEquipSlots; i++)
                    {
                        // If starting item set, equip it
                        if (startingEquippedItems.items.Count > i)
                        {
                            Item item = startingEquippedItems.items[i];
                            int count = GetItemCount(startingEquippedItems.items[i]);

                            ItemPile newEquipitem = new ItemPile((CoreItem)item, count);
                            _equippedItems.itemPiles.Add(newEquipitem);
                        }
                        // Otherwise, add empty item
                        else
                        {
                            ItemPile emptyPile = new ItemPile();
                            _equippedItems.itemPiles.Add(emptyPile);
                        }
                    }
                }
            }

            _suppressFiringOnChangedEvents = false;

            Events.RaiseEvent(Events.PLAYER_INVENTORY_CHANGED_EVENT);
        }

        public int GetItemCount(Item item)
        {
            int count = 0; // If the item doesn't exist in the list, return a count of 0
            for (int i = 0; i < Items.Count; i++)
            {
                if (Items[i].item == item)
                    count += Items[i].count;
            }
            return count;
        }

        public int GetItemIndex(Item item)
        {
            int index = -1; // If the item doesn't exist in the list, return a count of 0
            for (int i = 0; i < Items.Count; i++)
            {
                if (Items[i].item == item)
                    index = i;
            }
            return index;

        }

        /// <summary>
        /// Sets the item into the equip item slot in the inventory.
        /// </summary>
        /// <param name="item">The item to equip</param>
        /// <param name="slotNumber">Slot number to equip item into. [0,1]</param>
        public void EquipItem(Item item, int slotNumber)
        {
            if (slotNumber >= _numEquipSlots)
            {
                Debug.LogError($"Attempted to equip an item to a slot greater than max number of slots. Slot:{slotNumber}, Max:{_numEquipSlots}");
                return;
            }

            int count = GetItemCount(item);
            ItemPile newEquipItem = new ItemPile((CoreItem)item, count);

            // If you already have that item equipped in a different slot, clear it
            for (int i = 0; i < _equippedItems.itemPiles.Count; i++)
            {
                if (i != slotNumber && _equippedItems.itemPiles[i].HasItems() && _equippedItems.itemPiles[i].item == item)
                {
                    RemoveEquippedItem(i);
                }
            }

            if (_equippedItems.IsInRange(slotNumber))
            {
                _equippedItems.itemPiles[slotNumber] = newEquipItem;
            }
            else
                _equippedItems.itemPiles.Add(newEquipItem); //Sometimes you have only 1 item equipped. If thats the case, you have to add to the list.

            Events.RaiseEvent(Events.PLAYER_INVENTORY_CHANGED_EVENT);
            GlobalGameplayEvents.OnItemEquipped(this, (CoreItem)item);
        }

        /// <summary>
        /// If there is an available slot, auto equip that item
        /// </summary>
        /// <param name="item"></param>
        /// <param name="canRemove">if you click repeatedly, it will flip back and forth
        /// taking it out and putting it back in</param>
        public bool TryAutoEquipItem(Item item, bool canRemove)
        {
            if (item == null)
                return false;

            if (!IsAutoEquippable(item))
                return false;

            //First, check to see if you've already equipped it
            for (int i = 0; i < _numEquipSlots; i++)
            {
                if (_equippedItems.itemPiles[i].item == item)
                {
                    if (canRemove)
                        RemoveEquippedItem(i);

                    return false;
                }
            }

            //If not, find the first empty slot and equip it
            for (int i = 0; i < _numEquipSlots; i++)
            {
                // if at last slot, just equip item over existing item, if one there
                if (_equippedItems.itemPiles[i].item == null)
                {
                    EquipItem(item, i);

                    return true;
                }
            }

            return false;
        }

        protected virtual bool IsAutoEquippable(Item item)
        {
            if (item.GetType() == typeof(UpgradeItem))
                return false; // Don't autequip Permanent upgrades

            if (item.GetType() != typeof(AdvancedItem))
                return false;

            return true;
        }

        /// <summary>
        /// Get the equipped item at the slot number passed in.
        /// </summary>
        /// <param name="slotNumber"></param>
        /// <returns>The equipped item if set, null otherwise</returns>
        public Item GetEquippedItem(int slotNumber)
        {
            if (slotNumber >= _numEquipSlots)
            {
                Debug.LogError("Attempted to get an equipped item from a slot greater than " + _numEquipSlots);
                return default;
            }

            if (_equippedItems.IsInRange(slotNumber))
            {
                return _equippedItems.itemPiles[slotNumber].item;
            }

            return default;
        }

        public ItemPile GetEquippedPile(int slotNumber)
        {
            if (slotNumber >= _numEquipSlots)
            {
                Debug.LogError("Attempted to equip an item to a slot greater than " + _numEquipSlots);
                return default;
            }

            if (_equippedItems.IsInRange(slotNumber))
            {
                //Refresh the item count for equipped item first
                _equippedItems.itemPiles[slotNumber].count = GetCountOfItem(_equippedItems.itemPiles[slotNumber].item);

                return _equippedItems.itemPiles[slotNumber];
            }
            else
            {
                Debug.LogError("Equipped Item list is not of correct size.");
                return new ItemPile();
            }
        }

        public void RemoveEquippedItem(int index)
        {
            if (_equippedItems.IsInRange(index))
            {
                _equippedItems.itemPiles[index].item = null;
                Events.RaiseEvent(Events.PLAYER_INVENTORY_CHANGED_EVENT);
            }
        }

        public override void SetPileCount(int newCount)
        {
            InitializeList();

            if (Items.Capacity < newCount)
            {
                Items.Capacity = newCount;
            }

            int deltaPiles = newCount - pileCount;

            if (deltaPiles < 0)
            {
                for (int i = deltaPiles; i < 0; i++)
                    Items.RemoveAt(Items.Count - 1);
            }
            else
            {
                // Fill the item list with empty ItemPiles
                for (int i = 0; i < deltaPiles; i++)
                    Items.Add(new ItemPile());
            }

            pileCount = newCount;
        }

        public override int Add(CoreItem item, int count = 1, bool sendUpdateEvent = true)
        {
            int depositedCount = base.Add(item, count, sendUpdateEvent);

            // If deposit was successfull, send unsuppressable event
            if (depositedCount > 0)
            {
                // For now this only does achievement triggers
                Events.RaiseEvent(Events.PLAYER_ITEM_ACQUIRED, item);
            }

            // If deposit was successfull, send suppressable events
            if (depositedCount > 0 && !_suppressFiringOnChangedEvents)
            {
                TryAutoEquipItem(item, canRemove: false);

                if (sendUpdateEvent)
                {
                    // Seen items (core), tutorial actions, item finder
                    GlobalGameplayEvents.OnItemAddedToPlayerInventory(this, item, depositedCount, this);

                    // Tutorial action(s), equipped items (display and slot - not sure if display is deprecated)
                    SendPlayerInventoryUpdateEvents(item, depositedCount);
                }

                if (_uIMessages != null && sendUpdateEvent)
                {
                    string localizedMessage = $"+{depositedCount} {item.itemName}";
                    _uIMessages.CreateMessage(localizedMessage, item.icon);
                }
            }

            return depositedCount;
        }

        public int AddAt(ItemPile pile, int index, bool triggerEvents = true)
        {
            int depositedCount = base.AddAt(pile, index);

            //If deposite was successfull, trigger event to let listeners know
            if (depositedCount > 0)
            {
                GlobalGameplayEvents.OnItemAddedToPlayerInventory(this, pile.item, pile.count, this);

                if (triggerEvents)
                {
                    SendPlayerInventoryUpdateEvents(pile.item, pile.count);
                }
            }

            return depositedCount;
        }

        public override int Remove(CoreItem item, int count = 1, bool sendUpdateEvent = true)
        {
            int removedCount = base.Remove(item, count, sendUpdateEvent);

            if (removedCount > 0 && !_suppressFiringOnChangedEvents && sendUpdateEvent)
            {
                SendPlayerInventoryUpdateEvents(item, count);
            }

            return removedCount;
        }

        public override ItemPile RemoveAt(int index)
        {
            ItemPile removedPile = base.RemoveAt(index);

            if (removedPile != null && removedPile.count > 0)
            {
                SendPlayerInventoryUpdateEvents(removedPile.item, removedPile.count);
            }

            return removedPile;
        }

        public override ItemPile RemoveAt(int index, int count)
        {
            ItemPile removedPile = base.RemoveAt(index, count);

            // Not sure if player inventory events should be skipped even if sendUpdateEvent is false.
            if (removedPile != null && removedPile.count > 0)
            {
                SendPlayerInventoryUpdateEvents(removedPile.item, count);
            }

            return removedPile;
        }

        public override List<ItemPile> RemoveAll(bool triggerEvent = true)
        {
            List<ItemPile> removedItems = base.RemoveAll(triggerEvent);

            if (removedItems.Count > 0 && triggerEvent)
                SendPlayerInventoryUpdateEvents(null, 0);

            return removedItems;
        }

        /// <summary>
        /// Catches SendMessage from base class to display inventory full message on player
        /// </summary>
        public void InventoryFull()
        {
            if (_playerMessageDisplay != null)
            {
                _playerMessageDisplay.DisplayMessage(Loc.Get(fullInventoryMessageKey), Constants.DEFAULT_PLAYER_MESSAGE_DISPLAY_TIME);
            }
            Events.RaiseEvent(Events.PLAYER_INVENTORY_FULL_EVENT);
            _gameSounds.PlayOneShot(fullInventorySoundRef, transform.position);
        }

        /// <summary>
        /// Checks to see if the passed in item is equipped and if so, sets the slot number for
        /// that item.
        /// </summary>
        /// <param name="item">CoreItem to check if is equipped.</param>
        /// <param name="slotNumber">Slot number for the item is set if equipped, set to -1 otherwise.</param>
        /// <returns>True if item is equipped, false otherwise</returns>
        public bool IsItemEquipped(CoreItem item, out int slotNumber)
        {
            slotNumber = -1;

            for (int i = 0; i < EquipSlotCount; i++)
            {
                Item equippedItem = GetEquippedItem(i);

                if (equippedItem != null && equippedItem.itemName != string.Empty && equippedItem.itemName.Equals(item.itemName))
                {
                    slotNumber = i;
                    return true;
                }
            }

            return false;
        }

        // Events

        public void DisableEventFiring()
        {
            _suppressFiringOnChangedEvents = true;
        }

        public void EnableEventFiring()
        {
            _suppressFiringOnChangedEvents = false;
        }

        /// <summary>
        /// Triggers player specific inventory update events
        /// </summary>
        private void SendPlayerInventoryUpdateEvents(CoreItem item, int change)
        {
            Events.RaiseEvent(Events.PLAYER_INVENTORY_CHANGED_EVENT);
        }

        private void OnPlayerKilled(object sender, HealthEventArgs e)
        {
            if (_dropAllItemsOnDeath && this.GetTotalNumberOfItems() > 0)
            {
                DropInventory();

                SendPlayerInventoryUpdateEvents(null, 0);
                SendUpdateEvents(null, 0);
            }
        }

        public void SendUpdateEvent()
        {
            SendPlayerInventoryUpdateEvents(null, 0);
            SendUpdateEvents(null, 0);
        }

        public void DropInventory()
        {
            if (this.GetTotalNumberOfItems() < 0)
                return;

            Vector3 itemDropPosition = transform.position + Vector3.right + Vector3.back;

            //Deep Copy the item piles so we can clear the player's inventory immediately
            List<ItemPile> packItems = new List<ItemPile>();
            foreach (var pile in this.Items)
            {
                packItems.Add(new ItemPile(pile));
            }

            if (!string.IsNullOrEmpty(backpackAddressableAsset.AssetGUID))
            {
                backpackAddressableAsset.InstantiateAsync(itemDropPosition, Quaternion.identity).Completed += op =>
                {
                    if (op.Status == UnityEngine.ResourceManagement.AsyncOperations.AsyncOperationStatus.Succeeded)
                    {
                        GameObject createdItem = op.Result;
                        DroppedBackPack pack = createdItem.GetComponent<DroppedBackPack>();
                        pack.ReceiveContents(packItems, this.PileCount, this.PileSize);
                    }
                };
            }
            else
            {
                Debug.LogWarning("No valid AddressableAsset GUID for dropped backpack. Cannot drop");
            }

            RemoveAll(false);
        }

        [ContextMenu("TestAddCoreItems")]
        private void TestAddCoreItems()
        {
            var item = _masterList.GetItemByID<CoreItem>("BloodRock");
            Add(item, count: 1);
        }
    }
}