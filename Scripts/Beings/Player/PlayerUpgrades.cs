// Copyright Isto Inc.

using Isto.Core.Automation;
using Isto.Core.Configuration;
using Isto.Core.Game;
using Isto.Core.Items;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Beings
{
    /// <summary>
    /// For now this is specialized for AutomationPlayerUpgrades, either we should decouple or we should clarify
    /// and rename all the upgrade stuff.
    /// </summary>
    public class PlayerUpgrades : MonoBehaviour
    {
        // OTHER FIELDS

        private GameState _gameState;
        private AutomationPlayerController _player;
        private CraftingQueue _craftingQueue;
        private PlayerInventory _playerInventory;
        private List<UpgradeItem> _startingUpgrades = new List<UpgradeItem>();
        private List<UpgradeItem> _activeUpgrades = new List<UpgradeItem>();


        // PROPERTIES

        // TODO: these should not be tracked here
        public bool AutoCollectEnabled { get; set; }
        public float TorchDurationIncrease { get; set; }

        // INJECTION

        [Inject]
        public void Inject(GameplaySettings gameplaySettings, GameState gameState,
                          [InjectOptional] AutomationPlayerController playerController,
                          [InjectOptional] CraftingQueue craftingQueue,
                          [InjectOptional] PlayerInventory playerInventory)
        {
            ScenarioSetup startingSetup = gameplaySettings.GetStartingSetup();
            if(startingSetup != null)
            {
                _startingUpgrades = startingSetup.startingUpgrades;
            }

            _gameState = gameState;

            _player = playerController;
            _playerInventory = playerInventory;
            _craftingQueue = craftingQueue;
        }


        // LIFECYCLE EVENTS

        private void Start()
        {
            //Note: this will cause duplicate upgrades with OnLoadComplete
            //but I imagine that in certain testing flows it is needed, and it doesn't seem to cause any harm.
            InstallStartingUpgrades();

            if (_playerInventory != null)
            {
                _playerInventory.LoadStartingItemsAndEquipment();
            }

            RegisterEvents();
        }

        private void OnDestroy()
        {
            UnregisterEvents();
        }

        // OnEnable happens before injection with network instantiate
        private void OnEnable()
        {
            //RegisterEvents();
        }

        private void OnDisable()
        {
            //UnregisterEvents();
        }


        // EVENT HANDLING
        
        private void RegisterEvents()
        {
            if (_craftingQueue != null)
            {
                _craftingQueue.ItemCrafted += OnItemCrafted;
            }
            
            _gameState.LoadComplete += OnLoadComplete;
        }

        private void UnregisterEvents()
        {
            if (_craftingQueue != null)
            {
                _craftingQueue.ItemCrafted -= OnItemCrafted;
            }

            _gameState.LoadComplete -= OnLoadComplete;
        }

        private void OnLoadComplete(object sender, System.EventArgs e)
        {
            InstallStartingUpgrades();
        }

        /// <summary>
        /// Listens when the crafting queue creates an item and checks if it's an upgrade item.  If so, triggers
        /// the upgrade items effects.
        /// In early Atrio you crafted items to upgrade yourself, but this flow has been unused since research.
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e">Parameter containing the item that was crafted</param>
        private void OnItemCrafted(object sender, ItemEventArgs e)
        {
            // If item crafted was an Upgrade, activate it's effects
            if (e.item.GetType() == typeof(UpgradeItem))
            {
                UpgradeItem upgrade = e.item as UpgradeItem;

                InstallUpgrade(upgrade);
            }
        }


        // ACCESSORS

        public List<UpgradeItem> GetActiveUpgrades()
        {
            return _activeUpgrades;
        }


        // OTHER METHODS

        public void InstallStartingUpgrades()
        {
            if(_startingUpgrades != null)
            {
                InstallUpgrades(_startingUpgrades);
            }
        }

        public void InstallUpgrades(List<UpgradeItem> upgrades)
        {
            for (int i = 0; i < upgrades.Count; i++)
            {
                UpgradeItem upItem = upgrades[i];

                InstallUpgrade(upItem);
            }
        }

        // In Atrio in the end this was the only flow used to install non-starting upgrades, as they came from research
        public void InstallUpgrade(UpgradeItem upgrade)
        {
            if (_player == null)
                return;

            if (upgrade == null || _activeUpgrades.Contains(upgrade))
                return;

            _activeUpgrades.Add(upgrade);

            for (int j = 0; j < upgrade.effects.Count; j++)
            {
                upgrade.effects[j].ActivateEffect(_player);
            }
        }

        public void UninstallAllUpgrades()
        {
            if (_player == null)
                return;

            // Note: this is fragile but works for now, and is for cheats rather than normal flow
            for (int i = _activeUpgrades.Count - 1; i >= 0; i--)
            {
                UpgradeItem upItem = _activeUpgrades[i];

                for (int j = upItem.effects.Count - 1; j >= 0; j--)
                {
                    upItem.effects[j].DeactivateEffect(_player);
                }
            }

            _activeUpgrades.Clear();
        }
    }
}