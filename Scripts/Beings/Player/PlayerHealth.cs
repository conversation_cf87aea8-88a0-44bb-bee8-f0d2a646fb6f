// Copyright Isto Inc.

using FMODUnity;
using I2.Loc;
using Isto.Core.Analytics;
using Isto.Core.Audio;
using Isto.Core.Cameras;
using Isto.Core.Game;
using Isto.Core.Inputs;
using Isto.Core.Items;
using Isto.Core.Localization;
using Isto.Core.Scenes;
using Isto.Core.Tiles;
using Isto.Core.UI;
using System;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Beings
{
    public class PlayerHealth : MonoBehaviour, ISliderDisplay
    {
        // UNITY HOOKUP

        [Header("Core Health Config")]
        public float currentMaxHealth = 100f;
        public readonly float startingMaxHealth = 100f;
        public float healPerSecond = 2f;
        public float stunTimeAfterDamage = 0.5f;
        public float invincibleTimeAfterDamage = 0.5f;

        [Header("Core Health Status")]
        [SerializeField] protected float _currentHealth;
        [SerializeField] protected bool _isInvincible;

        [Header("Core Audio")]
        [EventRef] public string takeDamageAudioRef;

        [Header("Core Buffs and Resistances")]
        public BuffEffect activeBuff = null;
        public float buffTimeRemaining = 0;
        public float buffDurationUpgrade = 0; // additional percentage
        public List<ResistanceBuff> activeResistances;
        public float damageReduction = 0; // value between 0-1
        [SerializeField] private LocalizedString _buffExpiringMessage;

        [Header("Core Environment")]
        [SerializeField] protected TileGroundCheck _tileGroundCheck;


        // OTHER FIELDS

        private bool _buffWarningSent = false;


        // PROPERTIES

        public float CurrentHealth { get { return _currentHealth; } }
        public float StunPercent => 0f;
        public bool Alive { get { return _currentHealth > 0f; } }


        // EVENTS

        public event EventHandler<HealthEventArgs> Killed;
        public event EventHandler<HealthEventArgs> TookDamage;

        public event EventHandler<BuffEventArgs> NewBuffSet;
        public event System.EventHandler Respawned;


        // INJECTION

        private IControls _controls;
        protected IGameSounds _sounds;
        private IUIMessages _uIMessages;
        private IAnalyticsHandler _analytics;

        private UIDamageTextDisplay.Pool _dmgDisplayPool;
        protected CameraController _cameraControl;
        private GameScenesReference _gameScenesReference;

        [Inject]
        public void Inject(IControls controls, [InjectOptional] IGameSounds gameSounds, IAnalyticsHandler analytics,
            [InjectOptional(Id = UIMessageHandlerType.HighPriorityMessage)] IUIMessages messages,
            [InjectOptional] UIDamageTextDisplay.Pool dmgDisplayPool, GameScenesReference gameScenesReference,
            [InjectOptional] CameraController cameraController)
        {
            _controls = controls;
            _sounds = gameSounds;
            _analytics = analytics;
            _uIMessages = messages;

            _dmgDisplayPool = dmgDisplayPool;
            _gameScenesReference = gameScenesReference;
            _cameraControl = cameraController;
        }


        // LIFECYCLE EVENTS

        protected virtual void Awake()
        {
            _currentHealth = currentMaxHealth;
        }

        private void Update()
        {
            if (Alive)
            {
                _currentHealth = Mathf.Clamp(_currentHealth + (healPerSecond * Time.deltaTime), 0, currentMaxHealth);
            }

            UpdateBuffTimer();
            UpdateEnvironmentDamageAndResistances();
        }


        // ACCESSORS

        protected bool HasResistanceTo(Health.DamageTypeEnum damageType)
        {
            for (int i = 0; i < activeResistances.Count; i++)
            {
                if (activeResistances[i].DamageType == damageType)
                    return true;
            }

            return false;
        }

        public void SetHealth(float value)
        {
            _currentHealth = value;
        }

        public void SetMaxHealth(float increaseFactor)
        {
            currentMaxHealth = startingMaxHealth + (startingMaxHealth * increaseFactor);
        }

        /// <summary>
        /// Sets flag on player health to make player invincible or not.  Being invincible still means player takes 
        /// force impacts from damage though.
        /// </summary>
        /// <param name="invincible"></param>
        public void SetInvincible(bool invincible)
        {
            _isInvincible = invincible;
        }

        public void SetInvincible(float time)
        {
            _isInvincible = true;

            CancelInvoke();

            Invoke(nameof(DisableInvicibleInvoked), time);
        }

        public float GetHealthRemaining(Health.DamageTypeEnum type)
        {
            return _currentHealth;
        }

        public float GetCurrentPercent()
        {
            return _currentHealth / currentMaxHealth;
        }


        // OTHER METHODS

        public void TakeDamage(float damage, Health.DamageTypeEnum type, Health.DamageSourceEnum source, Transform damageSourceTransform = null)
        {
            if (type == null)
                type = Health.DamageTypeEnum.PHYSICAL;
            if (source == null)
                source = Health.DamageSourceEnum.MOB;


            //If health is already zero, the player is already dead
            if (!Alive || _isInvincible || GameState.StoryAnimationActive)
                return;

            //Modify the damage if resistances are active
            for (int i = 0; i < activeResistances.Count; i++)
            {
                damage = activeResistances[i].ModifyDamage(type, damage);
            }
            damage = damage - (damage * damageReduction);

            ApplyDamageByType(damage, type, source);

            _controls.VibrateController(Controls.VibrationMode.Long);

            TookDamage?.Invoke(this, new HealthEventArgs(damage, type, source, damageSourceTransform, stunTimeAfterDamage));

            // Show damage amount in the UI

            _dmgDisplayPool?.Spawn(-damage, transform.position, type);

            CameraController.CameraShakeTriggers shake = GetCameraShakeByType(type);
            if (shake != CameraController.CameraShakeTriggers.none)
            {
                _cameraControl?.AnimateCameraShake(shake);
            }

            if (!Alive)
            {
                ResetAllElementTimers();

                SendDeathAnalyticsData(type);
                SetNewBuff(null);
                Killed?.Invoke(this, new HealthEventArgs(damage, type, source));
            }
            else
            {
                SetInvincible(invincibleTimeAfterDamage);
            }
        }

        protected virtual void ApplyDamageByType(float damage, Health.DamageTypeEnum t, Health.DamageSourceEnum s)
        {
            if (t == Health.DamageTypeEnum.PHYSICAL)
            {
                // Override this with your damage type logic in your project.
                _sounds?.PlayOneShot(takeDamageAudioRef, transform.position);
                _currentHealth -= damage;
            }
            else
            {
                Debug.LogError("CorePlayerHealth attempted to apply unsupported type of damage: " + t.Name);
            }
        }

        protected virtual CameraController.CameraShakeTriggers GetCameraShakeByType(Health.DamageTypeEnum t)
        {
            // Override this with your damage type logic in your project.
            return CameraController.CameraShakeTriggers.shakeLight;
        }

        protected void SendDeathAnalyticsData(Health.DamageTypeEnum type)
        {
            string deathArea = _gameScenesReference.GetCurrentSceneName();
            if (String.IsNullOrEmpty(deathArea))
            {
                deathArea = "unknown";
            }

            // Note: untested - name used to have a different value. but old analytics are deprecated anyway
            _analytics.RegisterPlayerDeath(deathArea, gameObject.transform.position, new List<string> { type.Name });
        }

        public void Heal(float amount)
        {
            Heal(amount, Health.DamageTypeEnum.PHYSICAL);
        }

        public void Heal(float amount, Health.DamageTypeEnum type)
        {
            if (type == Health.DamageTypeEnum.PHYSICAL)
            {
                _currentHealth = Mathf.Clamp(_currentHealth + amount, 0, currentMaxHealth);
            }
            else
            {
                throw new System.NotImplementedException($"No handler for restoring health of type {type.Name} yet");
            }
        }

        public void Respawn()
        {
            _currentHealth = currentMaxHealth;

            Respawned?.Invoke(this, EventArgs.Empty);
        }

        public void SetNewBuff(BuffEffect buffEffect)
        {
            bool buffChanged = false;

            //Clear the old buff 
            if (activeBuff != null)
            {
                activeBuff.ExpireBuff(this.gameObject);
                buffChanged = true;
                _buffWarningSent = false;
            }

            //Set up new buff
            activeBuff = buffEffect;

            if (activeBuff != null)
            {
                activeBuff.ActivateBuff(this.gameObject);
                buffTimeRemaining = buffEffect.effectDuration * (1 + buffDurationUpgrade);
                buffChanged = true;
            }
            else
                buffTimeRemaining = 0;

            if (buffChanged)
                NewBuffSet?.Invoke(this, new BuffEventArgs(activeBuff));
        }

        protected virtual void UpdateBuffTimer()
        {
            if (!GameState.StoryAnimationActive)
            {
                buffTimeRemaining -= Time.deltaTime;

                if (buffTimeRemaining < 10 && !_buffWarningSent && activeBuff != null)
                {
                    _buffWarningSent = true;
                    _uIMessages.CreateHighPriorityMsg(Loc.Get(_buffExpiringMessage), HighPriorityMessage.MessageType.warning);
                }
                if (buffTimeRemaining < 0)
                {
                    if (activeBuff != null)
                        _uIMessages.CreateMessage(Loc.Get(_buffExpiringMessage));
                    SetNewBuff(null);

                }
                else if (activeBuff != null)
                {
                    activeBuff.UpdateBuf(gameObject);
                }
            }
        }

        [ContextMenu("Disable Buff")]
        public void DisableBuff()
        {
            buffTimeRemaining = 0f;
        }

        /// <summary>
        /// Override this to add any logic you need to handle environmental hazards.
        /// (In Atrio this was for handling water and corrosion according to the tilemap)
        /// </summary>
        protected virtual void UpdateEnvironmentDamageAndResistances()
        {
            // No assumed environmental hazards in core library.
        }

        /// <summary>
        /// Part of the flow for handling death.
        /// Override this to reset any logic you use to handle environmental hazards.
        /// </summary>
        protected virtual void ResetAllElementTimers()
        {
            // No assumed environmental hazards in core library so nothing to reset.
        }

        private void DisableInvicibleInvoked()
        {
            _isInvincible = false;
        }

#if UNITY_EDITOR
        [ContextMenu("Kill Player")]
        private void KillPlayer()
        {
            TakeDamage(currentMaxHealth + 1, Health.DamageTypeEnum.PHYSICAL, Health.DamageSourceEnum.MOB);
        }

        [ContextMenu("Take 20 Damage")]
        private void TakeSomeDamage()
        {
            TakeDamage(20f, Health.DamageTypeEnum.PHYSICAL, Health.DamageSourceEnum.MOB);
        }
#endif
    }
}