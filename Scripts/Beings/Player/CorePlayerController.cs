// Copyright Isto Inc.

using Isto.Core.Inputs;
using Isto.Core.StateMachine;
using System.Collections;
using UnityEngine;
using Zenject;

namespace Isto.Core.Beings
{

    /// <summary>
    /// This represents a comnmon root for all our types of player
    /// </summary>
    [RequireComponent(typeof(PlayerAbilities))]
    [RequireComponent(typeof(PlayerHealth))]
    public abstract class CorePlayerController : ScriptableStateMachine, IPlayerController
    {
        // OTHER FIELDS
        
        protected PlayerAbilities _abilities;
        protected PlayerHealth _playerHealth;
        
        protected bool _statesPaused = false; // Flag that when set causes the current state to no longer run it's update function
        
        
        // PROPERTIES
        
        protected abstract Collider MainHitBox { get; }
        public State CurrentState { get { return _currentState; } }
        public PlayerAbilities Abilities { get { return _abilities; } }
        public abstract State MovementState { get; }
        public abstract bool IsMoving { get; }


        // INJECTION

        protected IControls _controls;
        
        [Inject]
        private void Inject(IControls controls)
        {
            _controls = controls;
        }


        // LIFECYCLE EVENTS

        protected virtual void Awake()
        {
            _abilities = GetComponent<PlayerAbilities>();
            _playerHealth = GetComponent<PlayerHealth>();
        }
        
        protected virtual void OnEnable()
        {
            RegisterEvents();
        }

        protected virtual void OnDisable()
        {
            UnregisterEvents();
        }

        protected virtual void Update()
        {
            if (_statesPaused || _currentState == null)
                return;
            
            // Proccess current state
            State nextState = _currentState.Run(this) as State;

            if (nextState != _currentState)
                ChangeState(nextState);
        }


        // EVENT HANDLING

        protected virtual void RegisterEvents()
        {
            _playerHealth.Killed += OnPlayerKilled;
            _playerHealth.TookDamage += OnPlayerTakeDamage;
        }

        protected virtual void UnregisterEvents()
        {
            _playerHealth.TookDamage -= OnPlayerTakeDamage;
            _playerHealth.Killed -= OnPlayerKilled;
        }

        protected abstract void OnPlayerTakeDamage(object sender, HealthEventArgs e);
        protected abstract void OnPlayerKilled(object sender, HealthEventArgs e);


        // ACCESSORS

        public virtual Vector3 GetPlayerPosition()
        {
            return transform.position;
        }

        public virtual void SetPlayerPosition(Vector3 pos)
        {
            transform.position = pos;
        }

        // OTHER METHODS

        // This is a system used by Atrio to have its states control the player behavior with a common method
        // It probably should not be public, and we can probably improve on how we control access to this
        // I don't think I want to impose it on all player controller implementations.
        //public abstract State ProcessMovementAndActions(bool movement, bool ability);

        /// <summary>
        /// Sets the hit box collider to be active or deactive.  Basically makes the player invincible
        /// </summary>
        /// <param name="state">Are colliders active</param>
        public virtual void SetPlayerColliderState(bool state)
        {
            MainHitBox.enabled = state;
        }
        
        public virtual void DisableAllPlayerControls(float time)
        {
            _controls.DisableControls(time);
        }

        public virtual void EnablePlayerControls()
        {
            _controls.EnableControls();
        }
        
        /// <summary>
        /// Sets the pause flag which determines if the current states update method is called each frame or not
        /// </summary>
        /// <param name="paused">Should player be paused</param>
        /// <param name="resetTime">If non-zero, player is unpaused after specified amount of time.</param>
        public virtual void SetPlayerPauseState(bool paused, float resetTime = 0f)
        {
            _statesPaused = paused;

            if (resetTime != 0f)
            {
                Invoke(nameof(UnPausePlayer), resetTime);
            }
        }
        
        /// <summary>
        /// Unpauses the player.  Usually called from Invoke with delay
        /// </summary>
        private void UnPausePlayer()
        {
            _statesPaused = false;
        }
        
        /// <summary>
        /// Makes the player unresponsive for a set perioid of time.  Uses the SetPlayerPauseState method.
        /// </summary>
        /// <param name="time">Time in seconds to disable player</param>
        /// <returns></returns>
        public virtual IEnumerator DisablePlayerForTimeRoutine(float time)
        {
            SetPlayerPauseState(true);

            yield return new WaitForSeconds(time);

            SetPlayerPauseState(false);
        }

        public abstract void SetMovementEnabled(bool active);

        /// <summary>
        /// Changes the player to the default movement state.
        /// </summary>
        public abstract void ChangeToMoveState();

        public abstract void ChangeToCustomState(State state);

        public virtual void Respawn()
        {
            _playerHealth.Respawn();
        }

        // Note: giving the following methods default empty implementations is not ideal. If they exist then one should
        // be able to expect them to provide functionality. So they would have to be abstract.
        
        // However, as mentioned in IPLayerController, I am not sure we want to
        // impart this responsability on all variants of the player controller when all they want is to each support a
        // different version of this feature.

        //public abstract void MoveCharacterToPosition(Vector3 position);
        //public abstract void MoveCharacterToPosition(Vector3 position, float speed);
        //public abstract void MoveCharacter(float horiz, float vert);
        //public abstract void StopCharacter();
        //public abstract bool IsAtDestination();

        //public abstract void TeleportCharacter(Vector3 position);
        //public abstract void TeleportCharacter(Vector3 position, Vector3 forward);
        //public abstract void TeleportCharacter(Vector3 position, Quaternion orientation);
    }
}