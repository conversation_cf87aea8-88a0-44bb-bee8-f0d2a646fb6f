// Copyright Isto Inc.

using Isto.Core.StateMachine;
using UnityEngine;

namespace Isto.Core.Beings
{
    /// <summary>
    /// This interface describes how our main systems should drive the player.
    /// At its highest level our player code supports:
    /// -States
    /// -Movement
    /// -Abilities
    /// -Health
    /// </summary>
    public interface IPlayerController
    {
        bool IsMoving { get; }

        /// <summary>
        /// Sets the hit box collider to be active or inactive.  Basically makes the player invincible
        /// </summary>
        /// <param name="state">Are colliders active</param>
        void SetPlayerColliderState(bool state);

        void DisableAllPlayerControls(float time);

        void EnablePlayerControls();

        void SetPlayerPauseState(bool paused, float resetTime = 0f);

        public abstract void SetMovementEnabled(bool active);

        /// <summary>
        /// Changes the player to the default movement state.
        /// </summary>
        public abstract void ChangeToMoveState();

        public abstract void ChangeToCustomState(State state);

        void Respawn();

        Vector3 GetPlayerPosition();
        void SetPlayerPosition(Vector3 pos);

        // For consideration:

        // This is a system used by Atrio to have its states control the player behavior with a common method
        // It probably should not be public, and we can probably improve on how we control access to this
        // Consequently IPlayerController probably is not the right place to define this even if it might be a commonality
        //State ProcessMovementAndActions(bool movement, bool ability);

        // Too specific, not sure we want all the player controllers to have to support all of the versions of these,
        // despite all having movement functionality
        //void MoveCharacterToPosition(Vector3 position);
        //void MoveCharacterToPosition(Vector3 position, float speed);
        //void MoveCharacter(float horiz, float vert);
        //void StopCharacter();

        // Too specific, not sure we want all the player controllers to have to support all of the versions of these,
        // despite all having teleport functionality
        //void TeleportCharacter(Vector3 position);
        //void TeleportCharacter(Vector3 position, Vector3 forward);
        //void TeleportCharacter(Vector3 position, Quaternion orientation);
    }
}