// Copyright Isto Inc.
using Isto.Core.Automation;
using Isto.Core.Cameras;
using Isto.Core.Inputs;
using Isto.Core.Items;
using Isto.Core.Skins;
using Isto.Core.StateMachine;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AI;
using Zenject;

#if UNITY_2022_3_OR_NEWER
using NavMeshSurface = Unity.AI.Navigation.NavMeshSurface;
#else
using NavMeshSurface = UnityEngine.AI.NavMeshSurface;
#endif

namespace Isto.Core.Beings
{
    /// <summary>
    /// This represents our player with the functionality required for automation games.
    ///
    /// It supports point and click movement as well as joystick.
    /// It requires the player to have a hitbox and a navmesh agent.
    /// It expects an isometric camera at a certain angle and has directions optimized for that.
    ///
    /// It also has logic for the following features that may be better moved to a child class or made optional:
    /// -Automation
    /// -Item placement/dismantling
    /// -Inventory
    /// -Equip to hotbar
    /// -Interaction
    /// -Harvesting
    /// </summary>
    public class AutomationPlayerController : CorePlayerController
    {
        // UNITY HOOKUP

        public LayerMask pickupLayers;

        [Header("Animation")]
        [Tooltip("The distance before reaching destination when the animator switches to idle")]
        public float switchToIdleDistance = 0.11f;
        public List<GameObject> animators;

        [Header("Collisions")]
        public Collider mainHitBox;
        public LayerMask obstaclesToMovementMask;
        public Vector3 obstacleDetectionBoxSize;


        // OTHER FIELDS

        protected Collider[] _hitBuffer;
        protected Vector3 _forwardDirection;
        protected HashSet<GameObject> _currentCollisions; // Using hashset as it doesn't allow duplicate values
        protected float _desiredSpeed;
        protected float _desiredSpeedOverride; // need to track this state on a scene-scope gameobject, not in a scriptable object
        protected bool _desiredSpeedOverriden;
        private IPlayerAnimationController _animationControl;
        private SkinController _skinControl;
        protected NavMeshPath _pathBuffer;
        protected float _movementDestinationLeniency = 0f;
        protected Transform _currentHandTransform;

        //Stores information about what was the last recipe that the player has configured a Factory to.
        //Note that this information currently only lives until we change scenes.
        protected Dictionary<CategoryList, Recipe> _mostRecentRecipe;

        // protected bool _statesPaused = false; // Flag that when set causes the current state to no longer run it's update function
        protected bool _isCurrentInteractionConsumed = false; // Flag that prevents one UserActions.INTERACT from being read twice

        public float moveDeltaFactor = 0.2f;


        // PROPERTIES

        public Vector3 Forward { get { return _forwardDirection; } }
        public Vector3 MoveDelta { get; private set; }
        public float RemainingDistance { get { return _agent.remainingDistance; } }
        public float AvoidRadius { get { return _agent.radius; } }
        public bool HasPath { get { return _agent.hasPath || _agent.pathPending; } }
        public override bool IsMoving
        {
            get
            {
                // if using controller and any movement set by axis, return true
                if (_controls.UsingJoystick() && (_controls.GetAxis(Controls.MovementAxis.MoveHorizontal) != 0f || _controls.GetAxis(Controls.MovementAxis.MoveVertical) != 0f))
                    return true;

                return _agent.IsNullOrDestroyed() || _agent.IsMoving(stoppingExtraBuffer: _movementDestinationLeniency);
            }
        }
        public float InteractionRange { get { return _interactionState.InteractionRange; } }
        public NavMeshAgent Agent { get { return _agent; } }

        protected override Collider MainHitBox => mainHitBox;
        public override State MovementState => _moveState;
        public AutomationPlayerMoveState MoveState { get { return _moveState; } }
        public PlayerPlaceItemBaseState PlaceAdvItemState { get { return _placeAdvancedItemState; } }
        public PlayerDismantleState DismantleState { get { return _dismantleState; } }
        public PlayerInteractWithItemState InteractionState { get { return _interactionState; } }
        public PlayerInventory Inventory { get { return _inventory; } }
        public AnimationController.CharacterDirections Facing { get { return _animationControl != null ? _animationControl.GetFacing() : AnimationController.CharacterDirections.down_right; } }
        public PlayerItemInteraction ItemInteraction { get { return _itemInteraction; } }
        public PlayerHarvestState HarvestState { get { return _harvestState; } }

        public Transform Hand { get { return _currentHandTransform; } }

        public IPlayerAnimationController Animation { get { return _animationControl; } }

        public EquippableItemController itemInHand;


        // EVENTS

        public EventHandler<ItemEventArgs> ItemEquippedToPlayer;


        // INJECTION

        private PlayerInteractWithItemState _interactionState;
        private PlayerPlaceItemState _placeAdvancedItemState;
        private PlayerHarvestState _harvestState;
        private AutomationPlayerMoveState _moveState;
        private PlayerDismantleState _dismantleState;
        private PlayerItemInteraction _itemInteraction;

        private PlayerInventory _inventory;
        private CameraController _camController;
        protected NavMeshAgent _agent; // TODO: test this??
        protected MasterItemList _masteritemList;
        protected AutomationSystem _automationSystem;

        [Inject]
        private void Inject(PlayerInteractWithItemState interactState, AutomationPlayerMoveState moveState,
            PlayerDismantleState dismantlingState, PlayerItemInteraction itemInteraction, PlayerPlaceItemState advState,
            PlayerHarvestState harvestState, PlayerInventory playerInventory, CameraController camcontrol,
            NavMeshAgent agent, MasterItemList masterItemList, AutomationSystem automationSystem)
        {
            _interactionState = interactState;
            _placeAdvancedItemState = advState;
            _harvestState = harvestState;
            _moveState = moveState;
            _dismantleState = dismantlingState;
            _itemInteraction = itemInteraction;
            _inventory = playerInventory;
            _camController = camcontrol;

            _agent = agent;
            _masteritemList = masterItemList;
            _automationSystem = automationSystem;
        }


        // LIFECYCLE EVENTS

        protected override void Awake()
        {
            base.Awake();

            _hitBuffer = new Collider[20];
            _currentCollisions = new HashSet<GameObject>();
            _animationControl = GetComponent<IPlayerAnimationController>();
            //_skinControl = GetComponent<CoreSkinController>();

            _pathBuffer = new NavMeshPath();
        }

        protected override void Start()
        {
            StartCoroutine(DelayedInitialization());

            base.Start();
        }

        protected override void Update()
        {
            base.Update();

            if (_statesPaused)
                return;

            UpdateForwardDirection();
            UpdateEquippedItemPosition();

            UpdateMoveDelta();

            _agent.speed = GetAdjustedMoveSpeed();
        }

        private void LateUpdate()
        {
            if (Mathf.Abs(_agent.velocity.sqrMagnitude) > 0.25f)
            {
                SetFacingDirection(_agent.velocity);
            }
        }

        private void OnTriggerEnter(Collider other)
        {
            if (pickupLayers.IsInLayerMask(other.gameObject.layer))
            {
                AddToCollisions(other.gameObject);
            }
        }

        private void OnCollisionEnter(Collision collision)
        {
            if (pickupLayers.IsInLayerMask(collision.gameObject.layer))
            {
                AddToCollisions(collision.gameObject);
            }
        }

        private void OnCollisionExit(Collision collision)
        {
            if (pickupLayers.IsInLayerMask(collision.gameObject.layer))
            {
                RemoveFromCollisions(collision.gameObject);
            }
        }

        private void OnTriggerExit(Collider other)
        {
            RemoveFromCollisions(other.gameObject);
        }


        // EVENT HANDLING

        /// <summary>
        /// Event handler for when the player takes damage, should disable player for a brief time
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected override void OnPlayerTakeDamage(object sender, HealthEventArgs e)
        {
            // If damage from hunger, don't apply any disabling effects
            if (e.type == Health.DamageTypeEnum.PHYSICAL)
                return;

            StartCoroutine(DisablePlayerForTimeRoutine(e.stunTime));
        }

        protected override void OnPlayerKilled(object sender, HealthEventArgs e)
        {
            StopAllCoroutines();
            _statesPaused = false;
        }


        // ACCESSORS

        public override Vector3 GetPlayerPosition()
        {
            return _agent.transform.position;
        }

        public override void SetPlayerPosition(Vector3 pos)
        {
            TeleportCharacter(pos);
        }

        /// <summary>
        /// Returns the appropriate speed for the NavMeshAgent based on the direction the player is moving to make moving
        /// vertically as fast as moving horizontally
        /// </summary>
        private float GetAdjustedMoveSpeed()
        {
            float theta = Vector3.Angle(Constants.RIGHT, _forwardDirection);

            // If over 90, subtract it from 180 to stay in [0,90]
            theta = theta > 90 ? 180 - theta : theta;

            // Adjustment factor (1.2) based on testing.  Higher value will give more speed when moving vertically
            float adjustedSpeed = _desiredSpeed + ((theta / 90) * 1.2f);

            return adjustedSpeed;
        }

        public void SetMoveSpeed(float speed)
        {
            _desiredSpeed = speed;
        }

        public void SetBaseMoveSpeedOverride(float speed)
        {
            _desiredSpeedOverride = speed;
            _desiredSpeedOverriden = true;
            SetMoveSpeed(speed);
        }

        public void ResetBaseMoveSpeedOverride()
        {
            SetMoveSpeed(MoveState.speed);
            _desiredSpeedOverriden = false;
        }

        public float GetMoveSpeed()
        {
            return _desiredSpeed;
        }

        public float GetMoveSpeedOverride()
        {
            return _desiredSpeedOverride;
        }

        public bool IsSpeedOverrideEnabled()
        {
            return _desiredSpeedOverriden;
        }

        public void SetCurrentInteractionConsumed()
        {
            _isCurrentInteractionConsumed = true;
        }

        public virtual void SetFacingDirection(Vector3 direction)
        {
            _forwardDirection = direction.normalized;

            // If NaN, means direction was (0,0,0) so just make it default
            if (float.IsNaN(_forwardDirection.x))
            {
                _forwardDirection = Constants.FORWARD;
            }
        }

        public void SetPickupItem(IInteractable targetInteractable)
        {
            if (_currentState.isInterruptable)
            {
                _interactionState.SetPickupItem(targetInteractable);
                ChangeState(_interactionState);
            }
        }

        public void SetHarvestItem(GameObject targetObject)
        {
            // If not in harvest state or in harvest state but with different target, then change to harvest state
            if (_currentState.isInterruptable && ((_currentState != _harvestState) || (_currentState == _harvestState && _harvestState.CurrentTarget != targetObject)))
            {
                _harvestState.SetHarvestItem(targetObject);
                ChangeState(_harvestState);
            }
        }

        /// <summary>
        /// Sets if player can move in the current AdvancedItemState.  Used when placing items.
        /// </summary>
        /// <param name="active"></param>
        public override void SetMovementEnabled(bool active)
        {
            _placeAdvancedItemState.SetMovementState(active);
        }


        // OTHER METHODS

        private IEnumerator DelayedInitialization()
        {
            NavMeshSurface navmesh = null;
            while (navmesh == null)
            {
                // Usually takes about 3 frames for this to be ready after the scene is done loading, not sure why
                yield return null;
                navmesh = GameObject.FindObjectOfType<NavMeshSurface>(includeInactive: true);
            }

            _agent.enabled = true;
            Rigidbody rigidbody = GetComponent<Rigidbody>();
            if (rigidbody != null)
            {
                rigidbody.useGravity = true;
            }
        }

        private void UpdateMoveDelta()
        {
            // If player not moving, move towards zero much faster
            float adjustedFactor = _agent.velocity == Vector3.zero ? 0.5f : moveDeltaFactor;

            MoveDelta = Vector3.ClampMagnitude(Vector3.MoveTowards(MoveDelta, _agent.velocity, adjustedFactor), 1f);
        }

        /// <summary>
        /// Sets which hand transform to use based on movement direction
        /// </summary>
        protected virtual void UpdateEquippedItemPosition()
        {

        }

        private void AddToCollisions(GameObject hitObject)
        {
            IItemTopmostParent topObject = hitObject.GetComponentInParent<IItemTopmostParent>();

            if (topObject != null)
            {
                //Debug.Log("Adding to player collisions: " + topObject.GetGameObject().name);
                _currentCollisions.Add(topObject.GetGameObject());
            }
        }

        private void RemoveFromCollisions(GameObject hitObject)
        {
            IItemTopmostParent topObject = hitObject.GetComponentInParent<IItemTopmostParent>();

            if (topObject != null)
            {
                //Debug.Log("Removing from player collisions: " + topObject.GetGameObject().name);
                _currentCollisions.Remove(topObject.GetGameObject());
            }
        }

        public bool IsHittingAnyMobs(out GameObject mob)
        {
            foreach (GameObject item in _currentCollisions)
            {
                if (item.layer == Layers.MOB)
                {
                    mob = item;
                    return true;
                }
            }

            mob = null;
            return false;
        }

        /// <summary>
        /// Every state calls this to see if the user has clicked on something
        /// Then determines if it should change states, or move
        /// </summary>
        /// <param name="movement"></param>
        /// <param name="ability"></param>
        /// <param name="itemInteraction"></param>
        /// <returns></returns>
        public virtual State ProcessMovementAndInteractions(bool movement, bool ability, bool itemInteraction)
        {
            if (_isCurrentInteractionConsumed)
            {
                //Keep the interaction "consumed" until button is released, preventing side-effects
                _isCurrentInteractionConsumed = _controls.GetButton(UserActions.INTERACT);
            }

            if (ability)
            {
                PlayerAbilityState triggeredAbility = _abilities.GetReadyAbility();

                if (triggeredAbility != null)
                {
                    return triggeredAbility;
                }
            }

            //Item interaction sets the new state already so just return the current as that will be the correct state
            if (itemInteraction && _itemInteraction.ProcessInput())
            {
                _isCurrentInteractionConsumed = _controls.GetButtonDown(UserActions.INTERACT);
                return _currentState;
            }

            if (movement && SetNewMovement(_isCurrentInteractionConsumed))
            {
                return _moveState;
            }

            return null;
        }

        /// <summary>
        /// Checks user input and if a mouse click or key control for movement is used, set moving towards the new
        /// target position.
        /// </summary>
        /// <returns>True if there is new movement, false otherwise</returns>
        protected bool SetNewMovement(bool ignoreCurrentInteraction)
        {
            bool movementUpdated = false;

            float horizontal = _controls.GetAxis(Controls.MovementAxis.MoveHorizontal);
            float vertical = _controls.GetAxis(Controls.MovementAxis.MoveVertical);

            //If keyboard or joystick axis was used to change movement, move the character by using agent.Move()
            if (horizontal != 0f || vertical != 0f)
            {
                MoveCharacter(horizontal, vertical);
                movementUpdated = true;
            }
            //If using the mouse, set the target for the agent to the mouse click position
            else if (!_controls.UsingJoystick() && !ignoreCurrentInteraction && _controls.GetButton(UserActions.INTERACT, true) && !_controls.GetPointerOverUI())
            {
                Vector3 targetPosition = _controls.GetPointerPositionOnGround() + Constants.PLAYER_MOUSE_CENTERING_OFFEST;

                // If mouse point is under small delta from pervious position, don't update movement
                if (Vector3.Distance(targetPosition, transform.position) > 1.25f)
                {
                    MoveCharacterToPosition(targetPosition);
                    movementUpdated = true;
                }
            }

            return movementUpdated;
        }

        public void ClearNavPath()
        {
            if (_agent.isOnNavMesh)
            {
                _agent.ResetPath();
            }
        }

        /// <summary>
        /// Instants moves the player the passed in amount relative to it's current position
        /// </summary>
        /// <param name="relativePosition"></param>
        public void InstantRelativeMove(Vector3 relativePosition)
        {
            _agent.Move(relativePosition);
        }

        /// <summary>
        /// Movement methods for Mouse click position
        /// </summary>
        /// <param name="position"></param>
        /// <param name="setRunAnimation"></param>
        public void MoveCharacterToPosition(Vector3 position)
        {
            MoveCharacterToPosition(position, _desiredSpeed);
        }

        public void MoveCharacterToPosition(Vector3 position, float speed)
        {
            Vector3 targetPosition = position;
            _desiredSpeed = speed;
            targetPosition.y = transform.position.y;

            //Check if there might be things in the way at the destination, and compensate with some leniency in goal meeting distance
            //This might be a bit overkill and I'm not sure how "perfect" this solution really is, so if it's not working well enough,
            //we can change it to a single hand-tweaked number instead.
            Collider[] hits = Physics.OverlapBox(targetPosition, obstacleDetectionBoxSize, Quaternion.identity, obstaclesToMovementMask, QueryTriggerInteraction.Collide);
            _movementDestinationLeniency = 0f;
            foreach (Collider c in hits)
            {
                if (c.bounds.extents.x > _movementDestinationLeniency)
                {
                    _movementDestinationLeniency = c.bounds.extents.x;
                }
                if (c.bounds.extents.y > _movementDestinationLeniency)
                {
                    _movementDestinationLeniency = c.bounds.extents.y;
                }
            }

            //Move The Character
            _agent.CalculatePath(targetPosition, _pathBuffer);

            if (_pathBuffer.status == NavMeshPathStatus.PathInvalid)
            {
                if (NavMesh.SamplePosition(position, out NavMeshHit hit, 10f, NavMesh.AllAreas))
                {
                    _agent.CalculatePath(hit.position, _pathBuffer);
                    _agent.SetPath(_pathBuffer);
                }
            }
            else
            {
                _agent.SetPath(_pathBuffer);
            }
        }

        /// <summary>
        /// Movement methods for Keyboard/ Joystick
        /// </summary>
        /// <param name="horiz"></param>
        /// <param name="vert"></param>
        public void MoveCharacter(float horiz, float vert)
        {
            Vector3 moveDirection = UnityUtils.ConvertScreenVectorToIsometric(horiz, vert) * _desiredSpeed * Time.deltaTime;

            _agent.ResetPath();
            _agent.velocity = moveDirection.normalized * GetAdjustedMoveSpeed();
        }

        public void TeleportCharacter(Vector3 position)
        {
            if (_agent.isOnNavMesh)
            {
                _agent.SetDestination(position);
                _agent.Warp(position);
            }
            else
            {
                Debug.LogWarning("Trying to teleport player that is not on a nav mesh. Player may be set outside of normal gameplay bounds in EssentialScene" +
                                 " or the additive scene has not been loaded yet. Position: " + position, gameObject);
                _agent.transform.position = position;
            }
        }

        /// <summary>
        /// Clears the characters navigation and stops audio but DOES NOT change the characters animation state.
        /// </summary>
        public void StopCharacter()
        {
            ClearNavPath();
        }

        /// <summary>
        /// Stops the characters movement and sets the animation state to idle
        /// </summary>
        public virtual void SetCharacterIdle()
        {
            StopCharacter();
        }

        public void SetMoveToAndInteractWithItem(IInteractable targetInteractable, float interactionDistanceOverride = 0f)
        {
            if (_currentState.isInterruptable)
            {
                _itemInteraction.DelayInteraction(Constants.BUTTON_SPAM_DELAY);

                _interactionState.SetInteractWithItem(targetInteractable, interactionDistanceOverride);
                ChangeState(_interactionState);
            }
        }

        public bool UseEquippedItem(Vector3 position, ItemPile itemPile, IItemInteractionCallback callback)
        {
            if (itemPile.item is AdvancedItem dropItem)
            {
                if (!dropItem.simpleDrop)
                {
                    if ((_currentState.isInterruptable || _currentState == _placeAdvancedItemState)
                    && (_placeAdvancedItemState.CurrentItem == null || _placeAdvancedItemState.CurrentItem.CurrentItem.item.itemID != itemPile.item.itemID))
                    {
                        _placeAdvancedItemState.SetupItemDrop(itemPile.item);

                        if (_currentState != _placeAdvancedItemState)
                        {
                            ChangeState(_placeAdvancedItemState);
                        }
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
                else
                {
                    _interactionState.SetItemDrop(position, itemPile, callback);

                    ChangeState(_interactionState);
                    return true;
                }
            }
            else if (itemPile.item.coreItemAssetOverride != null && itemPile.item.coreItemAssetOverride.RuntimeKeyIsValid())
            {
                _interactionState.SetItemDrop(position, itemPile, callback);

                ChangeState(_interactionState);
                return true;
            }
            else
            {
                if (_inventory.GetCountOfItem(itemPile.item) > 0)
                {
                    // Drop the item
                    // Note that because we don't want to autoCollect the item we spawned, it will assume its source is not the Player, which shouldn't matter?
                    // I guess some tutorial actions could be fooled by this as they expect some item processors to produce items in this way.
                    if (_automationSystem.TryAddCoreItem(transform.position + (-Constants.FORWARD * 0.5f), itemPile.item, autoCollectItem: false))
                    {
                        _inventory.Remove(itemPile.item, 1);
                    }
                }

                return true;
            }
        }

        public void SetDropComplete(bool stopDropping = false)
        {
            _placeAdvancedItemState.SetDropComplete(stopDropping);
        }

        public void SetDropCancelled(bool stopDropping = false)
        {
            _placeAdvancedItemState.SetDropComplete(stopDropping, wasCancelled: true);
        }

        public void SetDismantleComplete(bool stopDropping = false)
        {
            _dismantleState.SetDropComplete(stopDropping);
        }

        public void SetDismantleCancelled()
        {
            _dismantleState.AbortAllDismantling();
            _dismantleState.SetDropComplete(stopDropping: true);
        }

        public void EquipItemOnPlayer(AdvancedItem item)
        {
            GameObject itemObject = item.DropItem(transform.position, transform, 1, true);

            EquippableItemController equipControl = itemObject.GetComponent<EquippableItemController>();

            if (equipControl != null)
            {
                if (itemInHand == null || equipControl.Consumable)
                {
                    equipControl.ItemEquipped();
                }
                else
                {
                    itemInHand.ItemUnequipped();
                    equipControl.ItemEquipped();
                }

                _inventory.Remove(item);

                ItemEquippedToPlayer?.Invoke(this, new ItemEventArgs(_inventory, item));
            }
        }

        public void StartDismantling()
        {
            // Dismantling State is secretly a PlayerAdvItemState because it uses the fake item, Eraser,
            // allowing us to cleanly mirror the item placement behavior for dismantling.

            AdvancedItem eraserItem = _masteritemList.GetItemByID<AdvancedItem>("Eraser");

            if (_currentState.isInterruptable || _currentState == _dismantleState)
            {
                _dismantleState.SetupItemDrop(eraserItem);

                if (_currentState != _dismantleState)
                {
                    ChangeState(_dismantleState);
                }
            }
        }

        /// <summary>
        /// Changes the player to the default movement state.
        /// </summary>
        public override void ChangeToMoveState()
        {
            ChangeState(_moveState);
        }

        public override void ChangeToCustomState(State state)
        {
            ChangeState(state);
        }

        /// <summary>
        /// Delays items from being interactable for the peroid of time.  Used to prevent spamming or add a delay when user is holding the
        /// button down (ie, harvesting)
        /// </summary>
        /// <param name="time">Time in seconds to delay interactions</param>
        public override void DisableAllPlayerControls(float time)
        {
            base.DisableAllPlayerControls(time);

            _itemInteraction.DelayInteraction(time);
        }

        public virtual void DelayItemInteraction(float time)
        {
            _itemInteraction.DelayInteraction(time);
        }

        /// <summary>
        /// Checks if an item on the layers set is within the range of the position passed in.
        /// </summary>
        /// <param name="centerPosition">Center point to check for items in range.</param>
        /// <param name="range">Range from center to check for items.</param>
        /// <returns>True if there is an item in range, false otherwise.</returns>
        private GameObject ItemInRange(Vector3 centerPosition, float range, int layers)
        {
            int hits = Physics.OverlapSphereNonAlloc(centerPosition, range, _hitBuffer, layers);

            if (hits != 0)
            {
                Collider closest = _hitBuffer[0];
                float closestDistance = Vector3.Distance(closest.transform.position, transform.position);

                //Find the closest item
                for (int i = 1; i < hits; i++)
                {
                    float currentDistance = Vector3.Distance(_hitBuffer[i].transform.position, transform.position);

                    if (currentDistance < closestDistance)
                    {
                        closestDistance = currentDistance;
                        closest = _hitBuffer[i];
                    }
                }

                // Find the root object from the collider
                IItemTopmostParent parentScript = closest.GetComponentInParent<IItemTopmostParent>();

                if (parentScript == null)
                {
                    string itemPath = "";
                    Transform current = closest.transform;

                    while (current != null)
                    {
                        itemPath += ":" + current.name;

                        current = current.parent;
                    }

                    throw new UnityException("Unable to find TopmostParent interface on gameobject: " + itemPath);
                }

                GameObject target = parentScript.GetGameObject();

                if (target == null)
                {
                    throw new UnityException("No Item Controller attached to item on pickup target layers.  Item name: " + closest.name);
                }

                return target;
            }

            return null;
        }

        /// <summary>
        /// Only updates the forward vector if the player is moving with a destination in the navmeshagent, otherwise we're using agent.Move and
        /// direction was set when that was called.
        /// </summary>
        private void UpdateForwardDirection()
        {
            if (_agent.hasPath)
            {
                _forwardDirection = _agent.velocity.normalized;
            }
        }
    }
}