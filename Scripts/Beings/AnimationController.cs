// Copyright Isto Inc.
using Isto.Core.Cameras;
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AI;
using Zenject;

namespace Isto.Core.Beings
{
    /// <summary>
    /// Controls the Animators attached to the gameObject for triggering all animations.  Should only be called from the PlayerController or
    /// MobController classes to avoid calling from outside classes that could lead to conflicts with multiple animation calls happening per frame.
    /// </summary>
    public class AnimationController : MonoBehaviour
    {
        // Events

        public event EventHandler<AnimationChangedEventArgs> DirectionChanged;

        // Public Variables

        public enum AnimationType
        {
            idle, run, dodge, walk, scan, drag, shield, showOffPick,
            disabled, stab, backup, eat, poop, runSprite, stumble, pre_attack, attack,
            post_attack, genericDeath1, deathFromBattery, springboarded, genericDeath2, deathFromPushBack, deathFromWater, deathFromCorrosion
        };

        public enum SubAnimationType { headLookBack, headLookForward, headLookRight, headLook<PERSON>eft, torchOff, torchOn }

        public enum CharacterDirections { front, back, left, right, up_right, up_left, down_left, down_right, not_set }

        public List<GameObject> animators; //reflections and whatnot

        [Tooltip("The nav agent speed that matches the speed of the animation, used to scale the speed if moving faster or slower")]
        public float baseWalkSpeed = 5.5f;
        public float baseRunSpeed = 5.5f;
        public bool player;
        public List<GameObject> torches;
        public CameraController _cameraControl;

        public AnimationType CurrentAnimation { get { return _previousAnimation; } }

        // Private Variables

        private NavMeshAgent _agent;
        private CharacterDirections _previousDirection;
        private AnimationType _previousAnimation;
        private HashSet<AnimatorControllerParameter> _animParameters = new HashSet<AnimatorControllerParameter>();
        private AnimationChangedEventArgs _animationEventArgs;

        // Methods	
        [Inject]
        public void Inject(CameraController cameraController)
        {
            _cameraControl = cameraController;
        }

        private void Awake()
        {
            _agent = GetComponent<NavMeshAgent>();
            _animationEventArgs = new AnimationChangedEventArgs();

            // Get a list of all the parameters for the first animator, assumes the other animators use the same parameters
            if (animators.Count > 0)
            {
                for (int i = 0; i < animators.Count; i++)
                {
                    if (animators[i].activeInHierarchy)
                    {
                        Animator anim = animators[i].GetComponent<Animator>();

                        if (anim.enabled)
                        {
                            AnimatorControllerParameter[] allParameters = anim.parameters;

                            for (int j = 0; j < allParameters.Length; j++)
                            {
                                _animParameters.Add(allParameters[j]);
                            }
                        }
                    }
                }

                //AnimatorControllerParameter[] allParameters = animators[0].GetComponent<Animator>().parameters;

                //for (int i = 0; i < allParameters.Length; i++)
                //{
                //    _animParameters.Add(allParameters[i]);
                //}

                //Animator anim = animators[0].GetComponent<Animator>();

                //if (anim.enabled)
                //{
                //    AnimatorControllerParameter[] allParameters = anim.parameters;

                //    for (int i = 0; i < allParameters.Length; i++)
                //    {
                //        _animParameters.Add(allParameters[i]);
                //    }
                //}
            }
        }

        private void Start()
        {
            _previousDirection = CharacterDirections.down_right;

            DisableAnimationEventFiring();
        }

        //Animation Functions
        public void SetCharacterAnimation(AnimationType animType, CharacterDirections direction)
        {
#if ANIMATION_LOGGING
            Debug.LogFormat("Setting animation on {0}, Anim: {1}, Dir: {2}", gameObject.name, animType, direction);
#endif

            //There are two objects, player and the reflection. Make sure they animate at the same time
            foreach (GameObject gameobj in animators)
            {
                if (!gameobj.activeInHierarchy)
                    continue;

                Animator animator = gameobj.GetComponent<Animator>();

                animator.ClearTriggers(_animParameters);

                string animation = "";
                //Add in the animation type
                animation += animType.ToString();
                animation += "_";


                if (player && animType == AnimationType.run)
                {
                    //If the player switches horizontal direction while running, do a quick turn
                    if ((_previousDirection == CharacterDirections.left && direction == CharacterDirections.right) || (_previousDirection == CharacterDirections.right && direction == CharacterDirections.left))
                    {
                        if (_previousAnimation == AnimationType.run)
                        {
                            animation = "quickturn_";
                        }
                    }
                }
                // Currently only have horizontal for scan animation
                if (animType == AnimationType.scan)
                {
                    animation += "h";
                    SetCharacterDirection(direction);
                }
                else if (animType == AnimationType.deathFromBattery)
                {
                    animation += "h";
                }
                else if (animType == AnimationType.eat)
                {
                    // Add down as we only have a down facing animation currently for eating
                    animation += "down";
                }
                else if (animType == AnimationType.stab)
                {
                    //Don't do anything else
                }
                else
                {
                    //Determine direction
                    if (direction == CharacterDirections.front)
                    {
                        animation += "f";
                    }
                    else if (direction == CharacterDirections.back)
                    {
                        animation += "b";
                    }
                    else if (direction == CharacterDirections.left)
                    {
                        animation += "h";
                    }
                    else if (direction == CharacterDirections.right)
                    {
                        animation += "h";
                    }
                    else if (direction == CharacterDirections.down_left || direction == CharacterDirections.down_right)
                    {
                        animation += "down";
                    }
                    else if (direction == CharacterDirections.up_left || direction == CharacterDirections.up_right)
                    {
                        animation += "up";
                    }
                }

                //Debug.LogFormat("{0} setting animation: {1}, Direction: {2}", gameObject.name, animation, direction);

                //Set new animation
                animator.SetTrigger(animation);

                if (player)
                    Events.RaiseEvent(Events.PLAYER_ANIMATION_SET, animation);

                _previousAnimation = animType;
                _previousDirection = direction;

            }
        }


        /// <summary>
        /// Takes a forward vector and determines the correct character direction from that and sets the animation direction and then the 
        /// animation state
        /// </summary>
        /// <param name="forward">Forward direction for the Character</param>
        /// <param name="type">Animation state to set</param>
        public void SetCharacterDirectionAndAnimation(Vector3 forward, AnimationType type)
        {
            //Set the appropriate running animation
            float angle = GetAngleFromForward(forward);
            float sign = GetCharacterHorizontalDirection(forward);

            SetCharacterAnimation(type, GetCharacterDirection(angle, sign));
        }

        public void SetCharacterDirection(CharacterDirections direction)
        {
            _animationEventArgs.previousDirection = _previousDirection;
            _animationEventArgs.newDirection = direction;

            //Broadcast down to ShadowController (on the mini deer only rn)
            DirectionChanged?.Invoke(this, _animationEventArgs);

            int horizontalDirection = 1;
            if (direction == CharacterDirections.left || direction == CharacterDirections.down_left || direction == CharacterDirections.up_left)
                horizontalDirection = -1;

            //There are two objects, player and the reflection. Make sure they animate at the same time
            foreach (GameObject gameobj in animators)
            {
                if (horizontalDirection == -1 && gameobj.transform.localScale.x > 0 || (horizontalDirection == 1 && gameobj.transform.localScale.x < 0))
                {
                    gameobj.transform.localScale = gameobj.transform.localScale.FlipAlongX();

#if ANIMATION_LOGGING
                    Debug.LogFormat("Setting localscale on {0}, Dir: {1}, Scale: {2}", gameobj.name, horizontalDirection, gameobj.transform.localScale);
#endif
                }
            }
        }

        public void SetCharacterSubAnimation(SubAnimationType animType)
        {
            foreach (GameObject gameobj in animators)
            {
                Animator animator = gameobj.GetComponent<Animator>();
                if (animType == SubAnimationType.torchOn)
                {
                    for (int i = 0; i < torches.Count; i++)
                    {
                        torches[i].SetActive(true);
                    }
                }

                else if (animType == SubAnimationType.torchOff)
                {
                    for (int i = 0; i < torches.Count; i++)
                    {
                        torches[i].SetActive(false);
                    }
                }


                if (animType == SubAnimationType.headLookBack)
                {
                    animator.SetTrigger("head_lookBack");
                }
                if (animType == SubAnimationType.headLookForward)
                {
                    animator.SetTrigger("head_lookForward");
                }
                if (animType == SubAnimationType.headLookRight)
                {
                    animator.SetTrigger("head_lookRight");
                }
                if (animType == SubAnimationType.headLookLeft)
                {
                    animator.SetTrigger("head_lookLeft");
                }

            }
        }

        public void SetAnimationSpeed(AnimationType animType)
        {
            //There are two objects, player and the reflection. Make sure they animate at the same time
            foreach (GameObject gameobj in animators)
            {
                Animator animator = gameobj.GetComponent<Animator>();

                if (animator.HasParameter("run_speed", AnimatorControllerParameterType.Float, _animParameters))
                {
                    float runFactor = _agent.speed / baseRunSpeed;
                    animator.SetFloat("run_speed", runFactor);
                }
            }
        }

        //Helper Functions

        /// <summary>
        /// Disables the error message generation on Animation events that don't have a reciever.  The first animation controller
        /// still raises errors but any others will have them turned off.  Typically this is for when the reflection animation controller
        /// doesn't have the same scripts as the main animator.  (Player reflection) 
        /// </summary>
        private void DisableAnimationEventFiring()
        {
            if (animators.Count > 1)
            {
                for (int i = 1; i < animators.Count; i++)
                {
                    Animator anim = animators[i].GetComponent<Animator>();
                    anim.fireEvents = false;
                }
            }
        }

        public CharacterDirections GetCharacterDirection(float moveAngle, float horizontalDirection)
        {
            //If it's the player
            if (player)
            {
                if (moveAngle > 360)
                    moveAngle = moveAngle % 360;
                if ((moveAngle >= 0 && moveAngle < 25))
                {
                    return CharacterDirections.back;
                }
                if ((moveAngle >= 25 && moveAngle < 155))
                {
                    if (horizontalDirection == -1)
                        return CharacterDirections.left;
                    else
                        return CharacterDirections.right;
                }
                if ((moveAngle >= 155 && moveAngle <= 180))
                {
                    return CharacterDirections.front;
                }
                return CharacterDirections.front;
            }


            //For everything else (Up and down motions)
            if (moveAngle > 360)
                moveAngle = moveAngle % 360;


            if ((moveAngle >= 0 && moveAngle < 90))
            {
                if (horizontalDirection == -1)
                    return CharacterDirections.up_left;
                else
                    return CharacterDirections.up_right;
            }
            if ((moveAngle >= 90 && moveAngle <= 180))
            {
                if (horizontalDirection == -1)
                    return CharacterDirections.down_left;
                else
                    return CharacterDirections.down_right;
            }
            else
            {
                //Should never end up here
                Debug.LogError("In Animation Controller - you got an angle above 180, which shouldn't be possible");
                return CharacterDirections.down_right;
            }


        }

        /// <summary>
        /// For some of the animations, we only have horizontal animations, so we force the characer to either left or right, regardless of what they're facing
        /// </summary>
        /// <param name="moveDirection"></param>
        /// <returns></returns>
        public float GetCharacterHorizontalDirection(Vector3 moveDirection)
        {
            Vector3 normal = Constants.UP;
            float sign = Mathf.Sign(Vector3.Dot(normal, Vector3.Cross(Constants.FORWARD, moveDirection)));
            return sign;
        }

        public float GetAngleFromForward(Vector3 moveDirection)
        {
            float angle = Vector3.Angle(moveDirection, Constants.FORWARD);
            return angle;
        }
    }

    public class AnimationChangedEventArgs : EventArgs
    {
        public AnimationController.CharacterDirections previousDirection;
        public AnimationController.CharacterDirections newDirection;

        public AnimationChangedEventArgs()
        {
        }
    }
}