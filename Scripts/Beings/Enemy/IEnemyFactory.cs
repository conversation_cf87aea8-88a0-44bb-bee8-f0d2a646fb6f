// Copyright Isto Inc.

using UnityEngine;

namespace Isto.Core.Beings
{
    /// <summary>
    /// Factory interface for creating enemy instances
    /// </summary>
    public interface IEnemyFactory
    {
        /// <summary>
        /// Create an enemy at a default spawn location
        /// </summary>
        /// <param name="enemyType">The type of enemy to create</param>
        /// <returns>The created enemy GameObject</returns>
        GameObject CreateEnemy(string enemyType);
        
        /// <summary>
        /// Create an enemy at a specific position and rotation
        /// </summary>
        /// <param name="enemyType">The type of enemy to create</param>
        /// <param name="position">The spawn position</param>
        /// <param name="rotation">The spawn rotation</param>
        /// <returns>The created enemy GameObject</returns>
        GameObject CreateEnemy(string enemyType, Vector3 position, Quaternion rotation);
        
        /// <summary>
        /// Get all available enemy types that can be spawned
        /// </summary>
        /// <returns>Array of enemy type names</returns>
        string[] GetAvailableEnemyTypes();
    }
}
