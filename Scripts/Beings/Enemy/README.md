# Enemy Management System

This system provides a comprehensive way to manage enemies in your game, similar to how the PlayerManager handles players.

## Overview

The Enemy Management System consists of several key components:

- **IEnemyController**: Interface that all enemy controllers must implement
- **CoreEnemyController**: Base class for enemy controllers with common functionality
- **IEnemyFactory**: Interface for creating enemy instances
- **SimpleEnemyFactory**: Basic implementation of the enemy factory
- **EnemyManager**: Central manager for all enemy instances (similar to PlayerManager)

## Setup Instructions

### 1. Update Your Enemy Controllers

Your existing enemy controllers (like SpiderController) should inherit from `CoreEnemyController` instead of `MonoStateMachine`:

```csharp
public class SpiderController : CoreEnemyController
{
    // Your existing code...
    
    protected override void Awake()
    {
        base.Awake(); // Important: call base.Awake()
        // Your existing Awake code...
    }
}
```

### 2. Set Up the Enemy Factory

1. Create a GameObject in your scene and add the `SimpleEnemyFactory` component
2. Configure the enemy prefabs in the factory:
   - Set the enemy type name (e.g., "Spider")
   - Assign the enemy prefab (must have a component that implements IEnemyController)
3. Set the enemy spawn tag (default: "EnemySpawn")

### 3. Set Up the Enemy Manager

1. Create a GameObject in your scene and add the `EnemyManager` component
2. The EnemyManager will automatically find and register existing enemies in the scene
3. Make sure your Zenject installer binds the IEnemyFactory:

```csharp
Container.Bind<IEnemyFactory>().To<SimpleEnemyFactory>().FromComponentInHierarchy().AsSingle();
Container.Bind<EnemyManager>().FromComponentInHierarchy().AsSingle();
```

### 4. Create Enemy Spawn Points (Optional)

Create GameObjects with the tag "EnemySpawn" (or whatever you configured in the factory) to define where enemies should spawn by default.

## Usage Examples

### Basic Enemy Management

```csharp
public class GameManager : MonoBehaviour
{
    [Inject] private EnemyManager _enemyManager;
    
    private void Start()
    {
        // Spawn a spider enemy
        GameObject spider = _enemyManager.CreateEnemy("Spider");
        
        // Spawn an enemy at a specific location
        Vector3 spawnPos = new Vector3(10, 0, 10);
        GameObject enemy = _enemyManager.CreateEnemy("Spider", spawnPos, Quaternion.identity);
        
        // Get all active enemies
        var activeEnemies = _enemyManager.ActiveEnemies;
        
        // Kill all enemies
        _enemyManager.KillAllEnemies();
        
        // Respawn all dead enemies
        _enemyManager.RespawnAllEnemies();
    }
}
```

### Finding Enemies

```csharp
// Find enemies within a radius
Vector3 playerPos = player.transform.position;
var nearbyEnemies = _enemyManager.GetEnemiesInRadius(playerPos, 15f);

// Find the closest enemy
var closestEnemy = _enemyManager.GetClosestEnemy(playerPos);

// Set all enemies to target the player
_enemyManager.SetAllEnemiesTarget(player.transform);
```

### Custom Enemy Controllers

When creating new enemy types, inherit from `CoreEnemyController`:

```csharp
public class MyEnemyController : CoreEnemyController
{
    protected override void OnKilled()
    {
        base.OnKilled();
        // Custom death behavior
        PlayDeathAnimation();
    }
    
    protected override void OnRespawned()
    {
        base.OnRespawned();
        // Custom respawn behavior
        ResetHealth();
    }
    
    protected override void OnTargetChanged(Transform newTarget)
    {
        base.OnTargetChanged(newTarget);
        // Custom target change behavior
        if (newTarget != null)
        {
            StartPursuing(newTarget);
        }
    }
}
```

## Key Features

### EnemyManager Properties

- `AllEnemies`: List of all enemy instances (active and dead)
- `ActiveEnemies`: List of only active (alive) enemies
- `DeadEnemies`: List of only dead enemies
- `TotalEnemyCount`: Total number of enemies
- `ActiveEnemyCount`: Number of active enemies
- `DeadEnemyCount`: Number of dead enemies

### EnemyManager Methods

- `CreateEnemy(string enemyType)`: Create enemy at default spawn point
- `CreateEnemy(string enemyType, Vector3 position, Quaternion rotation)`: Create enemy at specific location
- `RegisterEnemy(IEnemyController enemy)`: Manually register an enemy
- `UnregisterEnemy(IEnemyController enemy)`: Unregister an enemy
- `KillAllEnemies()`: Kill all active enemies
- `RespawnAllEnemies()`: Respawn all dead enemies
- `SetAllEnemiesTarget(Transform target)`: Set target for all enemies
- `ClearAllEnemyTargets()`: Clear all enemy targets
- `GetEnemiesInRadius(Vector3 position, float radius)`: Find enemies in area
- `GetClosestEnemy(Vector3 position)`: Find closest enemy

### IEnemyController Interface

All enemy controllers implement this interface, providing:

- `GameObject`: The enemy's GameObject
- `Transform`: The enemy's Transform
- `IsActive`: Whether the enemy is active
- `IsDead`: Whether the enemy is dead
- `CurrentTarget`: The enemy's current target
- `SetActive(bool active)`: Activate/deactivate the enemy
- `Kill()`: Kill the enemy
- `Respawn()`: Respawn the enemy
- `SetTarget(Transform target)`: Set a new target
- `ClearTarget()`: Clear the current target

## Integration with Existing Code

The system is designed to work with your existing SpiderController and can be extended for additional enemy types. The SpiderController has been updated to inherit from CoreEnemyController and implement the IEnemyController interface.

## Testing

Use the `EnemyManagerExample` component to test the system. It provides keyboard controls and a GUI for testing enemy management functionality:

- E: Spawn Enemy
- K: Kill All Enemies  
- R: Respawn All Enemies
- T: Target Player
- C: Clear Targets

## Network Support

The system is designed to work with your existing PUN2 networking setup. Enemies spawned through the factory will be properly injected with dependencies and can be networked if needed.
