// Copyright Isto Inc.

using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Zenject;

namespace Isto.Core.Beings
{
    /// <summary>
    /// Simple factory for creating enemy instances
    /// </summary>
    public class SimpleEnemyFactory : MonoBehaviour, IEnemyFactory
    {
        // UNITY HOOKUP
        
        [System.Serializable]
        public class EnemyPrefabEntry
        {
            public string enemyType;
            public CoreEnemyController enemyPrefab;
        }
        
        [Header("Enemy Prefabs")]
        [SerializeField] private List<EnemyPrefabEntry> _enemyPrefabs = new List<EnemyPrefabEntry>();
        
        [Header("Spawn Settings")]
        [SerializeField] private string _enemySpawnTag = "EnemySpawn";
        
        
        // INJECTION
        
        private DiContainer _container;
        
        [Inject]
        public void Inject(DiContainer container)
        {
            _container = container;
        }
        
        
        // INTERFACE IMPLEMENTATION
        
        public GameObject CreateEnemy(string enemyType)
        {
            Vector3 spawnPosition = Vector3.zero;
            Quaternion spawnRotation = Quaternion.identity;
            
            // Find a spawn point with the enemy spawn tag
            GameObject spawnPoint = GameObject.FindGameObjectWithTag(_enemySpawnTag);
            if (spawnPoint != null)
            {
                spawnPosition = spawnPoint.transform.position;
                spawnRotation = spawnPoint.transform.rotation;
            }
            
            return CreateEnemy(enemyType, spawnPosition, spawnRotation);
        }
        
        public GameObject CreateEnemy(string enemyType, Vector3 position, Quaternion rotation)
        {
            // Find the prefab for this enemy type
            EnemyPrefabEntry entry = _enemyPrefabs.FirstOrDefault(e => e.enemyType == enemyType);
            if (entry == null || entry.enemyPrefab == null)
            {
                Debug.LogError($"SimpleEnemyFactory: No prefab found for enemy type '{enemyType}'");
                return null;
            }
            
            // Instantiate the enemy
            GameObject enemy = Instantiate(entry.enemyPrefab.gameObject, position, rotation);
            
            // Inject dependencies if we have a container
            if (_container != null)
            {
                _container.InjectGameObject(enemy);
            }
            
            return enemy;
        }
        
        public string[] GetAvailableEnemyTypes()
        {
            return _enemyPrefabs.Where(e => e.enemyPrefab != null).Select(e => e.enemyType).ToArray();
        }
        
        
        // UTILITY METHODS
        
        /// <summary>
        /// Add a new enemy prefab to the factory
        /// </summary>
        /// <param name="enemyType">The type name for this enemy</param>
        /// <param name="prefab">The enemy prefab</param>
        public void AddEnemyPrefab(string enemyType, CoreEnemyController prefab)
        {
            // Check if this type already exists
            EnemyPrefabEntry existing = _enemyPrefabs.FirstOrDefault(e => e.enemyType == enemyType);
            if (existing != null)
            {
                existing.enemyPrefab = prefab;
            }
            else
            {
                _enemyPrefabs.Add(new EnemyPrefabEntry { enemyType = enemyType, enemyPrefab = prefab });
            }
        }
        
        /// <summary>
        /// Remove an enemy prefab from the factory
        /// </summary>
        /// <param name="enemyType">The type name to remove</param>
        public void RemoveEnemyPrefab(string enemyType)
        {
            _enemyPrefabs.RemoveAll(e => e.enemyType == enemyType);
        }
    }
}
