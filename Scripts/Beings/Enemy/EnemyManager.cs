// Copyright Isto Inc.

using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Zenject;

namespace Isto.Core.Beings
{
    /// <summary>
    /// Manages all enemy instances in the game, similar to PlayerManager
    /// </summary>
    public class EnemyManager : MonoBehaviour
    {
        // OTHER FIELDS
        
        private List<IEnemyController> _allEnemyInstances;
        private List<IEnemyController> _activeEnemyInstances;
        private List<IEnemyController> _deadEnemyInstances;
        
        
        // PROPERTIES
        
        /// <summary>
        /// All enemy instances (active and dead)
        /// </summary>
        public virtual List<IEnemyController> AllEnemies => _allEnemyInstances;
        
        /// <summary>
        /// Only active (alive) enemy instances
        /// </summary>
        public virtual List<IEnemyController> ActiveEnemies => _activeEnemyInstances;
        
        /// <summary>
        /// Only dead enemy instances
        /// </summary>
        public virtual List<IEnemyController> DeadEnemies => _deadEnemyInstances;
        
        /// <summary>
        /// Total count of all enemies
        /// </summary>
        public virtual int TotalEnemyCount => _allEnemyInstances.Count;
        
        /// <summary>
        /// Count of active enemies
        /// </summary>
        public virtual int ActiveEnemyCount => _activeEnemyInstances.Count;
        
        /// <summary>
        /// Count of dead enemies
        /// </summary>
        public virtual int DeadEnemyCount => _deadEnemyInstances.Count;
        
        
        // INJECTION
        
        private IEnemyFactory _enemyFactory;
        
        [Inject]
        public void Inject(IEnemyFactory enemyFactory)
        {
            _enemyFactory = enemyFactory;
        }
        
        
        // LIFECYCLE EVENTS
        
        private void Awake()
        {
            _allEnemyInstances = new List<IEnemyController>();
            _activeEnemyInstances = new List<IEnemyController>();
            _deadEnemyInstances = new List<IEnemyController>();
        }
        
        private void OnDestroy()
        {
            UnregisterEvents();
        }
        
        private void Start()
        {
            // Find any existing enemies in the scene and register them
            IEnemyController[] existingEnemies = FindObjectsOfType<MonoBehaviour>()
                .OfType<IEnemyController>()
                .ToArray();
            
            foreach (IEnemyController enemy in existingEnemies)
            {
                RegisterEnemy(enemy);
            }
            
            RegisterEvents();
        }
        
        
        // EVENT HANDLING
        
        private void RegisterEvents()
        {
            Events.Subscribe(Events.NETWORK_ROOM_JOINED, Events_OnNetworkRoomJoined);
            Events.SubscribeWithParams(Events.GAMEOBJECT_SPAWNED_FROM_NETWORK, Events_OnGameObjectSpawned);
        }
        
        private void UnregisterEvents()
        {
            Events.UnSubscribe(Events.NETWORK_ROOM_JOINED, Events_OnNetworkRoomJoined);
            Events.UnSubscribeWithParams(Events.GAMEOBJECT_SPAWNED_FROM_NETWORK, Events_OnGameObjectSpawned);
        }
        
        private void Events_OnNetworkRoomJoined()
        {
            // Handle any enemy spawning logic when joining a network room
            OnNetworkRoomJoined();
        }
        
        private void Events_OnGameObjectSpawned(object[] args)
        {
            GameObject spawnedObj = (GameObject)args[0];
            IEnemyController enemyComponent = spawnedObj.GetComponent<IEnemyController>();
            if (enemyComponent != null)
            {
                OnEnemySpawned(enemyComponent);
            }
        }
        
        protected virtual void OnNetworkRoomJoined()
        {
            // Override in derived classes for specific network room joined behavior
        }
        
        protected virtual void OnEnemySpawned(IEnemyController enemyController)
        {
            RegisterEnemy(enemyController);
        }
        
        
        // ENEMY MANAGEMENT METHODS
        
        /// <summary>
        /// Create a new enemy of the specified type
        /// </summary>
        /// <param name="enemyType">The type of enemy to create</param>
        /// <returns>The created enemy GameObject</returns>
        public virtual GameObject CreateEnemy(string enemyType)
        {
            if (_enemyFactory == null)
            {
                Debug.LogError("EnemyManager: No enemy factory available");
                return null;
            }
            
            GameObject enemy = _enemyFactory.CreateEnemy(enemyType);
            if (enemy != null)
            {
                IEnemyController enemyController = enemy.GetComponent<IEnemyController>();
                if (enemyController != null)
                {
                    RegisterEnemy(enemyController);
                }
            }
            
            return enemy;
        }
        
        /// <summary>
        /// Create a new enemy at a specific position and rotation
        /// </summary>
        /// <param name="enemyType">The type of enemy to create</param>
        /// <param name="position">The spawn position</param>
        /// <param name="rotation">The spawn rotation</param>
        /// <returns>The created enemy GameObject</returns>
        public virtual GameObject CreateEnemy(string enemyType, Vector3 position, Quaternion rotation)
        {
            if (_enemyFactory == null)
            {
                Debug.LogError("EnemyManager: No enemy factory available");
                return null;
            }
            
            GameObject enemy = _enemyFactory.CreateEnemy(enemyType, position, rotation);
            if (enemy != null)
            {
                IEnemyController enemyController = enemy.GetComponent<IEnemyController>();
                if (enemyController != null)
                {
                    RegisterEnemy(enemyController);
                }
            }
            
            return enemy;
        }
        
        /// <summary>
        /// Register an enemy with the manager
        /// </summary>
        /// <param name="enemy">The enemy to register</param>
        public virtual void RegisterEnemy(IEnemyController enemy)
        {
            if (!_allEnemyInstances.Contains(enemy))
            {
                _allEnemyInstances.Add(enemy);
                UpdateEnemyLists();
            }
        }
        
        /// <summary>
        /// Unregister an enemy from the manager
        /// </summary>
        /// <param name="enemy">The enemy to unregister</param>
        public virtual void UnregisterEnemy(IEnemyController enemy)
        {
            _allEnemyInstances.Remove(enemy);
            _activeEnemyInstances.Remove(enemy);
            _deadEnemyInstances.Remove(enemy);
        }
        
        /// <summary>
        /// Kill all active enemies
        /// </summary>
        public virtual void KillAllEnemies()
        {
            foreach (IEnemyController enemy in _activeEnemyInstances.ToList())
            {
                enemy.Kill();
            }
            UpdateEnemyLists();
        }
        
        /// <summary>
        /// Respawn all dead enemies
        /// </summary>
        public virtual void RespawnAllEnemies()
        {
            foreach (IEnemyController enemy in _deadEnemyInstances.ToList())
            {
                enemy.Respawn();
            }
            UpdateEnemyLists();
        }
        
        /// <summary>
        /// Set all enemies to target a specific transform
        /// </summary>
        /// <param name="target">The target to pursue</param>
        public virtual void SetAllEnemiesTarget(Transform target)
        {
            foreach (IEnemyController enemy in _activeEnemyInstances)
            {
                enemy.SetTarget(target);
            }
        }
        
        /// <summary>
        /// Clear all enemy targets
        /// </summary>
        public virtual void ClearAllEnemyTargets()
        {
            foreach (IEnemyController enemy in _activeEnemyInstances)
            {
                enemy.ClearTarget();
            }
        }
        
        /// <summary>
        /// Update the active and dead enemy lists based on current states
        /// </summary>
        private void UpdateEnemyLists()
        {
            _activeEnemyInstances.Clear();
            _deadEnemyInstances.Clear();
            
            foreach (IEnemyController enemy in _allEnemyInstances)
            {
                if (enemy.IsDead)
                {
                    _deadEnemyInstances.Add(enemy);
                }
                else if (enemy.IsActive)
                {
                    _activeEnemyInstances.Add(enemy);
                }
            }
        }
        
        /// <summary>
        /// Get enemies within a certain distance of a position
        /// </summary>
        /// <param name="position">The center position</param>
        /// <param name="radius">The search radius</param>
        /// <param name="includeDeadEnemies">Whether to include dead enemies</param>
        /// <returns>List of enemies within the radius</returns>
        public virtual List<IEnemyController> GetEnemiesInRadius(Vector3 position, float radius, bool includeDeadEnemies = false)
        {
            List<IEnemyController> result = new List<IEnemyController>();
            List<IEnemyController> searchList = includeDeadEnemies ? _allEnemyInstances : _activeEnemyInstances;
            
            float radiusSquared = radius * radius;
            
            foreach (IEnemyController enemy in searchList)
            {
                if (enemy.Transform != null)
                {
                    float distanceSquared = (enemy.Transform.position - position).sqrMagnitude;
                    if (distanceSquared <= radiusSquared)
                    {
                        result.Add(enemy);
                    }
                }
            }
            
            return result;
        }
        
        /// <summary>
        /// Get the closest enemy to a position
        /// </summary>
        /// <param name="position">The position to search from</param>
        /// <param name="includeDeadEnemies">Whether to include dead enemies</param>
        /// <returns>The closest enemy, or null if none found</returns>
        public virtual IEnemyController GetClosestEnemy(Vector3 position, bool includeDeadEnemies = false)
        {
            List<IEnemyController> searchList = includeDeadEnemies ? _allEnemyInstances : _activeEnemyInstances;
            
            IEnemyController closest = null;
            float closestDistanceSquared = float.MaxValue;
            
            foreach (IEnemyController enemy in searchList)
            {
                if (enemy.Transform != null)
                {
                    float distanceSquared = (enemy.Transform.position - position).sqrMagnitude;
                    if (distanceSquared < closestDistanceSquared)
                    {
                        closestDistanceSquared = distanceSquared;
                        closest = enemy;
                    }
                }
            }
            
            return closest;
        }
    }
}
