// Copyright Isto Inc.

using UnityEngine;
using Zenject;

namespace Isto.Core.Beings
{
    /// <summary>
    /// Example script showing how to use the EnemyManager
    /// </summary>
    public class EnemyManagerExample : MonoBehaviour
    {
        // UNITY HOOKUP
        
        [Header("Example Settings")]
        [SerializeField] private string _enemyTypeToSpawn = "Spider";
        [SerializeField] private int _maxEnemies = 5;
        [SerializeField] private float _spawnRadius = 20f;
        [SerializeField] private bool _autoSpawnEnemies = true;
        
        
        // INJECTION
        
        private EnemyManager _enemyManager;
        private PlayerManager _playerManager;
        
        [Inject]
        public void Inject(EnemyManager enemyManager, PlayerManager playerManager)
        {
            _enemyManager = enemyManager;
            _playerManager = playerManager;
        }
        
        
        // LIFECYCLE EVENTS
        
        private void Start()
        {
            if (_autoSpawnEnemies)
            {
                SpawnInitialEnemies();
            }
        }
        
        private void Update()
        {
            // Example: Press 'E' to spawn a new enemy
            if (Input.GetKeyDown(KeyCode.E))
            {
                SpawnEnemyAtRandomPosition();
            }
            
            // Example: Press 'K' to kill all enemies
            if (Input.GetKeyDown(KeyCode.K))
            {
                _enemyManager.KillAllEnemies();
                Debug.Log("Killed all enemies!");
            }
            
            // Example: Press 'R' to respawn all dead enemies
            if (Input.GetKeyDown(KeyCode.R))
            {
                _enemyManager.RespawnAllEnemies();
                Debug.Log("Respawned all dead enemies!");
            }
            
            // Example: Press 'T' to make all enemies target the local player
            if (Input.GetKeyDown(KeyCode.T))
            {
                if (_playerManager.LocalPlayer != null)
                {
                    _enemyManager.SetAllEnemiesTarget(_playerManager.LocalPlayer.transform);
                    Debug.Log("All enemies now targeting the local player!");
                }
            }
            
            // Example: Press 'C' to clear all enemy targets
            if (Input.GetKeyDown(KeyCode.C))
            {
                _enemyManager.ClearAllEnemyTargets();
                Debug.Log("Cleared all enemy targets!");
            }
        }
        
        
        // EXAMPLE METHODS
        
        /// <summary>
        /// Spawn initial enemies when the game starts
        /// </summary>
        private void SpawnInitialEnemies()
        {
            for (int i = 0; i < _maxEnemies; i++)
            {
                SpawnEnemyAtRandomPosition();
            }
            
            Debug.Log($"Spawned {_maxEnemies} initial enemies");
        }
        
        /// <summary>
        /// Spawn an enemy at a random position around this object
        /// </summary>
        private void SpawnEnemyAtRandomPosition()
        {
            if (_enemyManager.ActiveEnemyCount >= _maxEnemies)
            {
                Debug.Log("Maximum number of enemies reached!");
                return;
            }
            
            // Generate a random position around this object
            Vector2 randomCircle = Random.insideUnitCircle * _spawnRadius;
            Vector3 spawnPosition = transform.position + new Vector3(randomCircle.x, 0, randomCircle.y);
            
            // Spawn the enemy
            GameObject enemy = _enemyManager.CreateEnemy(_enemyTypeToSpawn, spawnPosition, Quaternion.identity);
            
            if (enemy != null)
            {
                Debug.Log($"Spawned {_enemyTypeToSpawn} at {spawnPosition}");
            }
            else
            {
                Debug.LogError($"Failed to spawn enemy of type {_enemyTypeToSpawn}");
            }
        }
        
        /// <summary>
        /// Example method to get enemies near the player
        /// </summary>
        [ContextMenu("Find Enemies Near Player")]
        private void FindEnemiesNearPlayer()
        {
            if (_playerManager.LocalPlayer == null)
            {
                Debug.Log("No local player found");
                return;
            }
            
            Vector3 playerPosition = _playerManager.LocalPlayer.transform.position;
            var nearbyEnemies = _enemyManager.GetEnemiesInRadius(playerPosition, 10f);
            
            Debug.Log($"Found {nearbyEnemies.Count} enemies within 10 units of the player");
            
            foreach (var enemy in nearbyEnemies)
            {
                Debug.Log($"Enemy at {enemy.Transform.position}, Distance: {Vector3.Distance(playerPosition, enemy.Transform.position):F2}");
            }
        }
        
        /// <summary>
        /// Example method to find the closest enemy to the player
        /// </summary>
        [ContextMenu("Find Closest Enemy to Player")]
        private void FindClosestEnemyToPlayer()
        {
            if (_playerManager.LocalPlayer == null)
            {
                Debug.Log("No local player found");
                return;
            }
            
            Vector3 playerPosition = _playerManager.LocalPlayer.transform.position;
            var closestEnemy = _enemyManager.GetClosestEnemy(playerPosition);
            
            if (closestEnemy != null)
            {
                float distance = Vector3.Distance(playerPosition, closestEnemy.Transform.position);
                Debug.Log($"Closest enemy is at {closestEnemy.Transform.position}, Distance: {distance:F2}");
            }
            else
            {
                Debug.Log("No enemies found");
            }
        }
        
        
        // GUI FOR TESTING
        
        private void OnGUI()
        {
            if (_enemyManager == null) return;
            
            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.Label($"Enemy Manager Status:");
            GUILayout.Label($"Total Enemies: {_enemyManager.TotalEnemyCount}");
            GUILayout.Label($"Active Enemies: {_enemyManager.ActiveEnemyCount}");
            GUILayout.Label($"Dead Enemies: {_enemyManager.DeadEnemyCount}");
            
            GUILayout.Space(10);
            GUILayout.Label("Controls:");
            GUILayout.Label("E - Spawn Enemy");
            GUILayout.Label("K - Kill All Enemies");
            GUILayout.Label("R - Respawn All Enemies");
            GUILayout.Label("T - Target Player");
            GUILayout.Label("C - Clear Targets");
            GUILayout.EndArea();
        }
        
#if UNITY_EDITOR
        private void OnDrawGizmosSelected()
        {
            // Draw spawn radius
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position, _spawnRadius);
        }
#endif
    }
}
