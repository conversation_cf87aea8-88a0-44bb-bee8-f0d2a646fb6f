// Copyright Isto Inc.

using BehaviorDesigner.Runtime;
using FMODUnity;
using Isto.Core.Audio;
using Isto.Core.Enums;
using Isto.Core.Pooling;
using Isto.Core.UI;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.Beings
{
    /// <summary>
    /// Attached to any object that should have health
    /// 
    /// Also controls the type of damage it can take/ will respond to
    /// </summary>
    public class Health : MonoBehaviour, IStunnable, IActionOnRecycle
    {
        public class DamageTypeEnum : Int32Enum<DamageTypeEnum>
        {
            // Preserving values that correspond to these types in Atrio
            public static readonly DamageTypeEnum PHYSICAL = new DamageTypeEnum(0, nameof(PHYSICAL));
            public static readonly DamageTypeEnum STUN = new DamageTypeEnum(3, nameof(STUN));
            public static readonly DamageTypeEnum FIRE = new DamageTypeEnum(5, nameof(FIRE));
            public static readonly DamageTypeEnum HARVEST = new DamageTypeEnum(6, nameof(HARVEST));

            public DamageTypeEnum(int value, string name) : base(value, name)
            {
            }
        }

        public class DamageSourceEnum : Int32Enum<DamageSourceEnum>
        {
            // Preserving values that correspond to these types in Atrio
            public static readonly DamageSourceEnum PLAYER = new DamageSourceEnum(0, nameof(PLAYER));
            public static readonly DamageSourceEnum MOB = new DamageSourceEnum(1, nameof(MOB));
            public static readonly DamageSourceEnum TRAP = new DamageSourceEnum(2, nameof(TRAP));
            public DamageSourceEnum(int value, string name) : base(value, name)
            {
            }
        }


        // UNITY HOOKUP

        [Header("Core Stats")]
        public float HP;
        public float fullHp;

        [Tooltip("If set, any damage that is taken is only applied to stun damage, not actualy HP.")]
        public bool takesStunDamage = false;
        public float stunDamageResistance = 0;

        public bool takesInternalStunDamage = false;

        [Header("HealthBar")]
        [SerializeField] private GameObject _healthBar;
        [SerializeField] private Image _healthBarSlider;
        [SerializeField] private float _turnHealthBarRedPercent = 0.2f;
        [SerializeField] private Color _redSliderColor = Constants.ATRIO_RED;
        [SerializeField] private Color _normalSliderColor = Constants.ATRIO_LIGHT_GREY_TEXT;
        [SerializeField] private Color _greenSliderColor = Constants.ATRIO_GREEN_TEXT;
        [SerializeField] private float _showGreenDuration = 1f; //How long to show green after healing (to avoid flickering)

        [Header("Stun")]
        public float stunHP;
        public float stunResetTime = 3f;
        public float stunTimeOnDamage = 0.5f;

        [Header("VFX - Damage Flash")]
        public GameObject bodyContainer;
        public Color damageColor = Color.red;
        public float damageColorTime = 0.5f;

        [Header("Audio")]
        [EventRef]
        public string physicalImpactRef;

        [Header("Behavior Tree")]
        [SerializeField] private BehaviorTree _tree;
        [SerializeField] private string _damageBoolVarName;
        [SerializeField] private string _damageSourceVarName;


        // OTHER FIELDS

        private float _showGreenTimer = 0;
        private bool _sliderIsGreen = false;

        private float _fullStunHP;
        private float _stunTimer;
        private float _damageSpamTimer;
        private Rigidbody _body;
        private bool _isInvincible = false;
        private float _previousPercent;
        private bool _hasHealthBar;

        private List<Material> _materials;
        private Color _startColor;

        private Coroutine _damageCoroutine = null;

        
        // PROPERTIES

        public float StunPercent { get { return stunHP <= 0 ? 1 - _stunTimer / stunResetTime : 0f; } }
        public float HealthPercent { get { return HP / fullHp; } }


        // EVENTS

        public event EventHandler<HealthEventArgs> TookDamage;
        public event EventHandler<HealthEventArgs> Stunned;
        public event EventHandler<HealthEventArgs> StunReset;
        public event EventHandler<HealthEventArgs> Killed;


        // INJECTION

        private IGameSounds _gameSounds;
        private UIDamageTextDisplay.Pool _dmgDisplayPool;
        
        [Inject]
        public void Inject(IGameSounds gameSounds, [InjectOptional] UIDamageTextDisplay.Pool dmgDisplayPool)
        {
            _gameSounds = gameSounds;
            _dmgDisplayPool = dmgDisplayPool;
        }


        // LIFECYCLE EVENTS

        private void Awake()
        {
            _fullStunHP = stunHP;
            HP = fullHp;
            _body = GetComponent<Rigidbody>();
            _materials = new List<Material>();

            _hasHealthBar = _healthBar != null;

            LoadAllMaterials();
        }

        private void Start()
        {
            this.enabled = NeedsUpdate();

            ValidateBehaviorTreeVariables();
        }

        // Our Update method seems inocuous but it's on thousands of objects so this adds up (even just the Time.deltaTime).
        // Most of the time this script does nothing so we'll disable it and avoid having to do any checks. Only need to
        // enable it when we need to run a coroutine or tick the timers, or when health needs to be displayed (so less than 100%).
        private void Update()
        {
            float deltaTime = Time.deltaTime;

            _stunTimer += deltaTime;

            if (_stunTimer > stunResetTime)
            {
                if (stunHP < _fullStunHP)
                    StunReset?.Invoke(this, null);

                stunHP = _fullStunHP;
            }

            _damageSpamTimer += deltaTime;

            UpdateHealthBar(deltaTime);

            if (!NeedsUpdate())
                this.enabled = false;
        }


        // ACCESSORS

        private bool NeedsUpdate()
        {
            return (_hasHealthBar && HP < fullHp && HealthPercent != _previousPercent)
                    || stunHP < _fullStunHP
                    || _damageSpamTimer < Constants.DAMAGE_COOLDOWN_TIME
                    || _damageCoroutine != null; // Technically not in the update method but we need to stay active for this to run
        }


        // OTHER METHODS

        public void TakeDamage(float damage, DamageTypeEnum type, DamageSourceEnum source, Transform damageSourceTransform = null)
        {
            this.enabled = true;

            float damageToApply = damage;

            bool cantTakeDamage = _isInvincible || !enabled || (_damageSpamTimer < Constants.DAMAGE_COOLDOWN_TIME && type != DamageTypeEnum.HARVEST);

            if (cantTakeDamage)
                return;

            _damageSpamTimer = 0f;

            //Debug.LogFormat("{0} taking {1} damage", gameObject.name, damage);

            HealthEventArgs damageEvent = ApplyDamageByType(damageToApply, type, source, damageSourceTransform);

            _damageCoroutine = StartCoroutine(FlashDamageColor(damageColorTime));

            if(damageEvent.type != DamageTypeEnum.HARVEST && Mathf.Abs(damageEvent.damage) >= 1f)
            {
                if (_dmgDisplayPool != null)
                {
                    _dmgDisplayPool.Spawn(-damageToApply, transform.position, damageEvent.type);
                }
                else
                {
                    Debug.Log($"{damageToApply} {damageEvent.type} damage on {this.gameObject.name}", this.gameObject);
                }
            }

            if (HP <= 0)
            {
                HP = 0;

                Killed?.Invoke(this, damageEvent);
            }
            else
            {
                if (damageSourceTransform != null)
                    UpdateBehaviorTree(damageSourceTransform.position);
                damageEvent.sourceTransform = damageSourceTransform;
                damageEvent.stunTime = stunTimeOnDamage;
                TookDamage?.Invoke(this, damageEvent);
            }
        }

        protected virtual HealthEventArgs ApplyDamageByType(float damageToApply, DamageTypeEnum type, DamageSourceEnum source, Transform damageSourceTransform)
        {
            // Just some default values. Override this with your damage type logic in your project.
            return new HealthEventArgs(damageToApply, type, source);
        }

        protected void PlayPhysicalDamageSound()
        {
            if (!string.IsNullOrEmpty(physicalImpactRef))
            {
                _gameSounds.PlayOneShot(physicalImpactRef, transform.position);
            }
        }

        protected void TakeStunDamage(DamageSourceEnum source, Transform damageSourceTransform, float damageToApply)
        {
            stunHP -= damageToApply;
            _stunTimer = 0f;

            if (stunHP <= 0f)
            {
                if (_body != null)
                {
                    _body.linearVelocity = Vector3.zero;
                }

                Stunned?.Invoke(this, new HealthEventArgs(damageToApply, DamageTypeEnum.STUN, source, damageSourceTransform, stunResetTime));
            }

            if (takesStunDamage && damageSourceTransform != null)
                UpdateBehaviorTree(damageSourceTransform.position);
        }

        private void ValidateBehaviorTreeVariables()
        {
            if (_tree != null)
            {
                List<SharedVariable> variables = _tree.GetAllVariables();

                bool hasBoolVar = false;
                bool hasSourceVar = false;

                for (int i = 0; i < variables.Count; i++)
                {
                    if (variables[i].Name.Equals(_damageBoolVarName))
                        hasBoolVar = true;
                    else if (variables[i].Name.Equals(_damageSourceVarName))
                        hasSourceVar = true;
                }

                if (!hasBoolVar || !hasSourceVar)
                {
                    Debug.LogError("BehaviorTree does not have variables that are named on component. Please check. Disabling the health notification on the BehaviorTree");
                    _tree = null;
                }
            }
        }

        private void UpdateBehaviorTree(Vector3 damagePosition)
        {
            if (_tree != null)
            {
                _tree.SetVariableValue(_damageBoolVarName, true);
                _tree.SetVariableValue(_damageSourceVarName, damagePosition);
            }
        }

        private void UpdateHealthBar(float deltaTime)
        {
            if (!_hasHealthBar)
                return;

            if (HealthPercent != _previousPercent)
            {
                _previousPercent = HealthPercent;

                if (_healthBarSlider != null)
                    _healthBarSlider.fillAmount = HealthPercent;

                if (HP == fullHp)
                {
                    SetHealthBarColor(_normalSliderColor);
                    _healthBar.SetActive(false);
                }
                else
                {
                    _healthBar.SetActive(true);

                    //If in the middle of healing
                    if (_sliderIsGreen)
                    {
                        _showGreenTimer += deltaTime;

                        if (_showGreenTimer > _showGreenDuration)
                        {
                            SetHealthBarColor(_normalSliderColor);
                            _sliderIsGreen = false;
                        }
                    }
                    else if (HealthPercent < _turnHealthBarRedPercent)
                    {
                        SetHealthBarColor(_redSliderColor);
                    }
                    else
                    {
                        SetHealthBarColor(_normalSliderColor);
                    }
                }
            }
        }

        private void SetHealthBarColor(Color healthBarColor)
        {
            if (!_hasHealthBar)
                return;

            _healthBarSlider.color = healthBarColor;
        }


        public void Heal(float amount)
        {
            Heal(amount, DamageTypeEnum.PHYSICAL);
        }

        public void Heal(float amount, DamageTypeEnum type)
        {
            if(type == DamageTypeEnum.STUN)
            {
                stunHP = Mathf.Clamp(stunHP + amount, 0f, _fullStunHP);
            }
            else if (type == DamageTypeEnum.PHYSICAL)
            {
                HP = Mathf.Clamp(HP + amount, 0f, fullHp);
                _sliderIsGreen = true;
                _showGreenTimer = 0;
                SetHealthBarColor(_greenSliderColor);
                if (this != null)
                {
                    this.enabled = true;
                }
            }
            else
            {
                throw new UnityException("No handler setup for Healing damage type: " + type.ToString());
            }
        }

        public virtual float GetHealthRemaining(DamageTypeEnum type)
        {
            if (type == DamageTypeEnum.PHYSICAL)
            {
                return HP;
            }
            else if (type == DamageTypeEnum.STUN)
            {
                return stunHP;
            }
            else
            {
                throw new UnityException("No handler for GetHealthRemaining of type " + type.ToString());
            }
        }

        public void SetMaxHealth(float amount)
        {
            fullHp = amount;
            HP = amount;
        }

        public void TakeDamage(float damage, Vector3 force, DamageTypeEnum type, DamageSourceEnum source)
        {
            if (_body != null)
            {
                _body.AddForce(force, ForceMode.Impulse);
            }

            TakeDamage(damage, type, source);
        }

        public void SetInvincible(bool invincible)
        {
            _isInvincible = invincible;
        }

        private void LoadAllMaterials()
        {
            if (bodyContainer == null)
                return;

            Renderer[] renderers = bodyContainer.GetComponentsInChildren<Renderer>(true);

            for (int i = 0; i < renderers.Length; i++)
            {
                _materials.Add(renderers[i].material);
            }

            if (_materials.Count > 0)
                _startColor = _materials[0].GetColor("_Color");
        }

        private IEnumerator FlashDamageColor(float time)
        {
            if (_materials.Count == 0)
                yield break;

            float timer = 0f;
            int colorMaterialID = Shader.PropertyToID("_Color");

            while (timer < time)
            {
                timer += Time.deltaTime;

                Color nextColor = Color.Lerp(_startColor, damageColor, timer / time);

                for (int i = 0; i < _materials.Count; i++)
                {
                    _materials[i].SetColor(colorMaterialID, nextColor);
                }

                yield return null;
            }

            timer = 0f;

            while (timer < time)
            {
                timer += Time.deltaTime;

                Color nextColor = Color.Lerp(damageColor, _startColor, timer / time);

                for (int i = 0; i < _materials.Count; i++)
                {
                    _materials[i].SetColor(colorMaterialID, nextColor);
                }

                yield return null;
            }

            ResetColor();
            _damageCoroutine = null;
        }

        private void ResetColor()
        {
            if (bodyContainer == null || _materials.Count == 0)
                return;

            int colorMaterialID = Shader.PropertyToID("_Color");

            for (int i = 0; i < _materials.Count; i++)
            {
                _materials[i].SetColor(colorMaterialID, _startColor);
            }
        }

        public void Respawn()
        {
            Debug.LogError("Respawn not implemented for Health script.");
        }

        public void OnRecycle()
        {
            stunHP = _fullStunHP;
            HP = fullHp;
            SetInvincible(false);
            ResetColor();
            UpdateHealthBar(Time.deltaTime);
        }

        // Editor Helpers

        [ContextMenu("Take 20 Damage")]
        public void DoDamage()
        {
            TakeDamage(20, DamageTypeEnum.PHYSICAL, DamageSourceEnum.PLAYER);
        }

        [ContextMenu("Kill")]
        public void Kill()
        {
            TakeDamage(3000, DamageTypeEnum.PHYSICAL, DamageSourceEnum.PLAYER);
        }
    }

}