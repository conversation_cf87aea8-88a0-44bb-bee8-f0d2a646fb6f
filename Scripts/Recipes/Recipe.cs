// Copyright Isto Inc.
using Isto.Core.Audio;
using Isto.Core.Beings;
using Isto.Core.Items;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using UnityEngine;

namespace Isto.Core
{
    /// <summary>
    /// Defines the input items required to create the output items.
    /// </summary>
    [CreateAssetMenu(fileName = "New Recipe", menuName = "Scriptables/Item/Recipe")]
    public class Recipe : ScriptableObject
    {
        // Public Variables
        public List<ItemPile> inputs;
        public List<ItemPile> outputs;
        [Tooltip("Time it takes to drop the outputs once the recipe ingredients are available")]
        public float cookTime = 1f;

        // Constructor

        public Recipe()
        {
            inputs = new List<ItemPile>();
            inputs.Add(new ItemPile(null, 1));
            inputs.Add(new ItemPile(null, 1));

            outputs = new List<ItemPile>();
            outputs.Add(new ItemPile(null, 1));
        }

        /// <summary>
        /// Checks if the passed in item is part of the ingredients list
        /// </summary>
        /// <param name="item"></param>
        /// <returns>True if the item is an ingredient, false otherwise</returns>
        public bool IsIngredient(CoreItem item)
        {
            for (int i = 0; i < inputs.Count; i++)
            {
                if (inputs[i].item == item)
                    return true;
            }

            return false;
        }

        public bool IsIngredient(string itemID)
        {
            for (int i = 0; i < inputs.Count; i++)
            {
                if (inputs[i].item.itemID == itemID)
                    return true;
            }

            return false;
        }

        // Static methods

        /// <summary>
        /// Taks a single itempile as input and returns the first recipe that is valid given that input from the list.
        /// </summary>
        /// <param name="input">ItemPile that is the input for the recipe.</param>
        /// <param name="recipes">List of all possible recipes to check.</param>
        /// <returns>Recipe if a valid one is found, null otherwise.</returns>
        public static Recipe FindValidRecipe(ItemPile input, List<Recipe> recipes)
        {
            for (int i = 0; i < recipes.Count; i++)
            {
                Recipe currentRecipe = recipes[i];

                if (currentRecipe.inputs.Count == 1)
                {
                    ItemPile recipeInput = currentRecipe.inputs[0];

                    if (recipeInput.item == input.item && recipeInput.count == input.count)
                    {
                        return currentRecipe;
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// Checks for a valid recipe in the list using only one item as input.
        /// </summary>
        /// <param name="input">Single item used as input for recipe.</param>
        /// <param name="recipes">List of all possible recipes to check.</param>
        /// <returns>Recipe if a valid one is found, null otherwise.</returns>
        public static Recipe FindValidRecipe(CoreItem input, List<Recipe> recipes)
        {
            for (int i = 0; i < recipes.Count; i++)
            {
                Recipe currentRecipe = recipes[i];

                if (currentRecipe.inputs.Count == 1)
                {
                    ItemPile recipeInput = currentRecipe.inputs[0];

                    if (recipeInput.item == input && recipeInput.count == 1)
                    {
                        return currentRecipe;
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// Takes a list of inputs and checks the list of recipes to see if a valid recipe exists based on the inputs.
        /// </summary>
        /// <param name="inputs">List of ItemPiles that are inputs for the recipe.</param>
        /// <param name="recipes">List of possible recipes.</param>
        /// <returns>Recipe if one is found that is valid, null otherwise.</returns>
        public static Recipe FindValidRecipe(List<ItemPile> inputs, List<Recipe> recipes)
        {
            for (int i = 0; i < recipes.Count; i++)
            {
                Recipe currentRecipe = recipes[i];

                bool validRecipe = true;

                for (int j = 0; j < currentRecipe.inputs.Count; j++)
                {
                    ItemPile recipeInput = currentRecipe.inputs[j];

                    bool inputsMatch = false;

                    for (int k = 0; k < inputs.Count; k++)
                    {
                        inputsMatch |= inputs[k].item == recipeInput.item && inputs[k].count >= recipeInput.count;
                    }

                    validRecipe &= inputsMatch;
                }

                if (validRecipe)
                    return currentRecipe;
            }

            return null;
        }

        /// <summary>
        /// Coroutine for cooking items in the recipe by waiting the cook time and then dropping the output items.
        /// </summary>
        /// <param name="recipe">Recipe we are cooking</param>
        /// <param name="cookTransform">Transform of object that is dropping the items.  Using transform in case it moves.</param>
        /// <param name="sounds">GameSounds for playing audio.</param>
        /// <param name="randomSpacing">Max amount of spacing around transform position to drop item.</param>
        /// <param name="delayBetweenItems">If recipe drops multiple items, how long to wait between each item.</param>
        /// <returns></returns>
        public static IEnumerator CookRecipeAndDropItems(Recipe recipe, Transform cookTransform, IGameSounds sounds, float randomSpacing, float delayBetweenItems = 0.2f)
        {
            yield return new WaitForSeconds(recipe.cookTime);

            WaitForSeconds delayBetweenItemDrops = new WaitForSeconds(delayBetweenItems);

            for (int i = 0; i < recipe.outputs.Count; i++)
            {
                ItemPile itemPile = recipe.outputs[i];

                for (int j = 0; j < itemPile.count; j++)
                {
                    //Add a bit of randomness to the drop position
                    Vector3 dropPosition = cookTransform.position + UnityEngine.Random.insideUnitSphere * randomSpacing;
                    dropPosition.y = 0f;

                    itemPile.item.DropItem(dropPosition);

                    yield return delayBetweenItemDrops;
                }
            }
        }

        public static bool CanMakeRecipe(Recipe recipe, ItemContainer container)
        {
            if (recipe == null)
                return false;

            for (int i = 0; i < recipe.inputs.Count; i++)
            {
                int amountHave = container.GetCountOfItem(recipe.inputs[i].item);
                int amountNeeded = recipe.inputs[i].count;

                if (amountHave < amountNeeded)
                    return false;
            }

            return true;
        }

        public static bool CanMakeRecipe(Recipe recipe, List<ItemPile> items)
        {
            for (int i = 0; i < recipe.inputs.Count; i++)
            {
                int amountHave = 0;

                for (int j = 0; j < items.Count; j++)
                {
                    if (items[i].item == recipe.inputs[i].item)
                    {
                        amountHave += items[i].count;
                    }
                }

                int amountNeeded = recipe.inputs[i].count;

                if (amountHave < amountNeeded)
                    return false;
            }

            return true;
        }

        public static void RemoveRecipeIngredients(Recipe recipe, PlayerInventory container)
        {
            container.DisableEventFiring();

            if (!CanMakeRecipe(recipe, container))
            {
                Debug.LogError($"Trying to remove recipe inputs from container on:{container.gameObject.name} but there isn't all items in container.  This shouldn't happen.  Recipe:{recipe.name}");
                return;
            }

            // Remove all the inputs for the recipe from the inventory from the smallest stack in the inventory for each item
            for (int i = 0; i < recipe.inputs.Count; i++)
            {
                int totalToRemove = recipe.inputs[i].count;

                while (totalToRemove > 0)
                {
                    int indexToRemoveFrom = GetSmallestStackIndexForItem(recipe.inputs[i].item, container);

                    int removed = container.RemoveAt(indexToRemoveFrom, recipe.inputs[i].count).count;

                    totalToRemove -= removed;

                    if (removed == 0 && totalToRemove > 0)
                    {
                        if (removed != recipe.inputs[i].count)
                            Debug.LogError("unable to remove expected amount from inventory for recipe, recipe.canmakerecipe should've caught this");
                    }
                }
            }

            container.EnableEventFiring();
        }

        private static int GetSmallestStackIndexForItem(CoreItem item, PlayerInventory container)
        {
            int index = -1;
            int smallestCount = int.MaxValue;

            for (int i = 0; i < container.Items.Count; i++)
            {
                // Using <= to grab the last index in the inventory if item counts are same
                if (container.Items[i].item == item && container.Items[i].count <= smallestCount)
                {
                    index = i;
                    smallestCount = container.Items[i].count;
                }
            }

            return index;
        }

        public static void AddRecipeIngredients(Recipe recipe, PlayerInventory container)
        {
            container.DisableEventFiring();

            //Remove Items from inventory then check if has space
            for (int i = 0; i < recipe.inputs.Count; i++)
            {
                int added = container.Add(recipe.inputs[i].item, recipe.inputs[i].count);

                if (added != recipe.inputs[i].count)
                    Debug.LogError("Unable to add expected amount to inventory for recipe, This should only be called to put items back into inventory if queuing fails");
            }

            container.EnableEventFiring();
        }

        // Overidden operators

        public static bool operator ==(Recipe a, Recipe b)
        {
            if (ReferenceEquals(a, null) && ReferenceEquals(b, null))
                return true;
            else if (ReferenceEquals(a, null) || ReferenceEquals(b, null))
                return false;
            else
                return a.Equals(b);
        }

        public static bool operator !=(Recipe a, Recipe b) => !(a == b);

        public override int GetHashCode() => base.GetHashCode();

        public override bool Equals(object other)
        {
            if (other == null)
                return false;

            if (other is Recipe recipe)
            {
                // Check all inputs are the same
                for (int i = 0; i < recipe.inputs.Count; i++)
                {
                    if (i >= this.inputs.Count)
                        return false;

                    if (!recipe.inputs[i].item.itemID.Equals(this.inputs[i].item.itemID, StringComparison.CurrentCultureIgnoreCase))
                        return false;
                }

                // Check all outputs are the same
                for (int j = 0; j < recipe.outputs.Count; j++)
                {
                    if (j >= this.outputs.Count)
                        return false;

                    if (!recipe.outputs[j].item.itemID.Equals(this.outputs[j].item.itemID, StringComparison.CurrentCultureIgnoreCase))
                        return false;
                }

                return true;
            }

            return base.Equals(other);
        }

        public override string ToString()
        {
            StringBuilder builder = new StringBuilder("Recipe" + this.name + " takes in ");
            foreach (ItemPile input in inputs)
            {
                builder.Append(input.count + " " + input.item.itemID);
            }
            builder.Append(" and produces ");
            foreach (ItemPile output in outputs)
            {
                builder.Append(output.count + " " + output.item.itemID);
            }
            builder.Append(" in " + cookTime + " seconds.");
            return builder.ToString();
        }
    }
}