// Copyright Isto Inc.
using Isto.Core.Automation;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Isto.Core.Items
{
    /// <summary>
    /// Used for any object in the game that can hold multiple items
    /// </summary>
	public class ItemContainer : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IItemTopmostParent, IInventory
    {
        // Public Variables

        [Tooltip("The total number of item piles that this container has")]
        public int pileCount = 10;
        [Tooltip("This maximum count for each item pile (stack size)")]
        public int maxPileSize = 50;
        [Tooltip("Items that can be placed in container, if empty, all items are valid")]
        public List<CoreItem> validItems = new List<CoreItem>();

        public List<ItemPile> Items => _items;

        public int PileCount => pileCount;

        public int PileSize => maxPileSize;

        // Events

        public event EventHandler<ItemEventArgs> InventoryChanged;

        // Private Variables

        private List<string> _validItemIDs = new List<string>();
        private List<ItemPile> _items;

        // Methods		

        public virtual void Awake()
        {
            InitializeList();
        }

        public void InitializeList()
        {
            if (_items != null)
                return;

            _items = new List<ItemPile>(pileCount);

            // Fill the list with empty item piles
            for (int i = 0; i < pileCount; i++)
            {
                _items.Add(new ItemPile());
            }

            for (int i = 0; i < validItems.Count; i++)
            {
                _validItemIDs.Add(validItems[i].itemID);
            }
        }

        /// <summary>
        /// Attempts to deposit the item into this container.  
        /// </summary>
        /// <param name="item">CoreItem to deposit.</param>
        /// <param name="count">Number of the CoreItem to deposit. Default is one.</param>
        /// <returns>Number of CoreItems that were successfully deposited.</returns>
        public virtual int Add(CoreItem item, int count = 1, bool sendUpdateEvent = true)
        {
            if (_items == null)
                _items = new List<ItemPile>(pileCount);

            if (_validItemIDs.Count != 0 && !_validItemIDs.Contains(item.itemID))
                return 0;

            if (item == null)
                return 0;

            List<ItemPile> existingPiles = _items.FindAll(x => x.item == item);

            int remainingToAdd = count;

            //If there are already any piles of that item in the container, just add the pile amount if we have space.
            for (int i = 0; i < existingPiles.Count; i++)
            {
                int remainingSpace = maxPileSize - existingPiles[i].count;

                //If there is only room for some items, fill up the pile then try again on the next inventory pile
                if (remainingSpace < remainingToAdd)
                {
                    existingPiles[i].count += remainingSpace;
                    remainingToAdd -= remainingSpace;
                }
                else
                {
                    //Deposite all the remaining items to add on the pile and return count as all the items were deposited.
                    existingPiles[i].count += remainingToAdd;
                    remainingToAdd = 0;
                }
            }

            while (remainingToAdd > 0)
            {
                // Find first open index
                int openIndex = _items.FindIndex(x => x.item == null || x.count == 0);

                // If no open spaces found, break from while loop
                if (openIndex == -1)
                {
                    //Self backpack full info message trigger (and probably deprecated ensuing backpack upgrade tutorial)
                    //Also handles on some objects just to print a log that says it's not handled... (via IInventoryListener)
                    // TODO: remove this message!
                    SendMessage(MessageFunctions.INVENTORY_FULL, SendMessageOptions.DontRequireReceiver);

                    break;
                }

                if (remainingToAdd > maxPileSize)
                {
                    _items[openIndex].Set(item, maxPileSize);
                    remainingToAdd -= maxPileSize;
                }
                else
                {
                    _items[openIndex].Set(item, remainingToAdd);
                    remainingToAdd = 0;
                }
            }

            int amountAdded = count - remainingToAdd;

            // If any items were added, send events
            if (amountAdded != 0 && sendUpdateEvent)
            {
                SendUpdateEvents(item, amountAdded);
            }

            return amountAdded;
        }

        /// <summary>
        /// Attempts to deposit the item pile into the container. Internally just calls the Add method with
        /// CoreItem and count parameters
        /// </summary>
        /// <param name="pile">Item pile representing the item and amount to deposit</param>
        /// <returns>Number of CoreItems that were successfully deposited.</returns>
        public int Add(ItemPile pile)
        {
            return Add(pile.item, pile.count);
        }

        /// <summary>
        /// Attemps to add the item pile at the desired index position in the container.
        /// </summary>
        /// <param name="pile"></param>
        /// <param name="index"></param>
        /// <returns>Returns the amount of the pile that was successfully deposited</returns>
        public virtual int AddAt(ItemPile pile, int index)
        {
            if (_items.Count <= index)
                return 0;

            int deposited = 0;

            // If its empty at the desired index or the same item, try adding the items
            if (_items[index].item == null || _items[index].item == pile.item)
            {
                _items[index].item = pile.item;

                int spaceAvail = maxPileSize - _items[index].count;

                deposited = spaceAvail >= pile.count ? pile.count : spaceAvail;

                _items[index].count += deposited;
            }

            // If something was deposited, send message to any components on this object that are listening
            if (deposited != 0)
                SendUpdateEvents(pile.item, deposited);

            return deposited;
        }

        /// <summary>
        /// Attempts to remove the count of CoreItem from this container.
        /// </summary>
        /// <param name="item">CoreItem to remove</param>
        /// <param name="count">Amount of the CoreItem to remove.</param>
        /// <returns>The amount that was successfully removed.</returns>
        public virtual int Remove(CoreItem item, int count = 1, bool sendUpdateEvent = true)
        {
            if (_items == null || _items.Count == 0 || item == null)
                return 0;

            List<ItemPile> piles = _items.FindAll(x => x.item == item).OrderBy(x => x.count).ToList();

            int remainingToRemove = count;

            for (int i = 0; i < piles.Count; i++)
            {
                //If pile is large enough, take all items from this pile
                if (piles[i].count > remainingToRemove)
                {
                    piles[i].count -= remainingToRemove;
                    remainingToRemove = 0;
                    break;
                }
                //If not enough on pile, take all off pile and continue looping
                else
                {
                    remainingToRemove -= piles[i].count;
                    piles[i].count = 0;
                }

                // Clear slot if none left
                if (piles[i].count == 0)
                    piles[i].item = null;
            }

            int amountRemoved = count - remainingToRemove;

            if (amountRemoved != 0 && sendUpdateEvent)
                SendUpdateEvents(item, -amountRemoved);

            return amountRemoved;
        }

        /// <summary>
        /// Attempts to remove an amount of items from a specific position in the container
        /// </summary>
        /// <param name="index">The position in the container to remove the items from.</param>
        /// <param name="count">Amount of items at that position to remove.</param>
        /// <returns>ItemPile containing which item was at that position and the requested amount if enough, otherwise
        /// as many as were available.</returns>
        public virtual ItemPile RemoveAt(int index, int count)
        {
            if (_items.Count < index + 1)
                return null;

            ItemPile pileAtIndex = _items[index];
            ItemPile outputPile = new ItemPile();

            //If not enough items at index, remove all of them and return the pile
            if (pileAtIndex.count <= count)
            {
                outputPile.Set(pileAtIndex);
                _items[index].Clear();
            }
            //If enough items at index, subtract from this inventory and return the requested amount
            else
            {
                pileAtIndex.count -= count;
                outputPile.Set(pileAtIndex.item, count);
            }

            if (outputPile.count != 0)
                SendUpdateEvents(outputPile.item, outputPile.count);

            return outputPile;
        }

        public virtual ItemPile RemoveHalfAt(int index)
        {
            if (_items.Count < index + 1)
                return null;

            int amountToRemove = (int)Mathf.Ceil((float)_items[index].count / 2.0f);

            return RemoveAt(index, amountToRemove);
        }

        /// <summary>
        /// Removes the entire pile at index location specified
        /// </summary>
        /// <param name="index">Index position to remove the ItemPile from</param>
        /// <returns></returns>
        public virtual ItemPile RemoveAt(int index)
        {
            if (_items.Count < index + 1)
                return null;

            return RemoveAt(index, _items[index].count);
        }

        /// <summary>
        /// Takes all items out of the container
        /// </summary>
        /// <param name="triggerEvent">Should the remove action trigger the Inventory Update event to be triggered</param>
        /// <returns>List containing all the item piles from the container.</returns>
        public virtual List<ItemPile> RemoveAll(bool triggerEvent = true)
        {
            List<ItemPile> allItems = new List<ItemPile>();

            for (int i = 0; i < _items.Count; i++)
            {
                if (_items[i].item != null && _items[i].count != 0)
                    allItems.Add(new ItemPile(_items[i].item, _items[i].count));

                _items[i].Clear();
            }

            // If there were items, clear list and send message
            if (allItems.Count != 0 && triggerEvent)
            {
                // Just passing in empty values for removing all items from inventory, should probably update
                SendUpdateEvents(null, 0);
            }

            return allItems;
        }

        // Is this copied and pasted from Inventory.cs? Why provide an implementation if it's going to be the same code?
        public virtual List<ItemPile> RemoveAllBut(List<Item> invalidItems, bool triggerEvent = true)
        {
            List<ItemPile> allItems = new List<ItemPile>();

            for (int i = 0; i < _items.Count; i++)
            {
                if (_items[i].item != null && _items[i].count != 0)
                {
                    if (!invalidItems.Contains(_items[i].item))
                    {
                        allItems.Add(new ItemPile(_items[i].item, _items[i].count));

                        _items[i].Clear();
                    }
                }
            }

            // If there were items, clear list and send message
            if (allItems.Count != 0 && triggerEvent)
            {
                // Just passing in empty values for removing all items from inventory, should probably update
                SendUpdateEvents(null, 0);
            }

            return allItems;
        }

        /// <summary>
        /// Gets the total count of the passed in item in the container.
        /// </summary>
        /// <param name="item">Item to get the count for.</param>
        /// <returns>Amount of item that exists in the container.</returns>
        public int GetCountOfItem(CoreItem item)
        {
            if (_items == null || item == null)
                return 0;

            return GetCountOfItem(item.itemID);
        }

        public int GetCountOfItem(string itemID)
        {
            if (_items == null || String.IsNullOrEmpty(itemID))
                return 0;

            int amt = 0;

            for (int i = 0; i < _items.Count; i++)
            {
                amt += _items[i].item?.itemID == itemID ? _items[i].count : 0;
            }

            return amt;
        }

        public int GetCountAt(int index)
        {
            if (_items == null)
                return 0;
            if (_items.Count < index)
                return 0;

            return _items[index].count;
        }

        public int GetHalfCountAt(int index)
        {
            if (_items == null)
                return 0;
            if (_items.Count < index + 1)
                return 0;

            int half = (int)Mathf.Ceil((float)_items[index].count / 2.0f);
            return half;
        }

        /// <summary>
        /// Returns the number of item piles currently in the inventory, NOT the count of unique items in the container.
        /// </summary>
        /// <returns>Number of ItemPile's in container</returns>
        public int GetNumberOfPiles()
        {
            int total = 0;

            for (int i = 0; i < _items.Count; i++)
            {
                if (_items[i] != null && _items[i].item != null)
                {
                    total++;
                }
            }

            return total;
        }

        /// <summary>
        /// Gets the total number of all items in the container.
        /// </summary>
        /// <returns></returns>
        public int GetTotalNumberOfItems()
        {
            int total = 0;

            for (int i = 0; i < _items.Count; i++)
            {
                if (_items[i] != null && _items[i].item != null)
                {
                    total += _items[i].count;
                }
            }

            return total;
        }

        /// <summary>
        /// Returns the item at the index specified.
        /// </summary>
        /// <param name="index">Index of item pile to retrieve.</param>
        /// <returns>ItemPile of item at specified index if found, null otherwise.</returns>
        public ItemPile GetItemAt(int index)
        {
            if (index >= _items.Count)
                return null;

            return _items[index];
        }

        /// <summary>
        /// Gets the index in the inventory for the first pile containing the item.
        /// </summary>
        /// <param name="item">Item to find the index of</param>
        /// <returns>The first index for the item in the container, -1 if item not found.</returns>
        public int GetFirstIndexOfItem(CoreItem item)
        {
            int index = _items.FindIndex(x => x.item == item);

            return index;
        }

        /// <summary>
        /// Checks if this container will accept the item passed in.
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        public bool AcceptsItem(CoreItem item)
        {
            if (_validItemIDs.Count != 0 && !_validItemIDs.Contains(item.itemID))
                return false;
            else
                return true;
        }

        public bool AcceptsItem(ItemPile pile)
        {
            if (pile.item != null)
                return AcceptsItem(pile.item);

            return false;
        }

        public void SortAlphabetically()
        {
            List<ItemPile> allItems = RemoveAll(false);

            for (int i = 0; i < allItems.Count; i++)
            {
                Add(allItems[i].item, allItems[i].count, false);
            }

            MyComparer compare = new MyComparer();
            _items.Sort((x, y) => compare.Compare(x, y));
        }

        /// <summary>
        /// Returns how many of the item can be placed into the container.
        /// </summary>
        /// <param name="item">CoreItem to be placed in container.</param>
        /// <returns></returns>
        public int GetSpaceAvailable(CoreItem item)
        {
            if (_items == null)
                _items = new List<ItemPile>(pileCount);

            if (_validItemIDs.Count != 0 && !_validItemIDs.Contains(item.itemID))
                return 0;

            List<ItemPile> existingPiles = _items.FindAll(x => x.item == item);

            int spaceAvailable = 0;

            //If there are already any piles of that item in the container, just add the pile amount if we have space.
            for (int i = 0; i < existingPiles.Count; i++)
            {
                spaceAvailable += maxPileSize - existingPiles[i].count;
            }

            // Find how many open spaces we have
            int openSpaces = _items.FindAll(x => x.item == null || x.count == 0).Count;

            spaceAvailable += openSpaces * maxPileSize;

            return spaceAvailable;
        }

        public int GetNumOpenSlots()
        {
            // Find how many open spaces we have
            return _items.FindAll(x => x.item == null || x.count == 0).Count;
        }

        /// <summary>
        /// Calculate how many the percentage of Open
        /// </summary>
        /// <returns>Percent from 0-1 of how full the scrap chest is</returns>
        public float GetContainerPercentUsed()
        {
            return 1 - ((float)GetNumOpenSlots() / (float)pileCount);
        }

        /// <summary>
        /// Checks if the container has enough space for all the items in the list.  Used by recipes to check if there is space before crafting
        /// </summary>
        /// <param name="outputs">List of ItemPiles to check if there is space for</param>
        /// <returns>True if there is space, false otherwise</returns>
        public bool HasSpaceFor(List<ItemPile> outputs)
        {
            bool spaceForAll = true;

            int nextIndex = 0;

            // If space for item, add to inventory temporarily
            for (int i = nextIndex; i < outputs.Count; i++)
            {
                if (GetSpaceAvailable(outputs[i].item) > outputs[i].count)
                {
                    Add(outputs[i].item, outputs[i].count, false);
                    nextIndex++;
                }
                else
                {
                    spaceForAll = false;
                    break;
                }
            }

            // Remove all items that were added
            for (int i = nextIndex - 1; i >= 0; i--)
            {
                Remove(outputs[i].item, outputs[i].count, false);
            }

            return spaceForAll;
        }

        /// <summary>
        /// Checks the ingredient list for the passed in item and returns if the player has enough of the ingredients to craft one
        /// of it.
        /// </summary>
        /// <param name="item">CoreItem to be checked</param>
        /// <returns>True if the player inventory has enough of the required items, false otherwise.</returns>
        public bool HasIngredients(CoreItem item)
        {
            bool hasAll = true;

            if (item == null || item.GetRecipe() == null)
                return false;

            for (int i = 0; i < item.GetRecipe().inputs.Count; i++)
            {
                ItemPile ingredient = item.GetRecipe().inputs[i];

                hasAll = hasAll && GetCountOfItem(ingredient.item) >= ingredient.count;
            }

            return hasAll;
        }

        public GameObject GetGameObject()
        {
            return gameObject;
        }


        // Events

        protected virtual void SendUpdateEvents(CoreItem item, int change)
        {
            // Used by mob player override, and baittrap (from IInventoryListener) - probably deprecated both
            SendMessage(MessageFunctions.INVENTORY_UPDATE, SendMessageOptions.DontRequireReceiver);

            // InventoryChanged - listeners list:

            // IInventory:
            // Turret, dispense chest, planter, research booster, scrap chest, battery entrance, item entrance
            // Factory menu, UIItemSlot
            // Inventory:
            // DeerStation, FuelDepot
            // Tutorial Actions:
            // BeeBox, craft juice, lightbulb, pickerpal, tangled chest, planter, plunger, automation, blue shroom tip
            // Core-IInventory:
            // Dropped BP

            //player stuff: UI Item tracker, crafting menu/tab/slot
            // crafting slot: state change
            // crafting tab: category saturation
            // crafting menu: fires IReceiveContainersUpdate.OnContainersUpdated() (goes to item slots only I think)
            // item tracker: containers update (mostly qty refresh I imagine)

            InventoryChanged?.Invoke(this, new ItemEventArgs(this, item, change));
        }

        public virtual void SetPileCount(int count)
        {
            if (pileCount != count)
            {
                if (Items.Count > count)
                {
                    int startIndex = count - 1;
                    int amtToRemove = Items.Count - count;

                    if (startIndex >= 0)
                        Items.RemoveRange(startIndex, amtToRemove);
                    else
                        Items.Clear();
                }
                else
                {
                    int amoutToAdd = count - Items.Count;

                    for (int i = 0; i < amoutToAdd; i++)
                    {
                        Items.Add(new ItemPile());
                    }
                }

                pileCount = count;
            }
        }
    }

    public class MyComparer : IComparer<ItemPile>
    {
        public int Compare(ItemPile x, ItemPile y)
        {
            if (x.item == null)
            {
                return 1;
            }
            else if (y.item == null)
            {
                return -1;
            }
            else
            {
                int value = string.Compare(x.item.itemName, y.item.itemName);
                return value;
            }
        }
    }

}