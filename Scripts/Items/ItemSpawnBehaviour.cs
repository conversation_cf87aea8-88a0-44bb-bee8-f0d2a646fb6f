// Copyright Isto Inc.
using FMODUnity;
using Isto.Core.Audio;
using Isto.Core.Automation;
using Isto.Core.Beings;
using Isto.Core.Pooling;
using System.Collections;
using UnityEngine;
using Zenject;

namespace Isto.Core.Items
{
    /// <summary>
    /// This launches the item out when spawed. This happens pretty much after every player harvest (and after the deer poop)
    /// </summary>
    public class ItemSpawnBehaviour : MonoBehaviour, IActionOnRecycle
    {
        // Public Variables

        public bool randomizeForce = true;

        [EventRef] public string pickupSoundRef = @"event:/SFX/Atrio SFX_Pick up-St";

        // Private Variables

        [SerializeField] private ItemController _itemController = default;

        private Rigidbody _rigidBody;
        private SphereCollider _collider;
        private bool _startDelay = true;
        private float _startForce = -1f; // This is set from injection CoreItemDropParameters

        private bool _autoCollect = false;
        private bool _collectionRunning;

        private PlayerInventory _inventory;
        private Transform _playerTrans;
        private ItemCollectionParameters _collectionParams;
        private IGameSounds _sounds;
        private CoreItemDropParameters _dropParams;

        // Lifecycle Events

        [Inject]
        public void Inject(PlayerInventory playerInv, ItemCollectionParameters collectParams,
            IGameSounds gameSounds, CoreItemDropParameters dropParams)
        {
            _inventory = playerInv;
            _playerTrans = playerInv.transform;
            _collectionParams = collectParams;
            _sounds = gameSounds;
            _dropParams = dropParams;
        }

        private void Awake()
        {
            _rigidBody = GetComponent<Rigidbody>();

            if (_itemController == null)
            {
                _itemController = GetComponent<ItemController>();
            }
        }

        private void Start()
        {
            if (!_startDelay)
            {
                this.enabled = false;
            }
        }

        public void Update()
        {
            if (_startDelay)
            {
                return;
            }

            if (_rigidBody != null && _rigidBody.linearVelocity.y == 0f && !_collectionRunning)
            {
                CompleteBounce();
            }
        }

        // Methods

        public void SetStartForce(float force)
        {
            _startForce = force;
        }

        public void StartItemBounce(bool playerCollectAfterBounce)
        {
            float xDirection;
            float zDirection;

            if (playerCollectAfterBounce)
            {
                xDirection = Random.Range(0.1f, 0.4f);
                zDirection = Random.Range(-0.4f, -0.1f);
            }
            else
            {
                float drift = Random.Range(-0.15f, 0.15f);
                xDirection = drift;
                zDirection = drift;
            }

            Vector3 launchDirection = randomizeForce ? new Vector3(xDirection, 1.25f, zDirection) : Vector3.up;

            StartItemBounce(playerCollectAfterBounce, launchDirection);
        }

        public void StartItemBounce(bool playerCollectAfterBounce, Vector3 launchDirection)
        {
            randomizeForce = playerCollectAfterBounce;
            _autoCollect = playerCollectAfterBounce;

            gameObject.SetChildLayers(Layers.PROJECTILE);

            if (_collider == null)
            {
                _collider = gameObject.AddComponent<SphereCollider>();
                _collider.radius = 0.4f;
                _collider.center = new Vector3(-0.5f, 0.4f, 0.5f);
            }

            if (TryGetComponent(out IInteractable interactable))
                interactable.SetInteractive(false);

            if (_rigidBody == null)
            {
                _rigidBody = this.gameObject.AddComponent<Rigidbody>();
                _rigidBody.freezeRotation = true;

                // Not sure this is really necessary to add but it was in the original core item rigidbody before I removed it.
                _rigidBody.linearDamping = 0.1f;
            }

            _rigidBody.isKinematic = false;
            _rigidBody.useGravity = true;

            float launchForce = _startForce == -1f ? _dropParams.defaultStartForce : _startForce;
            _rigidBody.AddForce(launchDirection * launchForce, ForceMode.Impulse);

            Invoke(nameof(AllowPickupInvoked), 0.10f);
        }

        /// <summary>
        /// Called by Invoke, allows item to be collected by player or used by IItemProcessor before the bounce is actually completed
        /// </summary>
        private void AllowPickupInvoked()
        {
            _startDelay = false;

            // If not using auto collect, let the player pickup the item now
            if (!_autoCollect)
            {
                gameObject.SetChildLayers(Layers.COLLECTABLE);

                if (TryGetComponent(out IInteractable interactable))
                {
                    interactable.SetInteractive(true);
                }
            }
        }

        // "complete" means stopping the motion and checking if we need to trigger the item collect
        public void CompleteBounce()
        {
            int amountCollected = 0;
            bool somethingStayedOnTheFloor = true;

            if (_autoCollect && PlayerHasSpaceForItem())
            {
                // Try to take as much of the item as possible

                _sounds.PlayOneShot(pickupSoundRef, transform.position);
                amountCollected = AddItemToPlayerInventory();

                if (_itemController.itemPile.count == amountCollected)
                {
                    somethingStayedOnTheFloor = false;
                }
            }

            if (_autoCollect && somethingStayedOnTheFloor)
            {
                // We're not registered in automation because the system assumed we'd be picked up entirely, fix that
                AutomationCoreItemRegister automationRegisterer = this.gameObject.GetComponent<AutomationCoreItemRegister>();
                if (automationRegisterer != null)
                {
                    automationRegisterer.SetupInAutomation();
                }
            }

            if (amountCollected > 0)
            {
                // Fix the item count only after we're certain to be registered in automation
                _itemController.ReduceCount(amountCollected);
            }

            if (somethingStayedOnTheFloor)
            {
                // Stop the motion and fix the state for normal use
                GameObject.Destroy(_rigidBody);
                _rigidBody = null;

                gameObject.SetChildLayers(Layers.COLLECTABLE);

                if (TryGetComponent(out IInteractable interactable))
                {
                    interactable.SetInteractive(true);
                }

                Destroy(_collider);

                this.enabled = false;
            }
            else
            {
                // Only visually get the item if it's entirely taken
                StartCoroutine(ShowCollectByPlayer());
            }
        }

        private bool PlayerHasSpaceForItem()
        {
            if (_itemController)
            {
                return _inventory.GetSpaceAvailable(_itemController.itemPile.item) > 0;
            }

            return false;
        }

        // Animate the item going onto the player
        private IEnumerator ShowCollectByPlayer()
        {
            _collectionRunning = true;

            float timer = 0f;
            Vector3 startPosition = transform.position;

            while (timer < _collectionParams.moveTime)
            {
                Vector3 nextPosition = Vector3.Lerp(startPosition, _playerTrans.position, _collectionParams.movementCurve.Evaluate(timer / _collectionParams.moveTime));

                transform.position = nextPosition;

                timer += Time.deltaTime;

                yield return null;
            }

            _itemController.DoPickupAction();
        }

        private int AddItemToPlayerInventory()
        {
            if (_itemController)
            {
                return _inventory.Add(_itemController.itemPile.item, _itemController.itemPile.count);
            }

            return 0;
        }

        public void OnRecycle()
        {
            // This is handled somewhere else but I want to make sure the GO is inactive before we turn these settings back on to be
            // sure it doesn't affect anything
            this.gameObject.SetActive(false);

            _autoCollect = false;
            _collectionRunning = false;
            _startDelay = true;

            if (_rigidBody != null)
            {
                GameObject.Destroy(_rigidBody);
                _rigidBody = null;
            }

            gameObject.SetChildLayers(Layers.COLLECTABLE);

            if (TryGetComponent(out IInteractable interactable))
                interactable.SetInteractive(true);

            if (_collider != null)
                Destroy(_collider);

            this.enabled = true;
        }
    }
}