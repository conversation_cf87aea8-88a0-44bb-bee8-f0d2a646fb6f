// Copyright Isto Inc.
using I2.Loc;
using Isto.Core.Localization;
using Isto.Core.UI;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace Isto.Core.Items
{
    /// <summary>
    /// Handles the displaying of tooltips and different dialogue bubbles on the building
    /// </summary>
	public class BuildingDialogue : MonoBehaviour
    {
        // Private Variables

        private bool _messageActive = false;

        [SerializeField] private Animator _dialogueAnimator = default;
        [SerializeField] private TextMeshProUGUI _buildingNameTextbox = default;
        [SerializeField] private TextMeshProUGUI _buildingStatusTextbox = default;


        //Three variations of messages 
        [SerializeField] private GameObject _buildingMessageText = default;
        [SerializeField] private TextMeshProUGUI _messageText1 = default;

        [SerializeField] private GameObject _buildingMessageTextAndSymbol = default;
        [SerializeField] private TextMeshProUGUI _messageText2 = default;
        [SerializeField] private Image _messageSymbol1 = default;

        [SerializeField] private GameObject _buildingMessageSymbol = default;
        [SerializeField] private Image _messageSymbol2 = default;

        [SerializeField] private CanvasGroup _controlDisplayGroup = default;

        [SerializeField] private LocalizedString _offlineText;

        // Methods		

        private void Awake()
        {
            UIUtils.SetCameraForCanvas(gameObject);
        }

        public void ShowToolTipDialogue(bool buildingOnline)
        {
            _dialogueAnimator.SetBool("ShowToolTip", true);

            if (buildingOnline)
            {
                _controlDisplayGroup.alpha = 1;
                _buildingStatusTextbox.text = "";
            }
            else
            {
                _controlDisplayGroup.alpha = 0;
                Loc.SetTMPro(_buildingStatusTextbox, _offlineText);
            }

            _buildingStatusTextbox.color = Color.white;
            _buildingNameTextbox.color = Color.white;
        }

        public void HideToolTipDialogue()
        {
            _dialogueAnimator.SetBool("ShowToolTip", false);
        }

        public void ShowDialogueText(string localizedMessage)
        {
            Loc.SetTMProLocalized(_messageText1, localizedMessage);
            _dialogueAnimator.SetBool("ShowMessage", true);
        }

        //Alert Message Methods
        public void ShowAlertMessage(string localizedText = "", Sprite image = null)
        {
            //Turn on the right combination of stuff
            _buildingMessageTextAndSymbol.SetActive(false);
            _buildingMessageText.SetActive(false);
            _buildingMessageSymbol.SetActive(false);

            if (localizedText == "")
            {
                _buildingMessageTextAndSymbol.SetActive(true);
                UpdateSymbol(image);
            }
            else if (image == null)
            {
                _buildingMessageText.SetActive(true);
                UpdateAlertMessageText(localizedText);
            }
            else
            {
                _buildingMessageTextAndSymbol.SetActive(true);
                UpdateSymbol(image);
                UpdateAlertMessageText(localizedText);
            }

            _dialogueAnimator.SetBool("ShowMessage", true);
            _messageActive = true;
        }

        //Only to be used when temporarily hiding the alert (not for setup!)
        public void UnHideAlertMessage()
        {
            _dialogueAnimator.SetBool("ShowMessage", true);
            _messageActive = true;
        }

        public void HideAlertMessage()
        {
            _dialogueAnimator.SetBool("ShowMessage", false);
            _messageActive = false;
        }

        //Update Alert Messages
        public void UpdateSymbol(Sprite image)
        {
            _messageSymbol1.sprite = image;
            _messageSymbol2.sprite = image;
        }

        public void UpdateAlertMessageText(string localizedMessage)
        {
            Loc.SetTMProLocalized(_messageText1, localizedMessage);
            Loc.SetTMProLocalized(_messageText2, localizedMessage);
        }

        //Temporary Hiding 
        public bool IsMessageActive()
        {
            return _messageActive;
        }
    }
}