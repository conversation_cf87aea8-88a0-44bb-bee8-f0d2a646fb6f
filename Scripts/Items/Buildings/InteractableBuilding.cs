// Copyright Isto Inc.
using Cinemachine;
using Isto.Core.Data;
using System;
using UnityEngine;

namespace Isto.Core.Items
{
    public abstract class InteractableBuilding : InteractableItem, IDataLoadCompleteHandler
    {
        public EventHandler<EventArgs> BuildingTurnedOn;

        public bool IsOn { get { return _buildingOn; } }

        // Public Variables

        [Header("Interactable Building Base Properties")]
        public GameObject moduleOutline;

        // In Atrio this corresponded to a heartbox level, but in Core this is not yet tied to a specific concept, so
        // we can't enforce it
        public int enabledInLevel;

        [Tooltip("Used when showing building when in menu")]
        public CinemachineVirtualCamera focusCamera;

        [Tooltip("Used when showing building in tutorial")]
        public CinemachineVirtualCamera overviewCamera;

        public Sprite warningSymbol;
        public Sprite stopSymbol;

        [Header("Dialogue")]
        [SerializeField] protected BuildingDialogue _buildingDialogue = default;

        // Protected Variables

        protected Color _onlineTextColor = Constants.ATRIO_PINK_LIGHT;
        protected Color _offlineTextColor = Constants.ATRIO_DISABLED_GREY2;
        protected bool _buildingOn;

        private bool _tempHideAlert = false;

        // Methods

        public override void ShowTooltip()
        {
            if (_buildingDialogue != null)
            {
                _buildingDialogue.ShowToolTipDialogue(_buildingOn);

                if (_buildingDialogue.IsMessageActive())
                {
                    _tempHideAlert = true;
                    _buildingDialogue.HideAlertMessage();
                }
            }
            if (moduleOutline != null)
            {
                moduleOutline.SetActive(true);
            }
        }

        public override void SetInactive()
        {
            if (moduleOutline != null)
            {
                moduleOutline.SetActive(false);
            }

            if (_buildingDialogue != null)
            {
                _buildingDialogue.HideToolTipDialogue();

                if (_tempHideAlert)
                {
                    _tempHideAlert = false;
                    _buildingDialogue.UnHideAlertMessage();
                }
            }
        }

        public virtual void OnDataLoadComplete()
        {
            if(enabledInLevel > 0)
            {
                Debug.LogWarning($"InteractableBuilding.OnDataLoadComplete: {this.gameObject.name} expected to be " +
                                 $"enabled at level #{enabledInLevel} but not validated in current flow", gameObject);
            }
        }
    }
}