// Copyright Isto Inc.
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Items
{
    [CreateAssetMenu(fileName = "Category List", menuName = "Scriptables/Item/Category List")]
    public class CategoryList : ScriptableObject
    {
        // Public Variables
        public List<CraftingCategory> categories;
        public CoreItem defaultItem;

        public List<CoreItem> GetAllItemsInList()
        {
            List<CoreItem> allItems = new List<CoreItem>();

            for (int i = 0; i < categories.Count; i++)
            {
                CraftingCategory cat = categories[i];

                for (int j = 0; j < cat.itemList.Count; j++)
                {
                    CraftingSubCategory subCat = cat.itemList[j];

                    for (int k = 0; k < subCat.list.Count; k++)
                    {
                        if (subCat.list[k].itemID != MasterItemList.LOCKED_ITEM_ID)
                        {
                            allItems.Add(subCat.list[k]);
                        }
                    }
                }
            }

            return allItems;
        }
    }
}