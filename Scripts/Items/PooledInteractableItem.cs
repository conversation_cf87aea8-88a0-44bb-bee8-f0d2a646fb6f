// Copyright Isto Inc.
using Isto.Core.Automation;
using Isto.Core.Pooling;
using System;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UPool;
using Zenject;

namespace Isto.Core.Items
{
    /// <summary>
    /// An interactable item that supports being pooled.
    /// Note that supporting it does not mean it will necessarily be pooled. See PoolingConfigurationSettings asset for
    /// case-by-case setup.
    /// Also note that since not all items have been configured to be pooled, not all of them have been tested with pooling,
    /// and some might need additional refactoring of their child classes (or other components) in order to fully work once pooled.
    /// </summary>
    public abstract class PooledInteractableItem : InteractableItem
    {
        public enum AllocationStates
        {
            None,
            Allocated,
            Deallocated
        }

        public AllocationStates AllocationState => _state;
        public PoolableObject PoolingComponent => _poolableObj;
        public bool IsPooled => _state != AllocationStates.None && !_excludedFromPool;
        public string ItemID => this is HarvestableController harvestable ? harvestable.item.itemID : itemPile.item?.itemID;

        protected PoolableObject _poolableObj;
        protected AllocationStates _state = AllocationStates.None;
        private bool _eventsRegistered = false;

        // This gets set as the game is loading if appropriate.
        // We lack context and ability to sequence properly from here, so we let the other components set this and destroy us when ready.
        private bool _excludedFromPool = false;

        // Injected

        protected PooledItemFactory _pooledItemFactory;

        [Inject]
        public void Inject(PooledItemFactory pooledItemFactory)
        {
            _pooledItemFactory = pooledItemFactory;
        }

        // DeconfigurePooling is meant to happen before we initialize if we're an item loaded from the scene so we don't expect
        // a pool. Awake seems to happen too early for that to work on certain items. Start should allow it to work
        protected virtual void Start()
        {
            if (TryGetComponent(out _poolableObj))
            {
                RegisterEvents();

                _state = AllocationStates.Deallocated;
            }
        }

        protected override void OnDestroy()
        {
            UnregisterEvents();

            base.OnDestroy();

            if (!IsPooled)
                return;

            if (!_pooledItemFactory.IsCoreItemInsidePool(_poolableObj) && !_pooledItemFactory.IsInsidePool(this))
            {
                // It is normal that everything gets destroyed in a dirtier way when the scene is closed so avoid the error in that case
                if (this.gameObject.scene.isLoaded)
                    Debug.LogError($"PooledInteractableItem.OnDestroy: {this.gameObject.name} is being destroyed outside of the pool instead of returned to it.", this.gameObject);
            }
        }

        // We have items in the game that are loaded as part of the world instead of instantiated
        // The pooling system expects to be instantiating its own items and doesn't want to receive external ones
        // Because this is a known edge case we clean up the pooling support from these items so that we can end their
        // lifecycle without triggering any errors (because this otherwise looks like a pooled item and should be one)
        public void DeconfigurePooling()
        {
            UnregisterEvents();

            if (!IsPooled)
            {
                _excludedFromPool = true;
                return;
            }

            if (_state == AllocationStates.Allocated)
            {
                Debug.LogError($"PooledInteractableItem.DeconfigurePooling: Should not deconfigure item {this.gameObject.name} that really comes from a pool");
            }

            _excludedFromPool = true;
        }

        private void RegisterEvents()
        {
            if (_eventsRegistered)
                return;

            _poolableObj.ObjectAllocated += OnAllocate;
            _poolableObj.ObjectDeallocated += OnDeallocate;

            _eventsRegistered = true;
        }

        private void UnregisterEvents()
        {
            if (!_eventsRegistered)
                return;

            _poolableObj.ObjectAllocated -= OnAllocate;
            _poolableObj.ObjectDeallocated -= OnDeallocate;

            _eventsRegistered = false;
        }

        // Called when we are brought from the pool to the world
        public virtual void OnAllocate()
        {
            if (!IsPooled)
            {
                Debug.LogError($"PooledInteractableItem: Should not be allocating item {this.gameObject.name} witch is not truly pooled !");
                return;
            }

            _state = AllocationStates.Allocated;
        }

        // Called when we are returned to the pool (after IActionOnRecycle.OnRecycle)
        public virtual void OnDeallocate()
        {
            if (!IsPooled)
            {
                Debug.LogError($"PooledInteractableItem: Should not be deallocating item {this.gameObject.name} witch is not truly pooled !");
                return;
            }

            _state = AllocationStates.Deallocated;
        }

        /// <summary>
        /// Will recycle the object if it is from a pool, otherwise release the instance if it is addressable, otherwise destroy it.
        /// </summary>
        public virtual void DestroyGameObject()
        {
            if (PoolingComponent != null && IsPooled)
            {
                // For this check, Harvestables count so they get returned to the pool
                bool isAdvancedItem = itemPile.item is AdvancedItem || (this is HarvestableController);

                AutomationCoreItemRegister coreItemRegisterer = this.GetComponent<AutomationCoreItemRegister>();

                // Warning: it can be a core item of an advanced item if it is dropped on the floor
                if (!isAdvancedItem || coreItemRegisterer != null)
                {
                    bool returned;

                    try
                    {
                        _pooledItemFactory.ReturnPooledCoreItem(PoolingComponent);
                        returned = true;
                    }
                    catch (InvalidOperationException)
                    {
                        returned = false;
                    }

                    if (!returned)
                    {
                        Debug.LogError($"PooledInteractableItem.DestroyGameObject Could not return item {gameObject.name} to the CoreItemPool. Trying the AdvancedItem Pools");
                        _pooledItemFactory.ReturnPooledAdvancedItem(this); // this means my differentiation sucks? fix it?
                    }
                }
                else // is an advanced item - this should be the normal flow
                {
                    _pooledItemFactory.ReturnPooledAdvancedItem(this);
                }
            }
            else
            {
                string ourname = this.gameObject.name;
                if (Addressables.ReleaseInstance(this.gameObject))
                {
#if POOL_LOGGING
                    Debug.Log($"ReleaseInstance succeeded in releasing GO: {ourname}", this.gameObject);
#endif
                }
                else
                {
                    // Most of our interactable items should be addressable assets, but item instances placed in the scene are not,
                    // probably because being loaded as part of the scene circumvents the addressable system.
                    // Gates in particular are not culled as part of the display system so we don't destroy them when the scene loads,
                    // and the gate in the scene is therefore a pooled item that didn't come from a pool, and even though it is addressable
                    // they are destroyed here. I'm leaving this warning because that is still not usual behavior. We could replace gates
                    // at loading with new gates if we find this annoying.
                    Debug.LogWarning($"ReleaseInstance failed to release GO: {ourname}. It will be destroyed directly.", this.gameObject);
                    GameObject.Destroy(this.gameObject);
                }
            }
        }

        public void DestroyGameObject(float delay)
        {
            Invoke(nameof(DestroyGameObject), delay);
        }
    }
}