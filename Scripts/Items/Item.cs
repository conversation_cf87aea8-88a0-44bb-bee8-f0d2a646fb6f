// Copyright Isto Inc.
using I2.Loc;
using Isto.Core.Localization;
using System;
using UnityEngine;
using UnityEngine.AddressableAssets;

namespace Isto.Core.Items
{
    /// <summary>
    /// Base item for most game elements.  This class is not meant to be instanced, hence the abstract.  
    /// Contains the basic elements that are shared between all items in the game.
    /// 
    /// Item -> CoreItem -> AdvancedItem
    /// Item -> HarvestableItem
    /// </summary>
    public abstract class Item : ScriptableObject
    {
        // Public Variables

        public string itemName { get { return Loc.Get(itemNameLoc); } }
        public string interactToolTipText { get { return Loc.Get(interactToolTipTextLoc); } }
        public string flavorText { get { return Loc.Get(flavorTextLoc); } }

        public GameObject prefab; //GameObject that is created when instancing this item into the world
        public AssetReference addressableAsset;

        [Header("-------------- Standard Fields")]
        public string itemID = "NO ID SET " + Guid.NewGuid();
        public LocalizedString itemNameLoc;
        public LocalizedString flavorTextLoc;
        [Tooltip("When you're hovering over it to pick it up")]
        public LocalizedString interactToolTipTextLoc;
        [Tooltip("If set, only drops one item when dropping from inventory to the ground.  Still drops full pile when moving to other containers.")]
        public bool dropSingle = false;

        [Header("Icon")]
        public Sprite icon;

        [Header("Build Settings")]
        public bool ignoreInBuild = false;

        public virtual string GetToolTip(bool playerCanInteract = true)
        {
            return interactToolTipText;
        }

        // Overriding comparison operators to check itemID instead of object as scriptables loaded with Addressables system in different contexts are not considered the same asset
        public static bool operator ==(Item a, Item b)
        {
            if (ReferenceEquals(a, null) && ReferenceEquals(b, null))
                return true;
            else if (ReferenceEquals(a, null) || ReferenceEquals(b, null))
                return false;
            else if (string.IsNullOrEmpty(a.itemID) && string.IsNullOrEmpty(b.itemID))
                return true;
            else if (string.IsNullOrEmpty(a.itemID) || string.IsNullOrEmpty(b.itemID))
                return false;
            else
                return a.itemID.Equals(b.itemID);
        }
        public static bool operator !=(Item a, Item b) => !(a == b);
        public override int GetHashCode() => base.GetHashCode();
        public override bool Equals(object other)
        {
            if (other == null)
                return false;

            if (other is Item item)
                return item.itemID.Equals(itemID);

            return base.Equals(other);
        }
    }

    [Serializable]
    public class ToolTipMapping
    {
        public Item item;
        public string text;
    }
}