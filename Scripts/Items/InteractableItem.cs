// Copyright Isto Inc.

using Isto.Core.Beings;
using Isto.Core.Inputs;
using Isto.Core.UI;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Items
{
    [SelectionBase]
    public abstract class InteractableItem : MonoBehaviour, IInteractable
    {
        // Public Variables
        public ItemPile itemPile;

        [Header("Item Outlines on mouse over")]
        public List<GameObject> outlines;

        // Private Variables

        protected UIToolTip _activeTooltip;
        protected Vector3 _toolTipOffset = new Vector3(0, 0, 0);

        protected List<UserActions> _actionList = new List<UserActions>();

        // Flag for disabling item interaction
        protected bool _interactive = true;
        protected bool _toolTipVisible = false;


        // Injected

        protected IControls _controls;
        protected MasterItemList _masterItemList;
        protected UIToolTip.Pool _toolTipPool;
        protected Transform _selectionHighlightTransform;

        [Inject]
        public virtual void Inject(IControls controls, MasterItemList masterItemList,
            [InjectOptional] UIToolTip.Pool tooltipPool,
            [InjectOptional(Id = "SelectionHighlight")] Transform selectionHighlight)
        {
            _toolTipPool = tooltipPool;
            _controls = controls;
            _masterItemList = masterItemList;
            _selectionHighlightTransform = selectionHighlight;
        }


        // Lifecycle Events

        protected virtual void Awake()
        {
            HideOutline();
        }

        protected virtual void Update()
        {
            MoveToolTip();
        }

        protected virtual void OnDestroy()
        {
            SetInactive();
        }

        // Abstract Methods

        public abstract string GetItemName();
        public abstract string GetToolTip(UserActions action);
        public abstract List<UserActions> GetValidActions();

        // Methods

        public virtual void ShowTooltip(float offset, bool showSelectionhighlight = true)
        {
            if (!_interactive || _activeTooltip != null)
                return;

            if (_toolTipPool != null)
            {
                _activeTooltip = _toolTipPool.Spawn(GetItemName());
            }
            
            if (_activeTooltip == null)
                return;

            _toolTipVisible = true;

            //Adjust for the offset
            _toolTipOffset = new Vector3(0, 0, 0);
            _toolTipOffset.y += offset;
            _activeTooltip.transform.position = UnityUtils.TransformWorldToCanvasPoint(transform.position + _toolTipOffset, _activeTooltip.Canvas);

            List<UserActions> actionList = GetValidActions();

            for (int i = 0; i < actionList.Count; i++)
            {
                UserActions action = actionList[i];

                string toolTip = GetToolTip(action);

                _activeTooltip.AddRow(_controls.GetIconForAction(action), toolTip);
            }

            if (showSelectionhighlight)
            {
                ShowOutline();
            }
        }

        public virtual void ShowTooltip()
        {
            ShowTooltip(0);
        }

        public virtual void SetInactive()
        {
            if (_activeTooltip != null)
            {
                if (_toolTipPool != null)
                {
                    _toolTipPool.Despawn(_activeTooltip);
                }

                _activeTooltip = null;
                _toolTipVisible = false;

                if (!_selectionHighlightTransform.IsNullOrDestroyed())
                {
                    _selectionHighlightTransform.gameObject.SetActive(false);
                }
            }

            HideOutline();
        }

        public virtual void SetInteractive(bool state)
        {
            _interactive = state;

            if (!state)
                SetInactive();
        }

        public virtual Vector3 GetInteractionPosition(Vector3 playerPosition)
        {
            float direction = UnityUtils.GetRelativeHorizontalDirection(playerPosition, transform.position);

            // Getting a point to the side of the item to use to find nearest position on collider with.
            Vector3 offset = direction > 0 ? Constants.HARVEST_BEAM_ALIGN_RIGHT : Constants.HARVEST_BEAM_ALIGN_LEFT;

            return transform.position + offset;
        }

        protected virtual void MoveToolTip()
        {
            if (_toolTipVisible)
            {
                _activeTooltip.transform.position = UnityUtils.TransformWorldToCanvasPoint(transform.position + _toolTipOffset, _activeTooltip.Canvas);

                if (!_selectionHighlightTransform.IsNullOrDestroyed() && _selectionHighlightTransform.gameObject.activeInHierarchy)
                {
                    _selectionHighlightTransform.position = transform.position;
                }
            }
        }

        protected virtual void ShowOutline()
        {
            if (outlines.Count > 0)
            {
                for (int i = 0; i < outlines.Count; i++)
                {
                    if (outlines[i] != null && outlines[i].transform.parent != null && outlines[i].transform.parent.gameObject.activeInHierarchy)
                        outlines[i].SetActive(true);
                }
            }
            else
            {
                if (!_selectionHighlightTransform.IsNullOrDestroyed())
                {
                    _selectionHighlightTransform.gameObject.SetActive(true);
                    _selectionHighlightTransform.position = transform.position;
                }
            }
        }

        protected virtual void HideOutline()
        {
            if (outlines.Count > 0)
            {
                for (int i = 0; i < outlines.Count; i++)
                {
                    if (outlines[i] != null)
                        outlines[i].SetActive(false);
                }
            }
        }

        public virtual void PlayerInteraction(AutomationPlayerController player, UserActions action)
        {
            player.SetPickupItem(this);
        }

        public virtual void ReduceCount(int amount)
        {
            itemPile.count -= amount;
        }
    }
}