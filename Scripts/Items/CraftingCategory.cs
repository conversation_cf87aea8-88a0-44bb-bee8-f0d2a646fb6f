// Copyright Isto Inc.
using I2.Loc;
using Isto.Core.Beings;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Items
{
    /// <summary>
    /// A crafting category is used in the crafting menu. It's the large categories of items
    /// that you can craft ex. Automation, Upgrades, Equipment etc. 
    /// 
    /// Every category has a list of items within it
    /// </summary>
    [CreateAssetMenu(fileName = "Crafting Category", menuName = "Scriptables/Item/CraftingCategory")]
    public class CraftingCategory : ScriptableObject
    {
        [Tooltip("Not in use, this string is kept only for reference")]
        public string title;

        public LocalizedString titleKey;
        public Sprite image;
        public List<CraftingSubCategory> itemList;

        public CoreItem GetItem(int row, int col)
        {
            if (row >= itemList.Count)
            {
                return null;
            }
            else if (col >= itemList[row].list.Count)
            {
                return null;
            }
            else
            {
                return itemList[row].list[col];
            }
        }

        public bool ContainsItem(CoreItem item)
        {
            for (int i = 0; i < itemList.Count; i++)
            {
                for (int j = 0; j < itemList[i].list.Count; j++)
                {
                    CoreItem listItem = itemList[i].list[j];

                    if (listItem.itemID.Equals(item.itemID))
                        return true;
                }
            }

            return false;
        }

        public bool HasUnlockedItems(PlayerProgress progress)
        {
            for (int i = 0; i < itemList.Count; i++)
            {
                CraftingSubCategory subCategory = itemList[i];

                for (int j = 0; j < subCategory.list.Count; j++)
                {
                    if (progress.IsItemUnlocked(subCategory.list[j]))
                        return true;
                }
            }

            return false;
        }

        public int GetNumberUnseenItems(PlayerProgress playerProgress)
        {
            int count = 0;
            for (int i = 0; i < itemList.Count; i++)
            {
                for (int j = 0; j < itemList[i].list.Count; j++)
                {
                    CoreItem currItem = itemList[i].list[j];

                    if (playerProgress.IsItemUnlocked(currItem))
                    {
                        if (!playerProgress.HasItemBeenSeen(currItem))
                        {
                            count++;
                        }
                    }
                }
            }
            return count;
        }
    }
}
