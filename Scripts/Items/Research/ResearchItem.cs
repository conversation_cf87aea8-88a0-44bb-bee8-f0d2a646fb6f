// Copyright Isto Inc.
using I2.Loc;
using Isto.Core.Localization;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Items
{
    [CreateAssetMenu(fileName = "New Research Item", menuName = "Scriptables/Research/Research Item")]
    public class ResearchItem : ScriptableObject
    {
        // Public Variables

        public string ID = "NO ID SET";

        [Header("Research Requirements")]
        public int buildingLevel = 0;
        public float researchTime = 120;

        public string itemName { get { return Loc.Get(itemNameLoc); } }
        public string description { get { return Loc.Get(descriptionLoc); } }
        public string activateText { get { return Loc.Get(activetedTextLoc); } }

        [Header("Research Description")]
        public LocalizedString itemNameLoc;
        public LocalizedString descriptionLoc;
        public LocalizedString activetedTextLoc;
        public Sprite icon;

        [Header("Research Complete")]
        public List<CoreItem> itemUnlocks;
        public List<UpgradeItem> upgradeUnlocks;
    }
}