// Copyright Isto Inc.
using Isto.Core.Automation;
using Isto.Core.Inputs;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Items
{
    /// <summary>
    /// The main controlling class for all of the items
    ///
    /// If the item is within selection range showm the tooltip, if its highlighted, show the tooltip
    /// Grabs the sprite and makes sure it's properly set
    /// </summary>
    [SelectionBase]
    public class ItemController : PooledInteractableItem, IItemTopmostParent, IInteractable, IActivatable, IAutomationItemDisplay, IActionOnPickup
    {
        // Events

        public event EventHandler ItemSet;

        // Public Variables

        public bool InitializedInAutomation { get { return _item != null; } }
        public bool HasItemBeenSet { get; private set; }
        public AutomationCoreItem AutomationItem { get { return _item; } }

        public SpriteRenderer spriteRend;

        // Private Variables

        protected AutomationSystem _autoSystem;
        protected AutomationCoreItem _item;

        // Lifecycle Events

        protected override void Awake()
        {
            base.Awake();

            InitializeRenderer();

            _actionList.Clear();
            _actionList.Add(UserActions.INTERACT);
        }

        protected override void Update()
        {
            base.Update();

            // Update the count in case it has changed in the automation system
            if (_item != null)
                itemPile.count = _item.count;
        }

        protected override void OnDestroy()
        {
            if (_item != null)
            {
                _item.Consumed -= OnItemConsumed;
                _item = null;
            }

            base.OnDestroy();
        }

        public void SetItem(CoreItem itemType, int count)
        {
            itemPile = new ItemPile(itemType, count);

            InitializeRenderer();

            HasItemBeenSet = true;

            ItemSet?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// Sets the item that represents this object in the automation system and adds listener for Consumed event
        /// on AutomationCoreItem.
        /// </summary>
        /// <param name="item"></param>
        /// <param name="system"></param>
        public virtual void SetItem(AutomationCoreItem item, AutomationSystem system)
        {
            _autoSystem = system;
            _item = item;

            if (_masterItemList.TryGetItemByID(item.ID, out CoreItem itemType))
            {
                SetItem(itemType, item.count);
            }

            item.Consumed += OnItemConsumed;
        }

        public void InitializeRenderer()
        {
            if (spriteRend != null && itemPile != null && itemPile.item != null)
            {
                spriteRend.sprite = itemPile.item.gameplaySprite;
            }
        }

        public void SetPropertyBlockSet(Texture emissionSprite, float emissionFactor, Color emissionTint)
        {
            if (spriteRend == null)
                return;

            CoreSpritePropertyBlockSet propertyBlockSet = spriteRend.GetComponent<CoreSpritePropertyBlockSet>();

            // Load properties into script and then set the Material property block to use them.
            if (propertyBlockSet != null)
            {
                propertyBlockSet.emissionMap = emissionSprite;
                propertyBlockSet.emissionFactor = emissionFactor;
                propertyBlockSet.emissionTint = emissionTint;
                propertyBlockSet.disableEmissionInDark = false;
            }

            // Load properties into script and then set the Material property block to use them.
            if (propertyBlockSet != null)
            {
                propertyBlockSet.SetPropertyBlock();
            }
            else
            {
                Debug.LogError("No Sprite Property Block set on gameobject with SpriteRenderer.  Object: " + gameObject.name);
            }
        }

        public GameObject GetGameObject()
        {
            return gameObject;
        }

        public override List<UserActions> GetValidActions()
        {
            if (!_interactive)
                return new List<UserActions>();
            else
                return _actionList;
        }

        public override Vector3 GetInteractionPosition(Vector3 playerPosition)
        {
            float direction = UnityUtils.GetRelativeHorizontalDirection(playerPosition, transform.position);

            // Getting a point to the side of the item to use to find nearest position on collider with.
            Vector3 offset = direction > 0 ? Constants.ITEM_PICKUP_ALIGN_RIGHT : Constants.ITEM_PICKUP_ALIGN_LEFT;

            return transform.position + offset;
        }

        public override string GetToolTip(UserActions action)
        {
            switch (action.Name)
            {
                case nameof(UserActions.INTERACT):
                    return itemPile.item.GetToolTip(true);
                default:
                    return "";
            }
        }

        public override string GetItemName()
        {
            if (itemPile.count <= 1)
                return itemPile.item.itemName;
            else
                return itemPile.item.itemName + "  x" + itemPile.count;
        }

        public virtual void Activate()
        {
            enabled = true;
            gameObject.SetColliderState(true);
        }

        protected virtual void OnItemConsumed(object sender, EventArgs e)
        {
            if (_item != null)
                _item.Consumed -= OnItemConsumed;
        }

        public virtual void DoPickupAction()
        {
            if (_autoSystem != null && _item != null)
            {
                // Some bugs in the system (probably mainly racing conditions) might cause the item to exist visually but
                // not in the automation system, and sometimes even not in the display system, in which case DeleteCoreItem will
                // fail to clean up the visual as it won't find it.
                // However at this point we were picked up and one instance of our item has probably been added to the player's inventory.
                if (!_autoSystem.DeleteCoreItem(_item))
                {
                    // Trying to prevent item duplication by self destructing visuals if something goes wrong
                    DestroyGameObject();
                }
            }
        }

        public override void ReduceCount(int amount)
        {
            if (InitializedInAutomation)
            {
                _item.count -= amount;
            }

            base.ReduceCount(amount);
        }

        public override void OnAllocate()
        {
            base.OnAllocate();
        }

        public override void OnDeallocate()
        {
            if (_item != null)
                _item.Consumed -= OnItemConsumed;

            _item = null;

            HasItemBeenSet = false;

            base.OnDeallocate();
        }
    }
}