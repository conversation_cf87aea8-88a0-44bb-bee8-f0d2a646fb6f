// Copyright Isto Inc.
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Items
{
    /// <summary>
    /// Scriptable Object for containing lists of Item objects.  Used for setting starting items,
    /// already created items, etc at game start.
    /// </summary>
    [CreateAssetMenu(fileName = "New Item List", menuName = "Scriptables/Item/Item List")]
    public class ItemList : ScriptableObject
    {
        // Public Variables
        public List<CoreItem> items;

        public bool IsInRange(int num)
        {
            if (num >= items.Count)
            {
                return false;
            }
            else
            {
                return true;
            }
        }

        public bool HasItem(int index)
        {
            if (index < items.Count)
                return true;
            else
                return false;
        }

        public bool ContainsItem(CoreItem item)
        {
            for (int i = 0; i < items.Count; i++)
            {
                if (items[i] == item)
                    return true;
            }

            return false;
        }
    }
}