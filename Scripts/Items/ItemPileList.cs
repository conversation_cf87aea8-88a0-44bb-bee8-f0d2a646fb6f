// Copyright Isto Inc.
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Items
{
    /// <summary>
    /// Scriptable Object for containing lists of Item objects.  Used for setting starting items,
    /// already created items, etc at game start.
    /// </summary>
    [CreateAssetMenu(fileName = "New Item List", menuName = "Scriptables/Item/ItemPile List")]
    public class ItemPileList : ScriptableObject
    {
        // Public Variables
        public List<ItemPile> itemPiles;

        /// <summary>
        /// Checks if the item pile list contains an ItemPile, possibly empty, at the index passed in.
        /// </summary>
        /// <param name="index"></param>
        /// <returns>True if the list has an itemPile at the specified index.  ItemPile could be empty.</returns>
        public bool IsInRange(int index)
        {
            if (index >= itemPiles.Count)
            {
                return false;
            }
            else
            {
                return true;
            }
        }

        /// <summary>
        /// Checks if the list has a non null item at the specified index in the list.
        /// </summary>
        /// <param name="index"></param>
        /// <returns>True if there is a non null item at the index position, false otherwise.</returns>
        public bool HasItem(int index)
        {
            if (index < itemPiles.Count && itemPiles[index].item != null)
                return true;
            else
                return false;
        }

        public bool HasItem(Item item)
        {
            if (itemPiles.Count > 0)
            {
                for (int i = 0; i < itemPiles.Count; i++)
                {
                    if (itemPiles[i].item == item)
                        return true;
                }
            }
            return false;
        }

        public int FindIndexOfItem(Item item)
        {
            if (HasItem(item))
            {
                int index = 0;
                for (int i = 0; i < itemPiles.Count; i++)
                {
                    if (itemPiles[i].item == item)
                        index = i;
                }
                return index;
            }
            else
            {
                Debug.LogError("Couldn't Find item in ItemPile (Despite doing a check)");
                return -1;
            }
        }
    }
}