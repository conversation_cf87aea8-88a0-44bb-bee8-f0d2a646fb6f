// Copyright Isto Inc.
using System;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Items
{
    [CreateAssetMenu(fileName = "Tile Spawn Rules", menuName = "Scriptables/Tilemap/TileSpawnRules")]
    public class TileSpriteSpawnRules : ScriptableObject
    {
        public List<SpriteRule> spriteRules;

        private HashSet<Sprite> _validSprites = new HashSet<Sprite>();

        public bool IsValidSpriteToSpawnOn(Sprite sprite)
        {
            if (_validSprites.Count == 0)
            {
                InitializeSpriteHash();
            }

            return _validSprites.Contains(sprite);
        }

        /// <summary>
        /// Speeds up checking if sprite is valid by storing it into a HashSet for quicker lookup than using list
        /// </summary>
        private void InitializeSpriteHash()
        {
            for (int i = 0; i < spriteRules.Count; i++)
            {
                SpriteRule rule = spriteRules[i];

                for (int j = 0; j < rule.validSprites.Count; j++)
                {
                    _validSprites.Add(rule.validSprites[j]);
                }
            }
        }

        [Serializable]
        public class SpriteRule
        {
            public string name;
            public List<Sprite> validSprites;
        }
    }
}