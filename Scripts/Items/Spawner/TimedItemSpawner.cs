// Copyright Isto Inc.
using Isto.Core.Automation;
using System;
using UnityEngine;
using Zenject;

namespace Isto.Core.Items
{
    public class TimedItemSpawner : MonoBehaviour
    {
        // Public Variables

        public CoreItem itemToDrop;
        public float timePerDrop = 1f;
        public int maxNumDrops = int.MaxValue;
        public bool paused = false;
        public Color gizmoColor = new Color(1, 1, 1, 1);
        private int _numTimesDropped = 0;


        // Private Variables

        private AutomationSystem _system;
        private AutomationSystemController _controller;
        private float _dropTimer;

        // Lifecycle Events

        [Inject]
        public void Inject(AutomationSystem automationSystem, AutomationSystemController automationSystemController)
        {
            _system = automationSystem;
            _controller = automationSystemController;
        }

        private void Update()
        {
            if (paused || _controller.SystemPaused)
            {
                return;
            }

            _dropTimer -= Time.deltaTime;

            if (_dropTimer < 0f)
            {
                TryDropItem();
                _dropTimer = timePerDrop;
            }
        }

        private void TryDropItem()
        {
            AutomationGridSpace space = _system.GetOrCreateGridSpace(transform.position);

            if (space.ActiveItem == null)
            {
                if (_numTimesDropped < maxNumDrops)
                {
                    _system.TryAddCoreItem(transform.position, itemToDrop);
                    _numTimesDropped++;
                }
            }
        }

        // Gizmos

        public void OnDrawGizmos()
        {
            Gizmos.color = gizmoColor;
            Gizmos.DrawCube(transform.position + Constants.GRID_CENTERING_OFFSET, Vector3.one);
        }
    }

#if UNITY_EDITOR
    [Serializable]
    public class ItemSpawnerData
    {
        public Vector3 position;
        public string droppedItemID;
        public float timePerDrop;

        public ItemSpawnerData() { }

        public ItemSpawnerData(Vector3 position, string droppedItemID, float timePerDrop)
        {
            this.position = position;
            this.droppedItemID = droppedItemID;
            this.timePerDrop = timePerDrop;
        }

        public override bool Equals(object obj)
        {
            ItemSpawnerData other = obj as ItemSpawnerData;

            if (other == null)
            {
                return false;
            }

            if (this.position != other.position)
            {
                return false;
            }

            if (!this.droppedItemID.IsEqualWithNullsAsEmpty(other.droppedItemID))
            {
                return false;
            }

            return this.timePerDrop.Approx(other.timePerDrop);
        }

        public override int GetHashCode()
        {
            return (position, droppedItemID, timePerDrop).GetHashCode();
        }
    }
#endif
}