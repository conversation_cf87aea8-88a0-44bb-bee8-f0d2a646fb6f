// Copyright Isto Inc.
using Isto.Core.Automation;
using Isto.Core.Data;
using Isto.Core.Tiles;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Items
{
    /// <summary>
    /// The base class spawner. Has a bunch of UI elements for the editor to show you where stuff will spawn
    /// </summary>
	public abstract class Spawner : MonoBehaviour, IDataLoadCompleteHandler
    {
        // Public Variables

        public Color gizmoColor = Color.blue;
        public float width;
        public float length;
        public SpawningConditions spawningConditions;
        public TileSpriteSpawnRules spriteSpawnRules;

        // Private Variables

        protected DiContainer _container;
        protected AutomationSystem _autoSystem;
        protected Collider[] _hitBuffer = new Collider[3];
        protected Bounds spawnArea;
        protected bool _complete;

        // Lifecycle Events

        [Inject]
        public void Inject(DiContainer container, AutomationSystem automationSystem)
        {
            _container = container;
            _autoSystem = automationSystem;
        }

        protected virtual void Awake()
        {
            _complete = false;
            spawnArea = new Bounds(transform.position, new Vector3(width, 0f, length));

            Debug.Assert(spawningConditions != null, "Spawner has no spawning conditions attached to it, so it cannot work properly.", this);
        }

        public void OnDataLoadComplete()
        {
            if (gameObject.activeInHierarchy)
            {
                SpawnItems();
            }
        }

        // Abstract Methods		

        public abstract void SpawnItems();
        public abstract bool SpawnsComplete();

        // Methods

        /// <summary>
        /// Returns a random position that is wihtin the bounds and at height = 0
        /// </summary>
        /// <param name="bounds">The Bounds to find a random position within</param>
        /// <returns></returns>
        public static Vector3 GetRandomPosition(Bounds bounds)
        {
            Vector3 position = Vector3.zero;

            position.x = UnityEngine.Random.Range(-bounds.extents.x, bounds.extents.x);
            position.z = UnityEngine.Random.Range(-bounds.extents.z, bounds.extents.z);

            return position + bounds.center;
        }

        public static List<Vector3> GetAllPositionsInBounds(Bounds bounds)
        {
            List<Vector3> positionList = new List<Vector3>();

            int xDistance = Mathf.RoundToInt(bounds.extents.x);
            int zDistance = Mathf.RoundToInt(bounds.extents.z);

            for (int i = -xDistance; i < xDistance; i++)
            {
                for (int j = -zDistance; j < zDistance; j++)
                {
                    positionList.Add(new Vector3(i, 0, j) + bounds.center);
                }
            }

            return positionList;
        }

        public static List<Vector3> GetAllPositionsInBounds(Bounds bounds, ref List<Vector3> positionBuffer)
        {
            positionBuffer.Clear();

            int xDistance = Mathf.RoundToInt(bounds.extents.x);
            int zDistance = Mathf.RoundToInt(bounds.extents.z);

            for (int i = -xDistance; i < xDistance; i++)
            {
                for (int j = -zDistance; j < zDistance; j++)
                {
                    positionBuffer.Add(new Vector3(i, 0, j) + bounds.center);
                }
            }

            return positionBuffer;
        }

        public static int GetCountOfResourceInBounds(List<Vector3> positionsInBounds, string resourceID, AutomationSystem automationSystem)
        {
            int totalCount = 0;

            for (int i = 0; i < positionsInBounds.Count; i++)
            {
                Vector3 currentPosition = positionsInBounds[i];

                if (automationSystem.TryGetExistingGridSpace(currentPosition, out AutomationGridSpace space))
                {
                    if (space.Resource != null && space.Resource.ID.Equals(resourceID))
                    {
                        totalCount++;
                    }
                }
            }

            return totalCount;
        }

        public static bool IsPositionValid(Vector3 position, Collider[] hitbuffer, LayerMask invalidLayers, float searchRadius = 0.5f)
        {
            int hits = Physics.OverlapSphereNonAlloc(position, searchRadius, hitbuffer, invalidLayers, QueryTriggerInteraction.Collide);

            return hits == 0;
        }

        private static HashSet<Vector3> _recordedPositionsBuffer = new HashSet<Vector3>();

        /// <summary>
        /// Gets all positions that are valid with in the bounds based on the TilemapCullingSettings and sprite spawn rules
        /// </summary>
        /// <param name="tilemapSettings">TilemapSettings for the scene where this spawner exists</param>
        /// <param name="bounds">Bounds for the Area of the Spawner</param>
        /// <param name="spriteSpawnRules">TilemapSpriteSpawnRules for which sprites are valid to spawn onto</param>
        /// <returns></returns>
        public static Dictionary<Sprite, List<Vector3>> GetAllPostionsForSpawning(TilemapCullingSettings tilemapSettings, Bounds bounds, TileSpriteSpawnRules spriteSpawnRules, GroundRuleTile defaultGroundTile = null)
        {
            HashSet<Vector3> recordedPositions = _recordedPositionsBuffer; // new HashSet<Vector3>();    // Tracks which positions we've already checked when going through multiple tilemaps
            recordedPositions.Clear();

            Dictionary<Sprite, List<Vector3>> possibleTiles = new Dictionary<Sprite, List<Vector3>>();

            foreach (TileValues tile in tilemapSettings.tiles)
            {
                bool isInBounds = bounds.Contains(tile.tileWorldPosition);
                bool hasNotBeenChecked = !recordedPositions.Contains(tile.tileWorldPosition);

                if (hasNotBeenChecked)
                {
                    if (isInBounds && spriteSpawnRules.IsValidSpriteToSpawnOn(tile.tileSprite))
                    {
                        if (possibleTiles.ContainsKey(tile.tileSprite))
                        {
                            possibleTiles[tile.tileSprite].Add(tile.tileWorldPosition + Vector3.right);
                        }
                        else
                        {
                            possibleTiles.Add(tile.tileSprite, new List<Vector3>() { tile.tileWorldPosition + Vector3.right });
                        }
                    }

                    if (isInBounds)
                    {
                        recordedPositions.Add(tile.tileWorldPosition);
                    }
                }
            }

            if (defaultGroundTile != null)
            {
                List<Vector3> allPositionsInBounds = GetAllPositionsInBounds(bounds);

                foreach (Vector3 position in allPositionsInBounds)
                {
                    if (!recordedPositions.Contains(position))
                    {
                        recordedPositions.Add(position);

                        if (spriteSpawnRules.IsValidSpriteToSpawnOn(defaultGroundTile.m_DefaultSprite))
                        {
                            if (possibleTiles.ContainsKey(defaultGroundTile.m_DefaultSprite))
                            {
                                possibleTiles[defaultGroundTile.m_DefaultSprite].Add(position);
                            }
                            else
                            {
                                possibleTiles.Add(defaultGroundTile.m_DefaultSprite, new List<Vector3>() { position });
                            }
                        }
                    }
                }
            }

            return possibleTiles;
        }

        // Gizmos

        public void OnDrawGizmosSelected()
        {
            Color fadeColor = gizmoColor * new Color(1, 1, 1, 0.25f);

            Vector3 areaPosition = transform.position;
            areaPosition.y = 0.1f;
            spawnArea = new Bounds(areaPosition, new Vector3(width, 0f, length));
            UnityUtils.DrawBox(spawnArea, gizmoColor, fadeColor, gizmoColor, gameObject.name);
        }
    }
}