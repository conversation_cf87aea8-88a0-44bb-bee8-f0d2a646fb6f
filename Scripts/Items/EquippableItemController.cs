// Copyright Isto Inc.
using Isto.Core.Beings;
using Isto.Core.Data;
using UnityEngine;
using Zenject;

namespace Isto.Core.Items
{

    /// <summary>
    /// Any item that the player has equipped on his body
    /// 
    /// This will turn the sprites on and off depending on whether its active or not
    /// </summary>
    public abstract class EquippableItemController : MonoBehaviour
    {
        // Public Variables

        public string ItemID { get { return item?.itemID; } }

        public bool Consumable { get { return _consumable; } }

        // Private Variables

        [Space()]
        [SerializeField] protected Item item = null;
        [SerializeField] protected bool _consumable = false;

        protected AutomationPlayerController _player;
        protected bool _active;

        // Lifecycle Events

        [Inject]
        public void Inject(AutomationPlayerController playerController)
        {
            _player = playerController;
        }

        public abstract void OnItemEquipped();
        public abstract void OnItemUnequipped();
        public abstract void OnActiveUpdate();
        public abstract EquippableItemData GetSaveData();
        public abstract void LoadData(EquippableItemData data);

        public void ItemEquipped()
        {
            _active = true;

            OnItemEquipped();
        }

        public void Update()
        {
            if (_active)
                OnActiveUpdate();
        }

        public virtual void ItemUnequipped()
        {
            OnItemUnequipped();

            _active = false;
        }

        public virtual void DeactivateItem()
        {
            _active = false;
        }

        public virtual void ActivateItem()
        {
            _active = true;

        }

        public void Toggle()
        {
            if (_active)
                DeactivateItem();
            else
                ActivateItem();
        }
    }
}