// Copyright Isto Inc.
using Isto.Core.Automation;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Items
{
    /// <summary>
    /// This behavior handles hiding sub-parts of the item (e.g. side panels) according to the existence of compatible
    /// neighbours to "connect" to.
    /// It is used on regular grav-pipes in Atrio.
    /// Some work might be required to support items that have an offset drop position.
    /// Some edge cases still have bugs. For instance, placing an item compatible for connection doesn't hide the panel
    /// of a gravpipe like placing the gravpipe next to the item does.
    /// </summary>
    [RequireComponent(typeof(ItemPlacementController))]
    public class ConnectToNeighbours : MonoBehaviour
    {
        // UNITY HOOKUP

        [SerializeField] private PlacementConditions _connectableConditions;

        // The border panels we need to hide when handling connections with neighbouring items visually
        [SerializeField] private GameObject _upRightPanelRoot = null;
        [SerializeField] private GameObject _upLeftPanelRoot = null;
        [SerializeField] private GameObject _downLeftPanelRoot = null;
        [SerializeField] private GameObject _downRightPanelRoot = null;


        // OTHER FIELDS

        // Cache for our required component
        private ItemPlacementController _placementController;

        // Cached affectation of border panels in relative terms
        private GameObject _dropPosition = null; // will need this if we need neighbour connections on items with drop offsets
        private GameObject _leftPanelRoot = null;
        private GameObject _rightPanelRoot = null;
        private GameObject _backPanelRoot = null;

        // Cache for tracking change relative to our border panel connections
        private bool _directionDirty = false;
        private Vector3Int _prevPos = Vector3Int.zero;
        private ConnectToNeighbours _prevOutputTarget = null;
        private readonly Dictionary<ItemPlacementController, bool> _leftPanelInfluenceSources = new Dictionary<ItemPlacementController, bool>();
        private readonly Dictionary<ItemPlacementController, bool> _rightPanelInfluenceSources = new Dictionary<ItemPlacementController, bool>();
        private readonly Dictionary<ItemPlacementController, bool> _backPanelInfluenceSources = new Dictionary<ItemPlacementController, bool>();


        // INJECTION

        private AutomationSystem _automation;

        [Inject]
        public void Inject(AutomationSystem automation)
        {
            _automation = automation;
        }


        // LIFECYCLE EVENTS

        private void Awake()
        {
            Debug.Assert(_connectableConditions != null);
            Debug.Assert(_upRightPanelRoot != null);
            Debug.Assert(_upLeftPanelRoot != null);
            Debug.Assert(_downLeftPanelRoot != null);
            Debug.Assert(_downRightPanelRoot != null);

            _placementController = gameObject.GetComponent<ItemPlacementController>();
        }


        // OTHER METHODS

        // During placement mode this is our self-check for interactions with neighbouring tiles.
        // ItemPlacementController will only do this when it's enabled (so during placement),
        // but we'll receive an update from the enabled placing tile if pertinent.
        public void UpdateNeighbourItemOverlaps(bool placementMode)
        {
            // Some items don't have any connections to create.
            if (_connectableConditions == null)
                return;

            Vector3Int currentPos = Vector3Int.RoundToInt(gameObject.transform.position);

            //Note: this "if" is meant to save on useless updates and mostly does a good job of it
            //but I struggle to find the good timing to apply overlaps during placement so I instead have to run this update
            //constantly while the item is in placement mode... which also means we update our connections sometimes in a bad
            //configuration, in which case this "if" prevents the later updates from fixing the connections (it thinks nothing changed).
            //if (currentPos != _prevPos || _directionDirty)
            {
                _prevPos = currentPos;

                if (_directionDirty || _leftPanelRoot == null)
                {
                    AssignPanelToRelativePositions();
                    _directionDirty = false;
                }

                // look for items around us with which there would be overlaps/connections
                Vector3 forward = _placementController.GetDirection();

                // For now ConnectToNeighbours only runs on gravpipes, so _dropPosition is always null
                /*if (_dropPosition != null)
                {
                    Vector3 myPos = gameObject.transform.position;
                    Vector3 dropPos = _dropPosition.transform.position;
                    forward = dropPos - myPos;
                    forward = UnityUtils.GetNearestAxisDirection(forward);
                }*/

                Vector3 left = Quaternion.AngleAxis(-90f, Vector3.up) * forward;
                Vector3 right = Quaternion.AngleAxis(90f, Vector3.up) * forward;
                Vector3 back = -forward;

                // In theory we have to add this item's halfsize towards each direction so we don't check inside of ourself
                // With the gravpipes this doesn't matter and that's the only item for now what needs to check all sides
                // For the items that output stuff, they don't all have regular shapes vs their outputs. So we need to specify
                // the drop pos per item.
                Vector3Int forwardPos;
                if (_dropPosition != null)
                {
                    Vector3 pos = _dropPosition.transform.position;
                    forwardPos = new Vector3Int((int)pos.x, (int)pos.y, (int)pos.z);
                }
                else
                {
                    forwardPos = currentPos + Vector3Int.RoundToInt(forward);
                }

                Vector3Int leftPos = currentPos + Vector3Int.RoundToInt(left);
                Vector3Int rightPos = currentPos + Vector3Int.RoundToInt(right);
                Vector3Int backPos = currentPos + Vector3Int.RoundToInt(back);

                // Some items don't have any borders to hide. If no panel is assigned, skip the item receiving directions
                if (_upRightPanelRoot != null)
                {
                    // Check the advanced items we could be receiving core items from
                    bool leftSpaceExists = _automation.TryGetExistingGridSpace(leftPos, out AutomationGridSpace leftSpace);
                    bool rightSpaceExists = _automation.TryGetExistingGridSpace(rightPos, out AutomationGridSpace rightSpace);
                    bool backSpaceExists = _automation.TryGetExistingGridSpace(backPos, out AutomationGridSpace backSpace);

                    bool wantToHideLeftPanel = leftSpaceExists && IsConnectedToUs(left, leftSpace);
                    TryHideBorder(_placementController, _leftPanelInfluenceSources, _leftPanelRoot, wantToHideLeftPanel);

                    bool wantToHideRightPanel = rightSpaceExists && IsConnectedToUs(right, rightSpace);
                    TryHideBorder(_placementController, _rightPanelInfluenceSources, _rightPanelRoot, wantToHideRightPanel);

                    bool wantToHideBackPanel = backSpaceExists && IsConnectedToUs(back, backSpace);
                    TryHideBorder(_placementController, _backPanelInfluenceSources, _backPanelRoot, wantToHideBackPanel);
                }

                // Check the spot we output core items to in case they need to know about us
                CleanupPreviousOutputTile();

                bool forwardExists = _automation.TryGetExistingGridSpace(forwardPos, out AutomationGridSpace forwardSpace);
                if (forwardExists)
                {
                    CheckForNeighbourReceivingConnection(forward, forwardSpace, placementMode);
                }
            }
        }

        private bool IsConnectedToUs(Vector3 relativePosition, AutomationGridSpace space)
        {
            bool result = space != null
                        && space.itemProcessor != null
                        && space.itemProcessor is IDirectionalItem directional
                        && directional.GetDirection() == -relativePosition // outputing towards us
                        && CanConnect(space.itemProcessor.ProcessorID); // whitelisted
            return result;
        }

        /// <summary>
        /// Function that checks if an item processor's ID is in the white list for establishing visual connections with gravpipes
        /// </summary>
        /// <param name="id">The id of an item processor</param>
        /// <returns>True if it can connect with a gravpipe</returns>
        private bool CanConnect(string id)
        {
            if (_connectableConditions == null)
                return false;

            for (int i = 0; i < _connectableConditions.validItemsToReplace.Count; i++)
            {
                string compatibleItemForConnection = _connectableConditions.validItemsToReplace[i].itemID;
                if (compatibleItemForConnection == id)
                    return true;
            }

            return false;
        }

        /// <summary>
        /// If the piece in front of us is apt to receive our connection we will inform it so it can hide its
        /// correct border, forming a visual connection to us.
        /// </summary>
        /// <param name="forwardNeighbour">the space in our output position</param>
        /// <param name="placementMode">true if this is placement flow, false if this is culling flow</param>
        private void CheckForNeighbourReceivingConnection(Vector3 forwardDir, AutomationGridSpace forwardNeighbour, bool placementMode)
        {
            if (_connectableConditions == null)
                return;

            forwardDir = UnityUtils.GetNearestAxisDirection(forwardDir);

            if (placementMode)
            {
                // In placement flow it works better to query the AutomationGridSpace for the display item because despite the weird handling
                // we do to animate placement makes it hard to actually detect the neighbour (it's mostly an issue fast placing in reverse).

                if (forwardNeighbour.itemProcessor == null || forwardNeighbour.DisplayItem == null)
                    return;

                GameObject displayObject = forwardNeighbour.DisplayItem;
                ConnectToNeighbours neighbourConnector = displayObject.GetComponent<ConnectToNeighbours>();
                if (neighbourConnector != null)
                {
                    neighbourConnector.UpdateNeighbourConnection(_placementController, forwardDir, wantsToHide: true);
                    _prevOutputTarget = neighbourConnector;
                }
            }
            else
            {
                // In culling flow it works better to detect the display item using physics - I'm not sure if that's just because the
                // management of the DisplayItem reference of the AutomationSpace is not good enough or if that's a new unhandled edge
                // case but I can easily observe gravpipes with ghost connections after culling.

                Vector3 itemCenter = forwardNeighbour.position + Constants.AUTOMATION_TO_WORLD_OFFSET;
                LayerMask overlapMask = _connectableConditions.invalidLayers;

                // Small zone size because we're not trying to pick up random stuff, we're trying to pick up compatible automation occupying
                // the specified space, and automation parts always fill up their space about 100%.
                Collider[] hits = Physics.OverlapBox(itemCenter, Vector3.one * 0.2f, Quaternion.identity, overlapMask, QueryTriggerInteraction.Collide);
                for (int i = 0; i < hits.Length; i++)
                {
                    Collider c = hits[i];
                    ConnectToNeighbours neighbourConnector = c.GetComponentInParent<ConnectToNeighbours>();
                    if (neighbourConnector != null)
                    {
                        neighbourConnector.UpdateNeighbourConnection(_placementController, forwardDir, wantsToHide: true);
                        _prevOutputTarget = neighbourConnector;

                        // There are items with many colliders, which means many hits, but still just 1 item. There should not be more than 1 item physically in one space.
                        break;
                    }
                }
            }
        }

        // This gets called by the neighbouring ItemPlacementController that wants us to know about its presence (as it's pointing at us).
        public void UpdateNeighbourConnection(ItemPlacementController source, Vector3 direction, bool wantsToHide)
        {
            // For now ConnectToNeighbours only runs on gravpipes, so _upRightDropPosition is always unassigned
            //if (_leftPanelRoot == null && _upRightDropPosition != null)
            //    AssignPanelToRelativePositions();

            Vector3 forward = _placementController.GetDirection();

            // For now ConnectToNeighbours only runs on gravpipes, so _dropPosition is always null
            /*if (_dropPosition != null)
            {
                Vector3 myPos = gameObject.transform.position;
                Vector3 dropPos = _dropPosition.transform.position;
                forward = dropPos - myPos;
                forward = UnityUtils.GetNearestAxisDirection(forward);
            }*/

            Vector3 left = Quaternion.AngleAxis(-90f, Vector3.up) * forward;
            Vector3 right = Quaternion.AngleAxis(90f, Vector3.up) * forward;
            Vector3 back = -forward;

            left = UnityUtils.GetNearestAxisDirection(left);
            right = UnityUtils.GetNearestAxisDirection(right);
            back = UnityUtils.GetNearestAxisDirection(back);

            // the direction is where we are compared to the sender, but we need to know where the sender is compared to us
            direction = -direction;

            if (direction == left)
            {
                TryHideBorder(source, _leftPanelInfluenceSources, _leftPanelRoot, wantsToHide);
            }
            else if (direction == right)
            {
                TryHideBorder(source, _rightPanelInfluenceSources, _rightPanelRoot, wantsToHide);
            }
            else if (direction == back)
            {
                TryHideBorder(source, _backPanelInfluenceSources, _backPanelRoot, wantsToHide);
            }
        }

        // We used to have access to _upRightDropPosition and others from ItemPlacementController but we don't
        // need them since they are always null on the gravpipes and the gravpipes are the only existing items that
        // connect to neighbours
        private void AssignPanelToRelativePositions()
        {
            Vector3 forward = _placementController.GetDirection();

            if (forward.z == 1)
            {
                //_dropPosition = _upRightDropPosition;
                _leftPanelRoot = _upLeftPanelRoot;
                _backPanelRoot = _downLeftPanelRoot;
                _rightPanelRoot = _downRightPanelRoot;
            }
            else if (forward.x == -1)
            {
                //_dropPosition = _upLeftDropPosition;
                _leftPanelRoot = _downLeftPanelRoot;
                _backPanelRoot = _downRightPanelRoot;
                _rightPanelRoot = _upRightPanelRoot;
            }
            else if (forward.z == -1)
            {
                //_dropPosition = _downLeftDropPosition;
                _leftPanelRoot = _downRightPanelRoot;
                _backPanelRoot = _upRightPanelRoot;
                _rightPanelRoot = _upLeftPanelRoot;
            }
            else if (forward.x == 1)
            {
                //_dropPosition = _downRightDropPosition;
                _leftPanelRoot = _upRightPanelRoot;
                _backPanelRoot = _upLeftPanelRoot;
                _rightPanelRoot = _downLeftPanelRoot;
            }
            else
            {
                Debug.LogError("ConnectToNeighbours.AssignPanelToRelativePositions: Forward direction didn't match any axis.");
                CleanupRelativePanelReferences();
            }
        }

        private void CleanupRelativePanelReferences()
        {
            _leftPanelRoot = null;
            _backPanelRoot = null;
            _rightPanelRoot = null;
        }

        public void ResetDirection()
        {
            CleanupBorderPanelHidingInfluences();

            // Time of reset means our state is not clean - raise a flag to recompute before our next update logic
            _directionDirty = true;
        }

        private void CleanupBorderPanelHidingInfluences()
        {
            _prevPos = Vector3Int.zero;
            CleanupPreviousOutputTile();
            _leftPanelInfluenceSources.Clear();
            _rightPanelInfluenceSources.Clear();
            _backPanelInfluenceSources.Clear();
            HideBorder(_leftPanelRoot, haveToHide: false);
            HideBorder(_rightPanelRoot, haveToHide: false);
            HideBorder(_backPanelRoot, haveToHide: false);
            CleanupRelativePanelReferences();
        }

        private void CleanupPreviousOutputTile()
        {
            if (_prevOutputTarget != null)
            {
                _prevOutputTarget.UpdateNeighbourRemoved(_placementController);
                _prevOutputTarget = null;
            }
        }

        private void UpdateNeighbourRemoved(ItemPlacementController source)
        {
            if (_leftPanelInfluenceSources.ContainsKey(source))
            {
                TryHideBorder(source, _leftPanelInfluenceSources, _leftPanelRoot, false);
                _leftPanelInfluenceSources.Remove(source);
            }
            if (_rightPanelInfluenceSources.ContainsKey(source))
            {
                TryHideBorder(source, _rightPanelInfluenceSources, _rightPanelRoot, false);
                _rightPanelInfluenceSources.Remove(source);
            }
            if (_backPanelInfluenceSources.ContainsKey(source))
            {
                TryHideBorder(source, _backPanelInfluenceSources, _backPanelRoot, false);
                _backPanelInfluenceSources.Remove(source);
            }
        }

        private static void TryHideBorder(ItemPlacementController hider, Dictionary<ItemPlacementController, bool> hiderTracker, GameObject borderWallObject, bool wantsToHide)
        {
            if (!hiderTracker.ContainsKey(hider))
                hiderTracker.Add(hider, wantsToHide);
            else
                hiderTracker[hider] = wantsToHide;

            bool needsToHide = NeedsToHide(hiderTracker);
            HideBorder(borderWallObject, needsToHide);
        }

        private static bool NeedsToHide(Dictionary<ItemPlacementController, bool> hiderTracker)
        {
            bool result = false;
            Dictionary<ItemPlacementController, bool>.KeyCollection.Enumerator keyEnumerator = hiderTracker.Keys.GetEnumerator();
            while (keyEnumerator.MoveNext())
            {
                if (hiderTracker[keyEnumerator.Current] == true)
                {
                    result = true;
                    break;
                }
            }
            return result;
        }

        private static void HideBorder(GameObject borderRoot, bool haveToHide)
        {
            if (borderRoot == null)
                return;

            if (haveToHide && borderRoot.activeSelf)
            {
                borderRoot.SetActive(false);
            }
            else if (!haveToHide && !borderRoot.activeSelf)
            {
                borderRoot.SetActive(true);
            }
        }
    }
}