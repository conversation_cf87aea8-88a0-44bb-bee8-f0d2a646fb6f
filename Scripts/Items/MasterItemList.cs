// Copyright Isto Inc.
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using System.Text;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Isto.Core.Items
{
    [CreateAssetMenu(fileName = "Master Item List", menuName = "Scriptables/Item/Master Item List")]
    public class MasterItemList : ScriptableObject
    {
        // Static values

        public static readonly string ITEM_ENTRANCE_ID = "ItemEntrance";
        public static readonly string LOCKED_ITEM_ID = "LockedItem";

        // Public Variables

        public List<Item> items;
        public List<ResearchItem> researchItems;
        public List<BuffEffect> buffEffects;

        private Dictionary<string, Item> _allItemsByID;

        public void Init()
        {
            _allItemsByID = new Dictionary<string, Item>();

            for (int i = 0; i < items.Count; i++)
            {
                if (!items[i])
                {
                    Debug.LogError("Missing or null item in MasterItem list. If an item was deleted this may need to be cleaned up", this);
                    continue;
                }

                _allItemsByID.Add(items[i].itemID, items[i]);

                // Adding all lower case version as well incase it's called from dev console and case doesn't match
                if (!_allItemsByID.ContainsKey(items[i].itemID.ToLower()))
                    _allItemsByID.Add(items[i].itemID.ToLower(), items[i]);
            }
        }

        // Methods

        public T GetItemByName<T>(string name) where T : Item
        {
            for (int i = 0; i < items.Count; i++)
            {
                if (string.Equals(items[i].itemName, name, System.StringComparison.CurrentCultureIgnoreCase))
                {
                    if (items[i] is T)
                    {
                        return (T)items[i];
                    }
                    else
                    {
                        Debug.LogWarning($"Expecting type {typeof(T).ToString()} for item {name} but type is {items[i].GetType()}");
                        return default;
                    }
                }
            }

            Debug.LogError("couldn't find the item: " + name);

            return default;
        }

        /// <summary>
        /// Attemps to find the item passed in by GUID.  Item name parameter is for debugging only to help track down item if 
        /// it cannot be found in the Item list
        /// </summary>
        /// <typeparam name="T">Expected Type of returned Item.</typeparam>
        /// <param name="id">GUID to search for.</param>
        /// <param name="itemName">Used for debugging if item not found.</param>
        /// <returns>Item if found, otherwise returns default.</returns>
        public T GetItemByID<T>(string id, string itemName = "") where T : Item
        {
            if (string.IsNullOrEmpty(id))
            {
                return default;
            }

            if (_allItemsByID == null)
            {
                Init();
            }

            if (_allItemsByID.ContainsKey(id))
            {
                if (_allItemsByID[id] is T)
                {
                    return (T)_allItemsByID[id];
                }
                else
                {
                    return default;
                }
            }

            if (!id.Equals("ItemEntrance") && !id.Equals("BoosterEntrance"))
            {
                Debug.LogWarning($"couldn't find Item:{itemName} with guid: {id} in master item list.  Make sure it exists in the list");
            }

            return default;
        }

        public bool TryGetBuffEffectByID(string id, out BuffEffect buffEffect)
        {
            for (int i = 0; i < buffEffects.Count; i++)
            {
                if (buffEffects[i].ID == id)
                {
                    buffEffect = buffEffects[i];
                    return true;
                }
            }

            buffEffect = null;
            return false;
        }

        public bool TryGetItemByID<T>(string id, out T item, string itemName = "") where T : Item
        {
            item = GetItemByID<T>(id, itemName);

            if (item == null)
                return false;
            else
                return true;
        }

        public List<AdvancedItem> GetAllAdvancedItems()
        {
            List<Item> filteredItems = items.Where(x => x.GetType() == typeof(AdvancedItem)).ToList();

            List<AdvancedItem> advItems = new List<AdvancedItem>();

            for (int i = 0; i < filteredItems.Count; i++)
            {
                advItems.Add((AdvancedItem)filteredItems[i]);
            }

            return advItems;
        }

        public List<EquippableItem> GetAllEquippableItems()
        {
            List<Item> filteredItems = items.Where(x => x.GetType() == typeof(EquippableItem)).ToList();

            List<EquippableItem> advItems = new List<EquippableItem>();

            for (int i = 0; i < filteredItems.Count; i++)
            {
                advItems.Add((EquippableItem)filteredItems[i]);
            }

            return advItems;
        }

        public List<CoreItem> GetAllCoreItems()
        {
            List<Item> filteredItems = items.Where(x => x.GetType() == typeof(CoreItem)).ToList();

            List<CoreItem> coreItems = new List<CoreItem>();

            for (int i = 0; i < filteredItems.Count; i++)
            {
                coreItems.Add((CoreItem)filteredItems[i]);
            }

            return coreItems;
        }

        public List<CoreItem> GetAllCoreHarvestedItems()
        {
            List<Item> filteredItems = items.Where(x => x.GetType() == typeof(CoreItem)).ToList();

            List<CoreItem> coreItems = new List<CoreItem>();

            for (int i = 0; i < filteredItems.Count; i++)
            {
                CoreItem item = (CoreItem)filteredItems[i];
                if (item.fromHarvestableItem)
                    coreItems.Add((CoreItem)filteredItems[i]);
            }

            return coreItems;
        }

        public List<HarvestableItem> GetAllHarvestableItems()
        {
            List<Item> filteredItems = items.Where(x => x.GetType() == typeof(HarvestableItem)).ToList();

            List<HarvestableItem> harvestableItems = new List<HarvestableItem>();

            for (int i = 0; i < filteredItems.Count; i++)
            {
                harvestableItems.Add((HarvestableItem)filteredItems[i]);
            }

            return harvestableItems;
        }

        public ResearchItem GetResearchItemByID(string id, string itemName)
        {
            for (int i = 0; i < researchItems.Count; i++)
            {
                if (string.Equals(researchItems[i].ID, id, System.StringComparison.CurrentCultureIgnoreCase))
                {
                    return researchItems[i];
                }
            }

            Debug.LogError($"couldn't find ResearchItem:{itemName} with ID: {id} in master item list.  Make sure it exists in the list");

            return default;
        }

#if UNITY_EDITOR
        public static void ValidateResearchItemList()
        {
            MasterItemList masterItemList = Resources.Load<MasterItemList>("MasterItemList");

            string[] researchItemsGUIDs = AssetDatabase.FindAssets("t:researchItem");

            for (int i = 0; i < researchItemsGUIDs.Length; i++)
            {
                string assetPath = AssetDatabase.GUIDToAssetPath(researchItemsGUIDs[i]);
                ResearchItem item = AssetDatabase.LoadAssetAtPath(assetPath, typeof(ResearchItem)) as ResearchItem;

                if (!masterItemList.researchItems.Contains(item))
                {
                    string addMsg = $"Research Item {item.name} not in master item list, add it?";

                    if (EditorUtility.DisplayDialog("Item Not In Master Item List", addMsg, "Add It", "Skip It"))
                    {
                        masterItemList.researchItems.Add(item);
                        EditorUtility.SetDirty(masterItemList);
                    }
                }
            }

            Debug.Log($"MasterItemList research items:{masterItemList.researchItems.Count}. ResearchItems:{researchItemsGUIDs.Length}");
        }

        public static void ValidateCoreItemList()
        {
            MasterItemList masterItemList = Resources.Load<MasterItemList>("MasterItemList");

            string[] coreItemGUIDs = AssetDatabase.FindAssets("t:coreitem");

            for (int i = 0; i < coreItemGUIDs.Length; i++)
            {
                string assetPath = AssetDatabase.GUIDToAssetPath(coreItemGUIDs[i]);
                CoreItem item = AssetDatabase.LoadAssetAtPath(assetPath, typeof(CoreItem)) as CoreItem;

                if (!item.ignoreInBuild && !masterItemList.items.Contains(item))
                {
                    string addMsg = $"Core Item {item.name} not in master item list, add it?";

                    if (EditorUtility.DisplayDialog("Item Not In Master Item List", addMsg, "Add It", "Skip It"))
                    {
                        masterItemList.items.Add(item);
                        EditorUtility.SetDirty(masterItemList);
                    }
                }
            }

            Debug.Log($"MasterItemList core items:{masterItemList.items.Count}. ResearchItems:{coreItemGUIDs.Length}");
        }

        public static void ValidateHarvestableItemList()
        {
            MasterItemList masterItemList = Resources.Load<MasterItemList>("MasterItemList");

            string[] coreItemGUIDs = AssetDatabase.FindAssets("t:HarvestableItem");

            bool allItemsInList = true;

            StringBuilder output = new StringBuilder();
            output.Append("Harvestable Items not in MasterItemList:");

            for (int i = 0; i < coreItemGUIDs.Length; i++)
            {
                string assetPath = AssetDatabase.GUIDToAssetPath(coreItemGUIDs[i]);
                HarvestableItem item = AssetDatabase.LoadAssetAtPath(assetPath, typeof(HarvestableItem)) as HarvestableItem;

                if (!item.ignoreInBuild && !masterItemList.items.Contains(item))
                {
                    allItemsInList = false;
                    output.AppendLine($"{item.name}, Location:{assetPath}");

                    string addMsg = $"Harvestable Item {item.name} not in master item list, add it?";

                    if (EditorUtility.DisplayDialog("Item Not In Master Item List", addMsg, "Add It", "Skip It"))
                    {
                        masterItemList.items.Add(item);
                        EditorUtility.SetDirty(masterItemList);
                    }
                }
            }

            if (!allItemsInList)
                Debug.Log(output.ToString());
            else
                Debug.Log("All Harvestable Items In List");
        }
#endif
    }
}