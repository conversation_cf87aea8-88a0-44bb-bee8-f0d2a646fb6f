// Copyright Isto Inc.
using Isto.Core.Automation;
using Isto.Core.Beings;
using Isto.Core.Inputs;
using Isto.Core.Localization;
using Isto.Core.Pooling;
using Isto.Core.StateMachine;
using Isto.Core.UI;
using System;
using System.Collections;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.Serialization;
using UPool;
using Zenject;

namespace Isto.Core.Items
{
    /// <summary>
    /// State Machine for placing Advanced items by player.  Various states can be set on the item to allow rotating or
    /// multi-step placement.
    /// Handles activating and show the tool tip above the item.
    /// </summary>
    public class ItemPlacementController : MonoPushdownStateMachine, IDirectionalItem, IActionOnSpawn, IActionOnRecycle
    {
        public ItemPile CurrentItem { get; private set; }
        public Vector3 Forward { get { return _forward; } }
        public bool OverSwappableItem { get; private set; }
        public bool IsFirstOfFastPlacement { get; set; } = false;
        private bool IsEraser => gameObject.GetComponent<Eraser>() != null;

        [Tooltip("If true, only one item will be placed and then will exit from placement state, if false, multiple items can be placed without leaving state")]
        public bool singlePlacement = false;

        [SerializeField] private Vector3 _forward;

        [Header("Optional item overlap stuff")]

        [SerializeField] private ConnectToNeighbours _neighbourConnector = null;

        [Tooltip("Only need this if item can create connections to neighbouring items")]
        [SerializeField][FormerlySerializedAs("connectableConditions")] private PlacementConditions _connectableConditions;

        // Drop position exists for items that drop items at unexpected positions so optional
        [SerializeField] private GameObject _upRightDropPosition = null;
        [SerializeField] private GameObject _upLeftDropPosition = null;
        [SerializeField] private GameObject _downLeftDropPosition = null;
        [SerializeField] private GameObject _downRightDropPosition = null;

        protected UIToolTip _activeTooltip;
        private bool _injected = false;
        private bool _started = false;

        // Injected
        private AutomationPlayerController _player;
        private IControls _controls;
        protected UIToolTip.Pool _toolTipPool;
        // Offset that is applied to the tool tips position relative to the item being dropped. In World Space coordinates. Inject from essentials scene installer
        private Vector3 _toolTipOffset;
        private AutomationSystem _automation;

        [Inject]
        public void Inject(AutomationPlayerController player, IControls controls, UIToolTip.Pool toolTipPool, [Inject(Id = "PlacementToolTipOffset")] Vector3 toolTipOffset,
            AutomationSystem automation)

        {
            _player = player;
            _controls = controls;
            _toolTipPool = toolTipPool;

            _toolTipOffset = toolTipOffset;
            _injected = true;
            _automation = automation;
        }

        protected override void Awake()
        {
            InteractableItem interactableItem = GetComponent<InteractableItem>();
            CurrentItem = interactableItem.itemPile;
            base.Awake();
        }

        protected override void Start()
        {
#if PLACEMENT_LOGGING || DISMANTLE_LOGGING
            Debug.Log($"ItemPlacementController.Start on {gameObject.name}", gameObject);
#endif
            if (IsEraser)
            {
                _controls.SetControlMode(Controls.Mode.Dismantling);
            }
            else
            {
                _controls.SetControlMode(Controls.Mode.Building);
            }

            // Note: this delay does not currently contribute to avoiding the frame with the tooltip being in the wrong position
            // However this delay does prevent the tooltip from blinking as frequently when you move the mouse a lot or fast place/dismantle
            // Additional Notes:
            // ---If the delay is shorter than the specific item being placed, the tooltip will not show until your line is done
            // ---Having a delay longer than certain items (the gravpipe is quite fast) and shorter than others makes inconsistent behavior
            // ---Fast dismantling works just like placing items and has its own speed setting
            // ---Undoing a dismantling line is instantaneous but does not go through this delay so the tooltip just follows perfectly
            Invoke(nameof(ActivateToolTipInvoked), Constants.BUTTON_SPAM_DELAY * 2);

            // This will initialize the state machine to startState
            base.Start();
            _started = true;
        }

        // Note that when item is acquired/created, ItemPlacementController is disabled. We are enabled via IActionOnSpawn instead,
        // which is called right after item acquisition/creation, as we prepare placement. Then OnEnable, Inject and Start all happen.
        // Also note that items in the pool were born disabled, so they will actually run Start as you obtain them, but only the first
        // time around. This code could be sorted out to better embrace that fact (current logic is ported from before pooling and patched up)
        private void OnEnable()
        {
            if (!_started)
            {
                return;
            }

            // Our initialization logic has to happen on a different timing than normal for pooling to work well,
            // so we'll be calling base.Start in OnEnable (except on the first spawn which behaves like usual).
            // But since Enable happens immediately (as opposed to start), this needs to wait for next frame or
            // it breaks state machine logic for beginning the flow and synchronizing item state with player state
            if (_injected && GetComponent<PoolableObject>() != null)
            {
                StartCoroutine(StartWithDelay());
            }
        }

        private IEnumerator StartWithDelay()
        {
            yield return new WaitForEndOfFrame();

            if (!this.gameObject.IsNullOrDestroyed())
            {
                Start();
            }
        }

        // We get disabled as we return to the item pool but also as we get placed because this component is only for
        // the item placement state
        private void OnDisable()
        {
#if POOL_LOGGING
            Debug.Log($"ItemPlacementController.OnDisable on {gameObject.name}", gameObject);
#endif
        }

        protected override void Update()
        {
            MoveToolTip();
            if (_neighbourConnector)
            {
                _neighbourConnector.UpdateNeighbourItemOverlaps(placementMode: true);
            }
            base.Update();
        }

        public override void ExitSubState()
        {
#if PLACEMENT_LOGGING
            Debug.Log($"ItemPlacementController.ExitSubState on {gameObject.name}", gameObject);
#endif
            if (_stateStack.Count == 1)
            {
                CompletePlacement();
            }
            else if (_stateStack.Count > 1)
            {
                base.ExitSubState();
            }
            else
            {
                // This should not happen in normal flow I think, but in my case it did happen for recycled erasers in playmode tests only - Frank
                Debug.LogWarning($"Error - ExitSubState cannot work if placement state is empty. But if this is a playmode test we ignore it. GameObject:{gameObject.name}");
            }
        }

        public void CancelDrop(bool exitFromPlacement = true)
        {
            if (exitFromPlacement)
            {
                _player.SetDropCancelled(stopDropping: true);
            }

            RemoveToolTip();

            _player.SetMovementEnabled(true);

            if (exitFromPlacement)
            {
                _controls.SetControlMode(Controls.Mode.Gameplay);
            }

#if PLACEMENT_LOGGING
            Debug.Log($"during cancel drop _stateStack.Count is {_stateStack.Count}");
#endif

            while (_stateStack.Count > 0)
            {
#if PLACEMENT_LOGGING
                Debug.Log($"POPPING: {_stateStack.Peek().GetType()}");
#endif
                PopState();
            }

            RemoveItem();

            enabled = false;
        }

        private void RemoveItem()
        {
            PooledInteractableItem pooledItem = gameObject.GetComponent<PooledInteractableItem>();
            if (pooledItem != null)
            {
                pooledItem.DestroyGameObject(); // It was instantiated via the pool
            }
            else if (!Addressables.ReleaseInstance(gameObject))
            {
                Debug.LogError("ItemPlacementController.RemoveItem on unpooled item that is not an addressable instance, this is unexpected");
                Destroy(gameObject); // it is most likely from the scene as it was not loaded through addressables
            }
        }

        public void SetStartRotation(float angle)
        {
            if (angle % 90 != 0)
            {
                Debug.LogError("Angle passed to set start rotation should be a multiple of 90. Angle: " + angle, this.gameObject);
                return;
            }

            int rotations = Math.Abs((int)(angle / 90));

            ItemRotateState rotateState = GetComponent<ItemRotateState>();

            if (rotateState != null)
            {
                for (int i = 0; i < rotations; i++)
                {
                    rotateState.HandleRotation(-1 * Math.Sign(angle));
                }
            }
        }

        public void SetStartRotation(Vector3 startDirection)
        {
            float angle = GetCurrentSnappedAngle(startDirection, Forward);

            SetStartRotation(angle);
        }

        /// <summary>
        /// Gets the snapped to 90 degree angle based on the rotation direction compared to forward direction
        /// </summary>
        /// <param name="rotationDirection">Direction to get snapped angle off forward from.</param>
        /// <param name="forward">The forwad direction to compare against.</param>
        /// <returns>A 90 snapped angle from the forward direction.</returns>
        private static float GetCurrentSnappedAngle(Vector3 rotationDirection, Vector3 forward)
        {
            float angle = Vector3.SignedAngle(rotationDirection, forward, Vector3.up);

            // Snap the angle
            float n = 90;
            float x = Mathf.Round(angle / n) * n;

            return x;
        }

        private void CompletePlacement()
        {
#if PLACEMENT_LOGGING
            Debug.Log($"ItemPlacementController.CompletePlacement on {gameObject.name}", gameObject);
#endif
            // Exit out of the final state
            _currentState.Exit(this);

            RemoveToolTip();

            // Disable the state machine
            enabled = false;
        }

        // Note: In the base method this is called after the actual asset is turned.
        // However the overrides call their base first and then apply the changes to the other parts of the asset, which
        // means we're actually being called in the middle of the transition and this is not a good place to access our children.
        public void SetForwardDirection(Vector3 forward)
        {
#if PLACEMENT_LOGGING
            Debug.Log($"ItemPlacementController.SetForwardDirection to {forward} on {gameObject.name}", gameObject);
#endif
            if (_neighbourConnector != null)
            {
                _neighbourConnector.ResetDirection();
            }

            _forward = forward;
        }

        public void DoSpawnAction()
        {
            enabled = true;
        }

        public void OnRecycle()
        {
#if POOL_LOGGING
            Debug.Log($"ItemPlacementController.OnRecycle on {gameObject.name}", gameObject);
            if(_stateStack.Count > 0)
                Debug.Log($"during OnRecycle, _stateStack.Count is {_stateStack.Count}"); // Some flows leave some states there, others don't
#endif
            while (_stateStack.Count > 0)
            {
#if POOL_LOGGING
                Debug.Log($"POPPING: {_stateStack.Peek().GetType()}");
#endif
                _stateStack.Pop();
            }

            if (_neighbourConnector != null)
            {
                _neighbourConnector.ResetDirection();
            }

            SetIsNotOverSwappableItem();
            IsFirstOfFastPlacement = false;

            // Material changing state might not be on the stack but eraser flow will get it and use it to change visuals
            // without restoring them before the item is removed
            if (TryGetComponent(out ItemSetMaterialState materialChanger))
            {
                materialChanger.RestoreMaterials();
            }
        }

        public Vector3 GetDirection()
        {
            return _forward;
        }

        private void MoveToolTip()
        {
            if (_activeTooltip != null)
            {
                _activeTooltip.transform.position = UnityUtils.TransformWorldToCanvasPoint(transform.position + _toolTipOffset, _activeTooltip.Canvas);
            }
        }

        /// <summary>
        /// Gets a tool tip from the pool and sets it's rows based on what actions are possible for this item
        /// </summary>
        private void ActivateToolTipInvoked()
        {
            if (_activeTooltip != null)
            {
                return;
            }

            if (IsEraser)
            {
                _activeTooltip = _toolTipPool.Spawn(Loc.GetString(Constants.TOOLTIP_DISMANTLING));

                _activeTooltip.AddRow(_controls.GetIconForAction(UserActions.DISMANTLE), Loc.GetString(Constants.USER_ACTION_DISMANTLE));
                _activeTooltip.AddRow(_controls.GetIconForAction(UserActions.CANCELDISMANTLING), Loc.GetString(Constants.USER_ACTION_CANCEL));
            }
            else
            {
                _activeTooltip = _toolTipPool.Spawn(CurrentItem.item.itemName);

                _activeTooltip.AddRow(_controls.GetIconForAction(UserActions.INTERACT), Loc.GetString(Constants.USER_ACTION_PLACE));
                _activeTooltip.AddRow(_controls.GetIconForAction(UserActions.CANCELBUILDING), Loc.GetString(Constants.USER_ACTION_CANCEL));

                // Only show rotate controls if the object has a rotate component on it.
                if (GetComponent<ItemRotateState>() != null)
                {
                    _activeTooltip.AddRow(_controls.GetIconForAction(UserActions.ROTATELEFT), Loc.GetString(Constants.USER_ACTION_ROTATE), _controls.GetIconForAction(UserActions.ROTATERIGHT));
                }
            }
        }

        /// <summary>
        /// Returns the active tool tip back to the pool or prevents current delayed invocation
        /// </summary>
        public void RemoveToolTip()
        {
            if (_activeTooltip != null)
            {
                _toolTipPool.Despawn(_activeTooltip);
                _activeTooltip = null;
            }
            else
            {
                if (gameObject == null)
                {
                    Debug.LogWarning("GameObject Null, this must be in cleanup?");
                }

                // Sometimes the tooltip can be cancelled before it's had time to show
                CancelInvoke(nameof(ActivateToolTipInvoked));
            }
        }

        public void SetIsOverSwappableItem()
        {
            OverSwappableItem = true;
        }

        public void SetIsNotOverSwappableItem()
        {
            OverSwappableItem = false;
        }
    }
}