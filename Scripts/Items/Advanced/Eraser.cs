// Copyright Isto Inc.
using Isto.Core.Beings;
using Isto.Core.Inputs;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Items
{
    /// <summary>
    /// The Eraser is a fake item that is used to allow use of the normal free placement flow,
    /// but for dismantling instead of placing. Thus it should never actually be placed.
    /// Notably it also isn't a IDirectionalItem but we'll lie about that too so we can update what's underneath us.
    /// </summary>
    public class Eraser : PooledInteractableItem
    {
        public InteractableItem EraseTarget { get; set; }

        // Overrides to avoid logic from base class
        protected override void Update() { }
        protected override void OnDestroy() { }

        public override void ShowTooltip(float offset, bool showSelectionhighlight = true)
        {
        }

        public override void ShowTooltip()
        {
        }

        public override void SetInactive()
        {
        }

        public override void PlayerInteraction(AutomationPlayerController player, UserActions action)
        {
            Debug.LogError("Should not be possible to interact on Eraser", this);
        }

        // Forced override of pure abstract methods

        public override string GetItemName()
        {
            Debug.LogError("Eraser item name should not be displayed in the game");
            return itemPile.item.itemName;
        }

        public override string GetToolTip(UserActions action)
        {
            Debug.LogError("Should not be invoking GetToolTip on Eraser", this);
            return "";
        }

        public override List<UserActions> GetValidActions()
        {
            _actionList.Clear();
            return _actionList;
        }

        public static void ReadyObjectForDismantling(GameObject target)
        {
#if DISMANTLE_LOGGING
            Debug.Log($"ReadyObjectForDismantling for item {target.name}");
#endif

            target.SetColliderState(false);

            if (target.TryGetComponent(out IActionOnReadToDismantle ready))
            {
                ready.OnReadyToDismantle();
            }

            if (target.TryGetComponent(out ItemSetMaterialState materialChanger))
            {
                materialChanger.LoadRenderers();
                materialChanger.SetMaterials(materialChanger.placementMaterial, materialChanger.DropMaterial);
            }
        }

        public static void UnreadyObjectForDismantling(GameObject target)
        {
#if DISMANTLE_LOGGING
            Debug.Log($"UnreadyObjectForDismantling for item {target.name}");
#endif

            target.SetColliderState(true);

            if (target.TryGetComponent(out IActionOnDismantleAborted aborted))
            {
                aborted.OnDismantleAborted();
            }

            if (target.TryGetComponent(out ItemSetMaterialState materialChanger))
            {
                materialChanger.RestoreMaterials();
            }
        }
    }
}