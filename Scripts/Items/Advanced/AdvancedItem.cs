// Copyright Isto Inc.
using UnityEngine;

namespace Isto.Core.Items
{
    /// <summary>
    /// Most advanced items are placeable automation items.
    /// Others are equipment like mines, stun traps, things the player can use.
    /// Item -> CoreItem -> AdvancedItem
    /// Item -> HarvestableItem
    /// </summary>
    [CreateAssetMenu(fileName = "New Advanced Item", menuName = "Scriptables/Item/AdvancedItem")]
    public class AdvancedItem : CoreItem
    {
        // Public Variables

        [Header("Advanced Properties")]
        // When placing the item, use this to determine valid placement spots
        public float width = 1;
        public float depth = 1;

        //Used to place and rotate items during automated testing.
        [Tooltip("The distance from the items origin to the bottom-right hand corner of the Items bounds." +
            "\nIf origin is bottom right, Offset is 0,0" +
            "\nIf origin is left 3 and up 2, offset would be x=3, z=-2 ")]
        public float offsetToCornerX = 0;
        public float offsetToCornerZ = 0;

        [Tooltip("Used when dropped from automation system, prefab should be treated like a core item, not a placed advanced item when dropped.")]
        public GameObject coreItemPrefab;

        public float gridSnapSize = 1f;

        [Tooltip("If set, item will just be instantly dropped (like a core item) when dropping instead of using advanced item placement state.")]
        public bool simpleDrop = false;
        public bool equipToPlayer = false;

        public override GameObject DropItem(Vector3 position, Transform parent, int count, bool droppedByPlayer)
        {
            if (width == 0f || depth == 0f)
            {
                Debug.LogError("No item width or depth set for item: " + itemName);
                return null;
            }

            Vector3 dropPosition = position;

            GameObject newItem;
            bool needsActivation = true;

            // If dropped by player, used advanced item prefab, otherwise use basic core item one
            if (droppedByPlayer)
            {
                newItem = CreateItemFromPrefab(prefab, dropPosition, count);
                needsActivation = false;
            }
            else
            {
                if (coreItemPrefab == null)
                {
                    Debug.LogError("Core Item Prefab is null for item: " + itemName);
                    return null;
                }

                newItem = CreateItemFromPrefab(coreItemPrefab, dropPosition, count);
            }

            newItem.transform.SetParent(parent);

            InitializeNewItem(newItem, simpleDrop || needsActivation);

            return newItem;
        }

        // Static Methods

        public static void InitializeNewItem(GameObject newItem, bool activateNow)
        {
            // Disable all the colliders on the object until it is dropped.
            newItem.SetColliderState(false);

            IActionOnSpawn[] actionOnSpawn = newItem.GetComponentsInChildren<IActionOnSpawn>();

            for (int i = 0; i < actionOnSpawn.Length; i++)
            {
                actionOnSpawn[i].DoSpawnAction();
            }

            // If it's a simple drop item, activate all its scripts immediately, otherwise this is done after placement
            if (activateNow)
            {
                IActivatable[] activateScripts = newItem.GetComponentsInChildren<IActivatable>();

                for (int i = 0; i < activateScripts.Length; i++)
                {
                    activateScripts[i].Activate();
                }
            }
        }
    }
}