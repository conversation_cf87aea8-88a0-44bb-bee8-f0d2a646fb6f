// Copyright Isto Inc.
using Isto.Core.Inputs;
using Isto.Core.Localization;
using UnityEngine;

namespace Isto.Core.Items
{
    [SelectionBase]
    public class AdvancedItemController : ItemController
    {
        [Tooltip("If set to true, when created, will tell all ItemOverlap components to check for overlap")]
        public bool checkOverlap = false;

        public GameObject floatingDirectionArrow;

        protected override void Awake()
        {
            base.Awake();

            _actionList.Clear();
            _actionList.Add(UserActions.DISMANTLE);
        }

        public override string GetToolTip(UserActions action)
        {
            switch (action.Name)
            {
                case nameof(UserActions.DISMANTLE):
                    return Loc.GetString(Constants.USER_ACTION_DISMANTLE);
                default:
                    return "";
            }
        }

        public override void ShowTooltip()
        {
            base.ShowTooltip();

            if (floatingDirectionArrow != null)
                floatingDirectionArrow.SetActive(true);
        }

        public override void SetInactive()
        {
            base.SetInactive();

            if (floatingDirectionArrow != null)
                floatingDirectionArrow.SetActive(false);
        }

        public override void OnAllocate()
        {
            base.OnAllocate();

            this.gameObject.SetColliderState(false);

            // New advanced items are always assumed to be one-ofs so they don't get assigned a count (it is set on the prefab)
            // When we are removing the item, the count gets decremented as the item gets added to the inventory of the player
            // When the pooled prefab is ready to be reused, I suppose we should restore the assumed count of a placeable item here
            itemPile.count = 1;

            if (floatingDirectionArrow != null)
                floatingDirectionArrow.SetActive(false);
        }
    }
}