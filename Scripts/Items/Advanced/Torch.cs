// Copyright Isto Inc.
using Isto.Core.Audio;
using Isto.Core.Beings;
using Isto.Core.Data;
using Isto.Core.UI;
using System.Collections;
using UnityEngine;
using Zenject;

namespace Isto.Core.Items
{
    public class Torch : EquippableItemController, ISliderDisplay
    {
        // Public Variables

        public AnimationCurve lightFadeCurve;
        public AnimationCurve lightRangeCurve;
        public float lifetime = 30f;
        public Light torchLight;
        public GameObject torchSprite;

        // Private Variables

        private float _lifeTimer = 0f;
        private float _startIntensity;  // Starting intensity for the torch light
        private float _startRange;      // Starting range for the torch light

        [SerializeField] [FMODUnity.EventRef] private string _lightOnSoundRef = "";
        [SerializeField] [FMODUnity.EventRef] private string _lightFlickerSoundRef = "";

        private IGameSounds _gameSounds;
        private PlayerUpgrades _playerUpgrades;
        private bool _lightFalling;

        // Lifecycle Events

        [Inject]
        public void Inject(IGameSounds gameSounds, PlayerUpgrades playerUpgrades)
        {
            _gameSounds = gameSounds;
            _playerUpgrades = playerUpgrades;
        }

        // Overridding base ItemUnequipped to not set item inactive, so torch keeps updating if it's dropped.
        public override void ItemUnequipped()
        {
            OnItemUnequipped();
        }

        public override void OnItemEquipped()
        {
            // Adjust torch duration based on upgrades
            lifetime += _playerUpgrades.TorchDurationIncrease * lifetime;

            transform.parent = _player.Hand;
            transform.localPosition = Vector3.zero;
            
            _startIntensity = torchLight.intensity;
            _startRange = torchLight.range;

            // Activate any components that need it attached to this object
            IActivatable[] activateScripts = GetComponentsInChildren<IActivatable>(true);

            for (int i = 0; i < activateScripts.Length; i++)
            {
                activateScripts[i].Activate();
            }

            _gameSounds.PlayOneShot(_lightOnSoundRef, transform.position);

            _player.itemInHand = this;
        }

        public override void OnItemUnequipped()
        {
            if (_player.itemInHand == this)
                _player.itemInHand = null;

            // If less than one second of life left, destroy, otherwise drop
            if (lifetime - _lifeTimer < 1f)
            {
                Destroy(gameObject);
            }
            else
            {
                transform.parent = null;

                StartCoroutine(DropTorchCoroutine());
            }
        }

        public override void OnActiveUpdate()
        {
            _lifeTimer += Time.deltaTime;

            if (_lifeTimer > lifetime)
            {
                ItemUnequipped();
            }
            else
            {
                float nextIntensity = lightFadeCurve.Evaluate(_lifeTimer / lifetime) * _startIntensity;

                // Check if intensity dropping, play flicker audio
                if (nextIntensity < torchLight.intensity - 0.05f && !_lightFalling)
                {
                    _gameSounds.PlayOneShot(_lightFlickerSoundRef, transform.position);
                    _lightFalling = true;
                }
                else if (nextIntensity >= torchLight.intensity)
                {
                    _lightFalling = false;
                }

                torchLight.intensity = nextIntensity;
                torchLight.range = lightRangeCurve.Evaluate(_lifeTimer / lifetime) * _startRange;
            }
        }

        public override void DeactivateItem()
        {
            base.DeactivateItem();

            torchLight.intensity = 0f;
            torchLight.range = 0f;
        }

        public float GetCurrentPercent()
        {
            if (_lifeTimer >= lifetime)
                return 0;
            else
                return (1 - (_lifeTimer / lifetime));
        }

        private IEnumerator DropTorchCoroutine()
        {
            torchSprite.SetActive(true);

            Rigidbody rigidBody = GetComponent<Rigidbody>();
            SphereCollider collider = GetComponent<SphereCollider>();

            // If no collider, add one and set a default size
            if (collider == null)
            {
                collider = gameObject.AddComponent<SphereCollider>();
                collider.radius = 0.4f;
                collider.center = new Vector3(-0.5f, 0.4f, 0.5f);
            }

            rigidBody.isKinematic = false;
            rigidBody.useGravity = true;
            rigidBody.AddForce(Vector3.up * 4.5f, ForceMode.Impulse);

            while (!rigidBody.IsSleeping())
                yield return null;

            Destroy(collider);

            rigidBody.isKinematic = true;
            rigidBody.useGravity = false;
        }

        public override EquippableItemData GetSaveData()
        {
            return new TorchItemData(item.itemID, lifetime - _lifeTimer);
        }

        public override void LoadData(EquippableItemData data)
        {
            TorchItemData torchData = data as TorchItemData;

            _lifeTimer = lifetime - torchData.lifeRemaining;
        }
    }
}