// Copyright Isto Inc.
using UnityEngine;

namespace Isto.Core.Items
{
    public class ItemRotateByEnableWithSharedState : ItemRotateState
    {
        [<PERSON><PERSON>("Child Objects for each direction")]
        [Tooltip("0,0,1 OR NE")]
        public Transform[] directionOne;
        [Toolt<PERSON>("-1,0,0 OR NW")]
        public Transform[] directionTwo;
        [Toolt<PERSON>("0,0,-1")]
        public Transform[] directionThree;
        [<PERSON>lt<PERSON>("1,0,0")]
        public Transform[] directionFour;

        public override void HandleRotation(int direction)
        {
            base.HandleRotation(direction);

            if (_controller.Forward.z == 1)
            {
                EnableDirection(1);
            }
            else if (_controller.Forward.x == -1)
            {
                EnableDirection(2);
            }
            else if (_controller.Forward.z == -1)
            {
                EnableDirection(3);
            }
            else if (_controller.Forward.x == 1)
            {
                EnableDirection(4);
            }
            else
            {
                Debug.LogError("Unable to map forward vector to a direction, please check.  Forward: " + _controller.Forward);
            }
        }

        /// <summary>
        /// Sets the appropriate GameObject active based on the direction passed in.
        /// </summary>
        /// <param name="direction"></param>
        private void EnableDirection(int direction)
        {
            for (int i = 0; i < directionOne.Length; i++)
            {
                directionOne[i].gameObject.SetActive(false);
            }

            for (int i = 0; i < directionTwo.Length; i++)
            {
                directionTwo[i].gameObject.SetActive(false);
            }

            for (int i = 0; i < directionThree.Length; i++)
            {
                directionThree[i].gameObject.SetActive(false);
            }

            for (int i = 0; i < directionFour.Length; i++)
            {
                directionFour[i].gameObject.SetActive(false);
            }

            Transform[] toEnable;

            switch (direction)
            {
                case 1:
                    toEnable = directionOne;
                    break;
                case 2:
                    toEnable = directionTwo;
                    break;
                case 3:
                    toEnable = directionThree;
                    break;
                case 4:
                    toEnable = directionFour;
                    break;
                default:
                    toEnable = directionOne;
                    break;
            }

            for (int i = 0; i < toEnable.Length; i++)
            {
                toEnable[i].gameObject.SetActive(true);
            }
        }
    }
}