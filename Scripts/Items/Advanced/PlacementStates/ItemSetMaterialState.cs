// Copyright Isto Inc.
using Isto.Core.StateMachine;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Serialization;
using Zenject;

namespace Isto.Core.Items
{
    /// <summary>
    /// MonoState that is used to change the material of the item to the placement material when the item is ready to be placed.
    /// </summary>
    public class ItemSetMaterialState : MonoState
    {
        public Material DropMaterial => _dropMaterial;

        public MonoState nextState;
        public Material placementMaterial;
        [Tooltip("If set, will use the Colors below as the invalid placement tint, otherwise uses the on set via the AdvItemDropParameters")]
        public bool overrideDefaultInvalidColor = false;
        public Color invalidPlaceTint = new Color(1f, 0f, 0f, 0.35f);
        [ColorUsage(true, true)]
        public Color invalidPlaceEmission = new Color(1f, 0f, 0f, 0.35f);

        [SerializeField] private List<SpriteRenderer> _dropTargetRenderes = null;
        [Tooltip("Tells the item that its drop targets will be placed in a separate step, requiring two levels of color change")]
        public bool separateDropTargetColoring = false;
        [SerializeField] [FormerlySerializedAs("dropMaterial")] private Material _dropMaterial = null;

        public Color standbyTint = new Color(0f, 0f, 1f, 0.35f);
        [ColorUsage(true, true)]
        public Color standbyEmission = new Color(0f, 0f, 1f, 0.35f);

        [Header("Floating Direction Arrow")]
        public GameObject floatingDirectionArrow;
        [Tooltip("Typically used for landing zones on SpringBoards or other distance movers")]
        public GameObject secondaryFloatingArrow;

        private ItemPlacementController _controller;
        private SpriteRenderer[] _renderers;
        private Material[] _previousMaterials;
        private Material[] _previousDropMaterials;
        private Color _previousTint;
        private Color _previousEmission;                    // Used by the drop target renderers
        private int _tintID;
        private int _emissionID;

        [Inject]
        public void Inject(AdvItemDropParameters dropParameters)
        {
            if (!overrideDefaultInvalidColor)
            {
                invalidPlaceTint = dropParameters.invalidPositionColorTint;
                invalidPlaceEmission = dropParameters.invalidPositionColorEmission;
            }
        }

        protected void Awake()
        {
#if PLACEMENT_LOGGING || DISMANTLE_LOGGING
            Debug.Log($"ItemSetMaterialState.Awake on {gameObject.name}", gameObject);
#endif
            _tintID = Shader.PropertyToID("_Tint");
            _emissionID = Shader.PropertyToID("_EmissionColor");
        }

        protected void OnDisable()
        {
#if PLACEMENT_LOGGING || DISMANTLE_LOGGING
            Debug.Log($"ItemSetMaterialState.OnDisable on {gameObject.name}", gameObject);
#endif
            //Make sure we reset the material on exit
            RestoreMaterialTint();
        }

        public override void Enter(MonoStateMachine controller)
        {
#if PLACEMENT_LOGGING || DISMANTLE_LOGGING
            Debug.Log($"ItemSetMaterialState.Enter on {gameObject.name}", gameObject);
#endif
            _controller = controller as ItemPlacementController;

            if (placementMaterial != null)
            {
                if (placementMaterial.HasProperty(_tintID))
                    _previousTint = placementMaterial.GetColor(_tintID);

                if (_dropMaterial != null && _dropMaterial.HasProperty(_emissionID)) // UnassignedReferenceException: The variable dropMaterial of ItemSetMaterialState has not been assigned. You probably need to assign the dropMaterial variable of the ItemSetMaterialState script in the inspector.
                    _previousEmission = _dropMaterial.GetColor(_emissionID);

                LoadRenderers();
                SetMaterials(placementMaterial, _dropMaterial);
            }

            if (floatingDirectionArrow != null)
            {
                floatingDirectionArrow.SetActive(true);
            }

            if (secondaryFloatingArrow != null)
            {
                secondaryFloatingArrow.SetActive(true);
            }
        }

        public override void Exit(MonoStateMachine controller)
        {
#if PLACEMENT_LOGGING || DISMANTLE_LOGGING
            Debug.Log($"ItemSetMaterialState.Exit on {gameObject.name}", gameObject);
#endif
            if (placementMaterial != null)
            {
                RestoreMaterials();
            }
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
#if PLACEMENT_LOGGING || DISMANTLE_LOGGING
            Debug.Log($"ItemSetMaterialState.ReturnFromSubState on {gameObject.name}", gameObject);
#endif
            // When coming back to this state, it means the placement has been completed, so just exit out of this state to replace the materials in the Exit() method
            _controller.ExitSubState();
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            _controller.EnterSubState(nextState);

            return this;
        }

        public void SetMaterialTint(Color tint, Color emission)
        {
#if PLACEMENT_LOGGING || DISMANTLE_LOGGING
            // Warning, this one happens every frame
            //Debug.Log($"setting tint to {tint} on {gameObject.name}", gameObject);
#endif
            if (placementMaterial != null && _renderers != null)
            {
                MaterialPropertyBlock propBlock = new MaterialPropertyBlock();

                for (int i = 0; i < _renderers.Length; i++)
                {
                    _renderers[i].GetPropertyBlock(propBlock);
                    propBlock.SetColor(_tintID, tint);
                    _renderers[i].SetPropertyBlock(propBlock);
                }

                for (int i = 0; i < _dropTargetRenderes.Count; i++)
                {
                    _dropTargetRenderes[i].GetPropertyBlock(propBlock);
                    propBlock.SetColor(_tintID, tint);
                    propBlock.SetColor(_emissionID, emission);
                    _dropTargetRenderes[i].SetPropertyBlock(propBlock);
                }
            }
        }

        private void SetMainMaterialTint(Color tint)
        {
            if (placementMaterial == null || _renderers == null)
                return;

            MaterialPropertyBlock propBlock = new MaterialPropertyBlock();

            for (int i = 0; i < _renderers.Length; i++)
            {
                _renderers[i].GetPropertyBlock(propBlock);
                propBlock.SetColor(_tintID, tint);
                _renderers[i].SetPropertyBlock(propBlock);
            }
        }

        private void SetDropMaterialTint(Color tint, Color emission)
        {
            if (_dropTargetRenderes == null)
                return;

            MaterialPropertyBlock propBlock = new MaterialPropertyBlock();

            for (int i = 0; i < _dropTargetRenderes.Count; i++)
            {
                _dropTargetRenderes[i].GetPropertyBlock(propBlock);
                propBlock.SetColor(_tintID, tint);
                propBlock.SetColor(_emissionID, emission);
                _dropTargetRenderes[i].SetPropertyBlock(propBlock);
            }
        }

        public void RestoreMaterialTint()
        {
#if PLACEMENT_LOGGING || DISMANTLE_LOGGING
            // Warning, this one happens every frame
            //Debug.Log($"Restoring tint {_previousTint} to object {gameObject.name}", gameObject);
#endif
            SetMaterialTint(_previousTint, _previousEmission);
        }

        /// <summary>
        /// Go through the internal list of renderers and changes the material to the one passed in
        /// </summary>
        /// <param name="placementMaterial"></param>
        public void SetMaterials(Material placementMaterial, Material dropMaterial)
        {
#if PLACEMENT_LOGGING || DISMANTLE_LOGGING
            Debug.Log($"Setting materials ({(placementMaterial!=null?placementMaterial.name:"null")}, {(dropMaterial!=null?dropMaterial.name:"null")}) for placement on {gameObject.name}", gameObject);
#endif

            SetMainMaterials(placementMaterial);
            SetDropMaterials(dropMaterial);
            if (separateDropTargetColoring)
            {
                SetDropMaterialTint(standbyTint, standbyEmission);
            }
        }

        private void SetMainMaterials(Material placementMaterial)
        {
            if (placementMaterial == null)
                return;

            for (int i = 0; i < _renderers.Length; i++)
            {
                _renderers[i].material = placementMaterial;
            }
        }

        private void SetDropMaterials(Material dropMaterial)
        {
            if (dropMaterial == null)
                return;

            for (int i = 0; i < _dropTargetRenderes.Count; i++)
            {
                _dropTargetRenderes[i].material = dropMaterial;
            }
        }

        /// <summary>
        /// Gets all the SpriteRenderers attached to this gameobjects children and saves their material
        /// </summary>
        public void LoadRenderers()
        {
            _renderers = GetComponentsInChildren<SpriteRenderer>(true).Where(x => x.GetComponent<ItemDontSetThisMaterial>() == null).ToArray();

            // Only add renderers that are not in the DropTarget list
            _previousMaterials = new Material[_renderers.Length - _dropTargetRenderes.Count];

            int arrayIndex = 0;

            for (int i = 0; i < _renderers.Length; i++)
            {
                if (!_dropTargetRenderes.Contains(_renderers[i]))
                {
                    _previousMaterials[arrayIndex] = _renderers[i].material;
                    arrayIndex++;
                }
            }

#if PLACEMENT_LOGGING || DISMANTLE_LOGGING
            Debug.Log($"LoadRenderers on object {gameObject.name}, prev material is {_previousMaterials[0].name}", gameObject);
#endif

            _previousDropMaterials = new Material[_dropTargetRenderes.Count];

            for (int j = 0; j < _dropTargetRenderes.Count; j++)
            {
                _previousDropMaterials[j] = _dropTargetRenderes[j].material;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        public void SetDropTargetColoring(bool activated)
        {
            if (!separateDropTargetColoring)
                return;

            if (activated)
            {
                SetMainMaterialTint(standbyTint);
                SetDropMaterialTint(_previousTint, _previousEmission);
            }
            else
            {
                SetMainMaterialTint(_previousTint);
                SetDropMaterialTint(standbyTint, standbyEmission);
            }
        }


        /// <summary>
        /// Resets all the SpriteRenderers back to their original materials.
        /// </summary>
        public void RestoreMaterials()
        {
            if (_renderers == null)
                return;

#if PLACEMENT_LOGGING || DISMANTLE_LOGGING
            if(_previousMaterials != null && _previousMaterials.Length > 0 && _previousMaterials[0] != null)
                Debug.Log($"Restoring materials to {_previousMaterials[0].name} on {gameObject.name}", gameObject);
            else
                Debug.Log($"Restoring materials on {gameObject.name}", gameObject);
#endif

            int normalRenderIndex = 0;
            int dropRenderIndex = 0;

            // Iterate over total number of renderes on item
            for (int i = 0; i < _renderers.Length; i++)
            {
                if (!_dropTargetRenderes.Contains(_renderers[i]))
                {
                    _renderers[i].material = _previousMaterials[normalRenderIndex];
                    normalRenderIndex++;
                }
                else
                {
                    _dropTargetRenderes[dropRenderIndex].material = _previousDropMaterials[dropRenderIndex];
                    dropRenderIndex++;
                }
            }

            // Refesh any sprite property block setters
            CoreSpritePropertyBlockSet[] setters = GetComponentsInChildren<CoreSpritePropertyBlockSet>();

            for (int i = 0; i < setters.Length; i++)
            {
                setters[i].SetPropertyBlock();
            }
        }
    }
}