// Copyright Isto Inc.
using Isto.Core.Beings;
using Isto.Core.Inputs;
using Isto.Core.Pooling;
using Isto.Core.StateMachine;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Items
{
    /// <summary>
    /// Simple placement state that only checks for overlaps
    /// </summary>
    public class EraserPositionState : ItemPlaceState, IActionOnRecycle
    {
        // Private Variables

        private Eraser _eraser;
        private bool _firstFrame = true;

        // Lifecycle methods

        public override void Enter(MonoStateMachine controller)
        {
#if DISMANTLE_LOGGING
            Debug.Log($"ErasePositionState.Enter on {gameObject.name}", gameObject);
#endif
            _firstFrame = true;

            base.Enter(controller);

            _eraser = gameObject.GetComponent<Eraser>();

            //Normally the state machine will manage Run and Enter on the states, but this means you have one frame of delay before Run starts up
            //To speed things up we want to execute part of the running logic right away.
            //Otherwise the PlayerDismantleState will Run without being in sync with us, and it's relying on us to control its flow.
            //We might then end up updating the Eraser's position twice without having updated the eraser validation logic in the middle.
            //Parts of Run are not needed here but executing Run here is just easier and less maintenance.
#if DISMANTLE_LOGGING
            Debug.Log($"ErasePositionState.Enter: pre-eminent run on {gameObject.name}! see comments!", gameObject);
#endif
            Run(controller);
        }

        public override MonoState Run(MonoStateMachine controller)
        {
#if DISMANTLE_LOGGING
            //Debug.Log("ErasePositionState.Run");
#endif
            _itemsAtOurPosition = GetPhysicsItemsAtOurPosition();
            UpdateOverlappedItem();

            InteractableItem targetedItem = FindValidEraseTarget();
            bool validPosition = targetedItem != null;

            // Update the eraser's tint when it is ready to activate
            if (validPosition != _validPosition && _setMaterialState != null)
            {
#if DISMANTLE_LOGGING
                Debug.Log("ErasePositionState.Run: updating tint to " + validPosition);
#endif
                if (validPosition)
                    _setMaterialState.RestoreMaterialTint();
                else
                    _setMaterialState.SetMaterialTint(_setMaterialState.invalidPlaceTint, _setMaterialState.invalidPlaceEmission);

                _validPosition = validPosition;
            }

            UserActions dismantleActivation = PlayerDismantleState.DismantleInput;
            UserActions dismantleAbort = UserActions.CANCELDISMANTLING;

            if (_controls.GetButton(dismantleAbort))
            {
#if DISMANTLE_LOGGING
                Debug.Log("ErasePositionState.Run: aborting");
#endif
                _playerController.SetDismantleCancelled();
            }
            else if (_controls.GetButton(dismantleActivation) || _firstFrame)
            {
#if DISMANTLE_LOGGING
                //Debug.Log("ErasePositionState.Run: progress?");
#endif
                _firstFrame = false;

                if (validPosition)
                {
#if DISMANTLE_LOGGING
                    Debug.Log($"ErasePositionState.Run: position is valid, ready for dismantling on {gameObject.name}", gameObject);
#endif
                    _eraser.EraseTarget = targetedItem;
                    Eraser.ReadyObjectForDismantling(_eraser.EraseTarget.gameObject);
                    this.gameObject.SetColliderState(true);
                    _controller.ChangeState(nextState);
                }
                else if (!_controls.UsingJoystick())
                {
#if DISMANTLE_LOGGING
                    //Debug.Log("ErasePositionState.Run: trying to adjust dismantling line");
#endif
                    if (_existingItemsAtOurPosition.Count > 0)
                    {
                        for (int i = 0; i < _existingItemsAtOurPosition.Count; i++)
                        {
                            ItemPlacementController controllerAtOurPosition = _existingItemsAtOurPosition[i].GetComponent<ItemPlacementController>();
                            _playerController.DismantleState.TryPartialDismantleRollback(controllerAtOurPosition);
                        }
                    }
                }
            }
            else
            {
#if DISMANTLE_LOGGING
                Debug.Log("ErasePositionState.Run: dismantling normal completion");
#endif
                // Normal dismantling completion flow
                _playerController.SetDismantleComplete(stopDropping: true);
                _controller.CancelDrop(exitFromPlacement: false);
            }

            return this;
        }

        public override void Exit(MonoStateMachine controller)
        {
            base.Exit(controller);
        }

        // Methods

        private void UpdateOverlappedItem()
        {
            if (OverEraser(_itemsAtOurPosition, out List<InteractableItem> otherItems))
            {
                if (_existingItemsAtOurPosition.Count > 0)
                    UnhideItems(_existingItemsAtOurPosition);

                HideItems(otherItems);
                _existingItemsAtOurPosition = otherItems;
            }
            else
            {
                if (_existingItemsAtOurPosition.Count > 0)
                    UnhideItems(_existingItemsAtOurPosition);

                _existingItemsAtOurPosition.Clear();
            }
        }

        private bool OverEraser(Collider[] hits, out List<InteractableItem> sameItem)
        {
            _controller.SetIsNotOverSwappableItem(); // Eraser never swaps

            sameItem = new List<InteractableItem>();

            for (int i = 0; i < hits.Length; i++)
            {
                InteractableItem otherItem = hits[i].GetComponentInParent<InteractableItem>();

                // If it's the same item type
                if (otherItem != null && otherItem.itemPile.item == _itemComponent.itemPile.item)
                {
                    sameItem.Add(otherItem);
                    return true;
                }
            }

            sameItem = null;
            return false;
        }

        private InteractableItem FindValidEraseTarget()
        {
            if (_itemsAtOurPosition.Length > 0)
            {
                // Check if we hit our self
                if (_itemsAtOurPosition.Length == 1)
                {
                    if (_itemsAtOurPosition[0].GetComponentInParent<ItemPlaceState>() == this)
                    {
                        Debug.LogWarning("Eraser item should never hit itself");
                        return null;
                    }
                }

                // NOTE: this is only for when we are over the same item type we are
                // Temporary eraser assets (waiting for dismantle confirmation) do get detected here
                if (_existingItemsAtOurPosition.Count > 0)
                {
                    return null;
                }
            }

            AdvancedItem item = _itemComponent.itemPile.item as AdvancedItem;
            if (item != null)
            {
                Vector3 itemCenter = FindItemCenter(_itemComponent);

                LayerMask overlapMask = placementConditions.invalidLayers;

                // If using mouse, allow under player to be valid position
                if (!_controls.UsingJoystick())
                    overlapMask.value &= ~(Layers.PLAYER_MASK);

                // Decreasing half extends by 10% to allow closer placing of items
                Collider[] hits = Physics.OverlapBox(itemCenter, _halfExtends * 0.9f, Quaternion.identity, overlapMask, QueryTriggerInteraction.Collide);

                if (hits.Length > 0)
                {
                    for (int i = 0; i < hits.Length; i++)
                    {
                        InteractableItem itemController = hits[i].GetComponentInParent<InteractableItem>();
                        if (itemController != null && itemController.GetValidActions().Contains(PlayerDismantleState.DismantleInput))
                        {
                            return itemController;
                        }
                    }

                    // Check if we hit our self
                    if (hits.Length == 1)
                    {
                        if (hits[0].GetComponentInParent<ItemPlaceState>() == this)
                        {
                            Debug.LogWarning("Eraser item should never hit itself");
                            return null;
                        }
                    }

                    return null;
                }
                else
                {
                    //Nothing to erase
                    return null;
                }
            }

            Debug.LogWarning("Eraser positioning hit unexpected flow");
            return null;
        }

        public void OnRecycle()
        {
            if (_eraser != null)
                _eraser.EraseTarget = null;

            this.gameObject.SetColliderState(false);
            _firstFrame = true;
        }
    }
}