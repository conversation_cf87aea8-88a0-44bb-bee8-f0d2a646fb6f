// Copyright Isto Inc.
using Isto.Core.Automation;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Items
{
    public class ItemPlaceOnTreeState : ItemRotateByEnableChildState
    {
        public List<HarvestableItem> validItems;

        [SerializeField] private bool _allowRotation = true;
        [SerializeField] private bool _allowSwapping = true;

        private IItemIdentityProvider _idProvider;

        protected override void Awake()
        {
            _idProvider = GetComponent<IItemIdentityProvider>();

            base.Awake();
        }

        protected override bool AtValidDropPosition()
        {
            if (_autoSystem.TryGetExistingGridSpace(transform.position, out AutomationGridSpace space))
            {
                AdvancedItem item = _itemComponent.itemPile.item as AdvancedItem;

                if (item != null)
                {
                    Vector3 itemCenter = FindItemCenter(_itemComponent);

                    LayerMask overlapMask = placementConditions.invalidLayers;

                    // If using mouse, allow under player to be valid position
                    if (!_controls.UsingJoystick())
                        overlapMask.value &= ~(Layers.PLAYER_MASK);

                    // Decreasing half extends by 10% to allow closer placing of items
                    Collider[] hits = Physics.OverlapBox(itemCenter, _halfExtends * 0.9f, Quaternion.identity, overlapMask, QueryTriggerInteraction.Collide);

                    // Expected hit count is 2, one for tree and one for this item, if more than that fail unless swaping is allowed
                    if (hits.Length > 2)
                    {
                        if (!_allowSwapping)
                        {
                            return false;
                        }
                        else
                        {
                            // If over another same item, just check if direction is different
                            if (space.itemProcessor != null)
                            {
                                if (space.itemProcessor.ProcessorID.Equals(_idProvider.ItemID))
                                {
                                    if (space.itemProcessor is IDirectionalItem dir)
                                    {
                                        Vector3 forward = dir.GetDirection();

                                        if (forward == _controller.Forward)
                                            return false;
                                    }
                                }
                                else
                                {
                                    return false;
                                }
                            }
                        }
                    }
                }

                // If over another same item, just check if direction is different
                if (space.itemProcessor != null)
                {
                    if (space.itemProcessor.ProcessorID.Equals(_idProvider.ItemID))
                    {
                        if (space.itemProcessor is IDirectionalItem dir)
                        {
                            Vector3 forward = dir.GetDirection();

                            if (forward == _controller.Forward)
                                return false;
                        }
                    }
                    else
                    {
                        return false;
                    }
                }

                if (space.Resource != null)
                {
                    for (int i = 0; i < validItems.Count; i++)
                    {
                        if (space.Resource.ID == validItems[i].itemID)
                            return true;
                    }
                }

                return false;
            }
            else
            {
                return false;
            }
        }

        protected override bool OverSwappableItems(Collider[] hits, out List<InteractableItem> swappableItems)
        {
            _controller.SetIsNotOverSwappableItem();

            swappableItems = new List<InteractableItem>();

            if (!_allowSwapping)
                return false;

            bool allSwappable = true;

            for (int i = 0; i < hits.Length; i++)
            {
                InteractableItem otherItem = hits[i].GetComponentInParent<InteractableItem>();

                if (otherItem != null)
                {
                    // If it's the same item type
                    if (otherItem.itemPile.item == _itemComponent.itemPile.item)
                    {
                        // Check if direction is different and it so set parameter and return true
                        if (otherItem.TryGetComponent(out IDirectionalItem directional))
                        {
                            if (directional.GetDirection() != _controller.Forward)
                            {
                                // Check to not duplicate items in list
                                if (!swappableItems.Contains(otherItem))
                                    swappableItems.Add(otherItem);
                            }
                            else
                            {
                                _controller.SetIsOverSwappableItem();
                            }
                        }
                    }
                    else if (otherItem is HarvestableController harvestable)
                    {
                        if (!validItems.Contains(harvestable.item))
                            allSwappable = false;
                    }
                }
            }

            if (!allSwappable)
            {
                swappableItems.Clear();
            }

            return allSwappable;
        }

        public override void HandleRotation(int direction)
        {
            if (_allowRotation)
            {
                base.HandleRotation(direction);
            }
        }
    }
}