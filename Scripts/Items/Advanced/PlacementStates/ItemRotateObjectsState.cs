// Copyright Isto Inc.
using UnityEngine;

namespace Isto.Core.Items
{
    /// <summary>
    /// Extends the basic item place state to allow for rotation of the item and updating of components that
    /// have a direcitonal aspect
    /// </summary>
    public class ItemRotateObjectsState : ItemRotateState
    {
        // Public Variables

        [Header("Rotation Children")]
        public Transform dropTransform;
        [<PERSON><PERSON>("Rotate Body")]
        public bool rotateObject;
        public Transform bodyTransform;

        // Methods

        public override void HandleRotation(int direction)
        {
            base.HandleRotation(direction);

            // If drop transform is set, rotate it
            if (dropTransform != null)
            {
                Vector3 offsetFromPivot = dropTransform.localPosition;

                Quaternion rotation = Quaternion.AngleAxis(direction * 90f, Vector3.up);
                Vector3 rotatedOffset = rotation * offsetFromPivot;

                dropTransform.localPosition = rotatedOffset;
            }

            if (rotateObject)
            {
                CoreItem item = _itemComponent.itemPile.item;

                float itemSize;

                if (item.GetType() == typeof(AdvancedItem))
                {
                    AdvancedItem advItem = item as AdvancedItem;
                    itemSize = Mathf.Max(advItem.width, advItem.depth);
                }
                else
                {
                    itemSize = 1;
                }

                Vector3 rotationPivot = transform.position + new Vector3(-itemSize / 2, 0f, itemSize / 2);

                bodyTransform.RotateAround(rotationPivot, Vector3.up, direction * 90f);
            }
        }
    }
}