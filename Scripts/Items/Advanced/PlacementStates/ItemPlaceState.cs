// Copyright Isto Inc.
using Isto.Core.Automation;
using Isto.Core.Beings;
using Isto.Core.Configuration;
using Isto.Core.Inputs;
using Isto.Core.StateMachine;
using Isto.Core.Tiles;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AI;
using Zenject;

namespace Isto.Core.Items
{
    /// <summary>
    /// Simple placement state that only checks for overlaps
    /// </summary>
    public class ItemPlaceState : MonoState
    {
        // Public Variables

        public MonoState nextState;

        [Header("Placement Checks")]
        [Tooltip("Layers that the object cannot be placed over.")]
        public bool allowContinuousPlacement;
        public bool allowForwardSwapOnSelf;
        [SerializeField] protected PlacementConditions placementConditions;

        [Header("Item Bounds")]
        [SerializeField] protected BoxCollider[] boundingColliders;

        [Header("Placement Behaviour")]
        public float placementDelay = Constants.BUTTON_SPAM_DELAY;

        // Private Variables

        protected ItemPlacementController _controller;
        protected IControls _controls;
        protected Settings _settings;
        protected AutomationSystem _autoSystem;
        protected AutomationPlayerController _playerController;
        protected TilemapGlobalController _tilemapController;
        protected InteractableItem _itemComponent;
        protected Vector3 _halfExtends; //Used for checking overlaps with OverlapBox physics call
        protected float _spamTimer;

        protected ItemSetMaterialState _setMaterialState;
        protected bool _validPosition = true;

        protected Collider[] _itemsAtOurPosition;
        protected List<InteractableItem> _existingItemsAtOurPosition = new List<InteractableItem>();

        private List<NavMeshObstacle> _navMeshObstacles;
        private InteractableItem _delayedItemToPickUp;

        // Used to track which objects have already been checked when looking at overlaps, etc.  Handles case where one object has several colliders
        private HashSet<GameObject> _checkedObjects = new HashSet<GameObject>();

        // Lifecycle methods

        [Inject]
        public void Inject(IControls controls, Settings settings, AutomationSystem automationSystem, AutomationPlayerController playerController, [Inject(Optional = true)] TilemapGlobalController tilemapCulling)
        {
            _controls = controls;
            _settings = settings;
            _autoSystem = automationSystem;
            _playerController = playerController;
            _tilemapController = tilemapCulling;
        }

        protected virtual void Awake()
        {
            _itemComponent = GetComponent<InteractableItem>();
            _setMaterialState = GetComponent<ItemSetMaterialState>();
            _controller = GetComponent<ItemPlacementController>();

            if (placementConditions == null)
                Debug.LogError("No placement conditions set for item : " + gameObject.name);
        }

        public override void Enter(MonoStateMachine controller)
        {
            _controller = controller as ItemPlacementController;

            _navMeshObstacles = new List<NavMeshObstacle>();

            var localComponent = gameObject.GetComponent<NavMeshObstacle>();
            if (localComponent != null)
                _navMeshObstacles.Add(localComponent);

            var childComponents = gameObject.GetComponentsInChildren<NavMeshObstacle>(includeInactive: true);
            if (childComponents.Length > 0)
            {
                _navMeshObstacles.AddRange(childComponents);
            }

            foreach (NavMeshObstacle navObst in _navMeshObstacles)
                navObst.enabled = false;

            _spamTimer = 0f;
        }

        public override void Exit(MonoStateMachine controller)
        {
            foreach (NavMeshObstacle navObst in _navMeshObstacles)
                navObst.enabled = true;
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            UpdateItemsUnderPosition();

            bool validPosition = AtValidDropPosition();

            UpdateMaterialColors(validPosition);

            ProcessInputsFromUser(validPosition);

            return this;
        }

        // Methods

        private void UpdateMaterialColors(bool validPosition)
        {
            //Update the tint for the object if this object has a SetMaterialState
            if (validPosition != _validPosition && _setMaterialState != null)
            {
                if (validPosition)
                    _setMaterialState.RestoreMaterialTint();
                else
                    _setMaterialState.SetMaterialTint(_setMaterialState.invalidPlaceTint, _setMaterialState.invalidPlaceEmission);

                _validPosition = validPosition;
            }
        }

        protected void ProcessInputsFromUser(bool validPosition)
        {
            _spamTimer += Time.deltaTime;

            if (_spamTimer > placementDelay)
            {
                bool interactPressed = _controls.GetButtonDown(UserActions.INTERACT);

                if (allowContinuousPlacement)
                    interactPressed = _controls.GetButton(UserActions.INTERACT);

                if (interactPressed && validPosition)
                {
                    _spamTimer = 0f;

                    if (_existingItemsAtOurPosition.Count > 0)
                    {
                        if (CanRemoveAllItems(_existingItemsAtOurPosition))
                        {
                            RemoveExistingItems(_existingItemsAtOurPosition);
                            _controller.ChangeState(nextState);
                        }
                    }
                    else
                    {
                        _controller.ChangeState(nextState);
                    }
                }
                else if (_controls.GetButton(UserActions.CANCELBUILDING))
                {
                    _spamTimer = 0f;

                    if (_existingItemsAtOurPosition.Count > 0)
                        UnhideItems(_existingItemsAtOurPosition);

                    _controller.CancelDrop();
                }
            }
        }

        protected virtual bool AtValidDropPosition()
        {
            AdvancedItem item = _itemComponent.itemPile.item as AdvancedItem;

            if (item != null)
            {
                Collider[] hits = _itemsAtOurPosition;

                int numberOfsameItemsUnder = 0;
                _checkedObjects.Clear();

                for (int i = 0; i < hits.Length; i++)
                {
                    InteractableItem otherItem = hits[i].GetComponentInParent<InteractableItem>();

                    if (IsSameItem(otherItem, _checkedObjects))
                        numberOfsameItemsUnder++;

                    if (!IsSwappableItem(otherItem))
                        return false;
                }

                List<Vector3> possiblePositions = GetAllPositionsUnderItem();

                if (AtValidGroundTile(possiblePositions))
                {
                    for (int i = 0; i < possiblePositions.Count; i++)
                    {
                        if (IsOverBlockedAutomationSpace(possiblePositions[i]))
                            return false;
                    }

                    // If hitting multiple of the same item under us, don't allow placement as it's difficult to handle all the edge cases with picking up multiple items
                    return numberOfsameItemsUnder < 2;
                }
                else
                {
                    return false;
                }
            }

            // Note: we only checked for placement issues if it's an advanced item. If it's not, we probably still
            // should check for any object that can't have an item placed on it. This is probably why our stun bombs
            // can stack for instance.
            // Also: see similar note in ItemPlacement, try to avoid code duplication.

            return true;
        }

        protected bool AtValidGroundTile(List<Vector3> positionsUnderItem)
        {
            if (_tilemapController == null)
                return true;

            for (int i = 0; i < positionsUnderItem.Count; i++)
            {
                bool navigatable = _tilemapController.IsTileNavigatable(positionsUnderItem[i]);

                if (!navigatable)
                    return false;
            }

            return true;
        }

        private bool IsSameItem(InteractableItem otherItem, HashSet<GameObject> previousCheckedObjects)
        {
            if (otherItem == null)
                return false;
            if (otherItem.itemPile == null)
                return false;

            bool isSelf = otherItem.gameObject == gameObject; // == this;

            if (isSelf)
                return false;

            if (previousCheckedObjects.Contains(otherItem.gameObject))
                return false;
            else
                previousCheckedObjects.Add(otherItem.gameObject);

            return otherItem.itemPile.item == _itemComponent.itemPile.item;
        }

        public virtual Vector3 FindItemCenter(InteractableItem interactableItem)
        {
            AdvancedItem item = interactableItem.itemPile.item as AdvancedItem;

            if (TryGetActiveBoundingCollider(out BoxCollider activeCollider))
            {
                _halfExtends = GetExtendsFromCollider(activeCollider);

                // Using bounds center as it's in world space vs collider center is in local space
                return activeCollider.bounds.center;
            }
            else
            {
                // Giving default height of 0.5 to collide with objets slightly above ground as well
                // If aligned along x-axis use width for x size, else use width for z size
                if (_controller.Forward.z != 0)
                {
                    _halfExtends.Set(item.width / 2, 0.5f, item.depth / 2);
                }
                else
                {
                    _halfExtends.Set(item.depth / 2, 0.5f, item.width / 2);
                }

                // Finding 'center' of object based on depth and width to determine where to do overlap check from
                Vector3 itemCenter = transform.position;

                // Adjust center because item position is always on the bottom of the grid space when rotating
                if (_controller.Forward.z == 1)
                    itemCenter.z += 1;
                if (_controller.Forward.x == -1)
                    itemCenter.x -= 1;

                itemCenter.x += _halfExtends.x * (_controller.Forward.x == 0 ? -1 : -_controller.Forward.x);
                itemCenter.z += _halfExtends.z * (_controller.Forward.z == 0 ? 1 : -_controller.Forward.z);

                return itemCenter;
            }
        }

        protected Vector3 GetExtendsFromCollider(BoxCollider box)
        {
            // Scale collider size by world scale to get accurate bounds for overlap check
            Vector3 extends = box.transform.rotation * (Vector3.Scale(box.size, box.transform.lossyScale)) / 2;

            // Make sure all lengths are non-negative for overlap checks
            extends.Set(Mathf.Abs(extends.x), Mathf.Abs(extends.y), Mathf.Abs(extends.z));

            return extends;
        }

        /// <summary>
        /// Intended to hide items that can be replaced when this item is over them, but we chose not to do this.
        /// </summary>
        protected void HideItems(List<InteractableItem> exisitingItems)
        {
        }

        /// <summary>
        /// Intended to unhide items that were hidden in HideItems, but we chose not to do this.
        /// </summary>
        protected void UnhideItems(List<InteractableItem> exisitingItems)
        {
        }

        //Only consider swappable items (case by case)
        //Same item different direction is OK
        //different item is OK if bottom item is a gravpipe only
        protected virtual bool OverSwappableItems(Collider[] hits, out List<InteractableItem> swappableItems)
        {
            _controller.SetIsNotOverSwappableItem();

            swappableItems = new List<InteractableItem>();

            bool allSwappable = true;

            for (int i = 0; i < hits.Length; i++)
            {
                InteractableItem otherItem = hits[i].GetComponentInParent<InteractableItem>();

                if (otherItem != null)
                {
                    if (IsSwappableItem(otherItem))
                    {
                        bool isSelf = otherItem.GetComponentInParent<ItemPlaceState>() == this;

                        if (!isSelf && !swappableItems.Contains(otherItem))
                            swappableItems.Add(otherItem);
                    }
                    else
                    {
                        allSwappable = false;
                    }
                }
                else
                {
                    allSwappable = false;
                }
            }

            if (!allSwappable)
            {
                swappableItems.Clear();
            }

            return allSwappable;
        }

        public List<Vector3> GetAllPositionsUnderItem()
        {
            if (TryGetActiveBoundingCollider(out BoxCollider activeCollider))
            {
                List<Vector3> positionsUnderItem = GetAllPositionsUnderCollider(activeCollider);

                return positionsUnderItem;
            }

            // If no active collider found assume 1x1 item
            return new List<Vector3> { (FindItemCenter(_itemComponent) - Constants.GRID_CENTERING_OFFSET).GetSnappedPosition(1f) };
        }

        protected bool TryGetActiveBoundingCollider(out BoxCollider boundingCollider)
        {
            for (int i = 0; i < boundingColliders.Length; i++)
            {
                if (boundingColliders[i].gameObject.activeInHierarchy)
                {
                    boundingCollider = boundingColliders[i];
                    boundingCollider.enabled = true;
                    return true;
                }
            }

            if (boundingColliders.Length > 0)
                Debug.LogError($"Error: {gameObject.name} has bounding colliders set but none are active.  This is wrong");

            boundingCollider = null;
            return false;
        }

        /// <summary>
        /// This method should compute a range of world positions that should be covered by the collider, assuming the collider has been
        /// positioned in a way to mostly line up with a legal item footprint. However, the results are very inconsistent across our
        /// items that have large footprints, so don't rely on this method until we fix its algorithm. See notes inside.
        /// </summary>
        protected List<Vector3> GetAllPositionsUnderCollider(BoxCollider boundingCollider)
        {
            List<Vector3> spaces = new List<Vector3>();

            Vector3 rotatedSize = boundingCollider.transform.TransformVector(boundingCollider.size);

            #region Work In Progress - fixing the size and position of the footprint we generate
            // !! Problem with the current algorithm:
            // This implementation is very naive since items have footprints ranging from 1x1, 1x3, 2x2, 2x3, 3x3, 3x5 and maybe more.
            // The mix of rounding, offsetting, dividing and snapping that we do only works for certain of those cases.
            // I have started developing a solution to account for those varying sizes, but the work is not complete yet.
            // I think my code has the right size but ends up off by 1, and I have to fix that then validate the result across all items.
            // For now this seems like it does not really affect items placement when not broken by other issues, so I'm stopping here
            // and hopefully I can pick this up later and fix it before we start building other logic that relies on this.

            /*
            // Fixing the rounding
            // We tend to have to have to trim the colliders a little bit so that items play nice when right next to each other;
            // rounding normally is bad because sometimes the trim is above 0.25 and thus adds up above 0.5 for both sides
            // in theory the collider should not end up bigger than the actual footprint, so I'm hoping that ceil works fine
            float xDistance = Mathf.Abs(Mathf.Ceil(rotatedSize.x));
            float zDistance = Mathf.Abs(Mathf.Ceil(rotatedSize.z));

            // This centering logic lines us where we would expect to be (near the middle space position) for odd numbered dimensions.
            // However for even numbers it biases us towards the lower row because of the offset.
            Vector3 searchCenter = boundingCollider.transform.TransformPoint(boundingCollider.center) - (Constants.GRID_CENTERING_OFFSET * 0.9f);

            // Since the center is not going to line up the same in the grid depending if the size is odd or even
            // this fancy math here takes that into account to line us up with the correct tile to start from so we can iterate
            // over the footprint entirely and correctly using the size values we found
            float xOffsetToStart = Mathf.Round(xDistance / 2f) - 1;
            float zOffsetToStart = Mathf.Round(zDistance / 2f) - 1;

            // Start at bottom left so we can iterate easily over the footprint
            Vector3 searchStart = searchCenter;
            searchStart.x -= xOffsetToStart;
            searchStart.z -= zOffsetToStart;
            searchStart.y = 0f;
            */
            #endregion

            int xDistance = Mathf.Abs(Mathf.RoundToInt(rotatedSize.x));
            int zDistance = Mathf.Abs(Mathf.RoundToInt(rotatedSize.z));

            Vector3 searchStart = boundingCollider.transform.TransformPoint(boundingCollider.center) - (Constants.GRID_CENTERING_OFFSET * 0.9f);
            searchStart.x -= xDistance / 2;
            searchStart.z -= zDistance / 2;
            searchStart.y = 0f;

            searchStart = searchStart.GetSnappedPosition(1f);

            for (int i = 0; i < xDistance; i++)
            {
                for (int j = 0; j < zDistance; j++)
                {
                    Vector3 nextPosition = new Vector3(i, 0f, j) + searchStart;

                    spaces.Add(nextPosition);
                }
            }

            return spaces;
        }

        protected bool IsOverBlockedAutomationSpace(Vector3 position)
        {
            if (_autoSystem.TryGetExistingGridSpace(position, out AutomationGridSpace space))
            {
                if (space.itemProcessor is AutomationBlockedSpace blocked)
                    return true;
            }

            return false;
        }

        protected bool IsOverReplaceableResource(Vector3 position)
        {
            if (_autoSystem.TryGetExistingGridSpace(position, out AutomationGridSpace space))
            {
                for (int j = 0; j < placementConditions.validResourcesToReplace.Count; j++)
                {
                    HarvestableItem item = placementConditions.validResourcesToReplace[j];

                    if (space.Resource != null && space.Resource.ID == item.itemID)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        protected bool IsSwappableItem(InteractableItem interactable)
        {
            if (interactable == null)
                return false;

            bool isSelf = interactable.GetComponentInParent<ItemPlaceState>() == this;

            if (isSelf)
                return true;

            if (interactable.TryGetComponent(out ItemPlaceCompleteState placeCompleteState))
            {
                if (!placeCompleteState.PlaceAnimationComplete)
                    return false;
            }

            // If same item check that forward is different
            if (interactable.itemPile.item == _itemComponent.itemPile.item && !allowForwardSwapOnSelf)
            {
                // Check if direction is different and it so set parameter and return true
                if (interactable.TryGetComponent(out IDirectionalItem directional))
                {
                    if (directional.GetDirection() != _controller.Forward)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }

                return false;
            }

            Item item;

            if (interactable is HarvestableController harvestable)
                item = harvestable.item;
            else
                item = interactable.itemPile?.item;

            if (item is AdvancedItem advancedItem)
            {
                return placementConditions.validItemsToReplace.Contains(advancedItem);
            }
            else if (item is HarvestableItem harv)
            {
                for (int j = 0; j < placementConditions.validResourcesToReplace.Count; j++)
                {
                    if (harv.itemID == placementConditions.validResourcesToReplace[j].itemID)
                        return true;
                }
            }

            return false;
        }

        protected virtual Collider[] GetPhysicsItemsAtOurPosition()
        {
            AdvancedItem item = _itemComponent.itemPile.item as AdvancedItem;

            if (item != null)
            {
                Vector3 itemCenter = FindItemCenter(_itemComponent);

                LayerMask overlapMask = placementConditions.invalidLayers;

                // If using mouse, allow under player to be valid position
                if (!_controls.UsingJoystick())
                    overlapMask.value &= ~(Layers.PLAYER_MASK);

                return Physics.OverlapBox(itemCenter, _halfExtends * 0.9f, Quaternion.identity, overlapMask, QueryTriggerInteraction.Collide);
            }

            return new Collider[0];
        }

        protected void UpdateOverlappedItems(Collider[] itemsAtOurPosition)
        {
            if (OverSwappableItems(itemsAtOurPosition, out List<InteractableItem> otherItems))
            {
                UnhideItems(_existingItemsAtOurPosition);
                HideItems(otherItems);

                _existingItemsAtOurPosition = otherItems;
            }
            else
            {
                if (_existingItemsAtOurPosition.Count > 0)
                    UnhideItems(_existingItemsAtOurPosition);

                _existingItemsAtOurPosition.Clear();
            }
        }

        protected void UpdateItemsUnderPosition()
        {
            _itemsAtOurPosition = GetPhysicsItemsAtOurPosition();

            UpdateOverlappedItems(_itemsAtOurPosition);
        }

        protected void RemoveExistingItems(List<InteractableItem> existingItems)
        {
            for (int i = 0; i < existingItems.Count; i++)
            {
                if (existingItems[i] is HarvestableController harvestable)
                {
                    if (_autoSystem.TryGetExistingGridSpace(harvestable.transform.position, out AutomationGridSpace space))
                        space.ClearResource();
                    else
                        Debug.LogWarning($"No automation grid space found at {harvestable.transform.position} for item {harvestable.gameObject.name}");
                }
                else
                {
                    // If inventory is full wait till Item placed event as it will remove one from inventory and make space for this existing item
                    if (_playerController.Inventory.GetSpaceAvailable(existingItems[i].itemPile.item) == 0)
                    {
                        _delayedItemToPickUp = existingItems[i];
                        _playerController.PlaceAdvItemState.ItemPlaced += OnItemPlaced;
                    }
                    else
                    {
                        _playerController.InteractionState.HandlePickupItem(existingItems[i]);
                    }
                }
            }

            existingItems.Clear();
        }

        /// <summary>
        /// Checks that the player inventory has space for all the existing items under the placement
        /// </summary>
        /// <param name="existingItems"></param>
        /// <returns></returns>
        private bool CanRemoveAllItems(List<InteractableItem> existingItems)
        {
            Dictionary<CoreItem, int> totalsOfItems = new Dictionary<CoreItem, int>();

            for (int i = 0; i < existingItems.Count; i++)
            {
                ItemPile nextItemPile = existingItems[i].itemPile;

                if (existingItems[i] is HarvestableController harvestable)
                {
                    // If it's harvestable we can remove it, no need to check inventory
                }
                else
                {
                    if (nextItemPile.item == null)
                        continue;

                    // Track total amount of items incase we're picking up multiple of the same item
                    if (totalsOfItems.ContainsKey(nextItemPile.item))
                        totalsOfItems[nextItemPile.item] += nextItemPile.count;
                    else
                        totalsOfItems.Add(nextItemPile.item, nextItemPile.count);

                    bool isSameItem = nextItemPile.item == _itemComponent.itemPile.item;

                    if (!isSameItem && _playerController.Inventory.GetSpaceAvailable(nextItemPile.item) < totalsOfItems[nextItemPile.item])
                    {
                        // Trigger inventory full message
                        _playerController.Inventory.InventoryFull();

                        return false;
                    }
                }
            }

            return true;
        }

        private void OnItemPlaced(object sender, ItemEventArgs e)
        {
            _playerController.InteractionState.HandlePickupItem(_delayedItemToPickUp);

            _delayedItemToPickUp = null;

            _playerController.PlaceAdvItemState.ItemPlaced -= OnItemPlaced;
        }

        // Gizmos
#if UNITY_EDITOR

        [Header("Gizmos")]
        public bool showGridSpacesBeingChecked = true;
        public Color gridSpacesBeingCheckedColor = Color.blue;
        public float gridSpacesBeingCheckedSphereRadius = 0.15f;
        [Space(15)]
        public bool showGridSpacesWithReplacableItems = true;
        public Color gridSpacesWithReplacableItemsColor = Color.yellow;
        public float gridSpacesWithReplacableItemsRadius = 0.3f;
        [Space(15)]
        public bool showGridSpacesWithReplacableResources = true;
        public Color gridSpacesWithReplacableResourcesColor = Color.red;
        public float gridSpacesWithReplacableResourcesRadius = 0.3f;

        public virtual void OnDrawGizmosSelected()
        {
            if (_controller != null)
            {
                _itemComponent = GetComponent<InteractableItem>();

                Vector3 itemCenter = FindItemCenter(_itemComponent);

                Gizmos.color = Color.cyan;
                Gizmos.DrawWireSphere(itemCenter, 0.25f);
                Gizmos.color = Color.blue;
                Gizmos.DrawWireCube(itemCenter, _halfExtends * 2f);
            }

            if (UnityEditor.EditorApplication.isPlaying && _controller != null && _controller.enabled)
            {
                List<Vector3> spacesUnderItem = GetAllPositionsUnderItem();

                if (showGridSpacesBeingChecked)
                {
                    for (int i = 0; i < spacesUnderItem.Count; i++)
                    {
                        Gizmos.color = gridSpacesBeingCheckedColor;
                        Gizmos.DrawSphere(spacesUnderItem[i], gridSpacesBeingCheckedSphereRadius);
                    }
                }

                //if(showGridSpacesWithReplacableItems)
                //{
                //    for (int i = 0; i < spacesUnderItem.Count; i++)
                //    {
                //        Vector3 position = spacesUnderItem[i];

                //        if(IsOverReplaceableItemOrEmpty(position))
                //        {
                //            Gizmos.color = gridSpacesWithReplacableItemsColor;
                //            Gizmos.DrawSphere(position + Constants.GRID_CENTERING_OFFSET, gridSpacesWithReplacableItemsRadius);
                //        }
                //    }
                //}

                if (showGridSpacesWithReplacableResources)
                {
                    for (int i = 0; i < spacesUnderItem.Count; i++)
                    {
                        Vector3 position = spacesUnderItem[i];

                        if (IsOverReplaceableResource(position))
                        {
                            Gizmos.color = gridSpacesWithReplacableResourcesColor;
                            Gizmos.DrawSphere(position + Constants.GRID_CENTERING_OFFSET, gridSpacesWithReplacableResourcesRadius);
                        }
                    }
                }
            }
        }
#endif
    }
}