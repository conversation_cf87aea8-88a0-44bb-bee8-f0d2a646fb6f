// Copyright Isto Inc.
using UnityEngine;

namespace Isto.Core.Items
{
    public class ItemPlaceOnWaterState : ItemRotateByEnableChildState, IAnimatorProvider
    {
        protected override bool AtValidDropPosition()
        {
            bool noOverlap = base.AtValidDropPosition();

            if (!noOverlap)
                return false;

            bool allOverWater = true;

            // Look to see if you overlap with the water mask
            if (_itemComponent.itemPile.item is AdvancedItem item)
            {
                int depthIterations = (int)item.depth;

                Vector3 nextPosition = transform.position + Constants.GRID_CENTERING_OFFSET;

                for (int i = 0; i < depthIterations; i++)
                {
                    allOverWater &= IsOverWater(nextPosition);

                    // Move position backwards from drop direction
                    nextPosition -= _controller.Forward;
                }

                if (allOverWater)
                    return true;
            }

            // Make sure to set material state because base may have passed and set it green but we failed here so set it red
            _setMaterialState.SetMaterialTint(_setMaterialState.invalidPlaceTint, _setMaterialState.invalidPlaceEmission);

            return false;
        }

        private bool IsOverWater(Vector3 position)
        {
            Environment.GroundType typeAtPosition = _tilemapController.GetGroundTypeForPosition(transform.position);
            return typeAtPosition == Environment.GroundType.BloodWater;
        }

        public override void HandleRotation(int direction)
        {
            base.HandleRotation(direction);
        }

        public Animator GetAnimator()
        {
            // Update the animator on the HeartPoweredObject with the active child for the current direction
            GameObject activeDirection;

            if (directionOne.gameObject.activeInHierarchy)
                activeDirection = directionOne.gameObject;
            else if (directionTwo.gameObject.activeInHierarchy)
                activeDirection = directionTwo.gameObject;
            else if (directionThree.gameObject.activeInHierarchy)
                activeDirection = directionThree.gameObject;
            else
                activeDirection = directionFour.gameObject;

            Animator directionAnimator = activeDirection.GetComponentInChildren<Animator>();

            return directionAnimator;
        }

        // Gizmos
#if UNITY_EDITOR
        public override void OnDrawGizmosSelected()
        {
            if (UnityEditor.EditorApplication.isPlaying)
            {
                Gizmos.color = Color.green;

                if (_itemComponent.itemPile.item is AdvancedItem item)
                {
                    int raycastIterations = (int)item.depth;

                    Vector3 raycastPosition = transform.position + Constants.GRID_CENTERING_OFFSET;

                    for (int i = 0; i < raycastIterations; i++)
                    {
                        Gizmos.DrawWireSphere(raycastPosition, 0.4f);

                        // Move position backwards from drop direction
                        raycastPosition -= _controller.Forward;
                    }
                }

                Vector3 itemCenter = FindItemCenter(_itemComponent);

                Gizmos.color = Color.green - Color.black * 0.5f;
                Gizmos.DrawWireCube(itemCenter, _halfExtends * 2);
                Gizmos.color = Color.red;
                Gizmos.DrawSphere(itemCenter, 0.2f);
            }
        }
#endif
    }
}