// Copyright Isto Inc.
using Isto.Core.Inputs;
using Isto.Core.StateMachine;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Items
{
    /// <summary>
    /// Simple rotation state that checks for input and rotates the arrow transform if set and updates any 
    /// attached components that use the IDirectional interface
    /// </summary>
    public class ItemRotateState : ItemPlaceState
    {
        // Public Variables

        [Header("Direction Arrow")]
        public Transform arrowTransform;

        // Private Variables

        protected List<IDirectionalItem> _directionalScripts;

        // Lifecycle Events

        protected override void Awake()
        {
            _directionalScripts = new List<IDirectionalItem>();
            _directionalScripts.AddRange(GetComponents<IDirectionalItem>());

            // Next state should never be null so check for it here
            if (nextState == null)
                Debug.LogError("No next state set in ItemRotateState component on:" + gameObject.name, gameObject);

            base.Awake();
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            UpdateItemsUnderPosition();
            bool validPosition = AtValidDropPosition();

            //Update the tint for the object if this object has a SetMaterialState
            if (_setMaterialState != null)
            {
                if (validPosition)
                    _setMaterialState.RestoreMaterialTint();
                else
                    _setMaterialState.SetMaterialTint(_setMaterialState.invalidPlaceTint, _setMaterialState.invalidPlaceEmission);

                _validPosition = validPosition;
            }

            ProcessInputsFromUser(validPosition);
            HandleRotationInput();

            return this;
        }

        // Methods

        protected void HandleRotationInput()
        {
            int rotateDirection = 0;

            if (_controls.GetButtonDown(UserActions.ROTATELEFT))
            {
                rotateDirection = -1;
            }
            else if (_controls.GetButtonDown(UserActions.ROTATERIGHT))
            {
                rotateDirection = 1;
            }

            if (rotateDirection != 0)
            {
                HandleRotation(rotateDirection);

                _spamTimer = 0f;
            }
        }

        public virtual void HandleRotation(int direction)
        {
            if (_controller == null)
            {
                _controller = GetComponent<ItemPlacementController>();
            }

            if (arrowTransform != null)
            {
                arrowTransform.Rotate(Vector3.up, direction * 90f, Space.World);
            }

            // Update internal forward vector and other scripts.
            Quaternion rotation = Quaternion.AngleAxis(direction * 90f, Vector3.up);

            Vector3 newForward = (rotation * _controller.Forward).GetSnappedPosition(1);

            UpdateForwardInScripts(newForward);
        }

        protected void UpdateForwardInScripts(Vector3 forward)
        {
            // If list is null, object hasn't been initialized which means this is being called from editor script, so load list items
            if (_directionalScripts == null)
            {
                _directionalScripts = new List<IDirectionalItem>();
                _directionalScripts.AddRange(GetComponents<IDirectionalItem>());
            }

            for (int i = 0; i < _directionalScripts.Count; i++)
            {
                _directionalScripts[i].SetForwardDirection(forward);
            }
        }
    }
}