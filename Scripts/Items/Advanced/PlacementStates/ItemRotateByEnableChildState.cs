// Copyright Isto Inc.
using UnityEngine;

namespace Isto.Core.Items
{
    public class ItemRotateByEnableChildState : ItemRotateState
    {
        // Public Variables

        [<PERSON><PERSON>("Child Objects for each direction")]
        [Tooltip("0,0,1 OR NE")]
        public Transform directionOne;
        [<PERSON>lt<PERSON>("-1,0,0 OR NW")]
        public Transform directionTwo;
        [<PERSON><PERSON><PERSON>("0,0,-1")]
        public Transform directionThree;
        [<PERSON>lt<PERSON>("1,0,0")]
        public Transform directionFour;

        // Methods		

        public override void HandleRotation(int direction)
        {
            base.HandleRotation(direction);

            if (_controller.Forward.z == 1)
                EnableDirection(1);
            else if (_controller.Forward.x == -1)
                EnableDirection(2);
            else if (_controller.Forward.z == -1)
                EnableDirection(3);
            else if (_controller.Forward.x == 1)
                EnableDirection(4);
            else
                throw new UnityException("Unalbe to map forward vector to a direction, please check.  Forward: " + _controller.Forward);
        }

        /// <summary>
        /// Sets the appropriate GameObject active based on the direction passed in.
        /// </summary>
        /// <param name="direction"></param>
        protected virtual void EnableDirection(int direction)
        {
            directionOne.gameObject.SetActive(direction == 1);
            directionTwo.gameObject.SetActive(direction == 2);
            directionThree.gameObject.SetActive(direction == 3);
            directionFour.gameObject.SetActive(direction == 4);
        }
    }
}