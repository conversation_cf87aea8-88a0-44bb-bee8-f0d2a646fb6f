// Copyright Isto Inc.
using Isto.Core.Automation;
using UnityEngine;

namespace Isto.Core.Items
{
    /// <summary>
    /// The game systems work together in a way that assumes that the items' positions are the same from all points of view.
    /// However some larger items had build constraints that meant this was more complicated to accomplish, so we ended up
    /// having items that have offsets between their positions in each system (mainly automation spaces vs world position of the visuals)
    /// </summary>
    public interface ISpecialPositioning
    {
        // This asks for where the automation space of the item is supposed to be so we can either get the proper AutomationGridSpace
        // or get the bounding sphere index for it (as the culling system registration is done by the automation system, so it knows
        // it by its automation position)
        public Vector3 GetAutomationPosition();

        // This configures the object with special case offsets if required before calling IAutomationGridSpaceDisplay.SetGridSpace()
        // We could move this logic into the beginning of SetGridSpace and pass a flag but most items don't need it so for now
        // let's just use this interface for the extra config
        public void SetupWorldPosition(AutomationGridSpace space);

        public Vector3 GetPositionOffset();
    }
}
