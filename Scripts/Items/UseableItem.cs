// Copyright Isto Inc.
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Items
{
    /// <summary>
    /// All items that consumed (or used) have an effect (eg. healing, boosting stamina) etc.
    /// </summary>
    [CreateAssetMenu(fileName = "New Useable Item", menuName = "Scriptables/Item/UseableItem")]
    public class UseableItem : CoreItem
    {
        // Public Variables
        public List<ItemEffect> effects;

        // Methods	
        /// <summary>
        /// Apply all of the effects to the mob or player that has consumed it.
        /// </summary>
        /// <param name="consumer"></param>
        public void UseItem(GameObject consumer)
        {
            foreach (ItemEffect effect in effects)
            {
                effect.UseItem(consumer);
            }
        }

    }
}