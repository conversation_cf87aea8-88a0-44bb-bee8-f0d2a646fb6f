// Copyright Isto Inc.
using UnityEngine;

namespace Isto.Core.Items
{
    /// <summary>
    /// Any item that the player has equipped on his body
    /// </summary>
    [CreateAssetMenu(fileName = "New Equippable Item", menuName = "Scriptables/Item/EquippableItem")]
    public class EquippableItem : CoreItem
    {
        // Public Variables
        public Sprite horIcon; //icon when you're horizontal
        public Sprite backIcon; //icon when you're factng away from the camera
        public Sprite frontIcon; //icon when you're factng towards the camera
    }
}