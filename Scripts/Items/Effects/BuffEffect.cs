// Copyright Isto Inc.
using Isto.Core.Beings;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Items
{
    /// <summary>
    /// Some items give the player a buff (resistances, heal over time)
    /// </summary>
    [CreateAssetMenu(fileName = "New Buff Effect", menuName = "Scriptables/ItemEffect/BuffEffect")]
    public class BuffEffect : ItemEffect
    {
        public string ID;
        public float effectDuration;
        public List<Buff> buffs;

        public override void UseItem(GameObject consumer)
        {
            if (consumer.TryGetComponent(out PlayerHealth playerHealth))
            {
                playerHealth.SetNewBuff(this);
            }
        }

        public void ActivateBuff(GameObject consumer)
        {
            for (int i = 0; i < buffs.Count; i++)
            {
                buffs[i].ActivateBuff(consumer);
            }
        }

        public void UpdateBuf(GameObject consumer)
        {
            for (int i = 0; i < buffs.Count; i++)
            {
                buffs[i].UpdateBuff(consumer);
            }
        }


        public void ExpireBuff(GameObject consumer)
        {
            for (int i = 0; i < buffs.Count; i++)
            {
                buffs[i].DeactivateBuff(consumer);
            }
        }
    }
}
