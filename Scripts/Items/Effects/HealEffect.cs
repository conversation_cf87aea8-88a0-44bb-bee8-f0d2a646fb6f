// Copyright Isto Inc.
using FMODUnity;
using Isto.Core.Audio;
using Isto.Core.Beings;
using UnityEngine;
using Zenject;

namespace Isto.Core.Items
{
    [CreateAssetMenu(fileName = "New Heal Effect", menuName = "Scriptables/ItemEffect/Heal")]
    public class HealEffect : ItemEffect
    {
        // public variables

        public float amount;

        [EventRef]
        [SerializeField] private string _refillAudio;

        private IGameSounds _sounds;

        [Inject]
        public void Inject(IGameSounds sounds)
        {
            _sounds = sounds;
        }

        // Methods

        public override void UseItem(GameObject consumer)
        {
            PlayerHealth h = consumer.GetComponent<PlayerHealth>();

            if (h != null)
            {
                h.Heal(amount);
                _sounds.PlayOneShot(_refillAudio, Vector3.zero);
            }
        }
    }
}
