// Copyright Isto Inc.
using FMODUnity;
using Isto.Core.Audio;
using Isto.Core.Automation;
using System;
using System.Collections.Generic;
using UnityEngine;
using UPool;
using Zenject;

namespace Isto.Core.Items
{
    public class GrowingHarvestableVisuals : MonoBehaviour, IAutomationGridSpaceDisplay
    {
        private bool UsingAnimator => _growStartClip != null || _growFinishClip != null;

        [Header("Planter Adjustments")]
        [SerializeField] private Vector3 _positionOffset = default;
        [SerializeField] private float _scaleFactor = default;
        [SerializeField] private Collider _harvestCollider;
        [SerializeField] private float _colliderScaleFactor = 1.25f;
        [SerializeField] private float _colliderYMoveAfterGrown = -0.25f;
        [SerializeField] private List<GameObject> _hideWhenOnPlanter;

        [Header("Visuals Container")]
        [SerializeField] private GameObject _visualsContainer;

        [Header("Animation Visuals")]
        [SerializeField] private AnimationClip _growStartClip = default;
        [SerializeField] private AnimationClip _growFinishClip = default;
        [SerializeField][EventRef] private string _growingFinishedAudio = default;

        [Header("Sprite Visuals")]
        [SerializeField] private List<Sprite> _sprites = default;
        [SerializeField] private SpriteRenderer _spriteRenderer = default;
        [Space(10)]
        [SerializeField] private Sprite _fullyGrownSprite;

        private Animator _anim;
        private PoolableObject _poolObj;
        protected AutomationGridSpace _gridSpace;
        private float _growthInterval = 0;
        private int _currentSpriteIndex = 0;
        private bool _onPlanter = false;

        // Injected

        private IGameSounds _gameSounds;

        [Inject]
        public void Inject(IGameSounds gameSounds)
        {
            _gameSounds = gameSounds;
        }

        public void Awake()
        {
            _anim = GetComponent<Animator>();

            if (_sprites.Count > 1)
            {
                _growthInterval = 1f / _sprites.Count;
            }

            // Hiding the visuals container to prevent pop of fully grown sprite when first created.
            if (_visualsContainer != null)
            {
                _visualsContainer.SetActive(false);
            }

            if (TryGetComponent(out _poolObj))
            {
                _poolObj.ObjectDeallocated += () =>
                {
                    if (_gridSpace != null && _gridSpace.Resource != null)
                    {
                        _gridSpace.Resource.GrowthComplete -= GrowingFinish;

                        if (UsingAnimator)
                            _anim.SetTrigger("reset");
                    }
                };
            }
        }

        public void Update()
        {
            UpdateSprites();
        }

        public void OnDestroy()
        {
            if (_gridSpace != null && _gridSpace.Resource != null)
            {
                _gridSpace.Resource.GrowthComplete -= GrowingFinish;
            }
        }

        /// <summary>
        /// Gets called when the display system puts it on screen/creates the visuals
        /// </summary>
        public virtual void SetGridSpace(AutomationGridSpace space)
        {
            _gridSpace = space;

            // This is when it gets created by the display system. This can happen halfway because if you're offscreen, it'll turn all visuals off.
            if (space.itemProcessor is AutomationPlanter _)
            {
                _onPlanter = true;

                transform.position += _positionOffset;
                transform.localScale = transform.localScale * _scaleFactor;

                for (int i = 0; i < _hideWhenOnPlanter.Count; i++)
                {
                    _hideWhenOnPlanter[i].SetActive(false);
                }
            }

            space.Resource.GrowthComplete += GrowingFinish;

            if (_visualsContainer != null)
            {
                _visualsContainer.SetActive(true);
            }

            if (UsingAnimator)
            {
                if (space.Resource.GrowthPercent < 1f)
                {
                    _anim.enabled = true;
                    _anim.Play(_growStartClip.name);
                }
            }
        }

        private void GrowingStart(object sender, EventArgs e)
        {
            if (UsingAnimator)
            {
                _anim.Play(_growStartClip.name);
            }
        }

        private void GrowingFinish(object sender, EventArgs e)
        {
            CompleteGrowing();
        }

        protected virtual void CompleteGrowing()
        {
            if (UsingAnimator)
            {
                _anim.Play(_growFinishClip.name);
            }

            _gridSpace.Resource.GrowthComplete -= GrowingFinish;

            if (_harvestCollider != null && _gridSpace.itemProcessor is AutomationPlanter _)
            {
                _harvestCollider.transform.localScale *= _colliderScaleFactor;
                _harvestCollider.transform.position += Vector3.up * _colliderYMoveAfterGrown;
            }
        }

        //Triggering from animator
        public void PlayGrowingSound()
        {
            if (!string.IsNullOrEmpty(_growingFinishedAudio))
            {
                _gameSounds.PlayOneShot(_growingFinishedAudio, this.transform.position);
            }
        }

        public bool IsOnPlanter()
        {
            return _onPlanter;
        }

        private void UpdateSprites()
        {
            if (_sprites.Count == 0 || _gridSpace == null || _gridSpace.Resource == null)
            {
                return;
            }

            float growthPercent = _gridSpace.Resource.GrowthPercent;

            if (growthPercent < 1 && _sprites.Count > 1)
            {
                if (_growthInterval * (_currentSpriteIndex + 1) < growthPercent)
                {
                    _currentSpriteIndex++;
                }

                if (_currentSpriteIndex < _sprites.Count)
                {
                    _spriteRenderer.sprite = _sprites[_currentSpriteIndex];
                }
            }
            else if (_fullyGrownSprite != null)
            {
                _spriteRenderer.sprite = _fullyGrownSprite;
            }
        }
    }
}