// Copyright Isto Inc.
using System;
using UnityEngine;

namespace Isto.Core
{
    [Serializable]
    public class ItemPile
    {
        // Public Variables
        public CoreItem item;
        public int count;
        [Range(0, 1)] public float dropPercentage = 1f;

        // Methods

        public ItemPile()
        {

        }

        public ItemPile(CoreItem item, int itemCount)
        {
            this.item = item;
            this.count = itemCount;
        }

        public ItemPile(ItemPile itemPile)
        {
            this.item = itemPile.item;
            this.count = itemPile.count;
        }

        /// <summary>
        /// Copys the values from the passed in ItemPile to this item pile
        /// </summary>
        /// <param name="pile"></param>
        public void Set(ItemPile pile)
        {
            this.item = pile.item;
            this.count = pile.count;
        }

        public void Set(CoreItem item, int count)
        {
            this.item = item;
            this.count = count;
        }

        public void Clear()
        {
            this.item = null;
            this.count = 0;
        }

        public bool HasItems()
        {
            return item != null && count > 0;
        }

        public override string ToString()
        {
            return string.Format("Item: {0}, Count: {1}", item.itemName, count);
        }
    }
}