// Copyright Isto Inc.
using FMODUnity;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Items
{
    /// <summary>
    /// An item that can be harvested by the player or by a factory
    /// 
    /// Item -> CoreItem -> AdvancedItem
    /// Item -> HarvestableItem
    /// </summary>
    [CreateAssetMenu(fileName = "New Harvestable Item", menuName = "Scriptables/Item/HarvestableItem")]
    public class HarvestableItem : Item
    {
        // Public Variables
        [Header("Harvest Tool Tips")]
        public string missingRequiredItemTip;
        [Space(10)]
        [Header("Harvest Settings")]
        [Tooltip("After you harvest, which items get dropped")]
        public List<ItemPile> droppedItems;
        [Space(10)]
        [Header("Growing Params")]
        public float growingTime = 0f;
        [Tooltip("If harvested while growing, these items are dropped")]
        public List<ItemPile> droppedItemsWhileGrowing;
        [Tooltip("If set, resource will have infinite health so will never expire")]
        public bool infiniteResrouce = false;
        public float harvestHP = 50;
        [Tooltip("The amount of health loss required to cause item to drop.  If set to zero, nothing will drop until all health is gone.")]
        public float harvestAmountPerItemDrop = 0f;
        public List<float> harvestHPVisualThresholds; //Changes to the visual state when you harvest enough
        public List<Sprite> visualStates; //State of the sprite changes while being harvested

        [Header("Audio")]

        [Tooltip("Sound played while item is being harvested")]
        [EventRef] public string harvestingSoundRef;

        [Tooltip("Does the audio loop? If not it'll just be played once everything item takes damage")]
        public bool harvestSoundIsLooping = false;

        [Tooltip("Sound played when Item is dropped.")]
        [EventRef] public string harvestDropItemSoundRef;

        [Tooltip("Sound played when Item is dropped.")]
        [EventRef] public string planterGrownSound;

        public override string GetToolTip(bool canHarvest)
        {
            // Check if player has item in inventory
            if (canHarvest)
            {
                return base.GetToolTip(canHarvest);
            }
            else
            {
                // Player doesn't have item 
                return missingRequiredItemTip;
            }
        }
    }
}