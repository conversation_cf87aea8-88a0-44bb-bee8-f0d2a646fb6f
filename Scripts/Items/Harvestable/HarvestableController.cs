// Copyright Isto Inc.
using Isto.Core.Audio;
using Isto.Core.Automation;
using Isto.Core.Beings;
using Isto.Core.Cameras;
using Isto.Core.Data;
using Isto.Core.Inputs;
using Isto.Core.Localization;
using Isto.Core.UI;
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AI;
using Zenject;

namespace Isto.Core.Items
{
    /// <summary>
    /// Any harvestable item when being harvested either by the player or a Mining machine.
    ///
    /// Has methods for taking harvest damage and whiche items to drop when the harvest is complete.
    /// </summary>
    [SelectionBase]
    public class HarvestableController : PooledInteractableItem,
                                         IItemTopmostParent, IAutomationGridSpaceDisplay, IDataLoadCompleteHandler
    {
        public HarvestableItem item;
        [Tooltip("Should the item be destroyed once harvested, or have animation set to harvested_idle?")]
        public bool destroyOnHarvest = true;
        [Tooltip("What is the radius of the circle from the pivot of the object where items are dropped?")]
        public float dropRange = 0f;
        [Tooltip("Offset added to drop position for items.  Use if items are dropping behind object or should go to " +
                 "specific position")]
        public Vector3 dropOffset;
        [Tooltip("Icon to update as the HP changes thresholds, if set")]
        public SpriteRenderer icon;

        [Header("")]
        [Tooltip("If false, only automatic harvesters can harvest this resource")]
        public bool playerHarvestable = true;
        [Tooltip("If not harvestable by player, this item is used to harvest it")]
        public AdvancedItem harvestedBy;

        [Tooltip("some items leave new objects once harvested - e.g. sapling")]
        [SerializeField] private GameObject _afterHarvestedPrefab = null;

        // Readonly properties
        public float HarvestHealth { get { return _autoResource == null ? 1f : _autoResource.Health; } }
        public float HarvestPercent { get { return _autoResource == null ? 0f : _autoResource.HarvestPercent; } }
        public bool IsGrowing { get { return _autoResource == null ? false : !_autoResource.IsFullyGrown; } }

        [SerializeField] private Transform _customSelectionHighlight = null;
        [SerializeField] private Animator _animator = default;

        [Header("Visual Culling")]
        public bool cullWhenOffScreen = true;
        [Tooltip("When the item is spawned by the display system if true will be placed locked to the grid, otherwise " +
                 "some with slight randomness in placement")]
        public bool lockToGrid = true;

        private IGameSounds _sounds;
        private IUIHarvestProgressDisplay _uiProgress;
        private ItemContainer _harvesterContainer;
        private CameraController _camController;
        private PlayerUpgrades _playerUpgrades;
        private AutomationSystem _autoSystem;
        private PlayerProgress _playerProgress;
        protected AutomationResource _autoResource;
        private Queue<AutomationCoreItem> _outputItemsQueue = new Queue<AutomationCoreItem>();

        // List of valid animation parameters pre-populated on Start for performance
        private HashSet<int> _validAnimatorParameters = new HashSet<int>();
        // Health component on the item, to detect damage from non-harvester sources (damage)
        private Health _externalHealth = default;
        private bool _isQuitting;

        // Lifecycle Events

        [Inject]
        public void Inject(IGameSounds sounds, IUIHarvestProgressDisplay harvestProgressDisplay, CameraController cameraController,
            PlayerUpgrades playerUpgrades, AutomationSystem automationSystem, PlayerProgress playerProgress)
        {
            _sounds = sounds;
            _uiProgress = harvestProgressDisplay;
            _camController = cameraController;
            _playerUpgrades = playerUpgrades;
            _autoSystem = automationSystem;
            _playerProgress = playerProgress;
        }

        protected override void Awake()
        {
            if (TryGetComponent(out Health health))
                _externalHealth = health;

            base.Awake();
        }

        protected override void Start()
        {
            base.Start();

            if (item == null)
            {
                Debug.LogErrorFormat("No item set for Harvestable Controller.  Harvestable items need a scriptable " +
                    "object set for the item to control health and other parameters.  Item: {0}", gameObject.name);
            }

            if (_animator != null)
            {
                AnimatorControllerParameter[] parameters = _animator.parameters;

                for (int i = 0; i < parameters.Length; i++)
                {
                    _validAnimatorParameters.Add(parameters[i].nameHash);
                }
            }

            if (_externalHealth != null)
            {
                _externalHealth.TookDamage += OnExternalHealthTookDamage;
            }
        }

        protected override void OnDestroy()
        {
            if (_isQuitting)
            {
                return;
            }

            base.OnDestroy();

            if (_autoResource != null)
            {
                _autoResource.Consumed -= OnConsumed;
                _autoResource.PullStart -= OnPullStart;
                _autoResource.PullEnd -= OnPullEnd;
            }

            if (_externalHealth != null)
            {
                _externalHealth.TookDamage -= OnExternalHealthTookDamage;
            }
        }

        protected void OnApplicationQuit()
        {
            _isQuitting = true;
        }

        public override void OnDeallocate()
        {
            if (_autoResource != null)
            {
                _autoResource.Consumed -= OnConsumed;
                _autoResource.PullStart -= OnPullStart;
                _autoResource.PullEnd -= OnPullEnd;
            }

            if (_externalHealth != null)
            {
                _externalHealth.TookDamage -= OnExternalHealthTookDamage;
            }

            base.OnDeallocate();
        }

        public void OnDataLoadComplete()
        {
            SetupInAutomationSystem(true);

            if (cullWhenOffScreen && gameObject.activeInHierarchy)
            {
                _state = AllocationStates.None;

                // After setup in automation, destroy this gameobject as the display system will take over displaying
                // this when it's on screen
                Destroy(gameObject);
            }
        }

        public virtual void SetupInAutomationSystem(bool spawnFullyGrown)
        {
            // Only register active items
            if (!gameObject.activeInHierarchy)
            {
                return;
            }

            if (_autoResource == null)
            {
                float growthPercent = spawnFullyGrown ? 1f : 0f;

                if (_autoSystem.TryAddResource(transform.position, item, false, cullWhenOffScreen, growthPercent))
                {
                    AutomationGridSpace space = _autoSystem.GetOrCreateGridSpace(transform.position);
                    _autoResource = space.Resource;
                    _autoResource.Consumed += OnConsumed;
                    _autoResource.PullStart += OnPullStart;
                    _autoResource.PullEnd += OnPullEnd;
                }
                else
                {
                    Debug.LogWarning($"Harvestable {item?.itemID} from {gameObject.name} could not be set in automation"
                                     + $" system at pos {transform.position}.", this.gameObject);
                }
            }
        }

        private void OnConsumed(object sender, AutomationResourceEventArgs e)
        {
            if (e.eventType == AutomationResourceEventArgs.EventType.FullyHarvested)
            {
                HarvestComplete();
            }
            else
            {
                StopAudio();

                // Set inactive to destroy the tooltip
                SetInactive();

                // Destorying with slight delay to allow any other listeners to handle the completion of the harvest
                DestroyGameObject(0.05f);
            }
        }

        private void OnPullStart(object sender, AutomationPullEventArgs e)
        {
            _animator.enabled = true;

            Vector3 directionOfPull = e.directionOfPull;
            _animator.SetBool("harvesting", true);

            if (directionOfPull.z == 1)
                _animator.SetTrigger("harvesting_r");//0,0,1 OR NE
            else if (directionOfPull.x == -1)
                _animator.SetTrigger("harvesting_l");//(-1,0,0) OR NW
            else if (directionOfPull.z == -1)
                _animator.SetTrigger("harvesting_l");// (-1,0,0) OR SW
            else if (directionOfPull.x == 1)
                _animator.SetTrigger("harvesting_r");//(1,0,0) OR SE
        }

        private void OnPullEnd(object sender, EventArgs e)
        {
            _animator.SetBool("harvesting", false);
        }

        private void OnExternalHealthTookDamage(object sender, HealthEventArgs e)
        {
            TakeHarvestDamageFromPlayer(e.damage, AnimationController.CharacterDirections.back);
        }

        public virtual void SetGridSpace(AutomationGridSpace space)
        {
            bool isOnProcessor = space.itemProcessor != null;

            if (!lockToGrid && !isOnProcessor)
            {
                transform.position = transform.position + UnityUtils.RandomXZVector(0.8f);
            }

            _autoResource = space.Resource;
            _autoResource.Consumed += OnConsumed;
            _autoResource.PullStart += OnPullStart;
            _autoResource.PullEnd += OnPullEnd;
        }

        public override void PlayerInteraction(AutomationPlayerController player, UserActions action)
        {
            player.SetHarvestItem(gameObject);
        }


        public void TakeHarvestDamageFromPlayer(float damage, AnimationController.CharacterDirections playerFacingDir)
        {
            TakeHarvestDamage(damage, playerFacingDir, Health.DamageSourceEnum.PLAYER);
        }

        public void TakeHarvestDamage(float damage, AnimationController.CharacterDirections playerFacingDir, Health.DamageSourceEnum source)
        {
            if (_autoResource == null || (_autoResource != null && _autoResource.Health <= 0f))
            {
                return;
            }

            if (_animator != null && _validAnimatorParameters.Contains(Animator.StringToHash("harvesting")))
            {
                _animator.enabled = true;
                _animator.SetBool("harvesting", true);
            }

            // Force harvesting to stop if we dont take harvester damage in the next 0.5 seconds
            CancelInvoke(nameof(HarvestingStopped));
            Invoke(nameof(HarvestingStopped), 0.5f);

            TriggerHarvestAnimation(playerFacingDir);

            if (!string.IsNullOrEmpty(item.harvestingSoundRef))
            {
                if (item.harvestSoundIsLooping)
                {
                    _sounds.PlayLoop(item.harvestingSoundRef, transform.position);
                }
                else
                {
                    _sounds.PlayOneShot(item.harvestingSoundRef, transform.position);
                }
            }

            if (_autoResource.TakeDamage(damage, ref _outputItemsQueue))
            {
                bool autoCollect = source == Health.DamageSourceEnum.PLAYER && _playerUpgrades.AutoCollectEnabled;

                DropHarvestItems(autoCollect);

                if (!string.IsNullOrEmpty(item.harvestDropItemSoundRef))
                {
                    _sounds.PlayOneShot(item.harvestDropItemSoundRef, transform.position);
                }
            }

            if (_autoResource != null && _autoResource.Health > 0)
            {
                HarvestHealthChange();
            }
        }

        /// <summary>
        /// Use this method when item is taking harvest damage from a harvester, so when an item drops
        /// it is given to the harvester instead of just dropping on the ground.
        /// </summary>
        /// <param name="damage">Amount of harvest damge</param>
        /// <param name="harvesterContainer">Container attached to the harvester for the item to be deposited into.</param>
        public void TakeHarvesterDamage(float damage, ItemContainer harvesterContainer)
        {
            _harvesterContainer = harvesterContainer;

            TakeHarvestDamageFromPlayer(damage, AnimationController.CharacterDirections.right);
        }

        public void HarvestingStopped()
        {
            if (_animator != null && _validAnimatorParameters.Contains(Animator.StringToHash("harvesting")))
            {
                _animator.SetBool("harvesting", false);
            }

            StopAudio();
        }

        private void StopAudio()
        {
            if (!string.IsNullOrEmpty(item.harvestingSoundRef))
            {
                if (item.harvestSoundIsLooping)
                {
                    _sounds.StopLoop(item.harvestingSoundRef);
                }
            }
        }

        /// <summary>
        /// Gets called from TakeHarvestDamage using Invoke to trigger this after set delay
        /// </summary>
        /// <param name="charOnSide">Which side of the harvestable the character is on.</param>
        /// <returns></returns>
        private void TriggerHarvestAnimation(AnimationController.CharacterDirections playerFacingDir)
        {
            if (_animator == null)
            {
                return;
            }

            string trigger = "harvesting";

            switch (playerFacingDir)
            {
                case AnimationController.CharacterDirections.left:
                case AnimationController.CharacterDirections.down_left:
                    trigger += "_r";
                    break;
                case AnimationController.CharacterDirections.right:
                case AnimationController.CharacterDirections.down_right:
                    trigger += "_l";
                    break;
                case AnimationController.CharacterDirections.back:
                case AnimationController.CharacterDirections.front:
                    trigger += "_r";
                    break;
                default:
                    Debug.LogWarning($"Direction {playerFacingDir} passed to TriggerHarvestAnimation has no handler, default direction used");
                    trigger += "_r";
                    break;
            }

            if (_validAnimatorParameters.Contains(Animator.StringToHash(trigger)))
            {
                _animator.enabled = true;
                _animator.SetTrigger(trigger);
            }
        }

        public void HarvestHealthChange()
        {
            // Some items change visual states depending on how much harvesting has occured.
            for (int i = 0; i < item.harvestHPVisualThresholds.Count; i++)
            {
                if (_autoResource.Health != 0 && item.harvestHPVisualThresholds[i] >= _autoResource.Health)
                {
                    if (icon == null)
                    {
                        Debug.LogError($"Icon property not set for item that has visual thresholds: {gameObject.name}");
                    }
                    else
                    {
                        icon.sprite = item.visualStates[i];
                    }
                }
            }

            UpdateProgressDisplay(_autoResource.HarvestPercent);
        }

        private void HarvestComplete()
        {
            StopAudio();

            if (!string.IsNullOrEmpty(item.harvestDropItemSoundRef))
            {
                _sounds.PlayOneShot(item.harvestDropItemSoundRef, transform.position);
            }

            if (destroyOnHarvest || !_autoResource.IsFullyGrown)
            {
                // Set inactive to destroy the tooltip
                SetInactive();

                // Destorying with slight delay to allow any other listeners to handle the completion of the harvest
                DestroyGameObject(0.05f);
            }
            else
            {
                if (item.visualStates.Count != 0)
                {
                    // Update to the last visual state
                    icon.sprite = item.visualStates[item.visualStates.Count - 1];
                }

                if (_animator != null)
                {
                    _animator.enabled = true;
                    _animator.SetTrigger("harvested_idle");
                }

                _interactive = false;

                DisableHarvestColliders();
            }

            _uiProgress.HideDisplay();

            _camController.AnimateCameraShake(CameraController.CameraShakeTriggers.shakeVeryLight);

            if (_afterHarvestedPrefab != null && _autoResource.IsFullyGrown)
            {
                // Mostly for the sapling
                Instantiate(_afterHarvestedPrefab, this.transform.position, Quaternion.identity);
            }
        }

        /// <summary>
        /// Finds all colliders on child objects that are on the Harvest layer and disables them to prevent
        /// harvesting item that's finished.
        /// </summary>
        private void DisableHarvestColliders()
        {
            Collider[] colliders = GetComponentsInChildren<Collider>();

            for (int i = 0; i < colliders.Length; i++)
            {
                if (colliders[i].gameObject.layer == Layers.HARVESTABLE)
                {
                    colliders[i].enabled = false;
                }
            }
        }

        private void DropHarvestItems(bool playerAutoCollect)
        {
            while (_outputItemsQueue.Count > 0)
            {
                AutomationCoreItem newItem = _outputItemsQueue.Dequeue();
                newItem.position = transform.position;

                _autoSystem.TryAddCoreItemToSpace(transform.position, newItem, playerAutoCollect);
            }
        }

        public override void ShowTooltip()
        {
            base.ShowTooltip();

            if (_customSelectionHighlight != null)
            {
                if(!_selectionHighlightTransform.IsNullOrDestroyed())
                {
                    _selectionHighlightTransform.gameObject.SetActive(false);
                }
                _customSelectionHighlight.gameObject.SetActive(true);
            }
        }

        public override void SetInactive()
        {
            base.SetInactive();

            if (_customSelectionHighlight != null)
            {
                _customSelectionHighlight.gameObject.SetActive(false);
            }
        }

        public GameObject GetGameObject()
        {
            return gameObject;
        }

        public override string GetItemName()
        {
            return item.itemName;
        }

        public override string GetToolTip(UserActions action)
        {
            switch (action.Name)
            {
                case nameof(UserActions.INTERACT):
                    if (_autoResource != null && !_autoResource.IsFullyGrown)
                        return item.GetToolTip(true);
                    if (!playerHarvestable && harvestedBy != null)
                        return $"{Loc.GetString(Constants.USER_ACTION_REQUIRES_X)} {harvestedBy.itemName}";

                    bool isPlayerHarvestable = _playerProgress.PlayerCanHarvest(item) || IsGrowing;

                    return item.GetToolTip(isPlayerHarvestable);
                default:
                    return "";
            }
        }

        public override List<UserActions> GetValidActions()
        {
            _actionList.Clear();

            if (playerHarvestable && _autoResource != null && _autoResource.IsHarvestable)
            {
                _actionList.Add(UserActions.INTERACT);
            }

            return _actionList;
        }

        public override Vector3 GetInteractionPosition(Vector3 playerPosition)
        {
            float direction = UnityUtils.GetRelativeHorizontalDirection(playerPosition, transform.position);

            // Getting a point to the side of the item to use to find nearest position on collider with.
            Vector3 offset = direction > 0 ? Constants.HARVEST_BEAM_ALIGN_RIGHT : Constants.HARVEST_BEAM_ALIGN_LEFT;

            Vector3 position = transform.position + offset;

            // Zero out y position so it's on ground
            position.y = 0f;

            // Check if position is on nav mesh
            if (NavMesh.SamplePosition(position, out NavMeshHit hit, 0.25f, Layers.NAV_MESH_WALKABLE_AREA_LAYER_MASK))
            {
                return position;
            }
            else
            {
                // If not, try the other direction
                position = transform.position - offset;

                if (NavMesh.SamplePosition(position, out hit, 0.25f, Layers.NAV_MESH_WALKABLE_AREA_LAYER_MASK))
                {
                    return position;
                }
                else
                {
                    // If still not on nav mesh, just return nearest point on nav mesh and hope player can pick up
                    return position;
                }
            }
        }

        public void DisableAnimator()
        {
            if (_animator != null)
            {
                _animator.enabled = false;
            }
        }

        private void UpdateProgressDisplay(float value)
        {
            if (_harvesterContainer == null && _uiProgress != null)
            {
                _uiProgress.SetProgress(value, transform);
            }
        }

        // Gizmos

        public void OnDrawGizmosSelected()
        {
            UnityUtils.DrawCircleRadius(transform.position + dropOffset, dropRange, Color.blue,
                                        new Color(0, 0, 0.5f, 0.1f), "Drop area", 0f);
        }
    }
}