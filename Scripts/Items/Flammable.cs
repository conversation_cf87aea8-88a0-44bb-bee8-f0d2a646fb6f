// Copyright Isto Inc.
using Isto.Core.Beings;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Items
{
    public class Flammable : MonoBehaviour
    {
        // Public Variables

        public List<ItemPile> itemDrops;
        public float dropSpread = 0f;
        public GameObject flamesPrefab;
        public float damagePerSecond = 1000f;

        // Private variables

        private bool _onFire;
        private Health _health;

        // Methods        

        private void Awake()
        {
            _health = GetComponent<Health>();
        }

        private void Update()
        {
            if (_onFire && _health != null)
            {
                _health.TakeDamage(damagePerSecond * Time.deltaTime, Health.DamageTypeEnum.FIRE, Health.DamageSourceEnum.PLAYER);
            }
        }

        public void HealthChanged(Health.DamageTypeEnum type)
        {
            if (type == Health.DamageTypeEnum.FIRE && !_onFire)
            {
                _onFire = true;
                StartOnFire();
            }
        }

        public void HealthGone(Health.DamageTypeEnum type)
        {
            if (type == Health.DamageTypeEnum.FIRE)
            {
                for (int i = 0; i < itemDrops.Count; i++)
                {
                    Vector3 dropOffset = UnityUtils.RandomXZVector(dropSpread);

                    itemDrops[i].item.DropItem(transform.position + dropOffset, null, itemDrops[i].count);
                }

                Destroy(gameObject);
            }
        }

        private void StartOnFire()
        {
            if (flamesPrefab != null)
            {
                Instantiate(flamesPrefab, transform.position, Quaternion.identity, transform);
            }
        }
    }
}