// Copyright Isto Inc.
using UnityEngine;

namespace Isto.Core.Items
{
    /// <summary>
    /// Used for scripts attached to items that have a forward direction for automation or other use.  Typically will
    /// get called by the ItemPlacement script when the item is placed.
    /// </summary>
	public interface IDirectionalItem
    {
        /// <summary>
        /// Updates the object to face the specified forward direction.
        /// Note that supported directions vary per item.
        /// </summary>
        /// <param name="forward">a normalized direction vector</param>
        void SetForwardDirection(Vector3 forward);
        Vector3 GetDirection();
    }
}