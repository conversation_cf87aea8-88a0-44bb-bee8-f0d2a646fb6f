// Copyright Isto Inc.
using Isto.Core.Beings;
using Isto.Core.Inputs;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Items
{
    public interface IInteractable
    {
        /// <summary> Returns a list of the actions that cause interactions with us, in order of priority. </summary>
        List<UserActions> GetValidActions();
        string GetToolTip(UserActions action);
        /// <summary> This seems to be how this object would learn that it is "active". </summary>
        void ShowTooltip();
        /// <summary> Called when we are done considering the object for interaction (e.g. displaying the tooltip). </summary>
        void SetInactive();
        void SetInteractive(bool state);
        Vector3 GetInteractionPosition(Vector3 playerPosition);
        void PlayerInteraction(AutomationPlayerController player, UserActions action);
    }
}