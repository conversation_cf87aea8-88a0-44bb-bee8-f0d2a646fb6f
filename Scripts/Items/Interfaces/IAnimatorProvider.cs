// Copyright Isto Inc.
using UnityEngine;

namespace Isto.Core.Items
{
    /// <summary>
    /// This interface is intended to be used when the logic to get the animator is too complex and we want to
    /// centralize it on a single component. This interface allows the other components that need the animator
    /// to simply ask for it from the correct source.
    /// (expected to be a monobehavior)
    /// </summary>
    public interface IAnimatorProvider
    {
        /// <summary>
        /// Gets the correct animator to use. Be aware that you might have to call this on the fly as which animator
        /// is in use is not guaranteed to be always the same.
        /// In Atrio this SHOULD only be able to change while you're in placement mode.
        /// </summary>
        public Animator GetAnimator();
    }
}