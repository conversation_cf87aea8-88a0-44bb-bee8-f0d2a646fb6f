// Copyright Isto Inc.
using Isto.Core.Beings;
using UnityEngine;

namespace Isto.Core.Items
{
    /// <summary>
    /// Base class for Upgrade effects which are attached to an UpgradeItem and are activated when the
    /// item is created.
    /// </summary>
	public abstract class UpgradeEffect : ScriptableObject
    {
        public abstract void ActivateEffect(AutomationPlayerController player);

        public abstract void DeactivateEffect(AutomationPlayerController player);
    }
}