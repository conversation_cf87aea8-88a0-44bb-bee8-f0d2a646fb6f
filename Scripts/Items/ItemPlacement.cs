// Copyright Isto Inc.
using Isto.Core.Inputs;
using System;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Items
{
    [RequireComponent(typeof(InteractableItem))]
    public class ItemPlacement : MonoBehaviour, IActivatable
    {
        // Public Variables

        public Transform arrowTransform;
        public Transform bodyTransform;
        public Transform dropTransform;

        public Material[] transparentMaterials;
        public Renderer[] objectRenderers;

        public bool rotateObject = false;

        public Vector3 Forward { get { return _forward; } }

        [Header("Placement Checks")]
        [Tooltip("Layers that the object cannot be placed over.")]
        public LayerMask collisionLayers;
        public Color invalidPlaceColor;

        public bool CanDrop { get { return _canDrop; } }


        // Private Variables

        private InteractableItem _itemController;
        private IControls _controls;
        private float _spamTimer;
        private Material[] _startMaterials;
        private bool _canDrop;
        private Vector3 _halfExtends;
        protected Vector3 _forward;
        private List<IDirectionalItem> _directionalScripts;
        private Color _startMaterialColor;


        // Lifecycle Events

        [Inject]
        public void Inject(IControls controls)
        {
            _controls = controls;
        }

        void Awake()
        {
            _itemController = GetComponent<InteractableItem>();

            _directionalScripts = new List<IDirectionalItem>();
            _halfExtends = new Vector3();
            _forward = Vector3.forward;

            SetStartMaterials();
        }

        void OnEnable()
        {
            _itemController.enabled = false;
            _spamTimer = 0f;

            if (transparentMaterials.Length != 0)
            {
                if (transparentMaterials[0].HasProperty("_Color"))
                {
                    _startMaterialColor = transparentMaterials[0].color;
                }
                else if (transparentMaterials[0].HasProperty("_Tint"))
                {
                    _startMaterialColor = transparentMaterials[0].GetColor("_Tint");
                }

                SetMaterialForObject(transparentMaterials);
            }

            _directionalScripts.AddRange(GetComponents<IDirectionalItem>());
        }

        private void OnDisable()
        {
            if (arrowTransform != null)
            {
                arrowTransform.gameObject.SetActive(false);
            }
        }

        void Update()
        {
            _spamTimer += Time.deltaTime;

            if (_spamTimer > Constants.BUTTON_SPAM_DELAY)
            {
                HandleRotationInput();
            }

            _canDrop = AtValidDropPosition();
        }


        // Methods

        private void HandleRotationInput()
        {
            if (_controls.GetButton(UserActions.ROTATELEFT))
            {
                Rotate(-1);
                _spamTimer = 0f;
            }
            else if (_controls.GetButton(UserActions.ROTATERIGHT))
            {
                Rotate(1);
                _spamTimer = 0f;
            }
        }

        protected virtual void Rotate(int direction)
        {
            if (arrowTransform != null)
            {
                arrowTransform.Rotate(Vector3.up, direction * 90f, Space.World);
            }

            if (rotateObject)
            {
                CoreItem item = _itemController.itemPile.item;

                float itemSize;

                if (item.GetType() == typeof(AdvancedItem))
                {
                    AdvancedItem advItem = item as AdvancedItem;
                    itemSize = Mathf.Max(advItem.width, advItem.depth);
                }
                else
                {
                    itemSize = 1;
                }

                Vector3 rotationPivot = transform.position + new Vector3(-itemSize / 2, 0f, itemSize / 2);

                bodyTransform.RotateAround(rotationPivot, Vector3.up, direction * 90f);
            }

            Quaternion rotation = Quaternion.AngleAxis(direction * 90f, Vector3.up);

            _forward = rotation * _forward;
            UpdateForwardInScripts(_forward);

            if (dropTransform != null)
            {
                Vector3 offsetFromPivot = dropTransform.localPosition;

                Vector3 rotatedOffset = rotation * offsetFromPivot;

                dropTransform.localPosition = rotatedOffset;
            }
        }

        private void SetMaterialForObject(Material[] materials)
        {
            if (objectRenderers.Length != materials.Length)
            {
                Debug.LogError("Length of Material arrays don't match. This should not happen");
                return;
            }

            for (int i = 0; i < objectRenderers.Length; i++)
            {
                Renderer current = objectRenderers[i];

                current.material = materials[i];
            }
        }

        private void SetStartMaterials()
        {
            _startMaterials = new Material[objectRenderers.Length];

            for (int i = 0; i < _startMaterials.Length; i++)
            {
                _startMaterials[i] = objectRenderers[i].material;
            }
        }

        private bool AtValidDropPosition()
        {
            AdvancedItem item = _itemController.itemPile.item as AdvancedItem;

            if (item != null)
            {
                // Determine size of box to check for collisions
                float itemHalfSize = Mathf.Max(item.depth, item.width) / 2;

                // Giving default height of 0.5 to collide with objets slightly above ground as well
                _halfExtends.Set(itemHalfSize, 0.5f, itemHalfSize);

                // Finding 'center' of object based on depth and width to determine where to do overlap check from
                Vector3 itemCenter = transform.position;
                itemCenter.x -= itemHalfSize;
                itemCenter.z += itemHalfSize;

                Collider[] hits = Physics.OverlapBox(itemCenter, _halfExtends, Quaternion.identity, collisionLayers);

                if (hits.Length > 0)
                {
                    SetSpriteColor(invalidPlaceColor);

                    return false;
                }
                else
                {
                    SetSpriteColor(_startMaterialColor);
                    return true;
                }
            }

            // Note: we only checked for placement issues if it's an advanced item. If it's not, we probably still
            // should check for any object that can't have an item placed on it. This is probably why our stun bombs
            // can stack for instance.
            // Also: see similar note in ItemPlaceState, try to avoid code duplication.

            return true;
        }

        private void SetSpriteColor(Color color)
        {
            for (int i = 0; i < objectRenderers.Length; i++)
            {
                Material currentMaterial = objectRenderers[i].material;

                if (currentMaterial.HasProperty("_Color"))
                {
                    currentMaterial.color = color;
                }
                else if (currentMaterial.HasProperty("_Tint"))
                {
                    currentMaterial.SetColor("_Tint", color);
                }

                objectRenderers[i].material.color = color;
            }
        }

        protected void UpdateForwardInScripts(Vector3 forward)
        {
            for (int i = 0; i < _directionalScripts.Count; i++)
            {
                _directionalScripts[i].SetForwardDirection(forward);
            }
        }

        public void Activate()
        {
            SetMaterialForObject(_startMaterials);

            enabled = false;
        }

        public void SetStartRotation(float angle)
        {
            if (angle % 90 != 0)
            {
                Debug.LogError("Angle passed to set start rotation should be a multiple of 90. Angle: " + angle);
                return;
            }

            int rotations = Math.Abs((int)(angle / 90));

            // Check that direction scripts is set and if not, look for any components that implement it.
            if (_directionalScripts == null || _directionalScripts.Count == 0)
            {
                _directionalScripts.AddRange(GetComponents<IDirectionalItem>());
            }

            for (int i = 0; i < rotations; i++)
            {
                Rotate(-1 * Math.Sign(angle));
            }
        }
    }
}