// Copyright Isto Inc.
using I2.Loc;
using Isto.Core.Localization;
using UnityEngine;

namespace Isto.Core.Items
{
    public abstract class Buff : ScriptableObject
    {
        public string BuffName { get { return Loc.Get(buffnameLoc); } }
        public bool IsActive => _isActive;

        [Header("--------- Standard Fields")]
        [SerializeField] private LocalizedString buffnameLoc;
        public Sprite icon;

        protected bool _isActive = false;

        public abstract void ActivateBuff(GameObject consumer);

        public abstract void DeactivateBuff(GameObject consumer);

        public virtual void UpdateBuff(GameObject consumer) { }
    }
}