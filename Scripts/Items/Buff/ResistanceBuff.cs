// Copyright Isto Inc.
using Isto.Core.Beings;
using Isto.Core.Enums;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;

namespace Isto.Core.Items
{
    /// <summary>
    /// Some items give the player a buff (resistances, heal over time)
    /// </summary>
    [CreateAssetMenu(fileName = "Buff_Resistance_", menuName = "Scriptables/ItemEffect/Buff-Resistance")]
    public class ResistanceBuff : Buff
    {
        // UNITY HOOKUP

        [Header("--------- Resistance")]
        [SerializeField]
        [FormerlySerializedAs("damageType")]
        [EnumDropdown(typeof(Health.DamageTypeEnum))]
        private int _damageType;

        [Tooltip("0 = no resistance, 1 = full resistance")]
        public float resistanceAmount = 0;


        // PROPERTIES

        public Health.DamageTypeEnum DamageType => Health.DamageTypeEnum.GetByValue(_damageType);


        // OTHER METHODS

        public override void ActivateBuff(GameObject consumer)
        {
            if (consumer.TryGetComponent(out PlayerHealth playerHealth))
            {
                //Error checking for duplicates
                int buffIndex = FindBuffIndex(playerHealth.activeResistances);
                if (buffIndex != -1)
                {
                    Debug.LogError("Trying to add a duplicate resistance. You can only have one resistance per damage " +
                                   "type. Removing the old resistance.");
                    playerHealth.activeResistances.RemoveAt(buffIndex);
                }
                else
                {
                    playerHealth.activeResistances.Add(this);
                }
            }
        }

        public override void DeactivateBuff(GameObject consumer)
        {
            if (consumer.TryGetComponent(out PlayerHealth playerHealth))
            {
                int buffIndex = FindBuffIndex(playerHealth.activeResistances);
                if (buffIndex != -1)
                {
                    playerHealth.activeResistances.RemoveAt(buffIndex);
                }
            }
        }

        public int FindBuffIndex(List<ResistanceBuff> buffList)
        {
            for (int i = 0; i < buffList.Count; i++)
            {
                if (buffList[i]._damageType == _damageType)
                {
                    return i;
                }
            }
            return -1;
        }

        public float ModifyDamage(Health.DamageTypeEnum type, float damage)
        {
            if (resistanceAmount < 0 || resistanceAmount > 1)
            {
                Debug.LogWarning($"Buff {this.BuffName}: Resistance Must be between 0 and 1");
                resistanceAmount = Mathf.Clamp01(resistanceAmount);
            }

            if (_damageType == type.Value)
            {
                return damage - (damage * resistanceAmount);
            }
            else
            {
                return damage;
            }
        }
    }
}
