// Copyright Isto Inc.
using Isto.Core.Beings;
using UnityEngine;

namespace Isto.Core.Items
{
    /// <summary>
    /// Some items give the player a buff (resistances, heal over time)
    /// </summary>
    [CreateAssetMenu(fileName = "Buff_HealOverTime_", menuName = "Scriptables/ItemEffect/Buff-HealOverTime")]

    public class HealOverTimeBuff : Buff
    {
        [Header("--------- Heal Over Time")]
        public float healthPerSecond;
        private Health _healthComponent;

        public override void ActivateBuff(GameObject consumer)
        {
            _isActive = true;

            if (consumer.TryGetComponent(out Health health))
            {
                _healthComponent = health;
            }
        }

        public override void DeactivateBuff(GameObject consumer)
        {
            _isActive = false;
        }

        public override void UpdateBuff(GameObject consumer)
        {
            if (_isActive)
            {
                //Heal over time
                if (_healthComponent != null)
                {
                    _healthComponent.Heal(healthPerSecond * Time.deltaTime);
                }
            }
        }
    }
}