//Copyrite Isto Inc. 2018
using FMODUnity;
using Isto.Core.Configuration;
using Isto.Core.Items;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AddressableAssets;

namespace Isto.Core
{
    /// <summary>
    /// Represents any item that can be placed in a container.
    /// 
    /// Item -> CoreItem -> AdvancedItem
    /// Item -> HarvestableItem
    /// </summary>
    [CreateAssetMenu(fileName = "New Core Item", menuName = "Scriptables/Item/CoreItem")]
    public class CoreItem : Item
    {
        // Public Variables
        [Header("--------------  Core Item")]
        public Sprite gameplaySprite;
        [SerializeField]
        [Tooltip("Recipe required to craft the item, if possible")]
        private Recipe _craftingRecipe;
        [Tooltip("If set, is used when the item is made via a factory instead of by the player")]
        [SerializeField] private Recipe _factoryCraftingRecipe;

        [Header("Emission")]
        public Texture emissionSprite;
        public float emissionFactor;
        public Color emissionTint;

        [Header("Harvesting")]
        public HarvestableItem harvestableSource;
        public bool fromHarvestableItem;

        [Header("Systems Interactions")]
        public bool flammable = false;
        public List<ItemPile> flammableItemDrops;
        public float fireDropSpread = 0f;

        [Header("For Internal Use")]
        [Tooltip("Used in DevMode Scene to calculate base resource amounts")]
        public bool baseLevelItem = false;
        public bool hasSeedItem = false;

        [Header("Display Override")]
        public AssetReference coreItemAssetOverride;

        [Header("Special Unlock Conditions")]
        [Tooltip("The item will always be considered locked in these game modes")]
        public GameModeDefinition[] forbiddenGameModes;

        // Methods		

        public void Consumed()
        {
            //Action performed when consumed
        }

        /// <summary>
        /// Creates a new instance of the item and places it at the specified position and activates it if necessary.
        /// </summary>
        /// <param name="position"></param>
        /// <returns></returns>
        protected virtual GameObject DropItem(Vector3 position, Transform parent, int count = 1)
        {
            GameObject newItem = CreateItemFromPrefab(prefab, position, count);
            newItem.name = itemName;

            newItem.transform.SetParent(parent);

            InstallSystems(newItem);

            IActivatable[] activateScripts = newItem.GetComponents<IActivatable>();

            for (int i = 0; i < activateScripts.Length; i++)
            {
                activateScripts[i].Activate();
            }

            return newItem;
        }

        public virtual GameObject DropItem(Vector3 position, Transform parent = null, int count = 1, bool autoCollect = false)
        {
            GameObject newItem = DropItem(position, parent, count);

            if (newItem.TryGetComponent(out ItemSpawnBehaviour spawnBehaviour))
            {
                spawnBehaviour.StartItemBounce(true);
            }

            return newItem;
        }

        /// <summary>
        /// Creates the item prefab and sets the item in the ItemController component.
        /// </summary>
        /// <param name="position">Position to spawn the object</param>
        /// <param name="count">Number of items in the pile to set in the ItemController component.</param>
        /// <returns>The created GameObect</returns>
        protected GameObject CreateItemFromPrefab(GameObject itemPrefab, Vector3 position, int count = 1)
        {
            // Prefab should have Zenject auto injector for injection if needed
            GameObject newItem = Instantiate(itemPrefab, position, Quaternion.identity, null);

            ItemController itemControl = newItem.GetComponent<ItemController>();

            if (itemControl != null)
            {
                itemControl.SetItem(this, count);
                itemControl.SetPropertyBlockSet(emissionSprite, emissionFactor, emissionTint);
            }

            return newItem;
        }

        /// <summary>
        /// Method to allow setting recipe from code, should only be used for Unit tests
        /// </summary>
        /// <param name="recipe"></param>
        public void SetRecipe(Recipe recipe)
        {
            _craftingRecipe = recipe;
        }

        public bool ForbiddenForCurrentGameMode(GameModeDefinition mode)
        {
            if (forbiddenGameModes == null)
            {
                return false;
            }

            if (forbiddenGameModes.Length == 0)
            {
                return false;
            }
            
            for (int i = 0; i < forbiddenGameModes.Length; i++)
            {
                if (mode == null)
                {
                    Debug.LogError("game mode is missing for this particular scene. Make sure to assign this scene a game mode in scene settings mapping");
                    return false;
                }

                if (forbiddenGameModes[i] == null)
                {
                    continue;
                }

                if (forbiddenGameModes[i].InternalName == mode.InternalName)
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Gets the recipe for this item. Currently only the mini deer factory uses the special factory 
        /// crafting recipe so that is what the bool is for
        /// </summary>
        /// <param name="isMiniDeerFactory">If set, tries to get the factory recipe from the item, otherwise just returns the normal recipe</param>
        /// <returns>Recipe for the item</returns>
        public Recipe GetRecipe(bool isMiniDeerFactory = false)
        {
            if (isMiniDeerFactory && _factoryCraftingRecipe != null)
            {
                return _factoryCraftingRecipe;
            }
            else
            {
                return _craftingRecipe;
            }
        }

        private void InstallSystems(GameObject newItem)
        {
            if (flammable)
            {
                Flammable flam = newItem.AddComponent<Flammable>();
                flam.itemDrops = flammableItemDrops;
                flam.dropSpread = fireDropSpread;
            }
        }

        // Static Methods

        public static bool AreEqual(CoreItem itemA, CoreItem itemB)
        {
            if (itemA == null && itemB == null)
            {
                return true;
            }

            if (itemA == null || itemB == null)
            {
                return false;
            }

            return itemA.itemID.Equals(itemB.itemID);
        }
    }
}
