// Copyright Isto Inc.
using Isto.Core.Automation;
using Isto.Core.Data;
using Isto.Core.Research;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Zenject;

namespace Isto.Core.Items
{
    /// <summary>
    /// Interface for automation items that handle moving or processing items.  Has functions for checking if the processor is available for an
    /// item to be moved to it or added to its inventory
    /// </summary>
    public interface IItemProcessor
    {
        string ProcessorID { get; }

        // This determines if the item's visibility will be handled by the culling system or not, the name could be better
        // I think the intent was "is this supposed to be visible? if so it needs to be handled via culling"
        // However in reality this flag allows us to have visible items independend from the culling system as well as hiding some items
        // And we can't easily rename it since it is serialized in save data
        bool Visible { get; }

        /// <summary>
        /// Do we have some space available for the specified item?
        /// </summary>
        int IsAvailable(AutomationCoreItem movingItem);
        void Tick(float deltaTime);

        AutomationProcessorData GetSaveData();
        void LoadData(AutomationProcessorData data);
    }

    public class AutomationProcessorFactory : PlaceholderFactory<AutomationProcessorData, IItemProcessor> { }

    /// <summary>
    /// Custom Zenject Factory to take the base data class and create the proper Item processor based on it's type
    /// </summary>
    public class CustomProcessorFactory : IFactory<AutomationProcessorData, IItemProcessor>
    {
        private DiContainer _container;

        public CustomProcessorFactory(DiContainer container)
        {
            _container = container;
        }

        public IItemProcessor Create(AutomationProcessorData param)
        {
            switch (param)
            {
                case AutomationHarvesterData d:
                    AutomationHarvester.Factory harvesterFactory = _container.Resolve<AutomationHarvester.Factory>();
                    AutomationHarvester harvester = harvesterFactory.Create(new AutomationHarvesterParams(d.harvesterDPS, d.typeId, d.destroyOnHarvestComplete));
                    harvester.LoadData(d);
                    return harvester;
                case AutomationItemPusherData d:
                    AutomationItemPusher.Factory pushFactory = _container.Resolve<AutomationItemPusher.Factory>();
                    AutomationItemPusher pusher = pushFactory.Create(new AutomationItemPusherParams(d.distance, d.pushInterval, d.currentCount, d.typeId));
                    pusher.LoadData(d);
                    return pusher;
                case AutomationItemSinglePullerData d: // Child class to AutomationItemMoverData so order is important
                    AutomationItemSinglePuller.Factory singlePullerFactory = _container.Resolve<AutomationItemSinglePuller.Factory>();
                    AutomationItemSinglePuller singlePuller = singlePullerFactory.Create(d.distance, d.moveType, d.typeId);
                    singlePuller.LoadData(d);
                    return singlePuller;
                case AutomationItemMoverData d:
                    AutomationItemMover.Factory moverFactory = _container.Resolve<AutomationItemMover.Factory>();
                    AutomationItemMover mover = moverFactory.Create(d.distance, d.moveType, d.typeId);
                    mover.LoadData(d);
                    return mover;
                case AutomationFactoryData d:
                    List<Vector3> footPrint = GetFootprintForAutomationFactory(d.typeId, d.direction);
                    AutomationFactory.Factory factoryFactory = _container.Resolve<AutomationFactory.Factory>();
                    Recipe outputRecipe = _container.Resolve<MasterItemList>().GetItemByID<CoreItem>(d.recipeItemId).GetRecipe();
                    AutomationFactory factory = factoryFactory.Create(new AutomationFactoryParams(AutomationFactory.FACTORY_PILE_COUNT, AutomationFactory.FACTORY_PILE_SIZE, outputRecipe, d.typeId, footPrint));
                    factory.LoadData(d);
                    return factory;
                case AutomationItemContainerData d:
                    AutomationItemContainer.Factory containerFactory = _container.Resolve<AutomationItemContainer.Factory>();
                    AutomationItemContainer itemContainer = containerFactory.Create(new AutomationItemContainerParams(d.inventoryPileCount, d.inventoryPileSize, d.typeId, d.isRigidInventory, d.pullsItemsFromNeighbors));
                    itemContainer.LoadData(d);
                    return itemContainer;
                case AutomationPlanterData d:
                    AutomationPlanter.Factory planterFactory = _container.Resolve<AutomationPlanter.Factory>();
                    AutomationPlanter planter = planterFactory.Create(AutomationPlanterData.GetParamsFromData(_container.Resolve<MasterItemList>(), d));
                    planter.LoadData(d);
                    return planter;
                case AutomationItemPullerData d:
                    AutomationItemPuller.Factory pullerFactory = _container.Resolve<AutomationItemPuller.Factory>();
                    AutomationItemPuller puller = pullerFactory.Create(new AutomationItemPullerParams() { ID = d.typeId, forward = d.direction, validResources = d.affectedHarvestableItemIDs.ToList() });
                    puller.LoadData(d);
                    return puller;
                case AutomationResearchBoosterData d:
                    AutomationResearchBooster.Factory boosterFactory = _container.Resolve<AutomationResearchBooster.Factory>();
                    PlayerResearchModule researchModule = _container.Resolve<PlayerResearchModule>();
                    ResearchBoosterItemMappings mappings = _container.Resolve<ResearchBoosterItemMappings>();
                    AutomationResearchBooster booster = boosterFactory.Create(new AutomationResearchBoosterParams(d.typeId, researchModule, mappings));
                    booster.LoadData(d);
                    return booster;
                case AutomationCreatureHiveData d:
                    AutomationCreatureHive.Factory hiveFactory = _container.Resolve<AutomationCreatureHive.Factory>();
                    AutomationCreatureHive hive = hiveFactory.Create(new AutomationCreatureHiveParams(d.typeId, d.creatureAssetGUID, d.stockpiledItemID, d.inventoryPileCount, d.inventoryPileSize, d.maxCreatureCount, d.timePerSapwn, d.creatureRange, d.itemDroppingPosition, d.direction));
                    hive.LoadData(d);
                    hive.SpawnInitialCreatures(d.currentCreaturesData);
                    return hive;
                case AutomationItemDispenserData d:
                    AutomationItemDispenser.Factory dispenserFactory = _container.Resolve<AutomationItemDispenser.Factory>();
                    AutomationItemDispenser dispenser = dispenserFactory.Create(new AutomationItemDispenser.ItemDispenserParams(d.typeId, d.inventoryPileCount, d.inventoryPileSize, d.timePerDrop, d.timeToNextDrop, d.direction));
                    dispenser.LoadData(d);
                    return dispenser;
                default:
                    AutomationGenericProcessor.Factory genericFactory = _container.Resolve<AutomationGenericProcessor.Factory>();
                    AutomationGenericProcessor generic = genericFactory.Create(param.typeId);
                    generic.LoadData(param);
                    return generic;
            }
        }

        // Large items take up more spaces in world than they do in automation.
        // The automation system processors need that footprint info to be able to look at their occupied spaces.
        // Since we're creating these processors straight from save data, we don't have a good way to differenciate factory types.
        // This method helps us reverse lookup the correct footprint data for each factory.
        public static List<Vector3> GetFootprintForAutomationFactory(string itemID, Vector3 direction)
        {
            List<Vector3> footPrint = new List<Vector3>();
            if (itemID == "MiniDeerCaptured_v2")
            {
                AutomationFactory.GetMiniDeerFootprint(direction, ref footPrint);
            }
            else if (itemID == "Factory_v2" || itemID == "ElectronicsFactory_V2" || itemID == "Smelter_v2")
            {

                AutomationFactory.GetFootprint(direction, ref footPrint);
            }
            else
            {
                // Old factories should not need to be supported but they were 1x1 so footprint should be correct as empty.
                // We will also assume that if a unhandled type of factory exists it's the same case.
            }

            return footPrint;
        }
    }

}