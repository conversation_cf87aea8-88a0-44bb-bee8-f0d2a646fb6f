// Copyright Isto Inc.

using UnityEngine;

namespace Isto.Core.Networking
{
    // In considerum:
    // If we lean more on the idea that this class does strictly nothing, it would be a Dummy.
    // However if we lean more to the side of it being a non-networked version, it should be InactiveNetworkManager
    public class DummyNetworkManager : INetworkManager
    {
        // LIFECYCLE EVENTS

        public DummyNetworkManager()
        {

        }

        ~DummyNetworkManager()
        {

        }


        // OTHER METHODS

        public bool IsMultiplayerAvailable()
        {
            return false;
        }

        public GameObject Spawn(GameObject prefab, Vector3 position, Quaternion rotation)
        {
            // Probably should not happen if we're not making multiplayer available?
            Debug.LogWarning("DummyNetworkManager not expecting to have support a method like Spawn.");

            // I'm hesitating but for now let's have it still comply. After all, spawning a single player object
            // should amount to a normal instantiation. This may be relevant to support eventually - hopefully
            // we'll come back on this question at such a time that the plan around INetworkManager is clearer.
            GameObject instance = GameObject.Instantiate(prefab, position, rotation);
            return instance;
        }
    }
}