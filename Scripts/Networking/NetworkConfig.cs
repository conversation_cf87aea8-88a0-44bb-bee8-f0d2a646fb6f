// Copyright Isto Inc.

using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace Isto.Core.Networking
{
    [CreateAssetMenu(fileName = ASSET_NAME, menuName = "Scriptables/New Network Config")]
    public class NetworkConfig : ScriptableObject
    {
        /// <summary>
        /// In dev mode you should see these as room options in the list so you don't have to enter a code.
        /// At all times these codes will be forbidden for player room creation or joining so it is always
        /// available to the devs even in production.
        /// </summary>
        [Serializable]
        public class RoomConfig
        {
            public string name;
            public string code = "ABCD";
            public bool autoJoin = false;
        }


        // UNITY HOOKUP

        [Header("Matchmaking")]
        [SerializeField] private int _maxPlayers = 4;
        [SerializeField] private int _joinCodeLength = 4;
        [SerializeField] private string _joinCodeCharWhitelist = "abc";

        [Header("Dev Options")]
        [SerializeField] private bool _singlePlayerTesting;
        [SerializeField] private bool _showConnectMenuOnGui;
        [SerializeField] private List<RoomConfig> _devRooms;


        // OTHER FIELDS

        private const string ASSET_NAME = "NetworkConfig";
        // TODO: figure out if we're liking this asset path style instead of resources folder,
        // or maybe look into making a project specific path registry in a scriptable?
        private static readonly string ASSET_DEFAULT_PATH = $"Assets/Scriptables/{ASSET_NAME}.asset";


        // PROPERTIES

        public int MaxPlayers => _maxPlayers;
        public int JoinCodeLength => _joinCodeLength;
        public string JoinCodeWhitelist => _joinCodeCharWhitelist;
        public bool ShowConnectMenuOnGui => _showConnectMenuOnGui;
        public List<RoomConfig> DevRooms => _devRooms;
        public RoomConfig AutoJoinRoom => _devRooms.Find(r => r.autoJoin);

        // Allow this one to be set through code since we have editor tools that want to toggle this
        public bool SinglePlayerTesting
        {
            get { return _singlePlayerTesting; }
            set { _singlePlayerTesting = value; }
        }


        // LIFECYCLE

        private void OnValidate()
        {
            int autojoiners = 0;
            foreach (RoomConfig room in _devRooms)
            {
                if (room.autoJoin)
                {
                    autojoiners++;
                }
            }

            if (autojoiners > 1)
            {
                Debug.LogError($"Fount {autojoiners} rooms with autoJoin enabled in NetworkConfig, you should select only one!");
            }
        }


        // OTHER METHODS

        public static NetworkConfig LoadFromDefaultPath()
        {
            return LoadFromPath(ASSET_DEFAULT_PATH);
        }

        public static NetworkConfig LoadFromPath(string path)
        {
            NetworkConfig config = (NetworkConfig)AssetDatabase.LoadAssetAtPath(path, typeof(NetworkConfig));
            if (config == null)
            {
                Debug.LogError($"Could not find NetworkConfig file at path: {path}");
            }
            return config;
        }
    }
}