// Copyright Isto Inc.

using Isto.Core.Enums;

namespace Isto.Core.Networking
{
    // Extend this enumeration in your project and do not use the auto-increment constructor?
    public class NetworkEventsEnum : Int32Enum<NetworkEventsEnum>
    {
        public static readonly NetworkEventsEnum DO_NOT_SEND = new NetworkEventsEnum(-1, nameof(DO_NOT_SEND));
        public static readonly NetworkEventsEnum TEST = new NetworkEventsEnum(0, nameof(TEST));

        // Parameterless
        public static readonly NetworkEventsEnum GAME_OVER = new NetworkEventsEnum(nameof(GAME_OVER));

        // Parametermore
        public static readonly NetworkEventsEnum HIGH_PRI_MESSAGE = new NetworkEventsEnum(nameof(HIGH_PRI_MESSAGE));
        public static readonly NetworkEventsEnum ENEMY_SPAWN = new NetworkEventsEnum(nameof(ENEMY_SPAWN));
        public static readonly NetworkEventsEnum PLAYER_DEATH = new NetworkEventsEnum(nameof(PLAYER_DEATH));


        /*
         * Core NetworkEvents to consider adding:
            PlayerJoined
            PlayerLost
            PlayerReturned
            HostChanged
            MatchBegin
            MatchEnd
            Pause
            LevelChange
         */

        public NetworkEventsEnum(int value, string name) : base(value, name)
        {

        }

        public NetworkEventsEnum(string name) : base(name)
        {
        }
    }
}