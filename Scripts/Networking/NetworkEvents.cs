// Copyright Isto Inc.

namespace Isto.Core.Networking
{
    /// <summary>
    /// Easy access helper for allowing you to respond to network events.
    /// Will never fire if there is not a INetworkManager in the project.
    /// </summary>
    public static class NetworkEvents
    {
        public static string GetEventNameFromNetworkEvent(NetworkEventsEnum e)
        {
            return "NETWORKEVENTS_" + e.Name;
        }

        public static void Subscribe(NetworkEventsEnum e, OrderedEvent.GameEvent callback)
        {
            string eventName = GetEventNameFromNetworkEvent(e);
            Events.Subscribe(eventName, callback);
        }

        public static void SubscribeWithParams(NetworkEventsEnum e, OrderedParamEvent.ParamGameEvent callback)
        {
            string eventName = GetEventNameFromNetworkEvent(e);
            Events.SubscribeWithParams(eventName, callback);
        }

        public static void UnSubscribe(NetworkEventsEnum e, OrderedEvent.GameEvent callback)
        {
            string eventName = GetEventNameFromNetworkEvent(e);
            Events.UnSubscribe(eventName, callback);
        }

        public static void UnSubscribeWithParams(NetworkEventsEnum e, OrderedParamEvent.ParamGameEvent callback)
        {
            string eventName = GetEventNameFromNetworkEvent(e);
            Events.UnSubscribeWithParams(eventName, callback);
        }
    }
}