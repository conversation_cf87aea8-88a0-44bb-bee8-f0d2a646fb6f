// Copyright Isto Inc.

namespace Isto.Core.Networking
{
    public interface INetworkEventsHandler
    {
        public enum Targets
        {
            NoTarget,
            Self,
            AllOtherPlayers,
            AllPlayers,
            Server
        }

        // inherit from this class to add data
        public abstract class NetworkEvent
        {
            public NetworkEventsEnum id;
            public Targets target;

            /// <summary>
            /// Should return easily serializable types only for networking compatibility.
            /// </summary>
            /// <returns></returns>
            public abstract object GetData();
        }

        public class SimpleMessage : NetworkEvent
        {
            public override object GetData()
            {
                return null;
            }
        }

        public void Send(NetworkEvent e);
        public void Receive(NetworkEventsEnum id, object data);
    }
}