// Copyright Isto Inc.

using UnityEngine;

namespace Isto.Core.Networking
{
    public interface INetworkManager
    {
        public bool IsMultiplayerAvailable();
        public GameObject Spawn(GameObject prefab, Vector3 position, Quaternion rotation);

        /*
         INetworkManager possible methods:
            OnEvent
            OnError
            IsOnline()
            
            GetConfig()
            GetAllPlayers()
            SetLogLevel()
            Connect()
            Disconnect()
            Host(Room)
            Join(Room)
            SendEvent(NetworkEvent)
         */
    }
}