// Copyright Isto Inc.

using Isto.Core.Networking;

public class DummyNetworkEvents : INetworkEventsHandler
{
    // LIFECYCLE EVENTS

    public DummyNetworkEvents()
    {

    }

    ~DummyNetworkEvents()
    {

    }


    // OTHER METHODS

    // These two methods could be at least set up to transmit the messages via the normal event system for local use
    // but this leaves me with the same dilemma as DummyNetworkManager. Is it a dummy handler or a offline handler?

    public void Receive(NetworkEventsEnum id, object data)
    {
    }

    public void Send(INetworkEventsHandler.NetworkEvent e)
    {
    }
}
