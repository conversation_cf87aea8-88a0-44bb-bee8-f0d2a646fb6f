// Copyright Isto Inc.

using Isto.Core.Automation;
using Isto.Core.Beings;
using Isto.Core.Cameras;
using Isto.Core.Cheats;
using Isto.Core.Configuration;
using Isto.Core.Data;
using Isto.Core.Game;
using Isto.Core.Items;
using Isto.Core.Localization;
using Isto.Core.UI;
using UnityEngine;
using UnityEngine.AI;
using UnityEngine.SceneManagement;
using Zenject;

namespace Isto.Core.Installers
{
    public class SimpleAutomationEssentialsInstaller : MonoInstaller<SimpleAutomationEssentialsInstaller>
    {
        [Header("Game Data")]
        public bool enableDataManagers = true;


        [Header("Player States (in project)")]
        public AutomationPlayerMoveState moveState;
        public PlayerInteractWithItemState interactState;
        public PlayerPlaceItemState placeItemState;
        public PlayerDismantleState dismantleState;
        public PlayerHarvestState harvestState;
        
        [Header("Languages")]
        public LanguageList supportedLanguages;

        [Header("Cheat Menu")]
        public CheatMenuState cheatMenuPrefab;


        // INJECTION

        CheatSettings _cheatSettings;

        [Inject]
        public void Inject(CheatSettings cheatSettings)
        {
            _cheatSettings = cheatSettings;
        }


        // OTHER METHODS

        public override void InstallBindings()
        {
            // We grab a reference to the settings first, in case the configuration has to modify the bindings
            GameplaySettings gameplaySettings = BindGameSettings();

            Container.Bind<CameraController>().FromComponentInHierarchy().AsSingle();

            BindPlayer();
            BindPlayerStates();

            BindAutomation();

            BindGUI();

            BindMasterItemListItems();

            BindDataManagers();

            BindLocalization();

            BindCheaterUI();
        }

        private GameplaySettings BindGameSettings()
        {
            GameplaySettings settingsToUse = null;

            GameState gameState = GameObject.FindObjectOfType<GameState>();
            GameModeDefinition currentMode = gameState?.CurrentGameMode;
            SceneSettingMappings settingsMapping = gameState?.settingsMapping;

            // Override takes priority
            /*if (settingsOverride != null)
            {
                settingsToUse = settingsOverride;
            }*/

            // Normally we get the settings from the game mode

            if (settingsToUse == null && currentMode != null
             && settingsMapping.TryGetSettings(currentMode, out GameplaySettings settingsFromCurrentMode))
            {
                settingsToUse = settingsFromCurrentMode;
                Debug.Log($"GameplaySettings: Game mode {currentMode?.InternalName ?? "(null)"} "
                        + $"requested gameplay settings: {settingsFromCurrentMode.name}");
            }

            // If a scene that is loaded exists in the settings mappings, use it to find any missing setup
            for (int i = 0; i < SceneManager.sceneCount; i++)
            {
                string sceneName = SceneManager.GetSceneAt(i).name;

                if (currentMode == null && settingsMapping.TryGetGameMode(sceneName, out GameModeDefinition modeFromCurrentScene) && gameState != null)
                {
                    gameState.CurrentGameMode = modeFromCurrentScene;
                    currentMode = gameState.CurrentGameMode;
                    Debug.LogWarning($"GameplaySettings: Unmapped Game Mode. Current scene \"{sceneName}\" associated to game mode \"{(modeFromCurrentScene != null ? modeFromCurrentScene.InternalName : "null")}\".");
                }

                if (settingsToUse == null && settingsMapping.TryGetSettings(sceneName, out GameplaySettings settingsFromCurrentScene))
                {
                    settingsToUse = settingsFromCurrentScene;
                    Debug.LogWarning($"GameplaySettings: Unmapped Game Mode. Current scene \"{sceneName}\" associated to gameplay settings \"{settingsFromCurrentScene.name}\".");
                }
            }

            // If no settings found use default
            if (settingsToUse == null)
            {
                settingsToUse = settingsMapping.defaultSettings;
                Debug.LogWarning($"GameplaySettings: No settings found for any game modes or scenes currently loaded, using default \"{settingsToUse.name}\"");
            }

            Container.Bind<GameplaySettings>().FromInstance(settingsToUse).AsSingle();
            return settingsToUse;
        }

        private void BindPlayer()
        {
            Container.Bind<AutomationPlayerController>().FromComponentInHierarchy().AsSingle();
            Container.Bind<IPlayerController>().FromComponentInHierarchy().AsSingle();
            Container.Bind<PlayerItemInteraction>().FromComponentInHierarchy().AsSingle();
            Container.Bind<PlayerInventory>().FromComponentInHierarchy().AsSingle();
            Container.Bind<NavMeshAgent>().FromComponentInHierarchy().AsSingle();
            Container.Bind<PlayerHealth>().FromComponentInHierarchy().AsSingle();
            //Container.Bind<IPlayerAnimationController>().FromComponentInHierarchy().AsSingle();
            Container.Bind<PlayerProgress>().FromComponentInHierarchy().AsSingle();
        }

        private void BindAutomation()
        {
            Container.BindFactory<CoreItemParams, AutomationCoreItem, AutomationCoreItem.Factory>();
            Container.BindFactory<AutomationResourceParams, AutomationResource, AutomationResource.Factory>();
            AutomationSystem system = new AutomationSystem(1, Container.Resolve<AutomationCoreItem.Factory>(), Container.Resolve<AutomationResource.Factory>());
            Container.Bind<AutomationSystem>().FromInstance(system).AsSingle();
        }

        private void BindGUI()
        {
            // UI Menu States
            Container.Bind<UISimpleMenusClosedState>().FromComponentInHierarchy().AsSingle();
            Container.Bind<UISimpleGameMenuState>().FromComponentInHierarchy().AsSingle();
            Container.Bind<SimpleGameMenuStateMachine>().FromComponentInHierarchy().AsSingle();
            Container.Bind<IUIGameMenu>().FromComponentInHierarchy().AsSingle();

            Container.Bind<UIAccessManager>().AsSingle();

            UIMessagesAsLogs messageHandlerDummy = new UIMessagesAsLogs();
            Container.Bind<IUIMessages>().WithId(UIMessageHandlerType.HighPriorityMessage).FromInstance(messageHandlerDummy);
            Container.Bind<IUIMessages>().WithId(UIMessageHandlerType.ProgressMessage).FromInstance(messageHandlerDummy);
            Container.Bind<IUIMessages>().WithId(UIMessageHandlerType.Message).FromInstance(messageHandlerDummy);
            Container.Bind<IUIDisplayMessage>().FromInstance(messageHandlerDummy).AsSingle();
        }

        private void BindPlayerStates()
        {
            Container.Bind<AutomationPlayerMoveState>().FromInstance(moveState).AsSingle();
            Container.QueueForInject(moveState);
            Container.Bind<PlayerInteractWithItemState>().FromInstance(interactState).AsSingle();
            Container.QueueForInject(interactState);
            Container.Bind<PlayerPlaceItemState>().FromInstance(placeItemState).AsSingle();
            Container.QueueForInject(placeItemState);
            Container.Bind<PlayerDismantleState>().FromInstance(dismantleState).AsSingle();
            Container.QueueForInject(dismantleState);
            Container.Bind<PlayerHarvestState>().FromInstance(harvestState).AsSingle();
            Container.QueueForInject(harvestState);
        }

        private void BindMasterItemListItems()
        {
            // This is bound in the project context scope, get it here to bind all the individual items
            MasterItemList itemList = Container.TryResolve<MasterItemList>();

            if (itemList == null)
                return;

            // Bind all the item scriptable objects
            for (int i = 0; i < itemList.items.Count; i++)
            {
                if (itemList.items[i] == null)
                {
                    Debug.LogError("Master Item List contains null entries, please check entry number: " + i);
                }
                else
                {
                    Container.QueueForInject(itemList.items[i]);

                    if (itemList.items[i] is UpgradeItem upgradeItem)
                    {
                        for (int j = 0; j < upgradeItem.effects.Count; j++)
                        {
                            if (upgradeItem.effects[j] != null)
                                Container.QueueForInject(upgradeItem.effects[j]);
                            else
                                Debug.LogError($"Null item effect on item: {upgradeItem.itemName}, please correct this");
                        }
                    }
                    else if (itemList.items[i] is UseableItem useableItem)
                    {
                        for (int k = 0; k < useableItem.effects.Count; k++)
                        {
                            if (useableItem.effects[k] != null)
                                Container.QueueForInject(useableItem.effects[k]);
                            else
                                Debug.LogError($"Null item effect on item: {useableItem.itemName}, please correct this", useableItem);
                        }
                    }
                }
            }
        }

        private void BindDataManagers()
        {
            if (!enableDataManagers)
                return;

            // Need to parent under this item to make sure it's created in the right scene when loading
            Transform dataManagersParent = new GameObject("Data Managers").transform;
            dataManagersParent.SetParent(transform);

            Container.Bind<PlayerDataManager>().FromNewComponentOn(dataManagersParent.gameObject).AsSingle().NonLazy();
        }

        private void BindLocalization()
        {
            Container.Bind<LanguageList>().WithId(InjectId.SUPPORTED_LANGUAGES).FromInstance(supportedLanguages).AsSingle();
            Container.QueueForInject(supportedLanguages);
            Container.Bind<ILocalizationProvider>().To<I2LocProvider>().AsSingle();
            Container.BindFactory<LocTerm.LocalizationType, string, LocTerm, LocTerm.Factory>().FromFactory<LocTermFactory>();
        }

        private void BindCheaterUI()
        {
            if (_cheatSettings.CheatsEnabled)
            {
                Container.Bind<CheatMenuState>().FromComponentsInNewPrefab(cheatMenuPrefab).AsSingle().NonLazy();
            }
        }
    }
}