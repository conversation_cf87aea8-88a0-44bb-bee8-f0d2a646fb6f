// Copyright Isto Inc.

using Isto.Core.UI;
using Zenject;

namespace Isto.Core.Installers
{
    public class SimpleTitleMenuSceneInstaller : MonoInstaller<SimpleTitleMenuSceneInstaller>
    {
        public override void InstallBindings()
        {
            Container.Bind<SimpleTitleMenuStateMachine>().FromComponentInHierarchy().AsSingle();
            Container.Bind<IUIMenu>().FromComponentInHierarchy().AsSingle();
        }
    }
}