// Copyright Isto Inc.

using Isto.Core.Achievements;
using Isto.Core.Analytics;
using Isto.Core.Audio;
using Isto.Core.Cheats;
using Isto.Core.Configuration;
using Isto.Core.Data;
using Isto.Core.Game;
using Isto.Core.Inputs;
using Isto.Core.Items;
using Isto.Core.Localization;
using Isto.Core.Networking;
using Isto.Core.Platforms;
using Isto.Core.Scenes;
using Isto.Core.Skins;
using Isto.Core.Speedrun;
using Isto.Core.Themes;
using Isto.Core.UI;
using Rewired.Integration.UnityUI;
using UnityEngine;
using UnityEngine.Serialization;
using Zenject;

namespace Isto.Core.Installers
{
    public class CoreProjectInstaller : MonoInstaller<CoreProjectInstaller>
    {
        // The master item list is expected to be a project configured asset with features not supported in Core.
        // Core needs to install a dummy version of it for testing. This helps us know when to do this.
        public static bool USE_CORE_TESTING_MASTER_ITEM_LIST = false;


        public GameObject eventSystemPrefab;
        public GameObject uiSoundsPrefab;
        public GameObject uiMusicPrefab;
        public GameObject gameSoundsCorePrefab;
        public GameObject gameStatePrefab;
        public GameObject analyticsManagerPrefab;
        public GameObject pcGameDataManagerPrefab;
        public GameScenesReference coreGameScenesReference;
        public UIModalPopup uiErrorPopupPrefab;
        public UIModalChoicePopup uiChoicePopupPrefab;

        [Header("Controls")]
        public GameObject controlsPrefab;
        public GameObject rewiredPrefab;
        public GameObject glyphsPrefab;

        [Header("Game Data")]
        public IGameData.GameDataLocation saveSlotLocation;
        public string fileNameSufix = "data.xml";

        [Header("Themes")]
        public ThemeSetup themeSetup;
        public ThemeManager themeManagerPrefab;

        [Header("Skins")]
        public MasterSkinList masterSkinList;

        [Header("Items")]
        [FormerlySerializedAs("masterItemList")] public MasterItemList masterItemList;
        [FormerlySerializedAs("coreMasterItemList")] public MasterItemList testMasterItemList;

        [Header("Languages")]
        public LanguageList supportedLanguages;

        [Header("Dev Console")]
        public CheatSettings cheatsSettings;
        [FormerlySerializedAs("developerConsolePrefab")]
        public GameObject cheatsPrefab;

        public override void InstallBindings()
        {
            InstallConfig();
            InstallLanguages();
            InstallDataManagers();

            InstallInputs();
            InstallAudio();
            InstallGUI();

            InstallThemes();
            InstallSkins();
            InstallItems();

            InstallAnalytics();

            InstallNetworking();

            InstallPlatformSpecificBindings();

            InstallDevConsole();
        }

        protected virtual void InstallConfig()
        {
            Container.Bind<GameScenesReference>().FromInstance(coreGameScenesReference).AsSingle();
            Container.Bind<Settings>().FromResource("CoreDefaultSettings");
            Container.Bind<SpeedrunSettings>().FromResource("CoreSpeedrunSettings");
            Container.Bind(typeof(GameState)).FromComponentInNewPrefab(gameStatePrefab).AsSingle().NonLazy();
        }

        protected virtual void InstallLanguages()
        {
            Container.Bind<LanguageList>().WithId(InjectId.SUPPORTED_LANGUAGES).FromInstance(supportedLanguages).AsSingle();

            // Localization
            Container.Bind<ILocalizationProvider>().To<I2LocProvider>().AsSingle().NonLazy();
            Container.BindFactory<LocTerm.LocalizationType, string, LocTerm, LocTerm.Factory>().FromFactory<LocTermFactory>();
        }

        protected virtual void InstallDataManagers()
        {
            string gameDataManagerName = "Game Data";
            Container.Bind<IGameData>().FromComponentInNewPrefab(pcGameDataManagerPrefab)
                                       .WithGameObjectName(gameDataManagerName).AsSingle().NonLazy();

            Container.Bind<string>().WithId(InjectId.SAVE_FILE_SUFFIX).FromInstance(fileNameSufix).AsSingle();

            Container.Bind<IGameData.GameDataLocation>().WithId("SaveSlotLocation")
                                                        .FromInstance(saveSlotLocation).AsSingle();
        }

        protected virtual void InstallInputs()
        {
            Container.Bind<IControls>().To<Controls>().FromComponentInNewPrefab(controlsPrefab).AsSingle();
            Container.Bind<RewiredStandaloneInputModule>().FromComponentInNewPrefab(eventSystemPrefab)
                                                          .AsSingle().NonLazy();
            Container.Bind<Rewired.InputManager>().FromComponentInNewPrefab(rewiredPrefab).AsSingle().NonLazy();
            Container.Bind<UserKeybindingDataStore>().FromComponentInHierarchy().AsSingle();
            Container.Bind<ControllerGlyphs>().FromComponentInNewPrefab(glyphsPrefab).AsSingle();
        }

        protected virtual void InstallAudio()
        {
            // One way or another the Core installer should maybe not rely on FMOD by default. These will change.
            Container.Bind<IGameMusic>().FromComponentInNewPrefab(uiMusicPrefab).UnderTransform(transform).AsSingle();
            Container.Bind<IGameVolume>().To<FMODGameVolume>().FromNewComponentOnNewGameObject().AsSingle();
            Container.Bind<UISounds>().FromComponentInNewPrefab(uiSoundsPrefab).UnderTransform(transform).AsSingle();
            Container.Bind<IGameSounds>().FromComponentInNewPrefab(gameSoundsCorePrefab).UnderTransform(transform).AsSingle();

            // We can use a dummy like this one, or one based on prefabs like above, or we can make a unity audio handler.
            //AudioDummy audioHandlerDummy = new AudioDummy();
            //Container.Bind<IGameSounds>().FromInstance(audioHandlerDummy).AsSingle();
            //Container.Bind<IGameMusic>().FromInstance(audioHandlerDummy).AsSingle();
        }

        protected virtual void InstallGUI()
        {
            Container.Bind<UIModalPopup>().FromComponentInNewPrefab(uiErrorPopupPrefab)
                              .WithGameObjectName("Error Dialog").AsSingle().NonLazy();

            Container.Bind<UIModalChoicePopup>().FromComponentInNewPrefab(uiChoicePopupPrefab)
                                                .WithGameObjectName("Choice Dialog").AsSingle().NonLazy();
        }

        protected virtual void InstallThemes()
        {
            Container.Bind<ThemeManager>().FromComponentInNewPrefab(themeManagerPrefab)
                                          .WithGameObjectName("Theme Manager").AsSingle().NonLazy();

            Container.Bind<ThemeSetup>().FromInstance(themeSetup).AsSingle();
        }

        protected virtual void InstallSkins()
        {
            Container.Bind<MasterSkinList>().FromInstance(masterSkinList).AsSingle();
        }

        protected virtual void InstallItems()
        {
            if (USE_CORE_TESTING_MASTER_ITEM_LIST)
            {
                // only dummy core items will be included - this is for running core automated tests
                Debug.Log("Installing automated testing simplified MasterItemList");
                Container.Bind<MasterItemList>().FromInstance(testMasterItemList).AsSingle();
            }
            else
            {
                Debug.Log("Installing project specific MasterItemList");
                Container.Bind<MasterItemList>().FromInstance(masterItemList).AsSingle();
            }
        }

        protected virtual void InstallAnalytics()
        {
            Container.Bind(typeof(IAnalyticsHandler)).FromComponentInNewPrefab(analyticsManagerPrefab)
                                                     .WithGameObjectName("Analytics Manager").AsSingle().NonLazy();
        }

        protected virtual void InstallNetworking()
        {
            // In the main Core there is not really any plugin for networking so we need to let the project
            // tie it in.
            Container.Bind<INetworkManager>().To<DummyNetworkManager>().FromNew().AsSingle().NonLazy();
            Container.Bind<INetworkEventsHandler>().To<DummyNetworkEvents>().FromNew().AsSingle().NonLazy();
        }

        protected virtual void InstallPlatformSpecificBindings()
        {
            Container.Bind<IPlatformTools>().FromInstance(new EmptyPlatformTools()).AsSingle();
            Container.Bind<IAchievements>().FromInstance(new DisabledAchievements()).AsSingle();
        }

        protected virtual void InstallDevConsole()
        {
            Container.Bind<CheatSettings>().FromInstance(cheatsSettings).AsSingle();
            Container.QueueForInject(cheatsSettings);
            Container.Bind<CheatsManager>().FromComponentsInNewPrefab(cheatsPrefab).AsSingle().NonLazy();
        }
    }
}