// Copyright Isto Inc.

using Isto.Core.Beings;
using Isto.Core.Cheats;
using Isto.Core.Data;
using Isto.Core.UI;
using UnityEngine;
using Zenject;

namespace Isto.Core.Installers
{
    public class SimpleEssentialsSceneInstaller : MonoInstaller<SimpleEssentialsSceneInstaller>
    {
        [Header("Game Data")]
        public bool enableDataManagers = true;

        [Header("Cheat Menu")]
        public CheatMenuState cheatMenuPrefab;


        // INJECTION

        CheatSettings _cheatSettings;

        [Inject]
        public void Inject(CheatSettings cheatSettings)
        {
            _cheatSettings = cheatSettings;
        }


        // OTHER METHODS

        public override void InstallBindings()
        {
            // Player
            Container.Bind<SimplePlayerController>().FromComponentInHierarchy().AsSingle();
            Container.Bind<IPlayerController>().FromComponentInHierarchy().AsSingle();
            Container.Bind<IPlayerFactory>().To<SimplePlayerFactory>().FromComponentInHierarchy().AsSingle();
            Container.Bind<PlayerManager>().FromComponentInHierarchy().AsSingle();

            // UI Menu States
            Container.Bind<UISimpleMenusClosedState>().FromComponentInHierarchy().AsSingle();
            Container.Bind<UISimpleGameMenuState>().FromComponentInHierarchy().AsSingle();
            Container.Bind<SimpleGameMenuStateMachine>().FromComponentInHierarchy().AsSingle();
            Container.Bind<IUIGameMenu>().FromComponentInHierarchy().AsSingle();

            Container.Bind<UIAccessManager>().AsSingle();

            BindDataManagers();
            BindCheaterUI();
        }

        private void BindDataManagers()
        {
            if (!enableDataManagers)
                return;

            // Need to parent under this item to make sure it's created in the right scene when loading
            Transform dataManagersParent = new GameObject("Data Managers").transform;
            dataManagersParent.SetParent(transform);

            Container.Bind<PlayerDataManager>().FromNewComponentOn(dataManagersParent.gameObject).AsSingle().NonLazy();
        }

        private void BindCheaterUI()
        {
            if (_cheatSettings.CheatsEnabled)
            {
                Container.Bind<CheatMenuState>().FromComponentsInNewPrefab(cheatMenuPrefab).AsSingle().NonLazy();
            }
        }
    }
}