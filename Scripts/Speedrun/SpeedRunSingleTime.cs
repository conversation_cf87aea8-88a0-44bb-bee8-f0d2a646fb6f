// Copyright Isto Inc.
using Isto.Core.Localization;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace Isto.Core.Speedrun
{
    public class SpeedRunSingleTime : MonoBehaviour
    {
        // UNITY HOOKUP

        [SerializeField] private Image _chapterIcon;
        [SerializeField] private TextMeshProUGUI _chapterName;
        [SerializeField] private TextMeshProUGUI _chapterTime;
        [SerializeField] private TextMeshProUGUI _chapterTarget;
        [SerializeField] private TextMeshProUGUI _timeDiff;
        [SerializeField] private Image _backgroundImage;


        // OTHER FIELDS

        private LocTerm _chapterNameLoc;


        // ACCESSORS

        public void SetChapterName(LocTerm term)
        {
            _chapterNameLoc = term;
            _chapterNameLoc.LocalizeInto(_chapterName);
        }

        public void SetTimer(string text)
        {
            _chapterTime.text = text;
        }

        public void SetColor(Color c)
        {
            _chapterTime.color = c;
        }

        public void SetColorBG(Color c)
        {
            _backgroundImage.color = c;
        }

        public void SetDiffTimer(string text)
        {
            if (!_timeDiff.gameObject.activeSelf)
            {
                _timeDiff.gameObject.SetActive(true);
            }

            _timeDiff.text = text;
        }

        public void SetDiffColor(Color c)
        {
            _timeDiff.color = c;
        }

        public void SetTargetTimer(string text)
        {
            if (!_chapterTarget.gameObject.activeSelf)
            {
                _chapterTarget.gameObject.SetActive(true);

                // target needs to be shown at the right so we adjust the other labels when we turn it on
                MoveTimers(-_chapterTarget.rectTransform.rect.width);
            }

            _chapterTarget.text = text;
        }

        public void SetIcon(Sprite sprite)
        {
            if (sprite != null)
            {
                _chapterIcon.sprite = sprite;
            }

            if (!_chapterIcon.gameObject.activeSelf)
            {
                _chapterIcon.gameObject.SetActive(true);
            }
        }

        public float GetContentsWidth()
        {
            float width = 0f;
            width += _chapterName.rectTransform.rect.width;
            width += _chapterTime.rectTransform.rect.width;
            if (_timeDiff.gameObject.activeSelf)
            {
                width += _timeDiff.rectTransform.rect.width;
            }
            if (_chapterTarget.gameObject.activeSelf)
            {
                width += _chapterTarget.rectTransform.rect.width;
            }
            if (_chapterIcon.gameObject.activeSelf)
            {
                width += _chapterIcon.rectTransform.rect.width;
            }
            return width;
        }

        // OTHER METHODS

        public void HideDiffTimer()
        {
            if (_timeDiff.gameObject.activeSelf)
            {
                _timeDiff.gameObject.SetActive(false);
            }
        }

        public void HideTargetTimer()
        {
            if (_chapterTarget.gameObject.activeSelf)
            {
                _chapterTarget.gameObject.SetActive(false);

                // target is at the right so we adjust the other labels to line up with the UI when we turn it off
                MoveTimers(_chapterTarget.rectTransform.rect.width);
            }
        }

        private void MoveTimers(float offset)
        {
            Vector3 diffPos = _timeDiff.rectTransform.position;
            diffPos.x += offset;
            _timeDiff.rectTransform.position = diffPos;

            Vector3 timePos = _chapterTime.rectTransform.position;
            timePos.x += offset;
            _chapterTime.rectTransform.position = timePos;

            Vector3 targetPos = _chapterTarget.rectTransform.position;
            targetPos.x += offset;
            _chapterTarget.rectTransform.position = targetPos;
        }

        public void HideIcon()
        {
            if (_chapterIcon.gameObject.activeSelf)
            {
                _chapterIcon.gameObject.SetActive(false);
            }
        }

        public void ActivateImage()
        {
            if (!_backgroundImage.enabled)
            {
                _backgroundImage.enabled = true;
            }
        }

        public void DeActivateImage()
        {
            if (_backgroundImage.enabled)
            {
                _backgroundImage.enabled = false;
            }
        }
    }
}
