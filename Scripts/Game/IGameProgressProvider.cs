// Copyright Isto Inc.

using Isto.Core.Localization;
using System.Collections.Generic;

namespace Isto.Core.Game
{
    public interface IGameProgressProvider
    {
        /// <summary>
        /// The progress starts at 0 and goes as high as you need, but your analytics also need to be configured to
        /// support this range of values.
        /// </summary>
        /// <returns>The current game level id, or -1 if initialization is not complete</returns>
        public int GetCurrentGameProgressLevel();

        public int GetMaxGameProgressLevel();

        public bool IsLastProgressLevel();

        public float GetGameSecondsElapsedInLevel(int progressLevel);

        public float GetGameSecondsElapsedInCurrentProgressLevel();

        public float GetTotalGameSecondsElapsedInPlaythrough();

        public LocTerm GetGameProgressLevelName(int progressLevel);

        public string GetGameProgressLevelInternalName(int progressLevel);
        
        public void LoadLevelTimes(Dictionary<int, float> dictionary);
    }
}