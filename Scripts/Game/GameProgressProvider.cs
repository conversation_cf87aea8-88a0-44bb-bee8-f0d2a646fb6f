// Copyright Isto Inc.
using Isto.Core.Localization;
using Isto.Core.Speedrun;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Game
{
    public class GameProgressProvider : MonoBehaviour, IGameProgressProvider
    {
        // INJECTED

        private LocTerm.Factory _localizedStringFactory;
        private SpeedrunSettings _speedrunSettings;

        [Inject]
        public void Inject(LocTerm.Factory localizedStringFactory,
                          [InjectOptional] SpeedrunSettings speedrunSettings)
        {
            _localizedStringFactory = localizedStringFactory;
            _speedrunSettings = speedrunSettings;
        }


        // IGameProgressProvider IMPLEMENTATION

        public virtual int GetCurrentGameProgressLevel()
        {
            return 0; // No levels in core module content by default.
        }

        public virtual int GetMaxGameProgressLevel()
        {
            return 1; // No levels in core module content by default.
        }

        public virtual bool IsLastProgressLevel()
        {
            return GetCurrentGameProgressLevel() == GetMaxGameProgressLevel();
        }

        public virtual float GetGameSecondsElapsedInLevel(int progressLevel)    
        {
            return Time.timeSinceLevelLoad; // No levels in core module content by default.
        }

        public virtual float GetGameSecondsElapsedInCurrentProgressLevel()
        {
            return GetGameSecondsElapsedInLevel(GetCurrentGameProgressLevel());
        }

        public virtual float GetTotalGameSecondsElapsedInPlaythrough()
        {
            // TODO: First we need to fix save games, which should be tracking time spent ideally.
            // Then we should be able to use the same solution here to track this.
            // In atrio the heartbox was doing the tracking so atm there's nothing doing it in core.
            // We probably should extract that responsibility from the heartbox and more the new class into core.
            return Time.timeSinceLevelLoad;
        }

        public virtual string GetGameProgressLevelInternalName(int progressLevel)
        {
            return "LevelNotFound";
        }

        public virtual LocTerm GetGameProgressLevelName(int progressLevel)
        {
            LocTerm term;

            if(_speedrunSettings != null
                && _speedrunSettings.GetLocalizationKey(progressLevel) is string locKey
                && locKey!= null)
            {
                term = _localizedStringFactory.Create(LocTerm.LocalizationType.Localized, locKey);
            }
            else
            {
                term = _localizedStringFactory.Create(LocTerm.LocalizationType.NonLocalized,
                                                      GetGameProgressLevelInternalName(progressLevel));
            }

            return term;
        }

        public virtual void LoadLevelTimes(Dictionary<int, float> dictionary)
        {
            Debug.LogWarning("Loading Game Progress Data Not Supported!");
        }
    }
}