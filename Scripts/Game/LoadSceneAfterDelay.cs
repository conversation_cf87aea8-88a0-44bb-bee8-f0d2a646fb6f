// Copyright Isto Inc.

using Isto.Core.Inputs;
using Isto.Core.Localization;
using Isto.Core.Scenes;
using UnityEngine;
using Zenject;

namespace Isto.Core.Game
{
    public class LoadSceneAfterDelay : MonoBehaviour
    {
        [SerializeField] private float _delay = 5f;
        [SerializeField] private float _delayOnXbox = 10f;
        [SerializeField][SceneReference] private string _sceneToLoad;
        [SerializeField][SceneReference] private string _sceneToLoadOnXbox;
        [SerializeField] private bool _allowCancel;
        [SerializeField] private bool _forceEnglish;

        private GameState _gameState;
        private IControls _controls;

        [Inject]
        public void Inject(GameState gameState, IControls controls)
        {
            _gameState = gameState;
            _controls = controls;
        }

        protected void OnEnable()
        {
            float invokeDelay = _delay;
#if PLATFORM_GAMECORE
            invokeDelay = _delayOnXbox;
#endif
            Invoke(nameof(LoadScene), invokeDelay);

            if (_controls != null)
            {
                _controls.SetControlMode(Controls.Mode.UI);
            }

            if (_forceEnglish)
            {
                // As long as the localization has not been updated, we will have only partial support for most of our languages (except
                // english) so we will not allow them until that has been done.
                LanguageChanger.SetLanguage(LanguageChanger.LanguageEnum.ENGLISH);
            }
        }

        protected void OnDisable()
        {
            CancelInvoke();
        }

        protected void Update()
        {
            if (_allowCancel && _controls != null && _controls.GetButton(UserActions.UICANCEL))
            {
                CancelInvoke();
                LoadScene();
            }
        }

        private void LoadScene()
        {
            Time.timeScale = 1f; // enforce this for safety

            string scenetoload = _sceneToLoad;
#if PLATFORM_GAMECORE
            if (!string.IsNullOrEmpty(_sceneToLoadOnXbox))
            {
                scenetoload = _sceneToLoadOnXbox;
            }
#endif
            _gameState.LoadScene(scenetoload);
        }
    }
}