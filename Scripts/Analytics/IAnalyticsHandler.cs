// Copyright Isto Inc.
using Isto.Core.Configuration;
using Isto.Core.Enums;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Analytics
{
    // Obviously none of this works if the appropriate backend is not ready to receive it (24h in advance)
    public interface IAnalyticsHandler
    {
        // Extend this enumeration in your project.
        // Note that this enum should not be serialized anywhere AFAIK, so it should be safe to edit the list
        // Should they all include game time?
        public class CoreAnalyticsEnum : Int32Enum<CoreAnalyticsEnum>
        {
            public static readonly CoreAnalyticsEnum TEST = new CoreAnalyticsEnum(0, nameof(TEST));

            public static readonly CoreAnalyticsEnum GAME_START = new CoreAnalyticsEnum(1, nameof(GAME_START));
            public static readonly CoreAnalyticsEnum THEME_CHANGE = new CoreAnalyticsEnum(2, nameof(THEME_CHANGE)); // param = theme name
            public static readonly CoreAnalyticsEnum MENU_PAGE_VISIT = new CoreAnalyticsEnum(3, nameof(ME<PERSON>_PAGE_VISIT));
            public static readonly CoreAnalyticsEnum GAME_MODE_SELECT = new CoreAnalyticsEnum(4, nameof(GAME_MODE_SELECT));
            public static readonly CoreAnalyticsEnum GAME_PROGRESS = new CoreAnalyticsEnum(5, nameof(GAME_PROGRESS));

            public static readonly CoreAnalyticsEnum UPGRADE_STEP_SKIP = new CoreAnalyticsEnum(6, nameof(UPGRADE_STEP_SKIP));
            public static readonly CoreAnalyticsEnum DIALOGUE_SKIP = new CoreAnalyticsEnum(7, nameof(DIALOGUE_SKIP));
            public static readonly CoreAnalyticsEnum CINEMATIC_SKIP = new CoreAnalyticsEnum(8, nameof(CINEMATIC_SKIP));

            public static readonly CoreAnalyticsEnum DEVELOPER_CONSOLE_OPEN = new CoreAnalyticsEnum(9, nameof(DEVELOPER_CONSOLE_OPEN));
            public static readonly CoreAnalyticsEnum CHEAT_ACTIVE = new CoreAnalyticsEnum(10, nameof(CHEAT_ACTIVE));
            public static readonly CoreAnalyticsEnum SECRET_CHEAT_ACTIVE = new CoreAnalyticsEnum(11, nameof(SECRET_CHEAT_ACTIVE));

            public static readonly CoreAnalyticsEnum PLAYER_DEATH = new CoreAnalyticsEnum(12, nameof(PLAYER_DEATH));

            public static readonly CoreAnalyticsEnum RESEARCH_UNLOCK = new CoreAnalyticsEnum(13, nameof(RESEARCH_UNLOCK));
            public static readonly CoreAnalyticsEnum RESEARCH_LEVEL_UP = new CoreAnalyticsEnum(14, nameof(RESEARCH_LEVEL_UP)); //param = level

            // Was used in Atrio, keeping this value around as a reminder not to add a #15 enum value in core.
            public static readonly CoreAnalyticsEnum PLACEHOLDER = new CoreAnalyticsEnum(15, nameof(PLACEHOLDER));

            public static readonly CoreAnalyticsEnum TITLE_SCREEN_SUBMENU_OPEN = new CoreAnalyticsEnum(16, nameof(TITLE_SCREEN_SUBMENU_OPEN)); //param = name
            public static readonly CoreAnalyticsEnum SAVE_SLOT_SELECT = new CoreAnalyticsEnum(17, nameof(SAVE_SLOT_SELECT)); //param = game mode?

            public static readonly CoreAnalyticsEnum BUILDING_INTERACT = new CoreAnalyticsEnum(18, nameof(BUILDING_INTERACT)); //param = building name

            public static readonly CoreAnalyticsEnum EASTER_EGG_GET = new CoreAnalyticsEnum(19, nameof(EASTER_EGG_GET)); //param = name

            public CoreAnalyticsEnum(int value, string name) : base(value, name)
            {
            }
        }

        // Note that this enum should not be serialized anywhere AFAIK, so it should be safe to edit the list
        public enum AnalyticsEvent 
        {
            Test = 0,

            // Supported events (in UnityAnalyticsManager)
            GameStart,
            ThemeChanged, 
            MenuPageVisited,
            GameModeSelected,
            GameLevelProgress, //5
            UpgradeStepSkipped, // Not integrated yet.
            DialogueSkipped, // Integrated but missing a parameter.
            CinematicSkipped, // Integrated but not configured on the dashboard.
            DeveloperConsoleOpened,
            CheatActivated, //10
            SecretCheatActivated,
            PlayerDeath,
            ResearchUnlocked,
            ResearchCenterLevelUp, //param = level
            CircuitJuiceUnlocked, // 15

            // Consider adding these ideas in the future
            TitleScreenMenuVisited, //param = name {new game, load, settings, credits, roadmap, patch notes, latest vlog, quit}
            SaveSlotSelected, //param = game mode?
            BuildingInteracted, //params = building name {fuel depot, heartbox, item finder, research module, punk monk, bulb man} + gametime
            EasterEggFound, //params = name + gametime
        }

        /// <summary>
        /// This class defines a bunch of parameter names for the analytics events.
        /// The event names themselves will always be the name of the AnalyticsEvent value.
        /// However we also need to specify consistent keys and values for the custom data we add to them.
        /// </summary>
        public class AnalyticsDataKeys
        {
            public static readonly string GAMESTART_BUILD_TYPE = "build_type"; // string - from VersionInfo.BuildType

            public static readonly string GAMESTART_BUILD_DATE = "build_date"; // string - representing build number
            public static readonly string GAMESTART_BUILD_DATE_DEFAULT = "2021-06-09";

            public static readonly string GAMESTART_BUILD_NUMBER = "build_number";
            public static readonly string GAMESTART_BUILD_NUMBER_FORMAT = "v{0}";
            public static readonly string GAMESTART_BUILD_NUMBER_DEFAULT = "0.0";

            public static readonly string THEMECHANGED_NAME = "internal_name"; // string - the internal name of the theme definition

            public static readonly string MENUPAGEVISIT_ID = "page_name"; // string - a unique descriptive name for the page

            public static readonly string GAMEMODESELECTED_NAME = "internal_name"; // string - the internal name of the game mode definition

            public static readonly string GAMEPROGRESS_LEVEL = "level_number"; // int - the level number
            public static readonly string GAMEPROGRESS_FIRST_EVER = "first_ever"; // bool - first occurence of this event for this player
            public static readonly string GAMEPROGRESS_LEVEL_TIME = "level_time"; // float - time in seconds spent in previous level
            public static readonly string GAMEPROGRESS_TOTAL_GAME_TIME = "total_game_time"; // float - time in seconds spent in this playthrough

            // Ideally I would like to add this data to the skip but it's in a different DI container and I decided to skip it for now
            public static readonly string DIALOGUESKIP_LEVEL_NUMBER = "level_number"; // int - current level number

            public static readonly string CHEATACTIVATED_NAME = "dev_action_name"; // string - the name of the developer action
            public static readonly string CHEATACTIVATED_LEVEL_NUMBER = "level_number"; // int - current level number
            public static readonly string CHEATACTIVATED_LEVEL_TIME = "level_time"; // float - time in seconds spent in current level
            public static readonly string CHEATACTIVATED_TOTAL_GAME_TIME = "total_game_time"; // float - time in seconds spent in this playthrough

            public static readonly string SECRETCHEATACTIVATED_NAME = "cheat_name"; // string - the internal name of the cheat

            public static readonly string PLAYERDEATH_AREA = "area"; // string - the name of the scene
            public static readonly string PLAYERDEATH_POS = "position"; // string - representation of the vector 2 of the map coordinates
            public static readonly string PLAYERDEATH_DAMAGE_SOURCE = "damage_source"; // string - the name of a type of damage the player received
            public static readonly string PLAYERDEATH_LEVEL_NUMBER = "level_number"; // int - current level number
            public static readonly string PLAYERDEATH_LEVEL_TIME = "level_time"; // float - time in seconds spent in current level
            public static readonly string PLAYERDEATH_TOTAL_GAME_TIME = "total_game_time"; // float - time in seconds spent in this playthrough

            public static readonly string RESEARCHUNLOCKED_NAME = "research_name"; // string - the name of the researched player upgrade
            public static readonly string RESEARCHUNLOCKED_LEVEL_NUMBER = "level_number"; // int - current game level number
            public static readonly string RESEARCHUNLOCKED_LEVEL_TIME = "level_time"; // float - time in seconds spent in current level
            public static readonly string RESEARCHUNLOCKED_TOTAL_GAME_TIME = "total_game_time"; // float - time in seconds in this playthrough

            public static readonly string RESEARCHUPGRADED_LEVEL = "researchupgrade_level"; // int - the Research Center's new level
            public static readonly string RESEARCHUPGRADED_LEVEL_NUMBER = "level_number"; // int - current game level number
            public static readonly string RESEARCHUPGRADED_LEVEL_TIME = "level_time"; // float - time in seconds spent in current level
            public static readonly string RESEARCHUPGRADED_TOTAL_GAME_TIME = "total_game_time"; // float - time in seconds in this playthrough
        }


        // Specific versions - prefer these when available

        public bool RegisterApplicationStarted();

        public bool RegisterThemeChanged(string internalThemeName);

        public bool RegisterMenuPageVisited(string uniqueMenuPageName);

        public bool RegisterGameModeSelected(GameModeDefinition mode);

        public bool RegisterGameProgress(int level, float gameSecondsSinceLastLevel, float gameSecondsSinceStart);

        public bool RegisterDialogueSkipUsed();

        public bool RegisterCinematicSkipUsed();

        // Make sure the damage source field is something that can be parsed and viewed in our dashboard
        public bool RegisterPlayerDeath(string areaName, Vector3 pos, List<string> recentDamageSourceNames);

        public bool RegisterResearchUnlocked(string researchName);

        public bool RegisterResearchCenterLevelUp(int level);

        public bool RegisterDeveloperConsoleOpened();

        // This is meant to register special cheats that cannot be activated in the developer console, e.g. the catrio code in the title screen
        public bool RegisterSecretCheatActivated(string cheatName);

        public bool RegisterCheatActivated(string cheatName);

        // Generic version

        /// <summary>
        /// A method for sending analytics events with flexible parameters.
        /// </summary>
        /// <param name="e">The predefined type of event this is</param>
        /// <param name="data">The custom data to send with the event (has to be something expected by the target analytics platform)</param>
        /// <returns>True if the information was registered successfully</returns>
        public bool TriggerEvent(AnalyticsEvent e, Dictionary<string, object> data = null);
    }
}