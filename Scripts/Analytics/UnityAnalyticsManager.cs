// Copyright Isto Inc.

using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Isto.Core.Configuration;
using Zenject;
using Isto.Core.Game;

#if UNITY_STANDALONE_WIN
using UnityEngine.Analytics;
#endif

namespace Isto.Core.Analytics
{
    /// <summary>
    /// This a base class to hold some reusable common logic for [old] unity analytics registration.
    /// However that analytics system was deprecated in june 2023, and is not on the cloud services dashboard anymore.
    /// It will be fully shut down on april 2024.
    /// 
    /// Note: no point in sending events from the editor as those won't get registered by the unity analytics.
    /// To test these events you will need a build.
    /// </summary>
    public class 
        UnityAnalyticsManager : MonoBehaviour, IAnalyticsHandler
    {
        [Inject] private GameState _gameState = default;

        protected IGameProgressProvider GameProgress => _gameState.GameProgressProvider;
        protected int GameProgressLevel => GameProgress?.GetCurrentGameProgressLevel() ?? 0;
        protected float GameSecondsInLevel => GameProgress?.GetGameSecondsElapsedInCurrentProgressLevel() ?? 0f;
        protected float GameSecondsSinceStart => GameProgress?.GetTotalGameSecondsElapsedInPlaythrough() ?? 0f;

        private void Awake()
        {
            IAnalyticsHandler analytics = (IAnalyticsHandler)this;
            analytics.RegisterApplicationStarted();
        }

        private void Start()
        {
            // To test, uncomment this line and make a build!
            //StartCoroutine(EventSequenceSimulation());
        }

        /// <summary>
        /// This is a bunch of events we wanted to test on the analytics dashboard.
        /// They will not work in the editor, you need to run this coroutine in a test build for it to upload events.
        /// </summary>
        private IEnumerator EventSequenceSimulation()
        {
            IAnalyticsHandler analytics = (IAnalyticsHandler)this;
            yield return null;

            //AnalyticsEvent.GameStart();
            yield return new WaitForSeconds(3f);

            Dictionary<string, object> data = new Dictionary<string, object>();
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.GAMEMODESELECTED_NAME, "MainGame");
            TriggerEvent(IAnalyticsHandler.AnalyticsEvent.GameModeSelected, data);
            data.Clear();

            //Analytics.CustomEvent("DeathPosTest", new Vector3(0f, 42f, 0f));

            float timeAtPreviousState = Time.timeSinceLevelLoad;
            yield return new WaitForSeconds(0.5f);

            for (int i = 1; i <= 13; i++)
            {
                //analytics.RegisterGameProgress(i, Time.timeSinceLevelLoad - timeAtPreviousState, Time.timeSinceLevelLoad);
                timeAtPreviousState = Time.timeSinceLevelLoad;

                //Registering some version of research center progression
                /*if (i % 6 == 0)
                {
                    yield return new WaitForSeconds(0.8f);
                    data.Clear();
                    data.Add("Total_Game_Time", Time.timeSinceLevelLoad);
                    AnalyticsEvent.LevelUp(2, data);
                }
                else if (i % 8 == 0)
                {
                    yield return new WaitForSeconds(0.8f);
                    data.Clear();
                    data.Add("Total_Game_Time", Time.timeSinceLevelLoad);
                    AnalyticsEvent.LevelUp(3, data);
                }*/
            }

            yield return new WaitForSeconds(1.33f);

            //data.Clear();
            //data.Add("Total_Game_Time", Time.timeSinceLevelLoad);
            //AnalyticsEvent.GameOver(eventData: data);
        }

        public bool TriggerEvent(IAnalyticsHandler.AnalyticsEvent e, Dictionary<string, object> data = null)
        {

#if UNITY_STANDALONE_WIN
            AnalyticsResult result = UnityEngine.Analytics.Analytics.CustomEvent(e.ToString(), data);

            if (result == AnalyticsResult.Ok)
                Debug.Log($"UnityAnalytics event {e} has been sent");
            else
                Debug.LogError($"UnityAnalytics event {e} has failed: {result}");

            return result == AnalyticsResult.Ok;
#else
            return false;
#endif // UNITY_STANDALONE_WIN
        }

        // We could use the properties instead of data parameters but this allows us to fake progress for testing
        bool IAnalyticsHandler.RegisterGameProgress(int level, float gameSecondsSinceLastLevel, float gameSecondsSinceStart)
        {
            Dictionary<string, object> data = new Dictionary<string, object>();
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.GAMEPROGRESS_LEVEL, level);
            string key = GenerateLevelKey(level);
            bool isFirstTimeLevelAchieved = PlayerPrefs.GetInt(key, 0) == 0;
            PlayerPrefs.SetInt(key, 1);
            PlayerPrefs.Save();
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.GAMEPROGRESS_FIRST_EVER, isFirstTimeLevelAchieved);
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.GAMEPROGRESS_LEVEL_TIME, gameSecondsSinceLastLevel);
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.GAMEPROGRESS_TOTAL_GAME_TIME, gameSecondsSinceStart);

            return TriggerEvent(IAnalyticsHandler.AnalyticsEvent.GameLevelProgress, data);
        }

        private static string GenerateLevelKey(int level)
        {
            return "Level_Visited_" + level;
        }

        bool IAnalyticsHandler.RegisterApplicationStarted()
        {
            BuildTypeEnum buildType = BuildTypeEnum.DEVELOPER;
            string buildDate = IAnalyticsHandler.AnalyticsDataKeys.GAMESTART_BUILD_DATE_DEFAULT;
            string rawBuildNumber = IAnalyticsHandler.AnalyticsDataKeys.GAMESTART_BUILD_NUMBER_DEFAULT;


            VersionInfo version = _gameState.version;
            if (version == null)
            {
                // Fallback attempt for empty projects, although I'm not sure we should really be using this
                // (but if we really do want to keep using this, it should probably be moved somewhere more central)
                version = Resources.Load<VersionInfo>("CoreGameVersionInfo");
            }

            if (version == null)
            {
                Debug.LogError("Can't load version info asset");
            }
            else
            {
                version.PrintCurrentVersion(); // For reference in the logs

                buildType = version.Type;
                buildDate = version.ActiveVersion.shortDate;
                rawBuildNumber = version.ActiveVersion.ShortVersionText;
            }
            string buildNumber = String.Format(IAnalyticsHandler.AnalyticsDataKeys.GAMESTART_BUILD_NUMBER_FORMAT, rawBuildNumber);

            Dictionary<string, object> data = new Dictionary<string, object>();
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.GAMESTART_BUILD_TYPE, buildType.ToString());
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.GAMESTART_BUILD_DATE, buildDate);
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.GAMESTART_BUILD_NUMBER, buildNumber);

            return TriggerEvent(IAnalyticsHandler.AnalyticsEvent.GameStart, data);
        }

        bool IAnalyticsHandler.RegisterThemeChanged(string internalThemeName)
        {
            Dictionary<string, object> data = new Dictionary<string, object>();
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.THEMECHANGED_NAME, internalThemeName);

            return TriggerEvent(IAnalyticsHandler.AnalyticsEvent.ThemeChanged, data);
        }

        bool IAnalyticsHandler.RegisterMenuPageVisited(string uniqueMenuPageName)
        {
            Dictionary<string, object> data = new Dictionary<string, object>();
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.MENUPAGEVISIT_ID, uniqueMenuPageName);

            return TriggerEvent(IAnalyticsHandler.AnalyticsEvent.MenuPageVisited, data);
        }

        bool IAnalyticsHandler.RegisterGameModeSelected(GameModeDefinition mode)
        {
            Dictionary<string, object> data = new Dictionary<string, object>();
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.GAMEMODESELECTED_NAME, mode.InternalName);

            return TriggerEvent(IAnalyticsHandler.AnalyticsEvent.GameModeSelected, data);
        }

        bool IAnalyticsHandler.RegisterDialogueSkipUsed()
        {
            return TriggerEvent(IAnalyticsHandler.AnalyticsEvent.DialogueSkipped);
        }

        bool IAnalyticsHandler.RegisterCinematicSkipUsed()
        {
            return TriggerEvent(IAnalyticsHandler.AnalyticsEvent.CinematicSkipped);
        }

        bool IAnalyticsHandler.RegisterPlayerDeath(string areaName, Vector3 pos, List<string> recentDamageSourceNames)
        {
            Dictionary<string, object> data = new Dictionary<string, object>();
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.PLAYERDEATH_AREA, areaName);
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.PLAYERDEATH_POS, pos.ToString());
            foreach (string source in recentDamageSourceNames)
            {
                data.Add(IAnalyticsHandler.AnalyticsDataKeys.PLAYERDEATH_DAMAGE_SOURCE, source);
            }
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.PLAYERDEATH_LEVEL_NUMBER, GameProgressLevel);
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.PLAYERDEATH_LEVEL_TIME, GameSecondsInLevel);
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.PLAYERDEATH_TOTAL_GAME_TIME, GameSecondsSinceStart);

            return TriggerEvent(IAnalyticsHandler.AnalyticsEvent.PlayerDeath, data);
        }

        bool IAnalyticsHandler.RegisterResearchUnlocked(string researchName)
        {
            Dictionary<string, object> data = new Dictionary<string, object>();
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.RESEARCHUNLOCKED_NAME, researchName);
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.RESEARCHUNLOCKED_LEVEL_NUMBER, GameProgressLevel);
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.RESEARCHUNLOCKED_LEVEL_TIME, GameSecondsInLevel);
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.RESEARCHUNLOCKED_TOTAL_GAME_TIME, GameSecondsSinceStart);

            return TriggerEvent(IAnalyticsHandler.AnalyticsEvent.ResearchUnlocked, data);
        }

        bool IAnalyticsHandler.RegisterResearchCenterLevelUp(int level)
        {
            Dictionary<string, object> data = new Dictionary<string, object>();
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.RESEARCHUPGRADED_LEVEL, level);
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.RESEARCHUPGRADED_LEVEL_NUMBER, GameProgressLevel);
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.RESEARCHUPGRADED_LEVEL_TIME, GameSecondsInLevel);
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.RESEARCHUPGRADED_TOTAL_GAME_TIME, GameSecondsSinceStart);

            return TriggerEvent(IAnalyticsHandler.AnalyticsEvent.ResearchCenterLevelUp, data);
        }

        bool IAnalyticsHandler.RegisterDeveloperConsoleOpened()
        {
            return TriggerEvent(IAnalyticsHandler.AnalyticsEvent.DeveloperConsoleOpened);
        }

        public virtual bool RegisterSecretCheatActivated(string cheatName)
        {
            int currentLevelNumber = 0;
            float levelTime = 0f;
            float gameSecondsSinceStart = Time.realtimeSinceStartup;

            Debug.LogWarning("CoreUnityAnalyticsManager.RegisterSecretCheatActivated() needs to be implemented!");

            Dictionary<string, object> data = new Dictionary<string, object>();
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.CHEATACTIVATED_NAME, cheatName);
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.CHEATACTIVATED_LEVEL_NUMBER, currentLevelNumber);
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.CHEATACTIVATED_LEVEL_TIME, levelTime);
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.CHEATACTIVATED_TOTAL_GAME_TIME, gameSecondsSinceStart);

            return TriggerEvent(IAnalyticsHandler.AnalyticsEvent.SecretCheatActivated, data);
        }

        public virtual bool RegisterCheatActivated(string cheatName)
        {
            Dictionary<string, object> data = new Dictionary<string, object>();
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.CHEATACTIVATED_NAME, cheatName);
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.CHEATACTIVATED_LEVEL_NUMBER, GameProgressLevel);
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.CHEATACTIVATED_LEVEL_TIME, GameSecondsInLevel);
            data.Add(IAnalyticsHandler.AnalyticsDataKeys.CHEATACTIVATED_TOTAL_GAME_TIME, GameSecondsSinceStart);

            return TriggerEvent(IAnalyticsHandler.AnalyticsEvent.CheatActivated, data);
        }
    }
}