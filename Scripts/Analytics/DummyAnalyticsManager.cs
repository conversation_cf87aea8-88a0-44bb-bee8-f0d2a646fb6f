// Copyright Isto Inc.
using Isto.Core.Configuration;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Analytics
{
    /// <summary>
    /// Implements the IAnalyticsHandler interface without doing anything with the analytics calls, for platforms where
    /// any current analytic tools we have are not supported.
    /// 
    /// Not sure if the methods should return false because the messages are not active, or true to simulate that the
    /// messages are working so the eventual handling logic doesn't have to worry about it. For now it's moot since none
    /// of the event registration calls handle the result of the methods� AFAIK.
    /// </summary>
    public class DummyAnalyticsManager : MonoBehaviour, IAnalyticsHandler
    {
        public bool TriggerEvent(IAnalyticsHandler.AnalyticsEvent e, Dictionary<string, object> data)
        {
            Debug.Log($"UnityAnalytics event {e} has been ignored because no analytics manager is enabled.");
            return false;
        }

        bool IAnalyticsHandler.RegisterApplicationStarted()
        {
            return false;
        }

        bool IAnalyticsHandler.RegisterDeveloperConsoleOpened()
        {
            return false;
        }

        bool IAnalyticsHandler.RegisterDialogueSkipUsed()
        {
            return false;
        }

        bool IAnalyticsHandler.RegisterCinematicSkipUsed()
        {
            return false;
        }

        bool IAnalyticsHandler.RegisterGameModeSelected(GameModeDefinition mode)
        {
            return false;
        }

        bool IAnalyticsHandler.RegisterGameProgress(int level, float gameSecondsSinceLastLevel, float gameSecondsSinceStart)
        {
            return false;
        }
        
        bool IAnalyticsHandler.RegisterMenuPageVisited(string uniqueMenuPageName)
        {
            return false;
        }

        public bool RegisterPlayerDeath(string areaName, Vector3 pos, List<string> recentDamageSourceNames)
        {
            return false;
        }

        bool IAnalyticsHandler.RegisterResearchCenterLevelUp(int level)
        {
            return false;
        }

        bool IAnalyticsHandler.RegisterResearchUnlocked(string researchName)
        {
            return false;
        }

        bool IAnalyticsHandler.RegisterSecretCheatActivated(string cheatName)
        {
            return false;
        }

        bool IAnalyticsHandler.RegisterThemeChanged(string internalThemeName)
        {
            return false;
        }

        public bool RegisterCheatActivated(string cheatName)
        {
            return false;
        }
    }
}