// Copyright Isto Inc.

using Isto.Core.Beings;
using Isto.Core.StateMachine;
using RootMotion.Demos;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

namespace Isto.TRP.Enemies
{
    public class SpiderController : CoreEnemyController
    {
        // UNITY HOOKUP

        [Header("-- SPIDER DEBUG --")]
        [SerializeField] private Canvas _debugBillboard;
        [SerializeField] private TextMeshProUGUI _debugEnemyStateText;
        [SerializeField] private bool _isDebugging = false;

        [Header("Spider Settings")]
        [SerializeField] private MechSpider _mechSpider;
        [SerializeField] private float _speed     = 6f;
        [SerializeField] private float _turnSpeed = 60f;

        [Header("State Setup")]
        [SerializeField] private EnemyIdleState _idleState;
        [SerializeField] private EnemyPatrolState _patrolState;
        [SerializeField] private EnemyChaseState _chaseState;
        [SerializeField] private EnemyAttackState _attackState;
        [SerializeField] private EnemyRetreatState _retreatState;
        [SerializeField] private EnemyScarePlayerState _scarePlayerState;
        [SerializeField] private EnemyDeadState _deadState;

        [Header("Spider Variables")]
        [Tooltip("Which tag to look for when hunting players")]
        [SerializeField] private string _playerTag = "Player";
        [Tooltip("How far the spider can 'see'")]
        [SerializeField] private float _detectionRadius = 60;
        [SerializeField] private float _loseSightDistance = 85f;
        [SerializeField] private float _attackDistance    = 15f;
        [SerializeField] private float _boredDuration = 10f;
        [SerializeField] private float _idleDuration = 5f;
        [SerializeField] private float _patrolDuration = 45f;
        [SerializeField] private Transform _patrolPointContainer;

        public enum EnemyEnum
        {
            Idle,
            Patrol,
            Chase,
            Attack,
            Retreat,
            ScarePlayer,
            Dead
        }

        // Used for debugging purposes
        private Transform _localPlayerCamera;
        private float _lastCameraSearchTime;
        private const float CAMERA_SEARCH_INTERVAL = 1f; // Search for camera every 1 second if not found

        private Dictionary<EnemyEnum, MonoState> _stateMap;
        private List<Transform> _patrolPoints;
        private Transform _target;
        private Transform _playerTarget; // Separate field for tracking player targets
        private bool _isAttacking = false;


        public Transform TargetTransform => _target;

        public bool IsDebugging => _isDebugging;
        public float DistanceToTarget => _target != null ? Vector3.Distance(transform.position, _target.position) : 0f;
        public float LoseSightDistance => _loseSightDistance;
        public float AttackDistance => _attackDistance;
        public float BoredDuration => _boredDuration;
        public float IdleDuration => _idleDuration;
        public bool IsAttacking => _isAttacking;
        public float PatrolDuration => _patrolDuration;
        public List<Transform> PatrolPoints => _patrolPoints;


        // UNITY LIFECYCLE

        // build the map once
        protected override void Awake()
        {
            base.Awake();
            _stateMap = new Dictionary<EnemyEnum, MonoState>
            {
                { EnemyEnum.Idle,           _idleState   },
                { EnemyEnum.Patrol,         _patrolState },
                { EnemyEnum.Chase,          _chaseState  },
                { EnemyEnum.Attack,         _attackState },
                { EnemyEnum.Retreat,        _retreatState},
                { EnemyEnum.ScarePlayer,    _scarePlayerState},
                { EnemyEnum.Dead,           _deadState   },
            };

            _patrolPoints = new List<Transform>();
            foreach (Transform child in _patrolPointContainer)
            {
                _patrolPoints.Add(child);
            }


            _debugBillboard.gameObject.SetActive(_isDebugging);

            // Initialize camera search
            if (_isDebugging)
            {
                FindLocalPlayerCamera();
            }
        }

        // run the state‐machine first, then do movement
        protected override void Update()
        {
            if (IsDebugging)
            {
                DrawDetectionRays();
                var currentState = _currentState as EnemyState;
                _debugEnemyStateText.text = currentState?.DebugStateMessage;
            }

            base.Update();

            if(_target == null || _isAttacking)
            {
                return;
            }

            Vector3 dir = (_target.position - transform.position).normalized;
            if (dir.sqrMagnitude > 0f)
            {
                Quaternion lookRot = Quaternion.LookRotation(dir);
                transform.rotation = Quaternion.RotateTowards(
                    transform.rotation,
                    lookRot,
                    Time.deltaTime * _turnSpeed
                );
                transform.Translate(
                    Vector3.forward * _speed * Time.deltaTime * _mechSpider.scale,
                    Space.Self
                );

                if(IsDebugging)
                {
                    // Have the billboard always face the local player's camera
                    UpdateBillboardRotation();
                }
            }
        }

        public void ChangeState(EnemyEnum newState)
        {
            Debug.Log("Spider changing state to " + newState + " from " + _currentState.GetType().Name);
            ChangeState(_stateMap[newState]);
        }

        public MonoState GetState(EnemyEnum newState)
        {
            return _stateMap[newState];
        }

        /// <summary>
        /// Scans all GameObjects with tag=playerTag, picks the closest one within detectionRadius.
        /// Does not overwrite the current target - only stores the player target separately.
        /// </summary>
        public bool TryFindNearestPlayerWithinRadius()
        {
            var players = GameObject.FindGameObjectsWithTag(_playerTag);
            Transform best = null;
            float bestSqr = _detectionRadius * _detectionRadius;
            Vector3 myPos = transform.position;

            foreach (var go in players)
            {
                float d2 = (go.transform.position - myPos).sqrMagnitude;
                if (d2 <= bestSqr)
                {
                    bestSqr = d2;
                    best    = go.transform;
                }
            }
            _playerTarget = best;
            return best != null;
        }

        public void FindNearestPlayer()
        {
            var players = GameObject.FindGameObjectsWithTag(_playerTag);
            Transform best = null;
            float bestSqr = float.MaxValue;
            Vector3 myPos = transform.position;

            foreach (var go in players)
            {
                float d2 = (go.transform.position - myPos).sqrMagnitude;
                if (d2 < bestSqr)
                {
                    bestSqr = d2;
                    best    = go.transform;
                }
            }
            _playerTarget = best;
            _target = best; // Set the main target to the player when actively finding them
        }

        public void SetTarget(Transform newTarget)
        {
            _target = newTarget;
        }

        /// <summary>
        /// Sets the target to the currently detected player if one exists.
        /// Used when transitioning from patrol to chase state.
        /// </summary>
        public void SetTargetToPlayer()
        {
            if (_playerTarget != null)
            {
                _target = _playerTarget;
            }
        }

        private void DrawDetectionRays()
        {
            int rayCount = 36;
            float step = 360f / rayCount;

            for (int i = 0; i < rayCount; i++)
            {
                float angle = step * i * Mathf.Deg2Rad;
                Vector3 dir = new Vector3(Mathf.Cos(angle), 0f, Mathf.Sin(angle));
                Debug.DrawRay(
                    transform.position,
                    dir * _detectionRadius,
                    Color.red
                );
            }
        }

        /// <summary>
        /// Finds and caches the local player's camera transform for billboard rotation.
        /// This method searches for the local player (PhotonView.IsMine == true) and gets their camera.
        /// </summary>
        private void FindLocalPlayerCamera()
        {
            // Only search if we don't have a camera reference or enough time has passed
            if (_localPlayerCamera != null || Time.time - _lastCameraSearchTime < CAMERA_SEARCH_INTERVAL)
            {
                return;
            }

            _lastCameraSearchTime = Time.time;

            // Find all TRPPlayerController instances in the scene
            TRPPlayerController[] playerControllers = FindObjectsOfType<TRPPlayerController>();

            foreach (TRPPlayerController playerController in playerControllers)
            {
                // Check if this is the local player
                if (playerController.IsMine)
                {
                    // Get the camera transform from the local player
                    _localPlayerCamera = playerController.CameraRoot;
                    break;
                }
            }

            // Fallback to Camera.main if no local player found (for testing or single-player scenarios)
            if (_localPlayerCamera == null && Camera.main != null)
            {
                _localPlayerCamera = Camera.main.transform;
            }
        }

        /// <summary>
        /// Updates the billboard rotation to face the local player's camera.
        /// Uses smooth rotation for better visual quality and performance.
        /// </summary>
        private void UpdateBillboardRotation()
        {
            // Ensure we have a camera reference
            if (_localPlayerCamera == null)
            {
                FindLocalPlayerCamera();
                return;
            }

            // Calculate direction from billboard to camera
            Vector3 directionToCamera = (_localPlayerCamera.position - _debugBillboard.transform.position).normalized;

            // Create rotation that looks at the camera
            Quaternion targetRotation = Quaternion.LookRotation(directionToCamera);

            // Apply smooth rotation for better visual quality
            _debugBillboard.transform.rotation = Quaternion.Slerp(
                _debugBillboard.transform.rotation,
                targetRotation,
                Time.deltaTime * 5f // Smooth rotation speed
            );
        }

        public void SetIsAttacking(bool isAttacking)
        {
            _isAttacking = isAttacking;
        }


        // CORE ENEMY CONTROLLER OVERRIDES

        protected override void OnKilled()
        {
            base.OnKilled();
            ChangeState(EnemyEnum.Dead);
        }

        protected override void OnRespawned()
        {
            base.OnRespawned();
            ChangeState(EnemyEnum.Idle);
        }

        protected override void OnTargetChanged(Transform newTarget)
        {
            base.OnTargetChanged(newTarget);
            _target = newTarget;

            // If we have a new target and we're not dead, start chasing
            if (newTarget != null && !IsDead)
            {
                ChangeState(EnemyEnum.Chase);
            }
        }

#if UNITY_EDITOR
        // Draw a wireframe sphere in the editor
        private void OnDrawGizmosSelected()
        {
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, _detectionRadius);
        }
#endif
    }
}